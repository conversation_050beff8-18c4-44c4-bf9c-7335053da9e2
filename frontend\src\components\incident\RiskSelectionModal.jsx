import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
// import { mockRiskData } from "../../config";
import { Search } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";

// Helper functions to convert numeric values to text labels with colors
const getImpactLabel = (value) => {
  if (!value) return { label: 'N/A', color: 'bg-gray-100 text-gray-500', dotColor: 'bg-gray-500' };
  const impactLabels = {
    '1': { label: 'Very Low', color: 'bg-green-100 text-green-800', dotColor: 'bg-green-500' },
    '2': { label: 'Low', color: 'bg-blue-100 text-blue-800', dotColor: 'bg-blue-500' },
    '3': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800', dotColor: 'bg-yellow-500' },
    '4': { label: 'High', color: 'bg-orange-100 text-orange-800', dotColor: 'bg-orange-500' },
    '5': { label: 'Very High', color: 'bg-red-100 text-red-800', dotColor: 'bg-red-500' }
  };
  return impactLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500', dotColor: 'bg-gray-500' };
};

const getDMRLabel = (value) => {
  if (!value) return { label: 'N/A', color: 'bg-gray-100 text-gray-500', dotColor: 'bg-gray-500' };
  const dmrLabels = {
    '1': { label: 'Very Low', color: 'bg-red-100 text-red-800', dotColor: 'bg-red-500' },
    '2': { label: 'Low', color: 'bg-orange-100 text-orange-800', dotColor: 'bg-orange-500' },
    '3': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800', dotColor: 'bg-yellow-500' },
    '4': { label: 'High', color: 'bg-blue-100 text-blue-800', dotColor: 'bg-blue-500' },
    '5': { label: 'Very High', color: 'bg-green-100 text-green-800', dotColor: 'bg-green-500' }
  };
  return dmrLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500', dotColor: 'bg-gray-500' };
};

const getProbabilityLabel = (value) => {
  if (!value) return { label: 'N/A', color: 'bg-gray-100 text-gray-500', dotColor: 'bg-gray-500' };
  const probabilityLabels = {
    '1': { label: 'Very Low', color: 'bg-green-100 text-green-800', dotColor: 'bg-green-500' },
    '2': { label: 'Low', color: 'bg-blue-100 text-blue-800', dotColor: 'bg-blue-500' },
    '3': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800', dotColor: 'bg-yellow-500' },
    '4': { label: 'High', color: 'bg-orange-100 text-orange-800', dotColor: 'bg-orange-500' },
    '5': { label: 'Very High', color: 'bg-red-100 text-red-800', dotColor: 'bg-red-500' }
  };
  return probabilityLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500', dotColor: 'bg-gray-500' };
};

export function RiskSelectionModal({ open, onClose, onSelect, risks = [] }) {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");

  const filteredRisks = risks.filter((risk) =>
    Object.values(risk).some((value) =>
      value && value.toString().toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-[90vw] md:max-w-[900px] max-h-[80vh] flex flex-col" aria-describedby="risk-selection-description">
        <DialogHeader>
          <DialogTitle>{t('admin.incidents.risk_selection.select_risk')}</DialogTitle>
          <p id="risk-selection-description" className="text-sm text-gray-500">
            {t('admin.incidents.risk_selection.select_a_risk_to_associate_with_this_incident')}
          </p>
          <div className="relative mt-4">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder={t('admin.incidents.risk_selection.search_risks')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:border-[#F62D51]"
            />
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto mt-4 pr-2">
          <div className="space-y-2">
            {filteredRisks.length > 0 ? (
              filteredRisks.map((risk) => (
                <div
                  key={risk.riskID || risk.id || risk._id}
                  className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    onSelect(risk);
                    onClose();
                  }}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex-1">
                      <h3 className="font-medium">{risk.name || risk.title}</h3>
                      <p className="text-sm text-gray-500">{risk.comment || risk.description || 'No description'}</p>
                    </div>
                    <div className="flex gap-3 text-sm">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium flex items-center ${getImpactLabel(risk.impact).color}`}>
                        <div className={`w-2 h-2 rounded-full mr-1 ${getImpactLabel(risk.impact).dotColor}`}></div>
                        {t('admin.incidents.risk_selection.impact')}: {getImpactLabel(risk.impact).label}
                      </span>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium flex items-center ${getDMRLabel(risk.DMR).color}`}>
                        <div className={`w-2 h-2 rounded-full mr-1 ${getDMRLabel(risk.DMR).dotColor}`}></div>
                        {t('admin.incidents.risk_selection.dmr')}: {getDMRLabel(risk.DMR).label}
                      </span>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium flex items-center ${getProbabilityLabel(risk.probability).color}`}>
                        <div className={`w-2 h-2 rounded-full mr-1 ${getProbabilityLabel(risk.probability).dotColor}`}></div>
                        {t('admin.incidents.risk_selection.probability')}: {getProbabilityLabel(risk.probability).label}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-4 border rounded-lg text-center text-gray-500">
                {t('admin.incidents.risk_selection.no_risks_found_matching_your_search_criteria')}
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="mt-4">
          <Button type="button" variant="outline" onClick={onClose}>
            {t('admin.incidents.risk_selection.cancel')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}



