const sequelize = require('../config/database');
const seedActionPlans = require('./seedActionPlans');

async function seedActionPlansOnly() {
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Seed action plans
    await seedActionPlans();

    console.log('Action plans seeding completed successfully.');
  } catch (error) {
    console.error('Error seeding action plans:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the seed function
seedActionPlansOnly()
  .then(() => {
    console.log('Seeding process completed.');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error during seeding process:', error);
    process.exit(1);
  });
