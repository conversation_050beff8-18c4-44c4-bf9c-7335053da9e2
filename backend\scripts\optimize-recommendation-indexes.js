require('dotenv').config();
const { Sequelize } = require('sequelize');

async function optimizeRecommendationIndexes() {
  const sequelize = new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USER,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      dialect: 'postgres',
      logging: console.log
    }
  );
  
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established.');
    
    console.log('Applying recommendation optimization indexes...');
    
    // ConstatRecommendation junction table indexes
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_constat_recommendation_constat_id 
      ON "ConstatRecommendation" ("constatId");
    `);
    
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_constat_recommendation_recommendation_id 
      ON "ConstatRecommendation" ("recommendationId");
    `);
    
    // Composite index for junction table queries
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_constat_recommendation_composite 
      ON "ConstatRecommendation" ("constatId", "recommendationId");
    `);
    
    // AuditActivity composite index for mission-based queries
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_audit_activity_mission_status 
      ON "AuditActivities" ("auditMissionID", "status");
    `);
    
    // AuditConstat composite index for activity-based queries
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_audit_constat_activity_type 
      ON "AuditConstats" ("auditActivityID", "type");
    `);
    
    // AuditRecommendation composite index for action plan queries
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_audit_recommendation_action_plan_priority 
      ON "AuditRecommendations" ("actionPlanID", "priorite");
    `);
    
    console.log('✅ All recommendation optimization indexes applied successfully!');
    
  } catch (error) {
    console.error('❌ Error applying indexes:', error);
    throw error;
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

// Run the optimization
if (require.main === module) {
  optimizeRecommendationIndexes()
    .then(() => {
      console.log('🎉 Recommendation indexes optimization completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Optimization failed:', error);
      process.exit(1);
    });
}

module.exports = optimizeRecommendationIndexes;
