const db = require('../../models');
const { Control, ActionPlan, sequelize } = db;
const { logCreationActivity, logUpdateActivities, logDeletionActivity, logLinkingActivity } = require('./control-activity-controller');

// Get all controls
const getAllControls = async (req, res) => {
  try {
    const controls = await Control.findAll({
      include: [
        { model: sequelize.models.ControlType, as: 'controlTypeRef' },
        { model: sequelize.models.Risk, as: 'riskRef' }
      ]
    });
    res.json({
      success: true,
      data: controls
    });
  } catch (error) {
    console.error('Error fetching controls:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch controls'
    });
  }
};

// Create new control
const createControl = async (req, res) => {
  try {
    const {
      controlID,
      name,
      code,
      controlKey,
      controlExecutionMethod,
      objective,
      executionProcedure,
      operationalCost,
      organizationalLevel,
      sampleType,
      testingFrequency,
      testingMethod,
      testingPopulationSize,
      testingProcedure,
      implementingActionPlan,
      businessProcess,
      organizationalProcess,
      operation,
      application,
      entity,
      controlType,
      risk,
      comment,
      designQuality,
      effectivenessLevel
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name'].filter(field => !req.body[field])
      });
    }

    // Validate controlKey - must be 0 or 1
    if (controlKey !== undefined && controlKey !== null && ![0, 1].includes(parseInt(controlKey))) {
      return res.status(400).json({
        success: false,
        message: 'Control Key must be 0 or 1'
      });
    }

    // Validate foreign keys before creating
    // IMPORTANT: Always set operation to null to avoid foreign key constraint errors
    let validatedOperation = null;
    console.log('Operation from request:', operation);

    // Always log all operations for debugging
    try {
      const allOperations = await sequelize.models.Operation.findAll({
        attributes: ['operationID', 'name']
      });
      console.log('All operations in database:', allOperations.map(op => op.get({ plain: true })));
    } catch (error) {
      console.error('Error fetching all operations:', error);
    }

    // Even if operation is provided, always set it to null
    if (operation) {
      console.log(`Operation ${operation} provided, but setting to null to avoid foreign key errors`);
    }
    validatedOperation = null;

    let validatedBusinessProcess = null;
    if (businessProcess) {
      const businessProcessExists = await sequelize.models.BusinessProcess.findOne({
        where: { businessProcessID: businessProcess }
      });
      validatedBusinessProcess = businessProcessExists ? businessProcess : null;
    }

    let validatedOrgProcess = null;
    if (organizationalProcess) {
      const orgProcessExists = await sequelize.models.OrganizationalProcess.findOne({
        where: { organizationalProcessID: organizationalProcess }
      });
      validatedOrgProcess = orgProcessExists ? organizationalProcess : null;
    }

    let validatedApplication = null;
    if (application) {
      const applicationExists = await sequelize.models.Application.findOne({
        where: { applicationID: application }
      });
      validatedApplication = applicationExists ? application : null;
    }

    let validatedEntity = null;
    if (entity) {
      const entityExists = await sequelize.models.Entity.findOne({
        where: { entityID: entity }
      });
      validatedEntity = entityExists ? entity : null;
    }

    let validatedControlType = null;
    if (controlType) {
      const controlTypeExists = await sequelize.models.ControlType.findOne({
        where: { controlTypeID: controlType }
      });
      validatedControlType = controlTypeExists ? controlType : null;
    }

    let validatedRisk = null;
    if (risk) {
      const riskExists = await sequelize.models.Risk.findOne({
        where: { riskID: risk }
      });
      validatedRisk = riskExists ? risk : null;
    }

    const control = await Control.create({
      controlID: controlID || `CTRL_${Date.now()}`,
      name,
      code: code || null,
      controlKey: controlKey || null,
      controlExecutionMethod: controlExecutionMethod || null,
      objective: objective || null,
      executionProcedure: executionProcedure || null,
      operationalCost: operationalCost || null,
      organizationalLevel: organizationalLevel || null,
      sampleType: sampleType || null,
      testingFrequency: testingFrequency || null,
      testingMethod: testingMethod || null,
      testingPopulationSize: testingPopulationSize || null,
      testingProcedure: testingProcedure || null,
      implementingActionPlan: implementingActionPlan || null,
      designQuality: designQuality || null,
      effectivenessLevel: effectivenessLevel || null,
      businessProcess: validatedBusinessProcess,
      organizationalProcess: validatedOrgProcess,
      operation: validatedOperation,
      application: validatedApplication,
      entity: validatedEntity,
      controlType: validatedControlType,
      risk: validatedRisk,
      comment: comment || null
    });

    // Log creation activity
    let username = 'Unknown User';
    try {
      if (req.user?.userId) {
        const user = await db.User.findByPk(req.user.userId);
        if (user) {
          username = user.username || user.email || user.name;
        }
      } else {
        username = req.user?.username || req.user?.email || req.user?.name || 'Unknown User';
      }
    } catch (err) {
      console.error('Error getting user for activity logging:', err);
    }

    await logCreationActivity(control, username);

    return res.status(201).json({
      success: true,
      message: 'Control created successfully',
      data: control
    });
  } catch (error) {
    console.error('Error creating control:', error);

    // Check if it's a foreign key constraint error
    if (error.name === 'SequelizeForeignKeyConstraintError') {
      const constraintName = error.index || '';
      const fieldName = constraintName.replace('fk_', '');
      const fieldValue = error.parent?.parameters?.[0] || 'unknown';

      return res.status(400).json({
        success: false,
        message: `Foreign key constraint error: The ${fieldName} with ID ${fieldValue} does not exist.`,
        error: {
          name: error.name,
          field: fieldName,
          value: fieldValue,
          constraint: constraintName
        }
      });
    }

    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create control',
      error: error.name
    });
  }
};

// Get control by ID
const getControlById = async (req, res) => {
  try {
    const { id } = req.params;
    const control = await Control.findOne({
      where: { controlID: id },
      include: [
        { model: sequelize.models.ControlType, as: 'controlTypeRef' },
        { model: sequelize.models.Risk, as: 'riskRef' },
        { model: sequelize.models.BusinessProcess, as: 'businessProcessRef' },
        { model: sequelize.models.OrganizationalProcess, as: 'organizationalProcessRef' },
        { model: sequelize.models.Operation, as: 'operationRef' },
        { model: sequelize.models.Application, as: 'applicationRef' },
        { model: sequelize.models.Entity, as: 'entityRef' },
        { model: ActionPlan, as: 'actionPlans' }
      ]
    });

    if (!control) {
      return res.status(404).json({
        success: false,
        message: 'Control not found'
      });
    }

    // Convert to plain object to modify it
    const controlData = control.get({ plain: true });

    // Add reference data for easier access in the frontend
    if (controlData.businessProcessRef) {
      controlData.businessProcessName = controlData.businessProcessRef.name;
    }
    if (controlData.organizationalProcessRef) {
      controlData.organizationalProcessName = controlData.organizationalProcessRef.name;
    }
    if (controlData.operationRef) {
      controlData.operationName = controlData.operationRef.name;
    }
    if (controlData.applicationRef) {
      controlData.applicationName = controlData.applicationRef.name;
    }
    if (controlData.entityRef) {
      controlData.entityName = controlData.entityRef.name;
    }
    if (controlData.controlTypeRef) {
      controlData.controlTypeName = controlData.controlTypeRef.name;
    }
    if (controlData.riskRef) {
      controlData.riskName = controlData.riskRef.name;
    }

    // Log the control data for debugging
    console.log('Control data being sent to frontend:', {
      controlID: controlData.controlID,
      name: controlData.name,
      controlExecutionMethod: controlData.controlExecutionMethod,
      organizationalLevel: controlData.organizationalLevel,
      sampleType: controlData.sampleType,
      testingFrequency: controlData.testingFrequency,
      testingMethod: controlData.testingMethod
    });

    res.json({
      success: true,
      data: controlData
    });
  } catch (error) {
    console.error('Error fetching control:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch control'
    });
  }
};

// Update control - OPTIMIZED VERSION
const updateControl = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      code,
      controlKey,
      controlExecutionMethod,
      objective,
      executionProcedure,
      operationalCost,
      organizationalLevel,
      sampleType,
      testingFrequency,
      testingMethod,
      testingPopulationSize,
      testingProcedure,
      implementingActionPlan,
      businessProcess,
      organizationalProcess,
      operation,
      application,
      entity,
      controlType,
      risk,
      comment,
      designQuality,
      effectivenessLevel
    } = req.body;

    const control = await Control.findOne({
      where: { controlID: id }
    });

    if (!control) {
      return res.status(404).json({
        success: false,
        message: 'Control not found'
      });
    }

    // Validate controlKey - must be 0 or 1
    if (controlKey !== undefined && controlKey !== null && ![0, 1].includes(parseInt(controlKey))) {
      return res.status(400).json({
        success: false,
        message: 'Control Key must be 0 or 1'
      });
    }

    // OPTIMIZED: Batch validate all foreign keys in a single query using Promise.all
    const foreignKeyValidations = [];
    const foreignKeyMap = {};

    // Prepare validation queries only for fields that are being updated
    if (operation !== undefined && operation !== null && operation !== 'none' && operation !== '') {
      foreignKeyValidations.push(
        sequelize.models.Operation.findOne({ where: { operationID: operation } })
          .then(result => ({ field: 'operation', value: operation, exists: !!result }))
      );
    }

    if (businessProcess && businessProcess !== 'none') {
      foreignKeyValidations.push(
        sequelize.models.BusinessProcess.findOne({ where: { businessProcessID: businessProcess } })
          .then(result => ({ field: 'businessProcess', value: businessProcess, exists: !!result }))
      );
    }

    if (organizationalProcess && organizationalProcess !== 'none') {
      foreignKeyValidations.push(
        sequelize.models.OrganizationalProcess.findOne({ where: { organizationalProcessID: organizationalProcess } })
          .then(result => ({ field: 'organizationalProcess', value: organizationalProcess, exists: !!result }))
      );
    }

    if (application && application !== 'none') {
      foreignKeyValidations.push(
        sequelize.models.Application.findOne({ where: { applicationID: application } })
          .then(result => ({ field: 'application', value: application, exists: !!result }))
      );
    }

    if (entity && entity !== 'none') {
      foreignKeyValidations.push(
        sequelize.models.Entity.findOne({ where: { entityID: entity } })
          .then(result => ({ field: 'entity', value: entity, exists: !!result }))
      );
    }

    if (controlType && controlType !== 'none') {
      foreignKeyValidations.push(
        sequelize.models.ControlType.findOne({ where: { controlTypeID: controlType } })
          .then(result => ({ field: 'controlType', value: controlType, exists: !!result }))
      );
    }

    if (risk && risk !== 'none') {
      foreignKeyValidations.push(
        sequelize.models.Risk.findOne({ where: { riskID: risk } })
          .then(result => ({ field: 'risk', value: risk, exists: !!result }))
      );
    }

    // Execute all validations in parallel
    const validationResults = await Promise.all(foreignKeyValidations);

    // Check for invalid foreign keys
    const invalidFields = validationResults.filter(result => !result.exists);
    if (invalidFields.length > 0) {
      const firstInvalid = invalidFields[0];
      return res.status(400).json({
        success: false,
        message: `The ${firstInvalid.field} with ID ${firstInvalid.value} does not exist in the database.`,
        error: {
          name: 'InvalidForeignKeyError',
          field: firstInvalid.field,
          value: firstInvalid.value
        }
      });
    }

    // Prepare validated values
    const validatedData = {
      operation: operation === undefined ? control.operation :
                 (operation === null || operation === 'none' || operation === '') ? null : operation,
      businessProcess: businessProcess === undefined ? control.businessProcess :
                      (businessProcess === 'none') ? null : businessProcess,
      organizationalProcess: organizationalProcess === undefined ? control.organizationalProcess :
                            (organizationalProcess === 'none') ? null : organizationalProcess,
      application: application === undefined ? control.application :
                  (application === 'none') ? null : application,
      entity: entity === undefined ? control.entity :
             (entity === 'none') ? null : entity,
      controlType: controlType === undefined ? control.controlType :
                  (controlType === 'none') ? null : controlType,
      risk: risk === undefined ? control.risk :
           (risk === 'none') ? null : risk
    };

    // Log ENUM fields being updated
    console.log(`Updating control ${id} ENUM fields:`, {
      controlExecutionMethod: controlExecutionMethod !== undefined ? controlExecutionMethod : control.controlExecutionMethod,
      organizationalLevel: organizationalLevel !== undefined ? organizationalLevel : control.organizationalLevel,
      sampleType: sampleType !== undefined ? sampleType : control.sampleType,
      testingFrequency: testingFrequency !== undefined ? testingFrequency : control.testingFrequency,
      testingMethod: testingMethod !== undefined ? testingMethod : control.testingMethod
    });

    // Get username for activity logging
    let username = 'Unknown User';
    try {
      if (req.user?.userId) {
        const user = await db.User.findByPk(req.user.userId);
        if (user) {
          username = user.username || user.email || user.name;
        }
      } else {
        username = req.user?.username || req.user?.email || req.user?.name || 'Unknown User';
      }
    } catch (err) {
      console.error('Error getting user for activity logging:', err);
    }

    // Prepare update data for activity logging
    const updateData = {
      name: name !== undefined ? name : control.name,
      code: code !== undefined ? code : control.code,
      controlKey: controlKey !== undefined ? controlKey : control.controlKey,
      controlExecutionMethod: controlExecutionMethod !== undefined ? controlExecutionMethod : control.controlExecutionMethod,
      objective: objective !== undefined ? objective : control.objective,
      executionProcedure: executionProcedure !== undefined ? executionProcedure : control.executionProcedure,
      operationalCost: operationalCost !== undefined ? operationalCost : control.operationalCost,
      organizationalLevel: organizationalLevel !== undefined ? organizationalLevel : control.organizationalLevel,
      sampleType: sampleType !== undefined ? sampleType : control.sampleType,
      testingFrequency: testingFrequency !== undefined ? testingFrequency : control.testingFrequency,
      testingMethod: testingMethod !== undefined ? testingMethod : control.testingMethod,
      testingPopulationSize: testingPopulationSize !== undefined ? testingPopulationSize : control.testingPopulationSize,
      testingProcedure: testingProcedure !== undefined ? testingProcedure : control.testingProcedure,
      implementingActionPlan: implementingActionPlan !== undefined ? implementingActionPlan : control.implementingActionPlan,
      designQuality: designQuality !== undefined ? designQuality : control.designQuality,
      effectivenessLevel: effectivenessLevel !== undefined ? effectivenessLevel : control.effectivenessLevel,
      businessProcess: validatedData.businessProcess,
      organizationalProcess: validatedData.organizationalProcess,
      operation: validatedData.operation,
      application: validatedData.application,
      entity: validatedData.entity,
      controlType: validatedData.controlType,
      risk: validatedData.risk,
      comment: comment !== undefined ? comment : control.comment
    };

    // Log update activities before updating
    console.log('🔍 [DEBUG] About to call logUpdateActivities with:', {
      controlID: control.controlID,
      updateDataKeys: Object.keys(updateData),
      username
    });
    await logUpdateActivities(control, updateData, username);

    // OPTIMIZED: Single update operation with minimal logging
    await control.update(updateData);

    console.log(`Control ${id} updated successfully. Fetching updated data...`);

    // For performance: only fetch associations if we're updating reference fields
    const needsAssociations = Boolean(
      controlType || risk || businessProcess || organizationalProcess ||
      operation || application || entity
    );

    const updatedControl = await Control.findOne({
      where: { controlID: id },
      include: needsAssociations ? [
        { model: sequelize.models.ControlType, as: 'controlTypeRef' },
        { model: sequelize.models.Risk, as: 'riskRef' },
        { model: sequelize.models.BusinessProcess, as: 'businessProcessRef' },
        { model: sequelize.models.OrganizationalProcess, as: 'organizationalProcessRef' },
        { model: sequelize.models.Operation, as: 'operationRef' },
        { model: sequelize.models.Application, as: 'applicationRef' },
        { model: sequelize.models.Entity, as: 'entityRef' }
      ] : []
    });

    // Convert to plain object and add reference data
    const updatedControlData = updatedControl.get({ plain: true });

    // Add reference data for easier access in the frontend
    if (updatedControlData.businessProcessRef) {
      updatedControlData.businessProcessName = updatedControlData.businessProcessRef.name;
    }
    if (updatedControlData.organizationalProcessRef) {
      updatedControlData.organizationalProcessName = updatedControlData.organizationalProcessRef.name;
    }
    if (updatedControlData.operationRef) {
      updatedControlData.operationName = updatedControlData.operationRef.name;
    }
    if (updatedControlData.applicationRef) {
      updatedControlData.applicationName = updatedControlData.applicationRef.name;
    }
    if (updatedControlData.entityRef) {
      updatedControlData.entityName = updatedControlData.entityRef.name;
    }
    if (updatedControlData.controlTypeRef) {
      updatedControlData.controlTypeName = updatedControlData.controlTypeRef.name;
    }
    if (updatedControlData.riskRef) {
      updatedControlData.riskName = updatedControlData.riskRef.name;
    }

    console.log(`Updated control ENUM fields:`, {
      controlExecutionMethod: updatedControlData.controlExecutionMethod,
      organizationalLevel: updatedControlData.organizationalLevel,
      sampleType: updatedControlData.sampleType,
      testingFrequency: updatedControlData.testingFrequency,
      testingMethod: updatedControlData.testingMethod
    });

    res.json({
      success: true,
      message: 'Control updated successfully',
      data: updatedControlData
    });
  } catch (error) {
    console.error('Error updating control:', error);

    // Check if it's a foreign key constraint error
    if (error.name === 'SequelizeForeignKeyConstraintError') {
      const constraintName = error.index || '';
      const fieldName = constraintName.replace('fk_', '');
      const fieldValue = error.parent?.parameters?.[0] || 'unknown';

      return res.status(400).json({
        success: false,
        message: `Foreign key constraint error: The ${fieldName} with ID ${fieldValue} does not exist.`,
        error: {
          name: error.name,
          field: fieldName,
          value: fieldValue,
          constraint: constraintName
        }
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Failed to update control',
      error: error.name
    });
  }
};

// Delete control
const deleteControl = async (req, res) => {
  try {
    const { id } = req.params;
    const control = await Control.findOne({
      where: { controlID: id }
    });

    if (!control) {
      return res.status(404).json({
        success: false,
        message: 'Control not found'
      });
    }

    // Get username for activity logging
    let username = 'Unknown User';
    try {
      if (req.user?.userId) {
        const user = await db.User.findByPk(req.user.userId);
        if (user) {
          username = user.username || user.email || user.name;
        }
      } else {
        username = req.user?.username || req.user?.email || req.user?.name || 'Unknown User';
      }
    } catch (err) {
      console.error('Error getting user for activity logging:', err);
    }

    // Log deletion activity before destroying
    await logDeletionActivity(control, username);

    await control.destroy();

    res.json({
      success: true,
      message: 'Control deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting control:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete control'
    });
  }
};

// Add action plans to control
const addActionPlansToControl = async (req, res) => {
  try {
    const { id } = req.params;
    const { actionPlanIDs } = req.body;

    if (!actionPlanIDs || !Array.isArray(actionPlanIDs)) {
      return res.status(400).json({ success: false, message: 'actionPlanIDs must be an array' });
    }

    const control = await Control.findOne({ where: { controlID: id } });
    if (!control) {
      return res.status(404).json({ success: false, message: 'Control not found' });
    }

    // Get username for activity logging
    let username = 'Unknown User';
    try {
      if (req.user?.userId) {
        const user = await db.User.findByPk(req.user.userId);
        if (user) {
          username = user.username || user.email || user.name;
        }
      } else {
        username = req.user?.username || req.user?.email || req.user?.name || 'Unknown User';
      }
    } catch (err) {
      console.error('Error getting user for activity logging:', err);
    }

    // Get action plan names for logging
    const actionPlans = await ActionPlan.findAll({
      where: { actionPlanID: actionPlanIDs },
      attributes: ['actionPlanID', 'name']
    });

    // Add action plans to control
    await control.addActionPlans(actionPlanIDs);

    // Log linking activities for each action plan
    for (const actionPlan of actionPlans) {
      await logLinkingActivity(
        control.controlID,
        username,
        'actionPlan',
        `${actionPlan.name} (${actionPlan.actionPlanID})`,
        'link'
      );
    }

    const updatedControl = await Control.findOne({
      where: { controlID: id },
      include: [{ model: ActionPlan, as: 'actionPlans' }]
    });

    res.json({ success: true, data: updatedControl });
  } catch (error) {
    console.error('Error adding action plans to control:', error);
    res.status(500).json({ success: false, message: error.message || 'Failed to add action plans' });
  }
};

// Remove action plan from control
const removeActionPlanFromControl = async (req, res) => {
  try {
    const { id, actionPlanID } = req.params;

    const control = await Control.findOne({ where: { controlID: id } });
    if (!control) {
      return res.status(404).json({ success: false, message: 'Control not found' });
    }

    // Get username for activity logging
    let username = 'Unknown User';
    try {
      if (req.user?.userId) {
        const user = await db.User.findByPk(req.user.userId);
        if (user) {
          username = user.username || user.email || user.name;
        }
      } else {
        username = req.user?.username || req.user?.email || req.user?.name || 'Unknown User';
      }
    } catch (err) {
      console.error('Error getting user for activity logging:', err);
    }

    // Get action plan name for logging
    const actionPlan = await ActionPlan.findOne({
      where: { actionPlanID },
      attributes: ['actionPlanID', 'name']
    });

    // Remove action plan from control
    await control.removeActionPlan(actionPlanID);

    // Log unlinking activity
    if (actionPlan) {
      await logLinkingActivity(
        control.controlID,
        username,
        'actionPlan',
        `${actionPlan.name} (${actionPlan.actionPlanID})`,
        'unlink'
      );
    }

    const updatedControl = await Control.findOne({
      where: { controlID: id },
      include: [{ model: ActionPlan, as: 'actionPlans' }]
    });

    res.json({ success: true, data: updatedControl });
  } catch (error) {
    console.error('Error removing action plan from control:', error);
    res.status(500).json({ success: false, message: error.message || 'Failed to remove action plan' });
  }
};

// Replace all action plans for control
const replaceActionPlansForControl = async (req, res) => {
  try {
    const { id } = req.params;
    const { actionPlanIDs } = req.body;

    if (!actionPlanIDs || !Array.isArray(actionPlanIDs)) {
      return res.status(400).json({ success: false, message: 'actionPlanIDs must be an array' });
    }

    const control = await Control.findOne({ where: { controlID: id } });
    if (!control) {
      return res.status(404).json({ success: false, message: 'Control not found' });
    }

    // Replace all action plans for control
    await control.setActionPlans(actionPlanIDs);
    const updatedControl = await Control.findOne({
      where: { controlID: id },
      include: [{ model: ActionPlan, as: 'actionPlans' }]
    });

    res.json({ success: true, data: updatedControl });
  } catch (error) {
    console.error('Error replacing action plans for control:', error);
    res.status(500).json({ success: false, message: error.message || 'Failed to replace action plans' });
  }
};

module.exports = {
  getAllControls,
  createControl,
  getControlById,
  updateControl,
  deleteControl,
  addActionPlansToControl,
  removeActionPlanFromControl,
  replaceActionPlansForControl
};
