const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../../middleware/auth');
const auditMissionRapportController = require('../../../controllers/audit/rapport/audit-mission-rapport-controller');

// Add specific CORS middleware for audit mission rapport routes
router.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:5173');
  res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, x-user-id');
  res.header('Access-Control-Allow-Credentials', 'true');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
});

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get mission report
router.get('/mission-report/:missionId', authorizeRoles(['audit_director', 'auditor']), auditMissionRapportController.getMissionReport);

// Get enhanced mission report with front page and sommaire
router.get('/enhanced-mission-report/:missionId', authorizeRoles(['audit_director', 'auditor']), auditMissionRapportController.getEnhancedMissionReport);

// Add PDF preview route
router.get('/mission-report/:missionId/preview-pdf', authorizeRoles(['audit_director', 'auditor']), require('../../../controllers/audit/rapport/audit-mission-rapport-preview-controller').previewMissionReportPdf);

// Add PDF text extraction route
router.get('/mission-report/:missionId/pdf-text', authorizeRoles(['audit_director', 'auditor']), require('../../../controllers/audit/rapport/audit-mission-rapport-preview-controller').extractMissionReportPdfText);

// Add Gemini conversation route
router.get('/mission-report/:missionId/gemini-conversation', authorizeRoles(['audit_director', 'auditor']), require('../../../controllers/audit/rapport/audit-mission-rapport-preview-controller').generateGeminiConversation);

module.exports = router;