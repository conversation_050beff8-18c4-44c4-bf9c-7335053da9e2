const { IncidentType, Incident } = require('../models');
const sequelize = require('../models').sequelize;

async function seedIncidentTypesAndAssign() {
  // 1. Generate 80+ types
  const incidentTypes = Array.from({ length: 80 }, (_, i) => ({
    incidentTypeID: `INC_TYPE_${String(i + 1).padStart(3, '0')}`,
    name: `Type ${i + 1}`,
    description: `Description for Type ${i + 1}`,
  }));

  try {
    // 2. Seed types
    await IncidentType.destroy({ where: {} });
    await IncidentType.bulkCreate(incidentTypes);

    // 3. Fetch all incidents
    const incidents = await Incident.findAll();

    // 4. Assign a type to each incident (round-robin)
    for (let i = 0; i < incidents.length; i++) {
      const typeIndex = i % incidentTypes.length;
      await incidents[i].update({ incidentTypeID: incidentTypes[typeIndex].incidentTypeID });
    }

    console.log('Seeded 80+ incident types and assigned them to all incidents.');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await sequelize.close();
  }
}

seedIncidentTypesAndAssign();