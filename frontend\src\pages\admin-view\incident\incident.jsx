import { useState, useEffect, useMemo } from 'react';
import { ArrowUpDown, Plus, Loader2, Trash2, AlertTriangle, Sparkles, Search, CalendarIcon, X, ChevronUp } from 'lucide-react';
import PageHeader from '@/components/ui/page-header';
import { useNavigate } from 'react-router-dom';
import { Button } from "../../../components/ui/button";
import { Checkbox } from "../../../components/ui/checkbox";
import { Input } from "../../../components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../../components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "../../../components/ui/popover";
import { Calendar } from "../../../components/ui/calendar";
import { Badge } from "../../../components/ui/badge";
import { format } from 'date-fns';
import axios from 'axios';
import { toast } from 'react-hot-toast';
import TablePagination from "../../../components/ui/table-pagination";
import FilterPanel from "../../../components/ui/filter-panel";
import { DateRangePickerShadcn } from "../../../components/ui/date-range-picker-shadcn";
import incidentIcon from '../../../assets/incident.png';
import { useSelector } from 'react-redux';
import { hasPermission } from '@/store/auth-slice';
import { getApiBaseUrl } from '../../../utils/api-config.js';
import { useTranslation } from "react-i18next";
const API_BASE_URL = getApiBaseUrl();
// Format date to YYYY-MM-DD using date-fns
const formatDate = (dateString) => {
  if (!dateString) return '';
  try {
    const date = new Date(dateString);
    return format(date, 'yyyy-MM-dd');
  } catch {
    return '';
  }
};

function Incident() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [allIncidents, setAllIncidents] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [selectedIncidents, setSelectedIncidents] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  // Permission check
  const state = useSelector(state => state);
  const canDelete = hasPermission(state, 'delete');

  // Reference data from Redux store
  const entities = useSelector((state) => state.referenceData?.entities?.data || []);
  const businessLines = useSelector((state) => state.referenceData?.businessLines?.data || []);
  const incidentTypes = useSelector((state) => state.referenceData?.incidentTypes?.data || []);
  const businessProcesses = useSelector((state) => state.referenceData?.businessProcesses?.data || []);
  const orgProcesses = useSelector((state) => state.referenceData?.organizationalProcesses?.data || []);

  // Filter states
  const [filters, setFilters] = useState({
    impact: "all",
    priority: "all",
    declarationDateRange: { start: null, end: null },
    detectionDateRange: { start: null, end: null },
    occurrenceDateRange: { start: null, end: null },
    entityID: "all",
    businessLineID: 'all',
    incidentTypeID: 'all',
    businessProcessID: 'all',
    organizationalProcessID: 'all',
    nearMiss: 'all',
    nature: 'all'
  });

  // Fetch incidents from the API when the component mounts
  const fetchIncidents = async () => {
    try {
      setLoading(true);
      
      // Get token from localStorage
      const token = localStorage.getItem('authToken');
      const headers = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      const response = await axios.get(`${API_BASE_URL}/incidents`, {
        withCredentials: true,
        headers,
        timeout: 30000
      });
      
      let incidentsArray = response.data;
      if (response.data && 'data' in response.data) {
        incidentsArray = response.data.data;
      }

      if (!Array.isArray(incidentsArray)) {
        throw new Error('Expected an array of incidents, received: ' + JSON.stringify(incidentsArray));
      }

      const transformedIncidents = incidentsArray.map(incident => {
        if (incidentsArray.indexOf(incident) === 0) {
          console.log('First incident data:', incident);
        }

        return {
          ...incident,
          entity: incident.entity ? incident.entity.name : '',
          control: incident.control ? incident.control.name : '',
          businessLine: incident.businessLine ? incident.businessLine.name : '',
          incidentType: incident.incidentType ? incident.incidentType.name : '',
          businessProcess: incident.businessProcess ? incident.businessProcess.name : '',
          organizationalProcess: incident.organizationalProcess ? incident.organizationalProcess.name : '',
          product: incident.product ? incident.product.name : '',
          application: incident.application ? incident.application.name : '',
          risk: incident.risk ? incident.risk.name : '',
          actionPlan: incident.actionPlan ? incident.actionPlan.name : '',
          nearMiss: incident.nearMiss !== undefined ? (incident.nearMiss ? '1' : '0') : '',
        };
      });
      setAllIncidents(transformedIncidents);
      setError(null);
    } catch (error) {
      console.error('Erreur lors du chargement des incidents :', error);
      setError(
        error.response?.data?.message ||
        error.message ||
        "Échec du chargement des incidents. Vérifiez si le serveur backend fonctionne."
      );
      toast.error("Échec du chargement des incidents. Veuillez rafraîchir la page.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchIncidents();
  }, []);

  // Reset to page 1 when searchQuery changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  // Clear all filters
  const clearFilters = () => {
    console.log('Clearing all filters');
    const clearedFilters = {
      impact: "all",
      priority: "all",
      declarationDateRange: { start: null, end: null },
      detectionDateRange: { start: null, end: null },
      occurrenceDateRange: { start: null, end: null },
      entityID: "all",
      businessLineID: 'all',
      incidentTypeID: 'all',
      businessProcessID: 'all',
      organizationalProcessID: 'all',
      nearMiss: 'all',
      nature: 'all'
    };

    setFilters(clearedFilters);

    console.log('Current filters before clearing:', filters);
    console.log('Filters cleared to:', clearedFilters);

    setTimeout(() => {
      console.log('Filters after clearing:', filters);
    }, 100);
  };

  // Filter incidents based on searchQuery and filters using useMemo for performance
  const filteredIncidents = useMemo(() => {
    return allIncidents.filter(incident => {
      if (filters.impact && filters.impact !== 'all') {
        console.log('Impact filter:', {
          filterValue: filters.impact,
          incidentValue: incident.impact,
          match: incident.impact === filters.impact
        });
        if (incident.impact !== filters.impact) return false;
      }

      if (filters.priority && filters.priority !== 'all') {
        console.log('Priority filter:', {
          filterValue: filters.priority,
          incidentValue: incident.priority,
          match: incident.priority === filters.priority
        });
        if (incident.priority !== filters.priority) return false;
      }

      if (filters.entityID && filters.entityID !== 'all') {
        console.log('Entity filter:', {
          filterValue: filters.entityID,
          incidentValue: incident.entityID,
          match: incident.entityID === filters.entityID
        });
        if (incident.entityID !== filters.entityID) return false;
      }

      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        if (
          !(
            (incident.name && incident.name.toLowerCase().includes(query)) ||
            (incident.description && incident.description.toLowerCase().includes(query)) ||
            (incident.declaredBy && incident.declaredBy.toLowerCase().includes(query)) ||
            (incident.entity && incident.entity.toLowerCase().includes(query)) ||
            (incident.control && incident.control.toLowerCase().includes(query)) ||
            (incident.incidentType && incident.incidentType.toLowerCase().includes(query)) ||
            (incident.businessLine && incident.businessLine.toLowerCase().includes(query))
          )
        ) {
          return false;
        }
      }

      if (filters.declarationDateRange.start || filters.declarationDateRange.end) {
        if (!incident.declarationDate) return false;

        try {
          const incidentDate = new Date(incident.declarationDate);

          if (filters.declarationDateRange.start && filters.declarationDateRange.end) {
            const startDate = new Date(filters.declarationDateRange.start);
            const endDate = new Date(filters.declarationDateRange.end);
            endDate.setHours(23, 59, 59, 999);

            if (incidentDate < startDate || incidentDate > endDate) {
              return false;
            }
          } else if (filters.declarationDateRange.start) {
            const startDate = new Date(filters.declarationDateRange.start);
            if (incidentDate < startDate) {
              return false;
            }
          } else if (filters.declarationDateRange.end) {
            const endDate = new Date(filters.declarationDateRange.end);
            endDate.setHours(23, 59, 59, 999);

            if (incidentDate > endDate) {
              return false;
            }
          }
        } catch (error) {
          console.error("Error filtering by declaration date:", error);
          return false;
        }
      }

      if (filters.detectionDateRange.start || filters.detectionDateRange.end) {
        if (!incident.detectionDate) return false;

        try {
          const incidentDate = new Date(incident.detectionDate);

          if (filters.detectionDateRange.start && filters.detectionDateRange.end) {
            const startDate = new Date(filters.detectionDateRange.start);
            const endDate = new Date(filters.detectionDateRange.end);
            endDate.setHours(23, 59, 59, 999);

            if (incidentDate < startDate || incidentDate > endDate) {
              return false;
            }
          } else if (filters.detectionDateRange.start) {
            const startDate = new Date(filters.detectionDateRange.start);
            if (incidentDate < startDate) {
              return false;
            }
          } else if (filters.detectionDateRange.end) {
            const endDate = new Date(filters.detectionDateRange.end);
            endDate.setHours(23, 59, 59, 999);

            if (incidentDate > endDate) {
              return false;
            }
          }
        } catch (error) {
          console.error("Error filtering by detection date:", error);
          return false;
        }
      }

      if (filters.occurrenceDateRange.start || filters.occurrenceDateRange.end) {
        if (!incident.occurrenceDate) return false;

        try {
          const incidentDate = new Date(incident.occurrenceDate);

          if (filters.occurrenceDateRange.start && filters.occurrenceDateRange.end) {
            const startDate = new Date(filters.occurrenceDateRange.start);
            const endDate = new Date(filters.occurrenceDateRange.end);
            endDate.setHours(23, 59, 59, 999);

            if (incidentDate < startDate || incidentDate > endDate) {
              return false;
            }
          } else if (filters.occurrenceDateRange.start) {
            const startDate = new Date(filters.occurrenceDateRange.start);
            if (incidentDate < startDate) {
              return false;
            }
          } else if (filters.occurrenceDateRange.end) {
            const endDate = new Date(filters.occurrenceDateRange.end);
            endDate.setHours(23, 59, 59, 999);

            if (incidentDate > endDate) {
              return false;
            }
          }
        } catch (error) {
          console.error("Error filtering by occurrence date:", error);
          return false;
        }
      }

      if (filters.businessLineID && filters.businessLineID !== 'all') {
        console.log('Business Line filter:', {
          filterValue: filters.businessLineID,
          incidentValue: incident.businessLineID,
          match: incident.businessLineID === filters.businessLineID
        });
        if (incident.businessLineID !== filters.businessLineID) return false;
      }

      if (filters.incidentTypeID && filters.incidentTypeID !== 'all') {
        console.log('Incident Type filter:', {
          filterValue: filters.incidentTypeID,
          incidentValue: incident.incidentTypeID,
          match: incident.incidentTypeID === filters.incidentTypeID
        });
        if (incident.incidentTypeID !== filters.incidentTypeID) return false;
      }

      if (filters.businessProcessID && filters.businessProcessID !== 'all') {
        console.log('Business Process filter:', {
          filterValue: filters.businessProcessID,
          incidentValue: incident.businessProcessID,
          match: incident.businessProcessID === filters.businessProcessID
        });
        if (incident.businessProcessID !== filters.businessProcessID) return false;
      }

      if (filters.organizationalProcessID && filters.organizationalProcessID !== 'all') {
        console.log('Organizational Process filter:', {
          filterValue: filters.organizationalProcessID,
          incidentValue: incident.organizationalProcessID,
          match: incident.organizationalProcessID === filters.organizationalProcessID
        });
        if (incident.organizationalProcessID !== filters.organizationalProcessID) return false;
      }

      if (filters.nearMiss !== 'all') {
        try {
          const nearMissValue = parseInt(filters.nearMiss, 10);

          let incidentNearMiss;

          if (typeof incident.nearMiss === 'boolean') {
            incidentNearMiss = incident.nearMiss ? 1 : 0;
          } else if (incident.nearMiss === '1' || incident.nearMiss === '0') {
            incidentNearMiss = parseInt(incident.nearMiss, 10);
          } else if (incident.nearMiss === 1 || incident.nearMiss === 0) {
            incidentNearMiss = incident.nearMiss;
          } else {
            incidentNearMiss = 0;
          }

          console.log('Near Miss comparison:', {
            filter: nearMissValue,
            incident: incidentNearMiss,
            originalValue: incident.nearMiss,
            match: incidentNearMiss === nearMissValue
          });

          if (incidentNearMiss !== nearMissValue) return false;
        } catch (error) {
          console.error('Error filtering by Near Miss:', error);
          return false;
        }
      }

      if (filters.nature && filters.nature !== 'all') {
        console.log('Nature filter:', {
          filterValue: filters.nature,
          incidentValue: incident.nature,
          match: incident.nature === filters.nature
        });
        if (incident.nature !== filters.nature) return false;
      }

      return true;
    });
  }, [allIncidents, searchQuery, filters]);

  // Sort incidents
  const sortedIncidents = useMemo(() => {
    let sortableItems = [...filteredIncidents];
    if (sortConfig.key !== null) {
      sortableItems.sort((a, b) => {
        const aValue = a[sortConfig.key] || '';
        const bValue = b[sortConfig.key] || '';
        return sortConfig.direction === 'asc'
          ? aValue.toString().localeCompare(bValue.toString(), undefined, { numeric: true })
          : bValue.toString().localeCompare(aValue.toString(), undefined, { numeric: true });
      });
    }
    return sortableItems;
  }, [filteredIncidents, sortConfig]);

  // Pagination logic
  const totalPages = Math.ceil(filteredIncidents.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sortedIncidents.slice(indexOfFirstItem, indexOfLastItem);

  // Event handlers
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedIncidents(currentItems.map(incident => incident.incidentID));
    } else {
      setSelectedIncidents([]);
    }
  };

  const handleSelectIncident = (incidentID) => {
    setSelectedIncidents(prev => {
      if (prev.includes(incidentID)) {
        return prev.filter(item => item !== incidentID);
      } else {
        return [...prev, incidentID];
      }
    });
  };

  const handleRowClick = (incidentID) => {
    const path = window.location.pathname;
    if (path.includes('/audit')) {
      navigate(`/audit/incident/edit/${incidentID}`);
    } else if (path.includes('/super-admin')) {
      navigate(`/super-admin/incident/edit/${incidentID}`);
    } else {
      navigate(`/admin/incident/edit/${incidentID}`);
    }
  };

  const handleWorkflowClick = (incidentID, e) => {
    e.stopPropagation();
    const path = window.location.pathname;
    if (path.includes('/audit')) {
      navigate(`/audit/incident/edit/${incidentID}/workflow`);
    } else if (path.includes('/super-admin')) {
      navigate(`/super-admin/incident/edit/${incidentID}/workflow`);
    } else {
      navigate(`/admin/incident/edit/${incidentID}/workflow`);
    }
  };

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const handleDeleteSelected = async () => {
    if (!canDelete) {
      toast.error("Vous n'avez pas la permission de supprimer des incidents");
      return;
    }
    if (selectedIncidents.length === 0) {
      toast.error("Aucun incident sélectionné");
      return;
    }
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer ${selectedIncidents.length} incident(s) sélectionné(s) ? Cela supprimera également toutes les pièces jointes et références associées à ces incidents.`)) {
      try {
        setLoading(true);
        let failedDeletions = [];

        const attemptDelete = async (incidentID, retries = 2) => {
          for (let attempt = 0; attempt <= retries; attempt++) {
            try {
              await axios.delete(`${API_BASE_URL}/incidents/${incidentID}`, {
                withCredentials: true,
                timeout: 60000,
                headers: { 'Content-Type': 'application/json' },
              });
              return { success: true };
            } catch (error) {
              if (attempt === retries) {
                let userMessage = 'Unknown error';
                
                if (error.response) {
                  if (error.response.data && error.response.data.message) {
                    userMessage = error.response.data.message;
                    console.error('Server error:', error.response.data);
                  } else {
                    userMessage = `Server error: ${error.response.status} ${error.response.statusText}`;
                  }
                } else if (error.request) {
                  userMessage = 'No response from server. Please check your connection.';
                } else {
                  userMessage = error.message || 'Unknown error occurred';
                }
                
                return { success: false, error: `Cannot delete incident ${incidentID}: ${userMessage}` };
              }
              await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
            }
          }
        };

        const batchSize = 3;
        for (let i = 0; i < selectedIncidents.length; i += batchSize) {
          const batch = selectedIncidents.slice(i, i + batchSize);
          await Promise.all(
            batch.map(async (incidentID) => {
              const result = await attemptDelete(incidentID);
              if (!result.success) failedDeletions.push({ id: incidentID, error: result.error });
              await new Promise(resolve => setTimeout(resolve, 500));
              return result;
            })
          );

          if (i + batchSize < selectedIncidents.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        await fetchIncidents();
        setSelectedIncidents([]);

        if (failedDeletions.length > 0) {
          const errorMessage = failedDeletions.map(f => f.error).join('; ');
          toast.error(`Échec de la suppression de ${failedDeletions.length} incident(s) : ${errorMessage}`);
        } else {
          toast.success("Tous les incidents sélectionnés et leurs références ont été supprimés avec succès");
        }
      } catch (error) {
        console.error("Erreur lors du processus de suppression :", error);
        toast.error("Une erreur est survenue lors du processus de suppression");
      } finally {
        setLoading(false);
      }
    }
  };

  const columns = [
    { key: 'name', label: 'Nom', sortable: true },
    { key: 'declaredBy', label: 'Déclaré par', sortable: true },
    { key: 'impact', label: 'Impact', sortable: true },
    { key: 'priority', label: 'Priorité', sortable: true },
    { key: 'declarationDate', label: 'Date de déclaration', sortable: true },
    { key: 'declarantEntity', label: "Entité déclarante", sortable: true },
    { key: 'detectionDate', label: 'Date de détection', sortable: true },
    { key: 'occurrenceDate', label: 'Date de survenance', sortable: true },
    { key: 'description', label: 'Description', sortable: true },
    { key: 'nearMiss', label: 'Quasi-incident', sortable: true },
    { key: 'nature', label: 'Nature', sortable: true },
    { key: 'actionPlan', label: 'Plan d\'action', sortable: true },
    { key: 'currency', label: 'Devise', sortable: true },
    { key: 'grossLoss', label: 'Perte brute', sortable: true },
    { key: 'recoveries', label: 'Recouvrements', sortable: true },
    { key: 'provisions', label: 'Provisions', sortable: true },
    { key: 'risk', label: 'Risque', sortable: true },
    { key: 'control', label: 'Contrôle', sortable: true },
    { key: 'entity', label: 'Entité', sortable: true },
    { key: 'businessLine', label: 'Ligne métier', sortable: true },
    { key: 'incidentType', label: 'Type d\'incident', sortable: true },
    { key: 'businessProcess', label: 'Processus métier', sortable: true },
    { key: 'organizationalProcess', label: 'Processus organisationnel', sortable: true },
    { key: 'product', label: 'Produit', sortable: true },
    { key: 'application', label: 'Application', sortable: true },
    { key: 'workflow', label: 'Workflow', sortable: false },
  ];

  if (loading) {
    return (
      <div className="h-screen w-full flex items-center justify-center bg-white">
        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
      </div>
    );
  }

  return (
    <div className="p-6 pb-20 flex flex-col">
      <PageHeader
        title="Incidents"
        description="Suivez, gérez et analysez tous les incidents de votre organisation. Surveillez le statut des incidents, attribuez des priorités et mesurez l'impact."
        section="Incident"
        icon={AlertTriangle}
        searchPlaceholder="Rechercher un incident..."
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
      />

      <div className="mb-6 filter-panel-container">
        <FilterPanel
          filters={[
            {
              id: "impact",
              label: "Impact",
              component: (
                <Select
                  value={filters.impact}
                  onValueChange={(value) => setFilters({ ...filters, impact: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Sélectionner l'impact" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous</SelectItem>
                    <SelectItem value="Very Low">Très faible</SelectItem>
                    <SelectItem value="Low">Faible</SelectItem>
                    <SelectItem value="Medium">Moyen</SelectItem>
                    <SelectItem value="High">Fort</SelectItem>
                    <SelectItem value="Very High">Très fort</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "priority",
              label: "Priorité",
              component: (
                <Select
                  value={filters.priority}
                  onValueChange={(value) => setFilters({ ...filters, priority: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Sélectionner la priorité" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes</SelectItem>
                    <SelectItem value="Low">Faible</SelectItem>
                    <SelectItem value="Medium">Moyenne</SelectItem>
                    <SelectItem value="High">Forte</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "declarationDateRange",
              label: "Date de déclaration",
              component: (
                <DateRangePickerShadcn
                  value={filters.declarationDateRange}
                  onChange={(value) => {
                    setFilters({
                      ...filters,
                      declarationDateRange: value,
                    });
                  }}
                  placeholder="Sélectionner la période de déclaration"
                />
              ),
            },
            {
              id: "detectionDateRange",
              label: "Date de détection",
              component: (
                <DateRangePickerShadcn
                  value={filters.detectionDateRange}
                  onChange={(value) => {
                    setFilters({
                      ...filters,
                      detectionDateRange: value,
                    });
                  }}
                  placeholder="Sélectionner la période de détection"
                />
              ),
            },
            {
              id: "occurrenceDateRange",
              label: "Date de survenance",
              component: (
                <DateRangePickerShadcn
                  value={filters.occurrenceDateRange}
                  onChange={(value) => {
                    setFilters({
                      ...filters,
                      occurrenceDateRange: value,
                    });
                  }}
                  placeholder="Sélectionner la période de survenance"
                />
              ),
            },
            {
              id: "entityID",
              label: "Entité",
              component: (
                <Select
                  value={filters.entityID}
                  onValueChange={(value) => setFilters({ ...filters, entityID: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Sélectionner l'entité" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes</SelectItem>
                    {entities.map((entity) => (
                      <SelectItem key={entity.entityID} value={entity.entityID}>
                        {entity.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "businessLineID",
              label: "Ligne métier",
              component: (
                <Select
                  value={filters.businessLineID}
                  onValueChange={(value) => setFilters({ ...filters, businessLineID: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Sélectionner la ligne métier" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes</SelectItem>
                    {businessLines.map((line) => (
                      <SelectItem key={line.businessLineID} value={line.businessLineID}>
                        {line.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "incidentTypeID",
              label: "Type d'incident",
              component: (
                <Select
                  value={filters.incidentTypeID}
                  onValueChange={(value) => setFilters({ ...filters, incidentTypeID: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Sélectionner le type d'incident" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous</SelectItem>
                    {incidentTypes.map((type) => (
                      <SelectItem key={type.incidentTypeID} value={type.incidentTypeID}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "businessProcessID",
              label: "Processus métier",
              component: (
                <Select
                  value={filters.businessProcessID}
                  onValueChange={(value) => setFilters({ ...filters, businessProcessID: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Sélectionner le processus métier" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous</SelectItem>
                    {businessProcesses && businessProcesses.map((process) => (
                      <SelectItem key={process.businessProcessID} value={process.businessProcessID}>
                        {process.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "organizationalProcessID",
              label: "Processus organisationnel",
              component: (
                <Select
                  value={filters.organizationalProcessID}
                  onValueChange={(value) => setFilters({ ...filters, organizationalProcessID: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Sélectionner le processus organisationnel" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous</SelectItem>
                    {orgProcesses && orgProcesses.map((process) => (
                      <SelectItem key={process.organizationalProcessID} value={process.organizationalProcessID}>
                        {process.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "nearMiss",
              label: "Quasi-incident",
              component: (
                <Select
                  value={filters.nearMiss}
                  onValueChange={(value) => setFilters({ ...filters, nearMiss: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Sélectionner quasi-incident" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous</SelectItem>
                    <SelectItem value="1">Oui (1)</SelectItem>
                    <SelectItem value="0">Non (0)</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "nature",
              label: "Nature",
              component: (
                <Select
                  value={filters.nature}
                  onValueChange={(value) => setFilters({ ...filters, nature: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Sélectionner la nature" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes</SelectItem>
                    <SelectItem value="Internal">Interne</SelectItem>
                    <SelectItem value="External">Externe</SelectItem>
                    <SelectItem value="Regulatory">Réglementaire</SelectItem>
                    <SelectItem value="Operational">Opérationnelle</SelectItem>
                    <SelectItem value="Financial">Financière</SelectItem>
                    <SelectItem value="Strategic">Stratégique</SelectItem>
                    <SelectItem value="Compliance">Conformité</SelectItem>
                    <SelectItem value="Other">Autre</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
          ]}
          activeFilters={filters}
          onClearFilters={clearFilters}
        />
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-800 rounded-lg">
          {error}
        </div>
      )}

      <div className="flex justify-end items-center gap-3 mb-4">
        <Button
          className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white shadow-md flex items-center gap-2 transition-all duration-300 hover:shadow-purple-500/50 hover:shadow-lg relative overflow-hidden group border-0"
          onClick={() => {
            const path = window.location.pathname;
            if (path.includes('/super-admin')) {
              navigate('/super-admin/incident/ai');
            } else {
              navigate('/admin/incident/ai');
            }
          }}
        >
          <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-purple-400/20 to-blue-400/20 blur-md transform scale-150 opacity-0 group-hover:opacity-100 transition-all duration-500"></span>
          <Sparkles className="h-4 w-4 relative z-10 animate-pulse" />
          <span className="relative z-10">Assistant IA</span>
        </Button>
        {selectedIncidents.length > 0 && (
          <Button
          variant="outline"
          className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
            canDelete && !loading
              ? 'text-red-500 hover:bg-red-50 hover:shadow-md hover:border-red-600'
              : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
          } flex items-center gap-2 px-4 py-2 font-semibold`}
          onClick={handleDeleteSelected}
          disabled={!canDelete || loading || selectedIncidents.length === 0}
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Suppression...
            </>
          ) : (
            <>
              <Trash2 className="h-4 w-4 mr-2" />
              Supprimer ({selectedIncidents.length})
            </>
          )}
        </Button>
        )}
        <Button
          className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2"
          onClick={() => {
            const path = window.location.pathname;
            if (path.includes('/super-admin')) {
              navigate('/super-admin/incident/add');
            } else {
              navigate('/admin/incident/add');
            }
          }}
        >
          <Plus className="h-4 w-4" />
          Ajouter un incident
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-4">
        <div className="overflow-x-auto" style={{ maxWidth: '100%', overflowX: 'auto' }}>
          <table className="w-full" style={{ minWidth: '2500px' }}>
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                  <Checkbox
                    checked={selectedIncidents.length === currentItems.length && currentItems.length > 0}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all"
                  />
                </th>
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                    onClick={() => column.sortable && handleSort(column.key)}
                    style={{
                      minWidth: column.key === 'name' ? '200px' :
                               column.key === 'description' || column.key === 'comment' ? '250px' :
                               column.key === 'impact' || column.key === 'priority' ? '120px' :
                               column.key === 'nearMiss' ? '100px' :
                               column.key === 'declarationDate' || column.key === 'detectionDate' || column.key === 'occurrenceDate' ? '150px' :
                               '130px'
                    }}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      {sortConfig.key === column.key && (
                        <ArrowUpDown className="w-4 h-4" />
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {currentItems.map((incident, index) => (
                <tr
                  key={incident.incidentID}
                  className={`hover:bg-gray-50 cursor-pointer ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                >
                  <td
                    className="px-6 py-4"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectIncident(incident.incidentID);
                    }}
                  >
                    <Checkbox
                      checked={selectedIncidents.includes(incident.incidentID)}
                      aria-label={`Select incident ${incident.incidentID}`}
                    />
                  </td>
                  {columns.map((column) => (
                    column.key === 'workflow' ? (
                      <td key={`workflow-${incident.incidentID}`} className="px-6 py-4 text-center">
                        <Button variant="ghost" size="icon" onClick={(e) => handleWorkflowClick(incident.incidentID, e)}>
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 text-blue-600">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 12a.75.75 0 01.75-.75h9a.75.75 0 010 1.5h-9a.75.75 0 01-.75-.75z" />
                          </svg>
                        </Button>
                      </td>
                    ) : (
                      <td
                        key={`${incident.incidentID}-${column.key}`}
                        className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap"
                        onClick={() => handleRowClick(incident.incidentID)}
                        style={{
                          maxWidth: column.key === 'description' || column.key === 'comment' ? '250px' : 'auto',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis'
                        }}
                      >
                        {column.key === 'name' ? (
                          <span className="text-[#242A33] font-bold flex items-center gap-2">
                            <img
                              src={incidentIcon}
                              alt="incident"
                              className="w-5 h-5 object-contain"
                            />
                            {incident[column.key]}
                          </span>
                        ) : column.key === 'impact' ? (
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            incident.impact === 'Very High' ? 'bg-red-100 text-red-800' :
                            incident.impact === 'High' ? 'bg-orange-100 text-orange-800' :
                            incident.impact === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                            incident.impact === 'Low' ? 'bg-green-100 text-green-800' :
                            incident.impact === 'Very Low' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {incident[column.key]}
                          </span>
                        ) : column.key === 'priority' ? (
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            incident.priority === 'High' ? 'bg-red-100 text-red-800' :
                            incident.priority === 'Medium' ? 'bg-orange-100 text-orange-800' :
                            incident.priority === 'Low' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {incident[column.key]}
                          </span>
                        ) : ['declarationDate', 'detectionDate', 'occurrenceDate'].includes(column.key) ? (
                          formatDate(incident[column.key])
                        ) : column.key === 'description' || column.key === 'comment' ? (
                          <div className="max-w-[200px] truncate" title={incident[column.key] || '-'}>
                            {incident[column.key] || '-'}
                          </div>
                        ) : (
                          incident[column.key]
                        )}
                      </td>
                    )
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">
            {filteredIncidents.length} incident{filteredIncidents.length !== 1 ? 's' : ''} trouvé{filteredIncidents.length !== 1 ? 's' : ''}
          </span>
          {(filters.impact !== 'all' ||
            filters.priority !== 'all' ||
            filters.entityID !== 'all' ||
            filters.businessLineID !== 'all' ||
            filters.incidentTypeID !== 'all' ||
            filters.nearMiss !== 'all' ||
            filters.nature !== 'all' ||
            filters.declarationDateRange.start !== null ||
            filters.declarationDateRange.end !== null ||
            filters.detectionDateRange.start !== null ||
            filters.detectionDateRange.end !== null ||
            filters.occurrenceDateRange.start !== null ||
            filters.occurrenceDateRange.end !== null) && (
            <Badge
              variant="outline"
              className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 cursor-pointer"
              onClick={() => {
                const filterPanel = document.querySelector('.filter-panel-container');
                if (filterPanel) {
                  filterPanel.scrollIntoView({ behavior: 'smooth' });
                }
              }}
            >
              Filtré
              <ChevronUp className="ml-1 h-3 w-3" />
            </Badge>
          )}
        </div>
      </div>

      <TablePagination
        currentPage={currentPage}
        totalPages={totalPages}
        itemsPerPage={itemsPerPage}
        totalItems={filteredIncidents.length}
        onPageChange={handlePageChange}
        onItemsPerPageChange={(value) => {
          setItemsPerPage(value);
          setCurrentPage(1);
        }}
        startIndex={indexOfFirstItem}
        endIndex={indexOfLastItem}
      />
    </div>
  );
}

export default Incident;