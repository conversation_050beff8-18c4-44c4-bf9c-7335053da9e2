const db = require('../models');

// Helper function to get random item from array
const getRandomItem = (array) => {
  if (!array || array.length === 0) return null;
  return array[Math.floor(Math.random() * array.length)];
};

// Helper function to get random integer between min and max (inclusive)
const getRandomInt = (min, max) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// Helper function to get random float between min and max with 2 decimal places
const getRandomFloat = (min, max) => {
  return parseFloat((Math.random() * (max - min) + min).toFixed(2));
};

// Helper function to get random boolean (true/false) - useful for future extensions
// const getRandomBoolean = () => {
//   return Math.random() > 0.5;
// };

// Sample data for text fields
const controlExecutionMethods = [
  'Manual Review', 'Automated Check', 'Observation', 'Inspection',
  'Sampling', 'Continuous Monitoring', 'Periodic Assessment',
  'Dual Control', 'Reconciliation', 'Physical Count'
];

const organizationalLevels = [
  'Global', 'Regional', 'Country', 'Business Unit', 'Department',
  'Team', 'Individual', 'Process', 'System'
];

const sampleTypes = [
  'Random', 'Stratified', 'Systematic', 'Judgmental', 'Statistical',
  'Full Population', 'Risk-Based', 'Exception-Based'
];

const testingFrequencies = [
  'Daily', 'Weekly', 'Bi-weekly', 'Monthly', 'Quarterly',
  'Semi-annually', 'Annually', 'Ad-hoc', 'Continuous'
];

const testingMethods = [
  'Inquiry', 'Observation', 'Inspection', 'Re-performance',
  'Analytical Procedures', 'External Confirmation', 'Recalculation'
];

const objectives = [
  'Ensure compliance with regulatory requirements',
  'Prevent unauthorized access to sensitive data',
  'Verify accuracy of financial reporting',
  'Maintain data integrity across systems',
  'Ensure proper segregation of duties',
  'Validate completeness of transaction processing',
  'Monitor system performance and availability',
  'Detect and prevent fraudulent activities',
  'Ensure proper approval of high-risk transactions',
  'Verify adherence to internal policies'
];

const executionProcedures = [
  'Review documentation for completeness and accuracy',
  'Compare system outputs against source documents',
  'Verify proper authorization signatures on documents',
  'Reconcile account balances between systems',
  'Test calculation accuracy using sample transactions',
  'Observe process execution by responsible personnel',
  'Interview process owners about control execution',
  'Examine system logs for unusual activities',
  'Verify proper segregation of duties in the system',
  'Test automated controls through system testing'
];

const testingProcedures = [
  'Select a sample of transactions and verify proper approval',
  'Review system access logs for unauthorized attempts',
  'Recalculate key figures to verify mathematical accuracy',
  'Compare master data across systems to ensure consistency',
  'Observe the execution of the control by the control owner',
  'Examine supporting documentation for completeness',
  'Verify system configurations against approved settings',
  'Test exception handling procedures with sample cases',
  'Review audit trails for completeness of processing',
  'Validate data integrity through cross-system reconciliation'
];

const implementingActionPlans = [
  'Enhance system controls to prevent unauthorized access',
  'Implement additional validation checks in the process',
  'Provide training to control owners on proper execution',
  'Update documentation to clarify control procedures',
  'Increase frequency of control execution for high-risk areas',
  'Automate manual control activities where possible',
  'Implement monitoring dashboard for control performance',
  'Enhance exception reporting and escalation procedures',
  'Strengthen review and approval workflows',
  'Implement system-based segregation of duties'
];

// Function to fetch data from database directly
async function fetchReferenceData() {
  try {
    // Fetch all reference data in parallel using the models directly
    const [
      businessProcesses,
      organizationalProcesses,
      operations,
      applications,
      entities,
      controlTypes,
      risks
    ] = await Promise.all([
      db.BusinessProcess.findAll(),
      db.OrganizationalProcess.findAll(),
      db.Operation.findAll(),
      db.Application.findAll(),
      db.Entity.findAll(),
      db.ControlType.findAll(),
      db.Risk.findAll()
    ]);

    return {
      businessProcesses: businessProcesses || [],
      organizationalProcesses: organizationalProcesses || [],
      operations: operations || [],
      applications: applications || [],
      entities: entities || [],
      controlTypes: controlTypes || [],
      risks: risks || []
    };
  } catch (error) {
    console.error('Error fetching reference data:', error);
    throw error;
  }
}

// Function to generate a random control
function generateRandomControl(referenceData, index) {
  // Randomly decide whether to include each optional field (70% chance)
  const includeOptionalField = () => Math.random() < 0.7;

  // Generate a unique control ID
  const controlID = `CTL_SEED_${String(index + 1).padStart(3, '0')}`;

  // Create the base control object with required fields
  const control = {
    controlID,
    name: `Control ${index + 1}: ${getRandomItem(objectives).substring(0, 30)}...`,
    code: `C${String(index + 1).padStart(3, '0')}`,
  };

  // Add optional fields with a 70% chance
  if (includeOptionalField()) control.controlKey = getRandomInt(1, 100);
  if (includeOptionalField()) control.controlExecutionMethod = getRandomItem(controlExecutionMethods);
  if (includeOptionalField()) control.objective = getRandomItem(objectives);
  if (includeOptionalField()) control.executionProcedure = getRandomItem(executionProcedures);
  if (includeOptionalField()) control.operationalCost = getRandomFloat(1000, 50000);
  if (includeOptionalField()) control.organizationalLevel = getRandomItem(organizationalLevels);
  if (includeOptionalField()) control.sampleType = getRandomItem(sampleTypes);
  if (includeOptionalField()) control.testingFrequency = getRandomItem(testingFrequencies);
  if (includeOptionalField()) control.testingMethod = getRandomItem(testingMethods);
  if (includeOptionalField()) control.testingPopulationSize = getRandomInt(10, 1000);
  if (includeOptionalField()) control.testingProcedure = getRandomItem(testingProcedures);
  if (includeOptionalField()) control.implementingActionPlan = getRandomItem(implementingActionPlans);

  // Add reference fields with a 70% chance
  if (includeOptionalField() && referenceData.businessProcesses.length > 0) {
    control.businessProcess = getRandomItem(referenceData.businessProcesses).businessProcessID;
  }

  if (includeOptionalField() && referenceData.organizationalProcesses.length > 0) {
    control.organizationalProcess = getRandomItem(referenceData.organizationalProcesses).organizationalProcessID;
  }

  if (includeOptionalField() && referenceData.operations.length > 0) {
    control.operation = getRandomItem(referenceData.operations).operationID;
  }

  if (includeOptionalField() && referenceData.applications.length > 0) {
    control.application = getRandomItem(referenceData.applications).applicationID;
  }

  if (includeOptionalField() && referenceData.entities.length > 0) {
    control.entity = getRandomItem(referenceData.entities).entityID;
  }

  if (includeOptionalField() && referenceData.controlTypes.length > 0) {
    control.controlType = getRandomItem(referenceData.controlTypes).controlTypeID;
  }

  // Only assign risk if there are valid risks in the database
  // This prevents foreign key constraint violations
  if (includeOptionalField() && referenceData.risks && referenceData.risks.length > 0) {
    // Log available risks for debugging
    console.log(`Available risks: ${referenceData.risks.length}`);
    console.log(`First few risk IDs: ${referenceData.risks.slice(0, 3).map(r => r.riskID).join(', ')}`);

    // Assign a valid risk ID from the database
    control.risk = getRandomItem(referenceData.risks).riskID;
  } else {
    // Ensure risk is null if no valid risks are available
    control.risk = null;
  }

  return control;
}

// Main seeding function
async function seedControls(numControls = 50) {
  try {
    console.log(`Starting to seed ${numControls} controls...`);

    // Clear existing controls
    await db.Control.destroy({ where: {} });
    console.log('Cleared existing controls');

    // Check if risks exist in the database
    const riskCount = await db.Risk.count();
    if (riskCount === 0) {
      console.log('No risks found in the database. Seeding risks first...');
      // Import and run the risk seeding function
      const seedRisks = require('./seedRisks');
      await seedRisks();
      console.log('Risks seeded successfully.');
    } else {
      console.log(`Found ${riskCount} existing risks in the database.`);
    }

    // Fetch reference data
    console.log('Fetching reference data...');
    const referenceData = await fetchReferenceData();
    console.log('Reference data fetched successfully');

    // Generate random controls
    const controls = [];
    for (let i = 0; i < numControls; i++) {
      controls.push(generateRandomControl(referenceData, i));
    }

    // Insert controls into database
    console.log(`Inserting ${controls.length} controls into database...`);
    await db.Control.bulkCreate(controls);

    console.log('Controls seeded successfully!');
  } catch (error) {
    console.error('Error seeding controls:', error);
  }
}

// Execute the seeding function with 50 controls
console.log('Starting the control seeding process...');
seedControls(50).then(() => {
  console.log('Seeding process completed.');
}).catch(err => {
  console.error('Seeding process failed:', err);
});

module.exports = seedControls;
