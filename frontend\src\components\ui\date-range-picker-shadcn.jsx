import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export function DateRangePickerShadcn({
  className,
  value = { start: null, end: null },
  onChange,
  placeholder = "Select date range",
}) {
  const [date, setDate] = React.useState({
    from: value.start ? new Date(value.start) : undefined,
    to: value.end ? new Date(value.end) : undefined,
  });

  // Update internal state when external value changes
  React.useEffect(() => {
    if (value.start || value.end) {
      setDate({
        from: value.start ? new Date(value.start) : undefined,
        to: value.end ? new Date(value.end) : undefined,
      });
    }
  }, [value.start, value.end]);

  // Handle date selection
  const handleSelect = (range) => {
    setDate(range);
    if (range?.from) {
      onChange({
        start: range.from,
        end: range.to,
      });
    } else {
      onChange({ start: null, end: null });
    }
  };
  
  // Format date for display
  const formatDateDisplay = () => {
    if (date?.from) {
      if (date.to) {
        return (
          <>
            {format(date.from, "LLL dd, y")} - {format(date.to, "LLL dd, y")}
          </>
        );
      }
      return format(date.from, "LLL dd, y");
    }
    return <span>{placeholder}</span>;
  };

  // Predefined date ranges
  const predefinedRanges = [
    {
      label: "Today",
      getValue: () => {
        const today = new Date();
        return { from: today, to: today };
      },
    },
    {
      label: "Yesterday",
      getValue: () => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return { from: yesterday, to: yesterday };
      },
    },
    {
      label: "Last 7 days",
      getValue: () => {
        const today = new Date();
        const lastWeek = new Date();
        lastWeek.setDate(lastWeek.getDate() - 6);
        return { from: lastWeek, to: today };
      },
    },
    {
      label: "Last 30 days",
      getValue: () => {
        const today = new Date();
        const lastMonth = new Date();
        lastMonth.setDate(lastMonth.getDate() - 29);
        return { from: lastMonth, to: today };
      },
    },
    {
      label: "This month",
      getValue: () => {
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        return { from: firstDayOfMonth, to: today };
      },
    },
    {
      label: "Last month",
      getValue: () => {
        const today = new Date();
        const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
        return { from: firstDayOfLastMonth, to: lastDayOfLastMonth };
      },
    },
    {
      label: "This year",
      getValue: () => {
        const today = new Date();
        const firstDayOfYear = new Date(today.getFullYear(), 0, 1);
        return { from: firstDayOfYear, to: today };
      },
    },
    {
      label: "Last year",
      getValue: () => {
        const lastYear = new Date().getFullYear() - 1;
        const firstDayOfLastYear = new Date(lastYear, 0, 1);
        const lastDayOfLastYear = new Date(lastYear, 11, 31);
        return { from: firstDayOfLastYear, to: lastDayOfLastYear };
      },
    },
  ];

  // Apply a predefined range
  const applyPredefinedRange = (getValue) => {
    const range = getValue();
    setDate(range);
    onChange({
      start: range.from,
      end: range.to,
    });
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-full justify-start text-left font-normal",
              !date.from && !date.to && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formatDateDisplay()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex flex-col sm:flex-row p-3 space-y-4 sm:space-y-0 sm:space-x-4">
            <div className="space-y-4">
              <div className="grid gap-2">
                <div className="font-medium text-sm">Predefined Ranges</div>
                <div className="grid gap-1">
                  {predefinedRanges.map((range) => (
                    <Button
                      key={range.label}
                      variant="outline"
                      size="sm"
                      className="justify-start font-normal"
                      onClick={() => applyPredefinedRange(range.getValue)}
                    >
                      {range.label}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
            <div className="border-t sm:border-t-0 sm:border-l border-border pt-4 sm:pt-0 sm:pl-4">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={date?.from}
                selected={date}
                onSelect={handleSelect}
                numberOfMonths={2}
                showOutsideDays={false}
              />
            </div>
          </div>
          <div className="flex items-center justify-between p-3 border-t border-border">
            <div className="text-sm text-muted-foreground">
              {date.from && date.to && (
                <>
                  <span className="font-medium">
                    {format(date.from, "PPP")}
                  </span>
                  {" to "}
                  <span className="font-medium">
                    {format(date.to, "PPP")}
                  </span>
                </>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setDate({ from: undefined, to: undefined });
                  onChange({ start: null, end: null });
                }}
              >
                Clear
              </Button>
              <Button
                size="sm"
                onClick={() => {}}
              >
                Apply
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
