const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../middleware/auth');
const {
  getTablesList,
  getTableStructure,
  importTableFromExcel,
  importMultipleTables,
  generateTableTemplate
} = require('../controllers/imports/database-import-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Only admin users can import database tables


// Get list of all tables
router.get('/tables',  getTablesList);

// Get table structure
router.get('/table-structure/:tableName',  getTableStructure);

// Import a single table
router.post('/table/:tableName',  importTableFromExcel);

// Import multiple tables
router.post('/tables',  importMultipleTables);

// Generate table template
router.get('/template/:tableName',  generateTableTemplate);

module.exports = router;