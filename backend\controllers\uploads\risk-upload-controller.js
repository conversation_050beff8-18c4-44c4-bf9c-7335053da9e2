const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { RiskAttachment } = require('../../models');

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../../uploads');
const riskDocsDir = path.join(uploadsDir, 'risk-documents');
const riskRefsDir = path.join(uploadsDir, 'risk-references');

// Upload a file
const uploadRiskFile = async (req, res) => {
  try {
    // Only log this in development mode
    if (process.env.NODE_ENV === 'development' && process.env.DEBUG_LOGS === 'true') {
      console.log('Risk upload request received:', {
        files: req.files ? Object.keys(req.files).length : 0,
        body: req.body
      });
    }

    // Ensure upload directories exist
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
      if (process.env.NODE_ENV === 'development' && process.env.DEBUG_LOGS === 'true') {
        console.log('Created uploads directory');
      }
    }

    if (!fs.existsSync(riskDocsDir)) {
      fs.mkdirSync(riskDocsDir, { recursive: true });
      if (process.env.NODE_ENV === 'development' && process.env.DEBUG_LOGS === 'true') {
        console.log('Created risk-documents directory');
      }
    }

    if (!fs.existsSync(riskRefsDir)) {
      fs.mkdirSync(riskRefsDir, { recursive: true });
      if (process.env.NODE_ENV === 'development' && process.env.DEBUG_LOGS === 'true') {
        console.log('Created risk-references directory');
      }
    }

    if (!req.files || Object.keys(req.files).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files were uploaded'
      });
    }

    const { type, riskID } = req.body;

    if (!type || !['business-document', 'external-reference'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid file type. Must be "business-document" or "external-reference"'
      });
    }

    if (!riskID) {
      return res.status(400).json({
        success: false,
        message: 'Risk ID is required'
      });
    }

    console.log(`Processing upload for risk ${riskID}, type: ${type}`);

    const uploadedFiles = Array.isArray(req.files.files) ? req.files.files : [req.files.files];
    const savedFiles = [];

    // Define allowed file extensions and MIME types
    const allowedExtensions = [
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.txt', '.csv', '.rtf', '.odt', '.ods', '.odp',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg',
      '.zip', '.rar', '.7z', '.tar', '.gz'
    ];

    const blockedExtensions = [
      '.exe', '.bat', '.cmd', '.msi', '.dll', '.bin',
      '.sh', '.com', '.scr', '.vbs', '.js', '.jar', '.py'
    ];

    for (const file of uploadedFiles) {
      const fileExtension = path.extname(file.name).toLowerCase();

      // Check if file extension is explicitly blocked
      if (blockedExtensions.includes(fileExtension)) {
        return res.status(400).json({
          success: false,
          message: `File type ${fileExtension} is not allowed for security reasons`
        });
      }

      // Check if file extension is allowed
      if (!allowedExtensions.includes(fileExtension)) {
        return res.status(400).json({
          success: false,
          message: `File type ${fileExtension} is not supported. Allowed types: ${allowedExtensions.join(', ')}`
        });
      }

      const fileName = `${uuidv4()}${fileExtension}`;
      const uploadDir = type === 'business-document' ? riskDocsDir : riskRefsDir;
      const filePath = path.join(uploadDir, fileName);

      // Move the file to the uploads directory
      await file.mv(filePath);

      // Generate a unique ID for the attachment
      const attachmentID = `RISK_ATT_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      // Save file metadata to database
      // Only log this in development mode
      if (process.env.NODE_ENV === 'development' && process.env.DEBUG_LOGS === 'true') {
        console.log('Attempting to save attachment to database with data:', {
          attachmentID,
          fileName: file.name,
          fileSize: file.size,
          fileType: file.mimetype,
          filePath: fileName,
          uploadDate: new Date(),
          type,
          riskID
        });
      }

      let attachment;
      try {
        attachment = await RiskAttachment.create({
          attachmentID,
          fileName: file.name,
          fileSize: file.size,
          fileType: file.mimetype,
          filePath: fileName,
          uploadDate: new Date(),
          type,
          riskID
        });

        // Only log this in development mode
        if (process.env.NODE_ENV === 'development' && process.env.DEBUG_LOGS === 'true') {
          console.log(`File metadata saved to database: ${file.name}, ID: ${attachmentID}`);
        }
      } catch (dbError) {
        console.error('Error saving attachment to database:', dbError);
        throw new Error(`Failed to save attachment to database: ${dbError.message}`);
      }

      // Only log this in development mode
      if (process.env.NODE_ENV === 'development' && process.env.DEBUG_LOGS === 'true') {
        console.log(`File saved: ${file.name}, ID: ${attachmentID}, Path: ${fileName}`);
      }

      savedFiles.push({
        id: attachmentID,
        name: file.name,
        size: file.size,
        type: file.mimetype,
        uploadDate: new Date()
      });
    }

    return res.status(201).json({
      success: true,
      message: `${savedFiles.length} file(s) uploaded successfully`,
      data: savedFiles
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to upload file'
    });
  }
};

// Get all attachments for a risk
const getRiskAttachments = async (req, res) => {
  try {
    const { riskID, type } = req.query;
    // Only log this in development mode
    if (process.env.NODE_ENV === 'development' && process.env.DEBUG_LOGS === 'true') {
      console.log('Fetching risk attachments with params:', { riskID, type });
    }

    if (!riskID) {
      return res.status(400).json({
        success: false,
        message: 'Risk ID is required'
      });
    }

    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    const query = { riskID };

    if (type && ['business-document', 'external-reference'].includes(type)) {
      query.type = type;
    }

    // Only log this in development mode
    if (process.env.NODE_ENV === 'development' && process.env.DEBUG_LOGS === 'true') {
      console.log('Querying RiskAttachment with:', query);
    }

    // Force refresh from database
    try {
      const attachments = await RiskAttachment.findAll({
        where: query,
        order: [['uploadDate', 'DESC']],
        raw: true // Get plain objects instead of Sequelize instances for better performance
      });

      // Only log this in development mode
      if (process.env.NODE_ENV === 'development' && process.env.DEBUG_LOGS === 'true') {
        console.log(`Found ${attachments.length} attachments for risk ${riskID}`);
      }
      return res.json({
        success: true,
        data: attachments.map(attachment => ({
          id: attachment.attachmentID,
          name: attachment.fileName,
          size: attachment.fileSize,
          type: attachment.fileType,
          uploadDate: attachment.uploadDate
        }))
      });
    } catch (queryError) {
      console.error('Error querying RiskAttachment:', queryError);
      return res.status(500).json({
        success: false,
        message: `Error querying attachments: ${queryError.message}`
      });
    }
  } catch (error) {
    console.error('Error fetching risk attachments:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch attachments'
    });
  }
};

// Delete an attachment
const deleteRiskAttachment = async (req, res) => {
  try {
    const { id } = req.params;

    const attachment = await RiskAttachment.findByPk(id);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // Delete the file from the filesystem
    const uploadDir = attachment.type === 'business-document' ? riskDocsDir : riskRefsDir;
    const filePath = path.join(uploadDir, attachment.filePath);

    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // Delete the record from the database
    await attachment.destroy();

    return res.json({
      success: true,
      message: 'Attachment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting risk attachment:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete attachment'
    });
  }
};

// Download an attachment
const downloadRiskAttachment = async (req, res) => {
  try {
    const { id } = req.params;

    const attachment = await RiskAttachment.findByPk(id);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    const uploadDir = attachment.type === 'business-document' ? riskDocsDir : riskRefsDir;
    const filePath = path.join(uploadDir, attachment.filePath);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'File not found on server'
      });
    }

    res.download(filePath, attachment.fileName);
  } catch (error) {
    console.error('Error downloading risk attachment:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to download attachment'
    });
  }
};

module.exports = {
  uploadRiskFile,
  getRiskAttachments,
  deleteRiskAttachment,
  downloadRiskAttachment
};
