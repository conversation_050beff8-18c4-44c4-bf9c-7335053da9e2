const express = require('express');
const router = express.Router();
const { verifyToken } = require('../../middleware/auth');
const { 
  sendReportEmail,
  getEmailRecipients
} = require('../../controllers/reports/report-email-controller');

// Handle preflight OPTIONS requests explicitly
router.options('*', (req, res) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:5173');
  res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, x-user-id');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.status(200).end();
});

// Get all users for recipient selection
router.get('/recipients', (req, res, next) => {
  // Add CORS headers specifically for this route
  res.header('Access-Control-Allow-Origin', 'http://localhost:5173');
  res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, x-user-id');
  res.header('Access-Control-Allow-Credentials', 'true');
  getEmailRecipients(req, res, next);
});

// Send report via email
router.post('/send', (req, res, next) => {
  // Add CORS headers specifically for this route
  res.header('Access-Control-Allow-Origin', 'http://localhost:5173');
  res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, x-user-id');
  res.header('Access-Control-Allow-Credentials', 'true');
  sendReportEmail(req, res, next);
});

// Add a test route
router.get('/test', (req, res) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:5173');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.status(200).json({
    success: true,
    message: 'CORS test successful',
    timestamp: new Date().toISOString()
  });
});

// Apply authentication middleware to all routes except OPTIONS
router.use((req, res, next) => {
  if (req.method === 'OPTIONS') {
    return next();
  }
  verifyToken(req, res, next);
});

module.exports = router;


