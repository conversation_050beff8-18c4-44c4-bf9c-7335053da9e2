module.exports = (sequelize, DataTypes) => {
  const ActivityLog = sequelize.define('ActivityLog', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    risk_id: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Risk',
        key: 'riskID'
      }
    },
    timestamp: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    user: {
      type: DataTypes.STRING,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('creation', 'transition', 'update'),
      allowNull: false
    },
    field: {
      type: DataTypes.STRING,
      allowNull: true
    },
    old_value: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    new_value: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    details: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'ActivityLogs',
    timestamps: false
  });

  ActivityLog.associate = (models) => {
    ActivityLog.belongsTo(models.Risk, {
      foreignKey: 'risk_id',
      targetKey: 'riskID',
      as: 'risk'
    });
  };

  return ActivityLog;
}; 