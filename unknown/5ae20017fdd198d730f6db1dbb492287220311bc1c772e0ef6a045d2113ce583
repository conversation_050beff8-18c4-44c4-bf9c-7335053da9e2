import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { toast } from 'sonner';
import { getApiBaseUrl } from '../../utils/api-config';

const API_URL = `${getApiBaseUrl()}/applications`;

// Initial state
const initialState = {
  applications: [],
  currentApplication: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: ''
};

// Get all applications
export const getAllApplications = createAsyncThunk(
  'applications/getAll',
  async (_, thunkAPI) => {
    try {
      const response = await axios.get(API_URL, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch applications';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get application by ID
export const getApplicationById = createAsyncThunk(
  'applications/getById',
  async (id, thunkAPI) => {
    try {
      const response = await axios.get(`${API_URL}/${id}`, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch application';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create new application
export const createApplication = createAsyncThunk(
  'applications/create',
  async (applicationData, thunkAPI) => {
    try {
      const response = await axios.post(API_URL, applicationData, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to create application';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update application
export const updateApplication = createAsyncThunk(
  'applications/update',
  async ({ id, applicationData }, thunkAPI) => {
    try {
      const response = await axios.put(`${API_URL}/${id}`, applicationData, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to update application';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete application
export const deleteApplication = createAsyncThunk(
  'applications/delete',
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${API_URL}/${id}`, {
        withCredentials: true
      });
      return id;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete application';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Application slice
const applicationSlice = createSlice({
  name: 'application',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all applications
      .addCase(getAllApplications.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllApplications.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.applications = action.payload.data || [];
      })
      .addCase(getAllApplications.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Get application by ID
      .addCase(getApplicationById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getApplicationById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentApplication = action.payload.data;
      })
      .addCase(getApplicationById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Create application
      .addCase(createApplication.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createApplication.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.applications.push(action.payload.data);
        toast.success('Application created successfully');
      })
      .addCase(createApplication.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Update application
      .addCase(updateApplication.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateApplication.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.applications = state.applications.map(application => 
          application.applicationID === action.payload.data.applicationID ? action.payload.data : application
        );
        state.currentApplication = action.payload.data;
        toast.success('Application updated successfully');
      })
      .addCase(updateApplication.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Delete application
      .addCase(deleteApplication.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteApplication.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.applications = state.applications.filter(application => application.applicationID !== action.meta.arg);
        toast.success('Application deleted successfully');
      })
      .addCase(deleteApplication.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
  }
});

export const { reset } = applicationSlice.actions;
export default applicationSlice.reducer;
