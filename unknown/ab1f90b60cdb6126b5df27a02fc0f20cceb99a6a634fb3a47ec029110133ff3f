import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from '../../utils/axios-config'; // Fixed path to axios-config
import { toast } from 'sonner';
import { getApiBaseUrl } from '../../utils/api-config';

const API_URL = `${getApiBaseUrl()}/risk`;

// Initial state
const initialState = {
  risks: [],
  risk: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    pageSize: 10
  },
  filters: {},
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: '',
  loadingProgress: 0 // Track loading progress
};

// Get all risks
export const getAllRisks = createAsyncThunk(
  'risks/getAll',
  async (_, thunkAPI) => {
    try {
      const response = await axios.get(`${API_URL}`, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch risks';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Fetch paginated risks
export const fetchPaginatedRisks = createAsyncThunk(
  'risk/fetchPaginatedRisks',
  async ({ page, pageSize, filters }, { rejectWithValue }) => {
    try {
      // Add token from localStorage as a fallback
      const token = localStorage.getItem('authToken');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      
      const response = await axios.get(`${API_URL}`, {
        params: {
          page,
          pageSize,
          ...filters
        },
        withCredentials: true,
        headers
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Get risk by ID
export const getRiskById = createAsyncThunk(
  'risks/getById',
  async (id, thunkAPI) => {
    try {
      const response = await axios.get(`${API_URL}/${id}`, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch risk';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create new risk
export const createRisk = createAsyncThunk(
  'risks/create',
  async (riskData, thunkAPI) => {
    try {
      const response = await axios.post(API_URL, riskData, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to create risk';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update risk
export const updateRisk = createAsyncThunk(
  'risks/update',
  async ({ id, riskData }, thunkAPI) => {
    try {
      const response = await axios.put(`${API_URL}/${id}`, riskData, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to update risk';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete risk
export const deleteRisk = createAsyncThunk(
  'risks/delete',
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${API_URL}/${id}`, {
        withCredentials: true
      });
      return id;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete risk';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete multiple risks
export const deleteMultipleRisks = createAsyncThunk(
  'risks/deleteMultiple',
  async (ids, thunkAPI) => {
    try {
      await axios.post(`${API_URL}/delete-multiple`, { ids }, {
        withCredentials: true
      });
      return ids;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete risks';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Risk slice
const riskSlice = createSlice({
  name: 'risk',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
      state.loadingProgress = 0;
    },
    setFilters: (state, action) => {
      state.filters = action.payload;
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    setLoadingProgress: (state, action) => {
      state.loadingProgress = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all risks
      .addCase(getAllRisks.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllRisks.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.risks = action.payload.data || [];
      })
      .addCase(getAllRisks.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Fetch paginated risks
      .addCase(fetchPaginatedRisks.pending, (state) => {
        if (!Object.keys(state.filters).length) {
          state.isLoading = true;
        }
      })
      .addCase(fetchPaginatedRisks.fulfilled, (state, action) => {
        state.risks = action.payload.data;
        state.pagination = action.payload.pagination;
        state.isLoading = false;
        state.isError = false;
        state.message = '';
      })
      .addCase(fetchPaginatedRisks.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload || 'Failed to fetch risks';
      })

      // Get risk by ID
      .addCase(getRiskById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getRiskById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.risk = action.payload.data;
      })
      .addCase(getRiskById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Create risk
      .addCase(createRisk.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createRisk.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Don't update the risks array here as it would break pagination
        toast.success('Risk created successfully');
      })
      .addCase(createRisk.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Update risk
      .addCase(updateRisk.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateRisk.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update the risk in the array if it exists
        const index = state.risks.findIndex(risk => risk.riskID === action.payload.data.riskID);
        if (index !== -1) {
          state.risks[index] = action.payload.data;
        }
        state.risk = action.payload.data;
        toast.success('Risk updated successfully');
      })
      .addCase(updateRisk.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Delete risk
      .addCase(deleteRisk.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteRisk.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.risks = state.risks.filter(risk => risk.riskID !== action.payload);
        toast.success('Risk deleted successfully');
      })
      .addCase(deleteRisk.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Delete multiple risks
      .addCase(deleteMultipleRisks.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteMultipleRisks.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.risks = state.risks.filter(risk => !action.payload.includes(risk.riskID));
        toast.success('Risks deleted successfully');
      })
      .addCase(deleteMultipleRisks.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      });
  }
});

export const { reset, setFilters, clearFilters } = riskSlice.actions;
export default riskSlice.reducer;
