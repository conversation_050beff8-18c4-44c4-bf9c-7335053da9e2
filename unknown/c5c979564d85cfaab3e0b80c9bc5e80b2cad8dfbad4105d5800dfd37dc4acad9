module.exports = (sequelize, DataTypes) => {
  const ControlType = sequelize.define('ControlType', {
    controlTypeID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    parentControlTypeID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'ControlType',
        key: 'controlTypeID',
      },
    },
  }, {
    tableName: 'ControlType',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['controlTypeID'],
      },
    ],
  });

  return ControlType;
};