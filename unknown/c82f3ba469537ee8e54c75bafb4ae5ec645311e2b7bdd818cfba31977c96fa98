import { useState, useEffect } from "react";
import { useNavigate, useOutletContext } from "react-router-dom";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";
function BusinessProcessesFeatures() {
  const { process, businessProcesses, refreshProcess } = useOutletContext();
  const navigate = useNavigate();
  const API_BASE_URL = getApiBaseUrl();
  const [isLoading, setIsLoading] = useState(false);

  const [formData, setFormData] = useState({
    name: "",
    code: "",
    comment: "",
    parentBusinessProcessID: "none",
  });

  // Initialize form data when process is loaded
  useEffect(() => {
    if (process) {
      setFormData({
        name: process.name || "",
        code: process.code || "",
        comment: process.comment || "",
        parentBusinessProcessID: process.parentBusinessProcessID || "none",
      });
    }
  }, [process]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle select changes
  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setIsLoading(true);
      
      // Prepare data for submission
      const updateData = {
        name: formData.name,
        code: formData.code,
        comment: formData.comment,
        parentBusinessProcessID: formData.parentBusinessProcessID === "none" ? null : formData.parentBusinessProcessID,
      };

      // Check if trying to set parent to self
      if (updateData.parentBusinessProcessID === process.businessProcessID) {
        toast.error("A business process cannot be its own parent");
        setIsLoading(false);
        return;
      }

      const response = await axios.put(`${API_BASE_URL}/businessProcesses/${process.businessProcessID}`, updateData, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
      });
      
      if (response.data.success) {
        toast.success("Business Process updated successfully");
        refreshProcess();
        navigate(`/admin/processes/business-processes/${process.businessProcessID}`);
      } else {
        toast.error(response.data.message || "Failed to update business process");
      }
    } catch (error) {
      console.error("Update error:", error);
      
      if (error.response?.status === 500) {
        toast.error("Server error. Please check the console for details.");
      } else {
        toast.error(error?.message || "Failed to update business process");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h2 className="text-xl font-semibold mb-6">Edit Business Process</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Name Field */}
        <div className="space-y-2">
          <Label htmlFor="name">Name</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
          />
        </div>

        {/* Code Field */}
        <div className="space-y-2">
          <Label htmlFor="code">Code</Label>
          <Input
            id="code"
            name="code"
            value={formData.code}
            onChange={handleChange}
          />
        </div>
      </div>

      {/* Parent Business Process Field */}
      <div className="space-y-2">
        <Label htmlFor="parentBusinessProcessID">Parent Business Process</Label>
        <div className="relative">
          <select
            id="parentBusinessProcessID"
            className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            value={formData.parentBusinessProcessID || "none"}
            onChange={(e) => handleSelectChange("parentBusinessProcessID", e.target.value)}
          >
            <option value="none">None</option>
            {businessProcesses && businessProcesses
              .filter(p => p.businessProcessID !== process.businessProcessID)
              .map(p => (
                <option key={p.businessProcessID} value={p.businessProcessID}>
                  {p.name}
                </option>
              ))}
          </select>
        </div>
      </div>

      {/* Comment Field */}
      <div className="space-y-2">
        <Label htmlFor="comment">Comment</Label>
        <Textarea
          id="comment"
          name="comment"
          value={formData.comment}
          onChange={handleChange}
          rows={4}
        />
      </div>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => navigate(`/admin/processes/business-processes/${process.businessProcessID}`)}
        >
          Cancel
        </Button>
        <Button 
          type="submit" 
          disabled={isLoading}
          className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Changes"
          )}
        </Button>
      </div>
    </form>
  );
}

export default BusinessProcessesFeatures;
