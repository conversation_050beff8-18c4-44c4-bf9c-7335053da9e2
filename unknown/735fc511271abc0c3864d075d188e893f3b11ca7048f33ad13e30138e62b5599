const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middleware/auth');
const {
  getReferences,
  createReference,
  deleteReference
} = require('../controllers/references/reference-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// GET /api/references?incidentID=INC_123
router.get('/', getReferences);

// POST /api/references
router.post('/', createReference);

// DELETE /api/references/:id
router.delete('/:id', deleteReference);

module.exports = router; 