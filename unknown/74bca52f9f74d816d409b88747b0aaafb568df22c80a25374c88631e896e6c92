const express = require('express');
const router = express.Router();
const contributorController = require('../../controllers/risks/contributor-controller');
const { authenticateToken, authorizeRoles } = require('../../middleware/auth');

/**
 * @route   GET /api/risks/:riskId/contributors
 * @desc    Get all contributors for a risk
 * @access  Private
 */
router.get('/:riskId/contributors', 
  authenticateToken, 
  contributorController.getRiskContributors
);

/**
 * @route   POST /api/risks/:riskId/contributors
 * @desc    Assign a contributor to a risk
 * @access  Private - Higher roles only
 */
router.post('/:riskId/contributors', 
  authenticateToken, 
  authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager']), 
  contributorController.assignContributor
);

/**
 * @route   DELETE /api/risks/:riskId/contributors/:contributorId
 * @desc    Remove a contributor from a risk
 * @access  Private - Higher roles only
 */
router.delete('/:riskId/contributors/:contributorId', 
  authenticateToken, 
  authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager']), 
  contributorController.removeContributor
);

/**
 * @route   GET /api/risks/:riskId/contributors/check
 * @desc    Check if a user is a contributor to a risk
 * @access  Private
 */
router.get('/:riskId/contributors/check', 
  authenticateToken, 
  contributorController.checkContributor
);

module.exports = router; 