import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from "@/components/ui/button";
import { MoveLeft, Home } from 'lucide-react';

export default function NotFound() {
  const navigate = useNavigate();

  return (
    <div className="relative h-screen w-full flex items-center justify-center bg-white overflow-hidden">
      {/* Decorative Elements - Moved to the top level and centered */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="relative w-[600px] h-[600px]">
          <div className="absolute inset-0 rounded-full border-8 border-[#F62D51] opacity-10 animate-ping" />
          <div className="absolute inset-0 rounded-full border-2 border-[#F62D51] opacity-10" />
        </div>
      </div>

      {/* Content */}
      <div className="relative text-center space-y-8 px-4">
        {/* 404 Large Text */}
        <h1 className="text-[150px] font-bold text-[#F62D51] leading-none">
          404
        </h1>

        {/* Error Messages */}
        <div className="space-y-2">
          <h2 className="text-2xl font-semibold text-gray-800">
            Oops! Page Not Found
          </h2>
          <p className="text-gray-500 max-w-md mx-auto">
            The page you are looking for might have been removed, had its name changed, 
            or is temporarily unavailable.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button
            onClick={() => navigate(-1)}
            variant="outline"
            className="min-w-[200px] bg-white hover:bg-gray-50"
          >
            <MoveLeft className="mr-2 h-4 w-4" />
            Go Back
          </Button>

          <Button
            onClick={() => navigate('/auth/login')}
            className="min-w-[200px] bg-[#F62D51] hover:bg-red-700"
          >
            <Home className="mr-2 h-4 w-4" />
            Back to Home
          </Button>
        </div>
      </div>
    </div>
  );
}
