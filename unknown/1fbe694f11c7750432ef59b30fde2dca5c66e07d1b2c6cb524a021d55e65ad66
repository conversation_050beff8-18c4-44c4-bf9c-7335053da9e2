import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { toast } from 'sonner';
import { getApiBaseUrl } from '../../utils/api-config';

const API_URL = `${getApiBaseUrl()}/operations`;

// Initial state
const initialState = {
  operations: [],
  currentOperation: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: ''
};

// Get all operations
export const getAllOperations = createAsyncThunk(
  'operations/getAll',
  async (_, thunkAPI) => {
    try {
      const response = await axios.get(API_URL, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch operations';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get operation by ID
export const getOperationById = createAsyncThunk(
  'operations/getById',
  async (id, thunkAPI) => {
    try {
      const response = await axios.get(`${API_URL}/${id}`, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch operation';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create new operation
export const createOperation = createAsyncThunk(
  'operations/create',
  async (operationData, thunkAPI) => {
    try {
      const response = await axios.post(API_URL, operationData, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to create operation';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update operation
export const updateOperation = createAsyncThunk(
  'operations/update',
  async ({ id, operationData }, thunkAPI) => {
    try {
      const response = await axios.put(`${API_URL}/${id}`, operationData, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to update operation';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete operation
export const deleteOperation = createAsyncThunk(
  'operations/delete',
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${API_URL}/${id}`, {
        withCredentials: true
      });
      return id;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete operation';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Operation slice
const operationSlice = createSlice({
  name: 'operation',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all operations
      .addCase(getAllOperations.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllOperations.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.operations = action.payload.data || [];
      })
      .addCase(getAllOperations.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Get operation by ID
      .addCase(getOperationById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getOperationById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentOperation = action.payload.data;
      })
      .addCase(getOperationById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Create operation
      .addCase(createOperation.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createOperation.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.operations.push(action.payload.data);
        toast.success('Operation created successfully');
      })
      .addCase(createOperation.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Update operation
      .addCase(updateOperation.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateOperation.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.operations = state.operations.map(operation => 
          operation.operationID === action.payload.data.operationID ? action.payload.data : operation
        );
        state.currentOperation = action.payload.data;
        toast.success('Operation updated successfully');
      })
      .addCase(updateOperation.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Delete operation
      .addCase(deleteOperation.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteOperation.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.operations = state.operations.filter(operation => operation.operationID !== action.meta.arg);
        toast.success('Operation deleted successfully');
      })
      .addCase(deleteOperation.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
  }
});

export const { reset } = operationSlice.actions;
export default operationSlice.reducer;
