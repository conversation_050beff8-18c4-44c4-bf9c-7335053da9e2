const express = require('express');
const router = express.Router();
const financialEntryController = require('../../controllers/financial/financial-entry-controller');

// Get financial entries by incident ID
router.get('/entries/incident/:incidentId', financialEntryController.getFinancialEntriesByIncident);

// Batch update financial entries for an incident
router.put('/entries/incident/:incidentId', financialEntryController.updateIncidentFinancialEntries);

// Get all financial entries
router.get('/entries', financialEntryController.getAllFinancialEntries);

// Create new financial entry
router.post('/entries', financialEntryController.createFinancialEntry);

// Update financial entry
router.put('/entries/:id', financialEntryController.updateFinancialEntry);

// Delete financial entry
router.delete('/entries/:id', financialEntryController.deleteFinancialEntry);

module.exports = router; 