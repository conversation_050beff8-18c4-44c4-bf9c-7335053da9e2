import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { hasAllPermissions, hasAnyPermission } from '@/store/auth-slice';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

/**
 * A protected route component that checks if the user is authenticated
 * and has the required permissions before rendering its children.
 * 
 * @param {object} props - Component props
 * @param {React.ReactNode} props.children - The components to render if user is authenticated and has permissions
 * @param {string|string[]} [props.requiredPermissions] - Optional permission(s) required to access this route
 * @param {boolean} [props.requireAll=false] - If true, user must have ALL permissions, otherwise ANY permission suffices
 * @param {string} [props.redirectPath='/auth/login'] - Where to redirect if authentication/authorization fails
 * @returns {React.ReactNode} The protected component or a redirect
 */
const ProtectedRoute = ({ 
  children, 
  requiredPermissions, 
  requireAll = false,
  redirectPath = '/auth/login'
}) => {
  const location = useLocation();
  const { isAuthenticated, isLoading, user } = useSelector(state => state.auth);
  
  // Check permissions using the Redux store
  const hasRequiredPermissions = () => {
    if (!requiredPermissions) return true;
    
    const permissions = Array.isArray(requiredPermissions) 
      ? requiredPermissions 
      : [requiredPermissions];
    
    if (requireAll) {
      return hasAllPermissions({ auth: { isAuthenticated, user } }, permissions);
    } else {
      return hasAnyPermission({ auth: { isAuthenticated, user } }, permissions);
    }
  };
  
  // Show loading indicator while checking authentication
  if (isLoading) {
    return (
      <div className="h-screen w-full flex items-center justify-center bg-white">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }
  
  // If authenticated but missing required permissions
  if (requiredPermissions && !hasRequiredPermissions()) {
    toast.error('You do not have permission to access this page');
    
    // Redirect based on role
    if (user?.roles?.some(role => role.code === 'grc_admin')) {
      return <Navigate to='/super-admin/welcome' replace />;
    } else {
      return <Navigate to='/admin/welcome' replace />;
    }
  }
  
  // User is authenticated and has required permissions
  return children;
};

export default ProtectedRoute; 