const express = require('express');
const router = express.Router();
const workflowController = require('../../controllers/risks/workflow-controller');
const { authenticateToken } = require('../../middleware/auth');
const { authorizeRiskContributor } = require('../../middleware/contributor-auth');

/**
 * @route   GET /api/risks/:riskId/workflow/state
 * @desc    Get the current workflow state and events for a risk
 * @access  Private
 */
router.get('/:riskId/workflow/state', authenticateToken, workflowController.getWorkflowState);

/**
 * @route   GET /api/risks/:riskId/workflow/transitions
 * @desc    Get available transitions for a risk's current state
 * @access  Private
 */
router.get('/:riskId/workflow/transitions', authenticateToken, workflowController.getAvailableTransitions);

/**
 * @route   POST /api/risks/:riskId/workflow/transition
 * @desc    Transition a risk to a new state
 * @access  Private - Contributor
 */
router.post('/:riskId/workflow/transition', 
  authenticateToken,
  authorizeRiskContributor(),
  workflowController.transitionWorkflow
);

module.exports = router; 