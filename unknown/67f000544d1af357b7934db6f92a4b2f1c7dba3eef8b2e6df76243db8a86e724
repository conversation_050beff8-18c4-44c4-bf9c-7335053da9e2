const { Sequelize } = require('sequelize');
require('dotenv').config();

async function migrateActionPlanIncident() {
  const sequelize = new Sequelize(process.env.DB_NAME, process.env.DB_USER, process.env.DB_PASSWORD, {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: false
  });

  try {
    await sequelize.authenticate();
    console.log('Connection has been established successfully.');

    // Add incidentID column to action_plan table
    await sequelize.query(`
      ALTER TABLE "action_plan"
      ADD COLUMN IF NOT EXISTS "incidentID" VARCHAR(255),
      ADD CONSTRAINT "fk_action_plan_incident" 
      FOREIGN KEY ("incidentID") 
      REFERENCES "Incident" ("incidentID") 
      ON DELETE SET NULL;
    `);

    // Add actionPlanID column to Incident table
    await sequelize.query(`
      ALTER TABLE "Incident"
      ADD COLUMN IF NOT EXISTS "actionPlanID" VARCHAR(255),
      ADD CONSTRAINT "fk_incident_action_plan" 
      FOREIGN KEY ("actionPlanID") 
      REFERENCES "action_plan" ("actionPlanID") 
      ON DELETE SET NULL;
    `);

    console.log('incidentID column added to action_plan table and actionPlanID column added to Incident table successfully.');

  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await sequelize.close();
  }
}

migrateActionPlanIncident();

//node scripts/migrate-action-plan-incident.js