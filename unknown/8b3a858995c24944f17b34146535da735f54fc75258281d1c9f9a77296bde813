import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { toast } from 'sonner';
import { getApiBaseUrl } from '../../utils/api-config';

const API_URL = `${getApiBaseUrl()}/organizationalProcesses`;

// Initial state
const initialState = {
  organizationalProcesses: [],
  currentOrganizationalProcess: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: ''
};

// Get all organizational processes
export const getAllOrganizationalProcesses = createAsyncThunk(
  'organizationalProcesses/getAll',
  async (_, thunkAPI) => {
    try {
      const response = await axios.get(API_URL, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch organizational processes';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get organizational process by ID
export const getOrganizationalProcessById = createAsyncThunk(
  'organizationalProcesses/getById',
  async (id, thunkAPI) => {
    try {
      const response = await axios.get(`${API_URL}/${id}`, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch organizational process';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create new organizational process
export const createOrganizationalProcess = createAsyncThunk(
  'organizationalProcesses/create',
  async (organizationalProcessData, thunkAPI) => {
    try {
      const response = await axios.post(API_URL, organizationalProcessData, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to create organizational process';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update organizational process
export const updateOrganizationalProcess = createAsyncThunk(
  'organizationalProcesses/update',
  async ({ id, organizationalProcessData }, thunkAPI) => {
    try {
      const response = await axios.put(`${API_URL}/${id}`, organizationalProcessData, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to update organizational process';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete organizational process
export const deleteOrganizationalProcess = createAsyncThunk(
  'organizationalProcesses/delete',
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${API_URL}/${id}`, {
        withCredentials: true
      });
      return id;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete organizational process';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Organizational process slice
const organizationalProcessSlice = createSlice({
  name: 'organizationalProcess',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all organizational processes
      .addCase(getAllOrganizationalProcesses.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllOrganizationalProcesses.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.organizationalProcesses = action.payload.data || [];
      })
      .addCase(getAllOrganizationalProcesses.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Get organizational process by ID
      .addCase(getOrganizationalProcessById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getOrganizationalProcessById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentOrganizationalProcess = action.payload.data;
      })
      .addCase(getOrganizationalProcessById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Create organizational process
      .addCase(createOrganizationalProcess.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createOrganizationalProcess.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.organizationalProcesses.push(action.payload.data);
        toast.success('Organizational process created successfully');
      })
      .addCase(createOrganizationalProcess.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Update organizational process
      .addCase(updateOrganizationalProcess.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateOrganizationalProcess.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.organizationalProcesses = state.organizationalProcesses.map(organizationalProcess => 
          organizationalProcess.organizationalProcessID === action.payload.data.organizationalProcessID ? action.payload.data : organizationalProcess
        );
        state.currentOrganizationalProcess = action.payload.data;
        toast.success('Organizational process updated successfully');
      })
      .addCase(updateOrganizationalProcess.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Delete organizational process
      .addCase(deleteOrganizationalProcess.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteOrganizationalProcess.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.organizationalProcesses = state.organizationalProcesses.filter(organizationalProcess => organizationalProcess.organizationalProcessID !== action.meta.arg);
        toast.success('Organizational process deleted successfully');
      })
      .addCase(deleteOrganizationalProcess.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
  }
});

export const { reset } = organizationalProcessSlice.actions;
export default organizationalProcessSlice.reducer;
