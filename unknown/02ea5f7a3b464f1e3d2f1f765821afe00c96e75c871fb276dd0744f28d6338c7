const { sequelize } = require('../index');

const getIncidentsOverTime = async (timePeriod = 'month', start, end) => {
  try {
    let groupByClause;
    switch (timePeriod) {
      case 'day':
        groupByClause = "DATE_TRUNC('day', \"occurrenceDate\")";
        break;
      case 'year':
        groupByClause = "DATE_TRUNC('year', \"occurrenceDate\")";
        break;
      case 'month':
      default:
        groupByClause = "DATE_TRUNC('month', \"occurrenceDate\")";
    }

    let query = `
      SELECT 
        ${groupByClause} AS period, 
        COUNT(*)::integer AS incident_count
      FROM 
        "Incident"
      WHERE 
        "occurrenceDate" IS NOT NULL
    `;
    const params = [];
    if (start) {
      query += ` AND "occurrenceDate" >= $1`;
      params.push(start);
    }
    if (end) {
      query += ` AND "occurrenceDate" <= $2`;
      params.push(end);
    }
    query += `
      GROUP BY 
        ${groupByClause}
      ORDER BY 
        period;
    `;

    const results = await sequelize.query(query, { 
      bind: params,
      type: sequelize.QueryTypes.SELECT 
    });
    return results;
  } catch (error) {
    console.error('Error fetching incidents over time:', error);
    throw error;
  }
};

module.exports = { getIncidentsOverTime };