const { Sequelize } = require('sequelize');
require('dotenv').config();

async function createActionActionPlanRelation() {
  const sequelize = new Sequelize(process.env.DB_NAME, process.env.DB_USER, process.env.DB_PASSWORD, {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: false
  });

  try {
    await sequelize.authenticate();
    console.log('Connection has been established successfully.');

    // Create junction table for many-to-many relationship
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS "ActionActionPlan" (
        "actionID" VARCHAR(255) NOT NULL,
        "actionPlanID" VARCHAR(255) NOT NULL,
        "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY ("actionID", "actionPlanID"),
        CONSTRAINT "fk_action_action_plan_action" 
          FOREIGN KEY ("actionID") 
          REFERENCES "action" ("actionID")
          ON DELETE CASCADE,
        CONSTRAINT "fk_action_action_plan_action_plan" 
          FOREIGN KEY ("actionPlanID") 
          REFERENCES "action_plan" ("actionPlanID")
          ON DELETE CASCADE
      );
    `);

    // Make actionPlanID nullable in the action table
    await sequelize.query(`
      ALTER TABLE "action"
      ALTER COLUMN "actionPlanID" DROP NOT NULL;
    `);

    // Migrate existing relationships to the junction table
    await sequelize.query(`
      INSERT INTO "ActionActionPlan" ("actionID", "actionPlanID")
      SELECT "actionID", "actionPlanID"
      FROM "action"
      WHERE "actionPlanID" IS NOT NULL;
    `);

    console.log('Action-ActionPlan many-to-many relationship created successfully.');

  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await sequelize.close();
  }
}

createActionActionPlanRelation(); 

//node backend/scripts/create-action-action-plan-relation.js