module.exports = (sequelize, DataTypes) => {
  const Process = sequelize.define('Process', {
    processID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    type: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    entityID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Entity',
        key: 'entityID',
      },
    },
    operationID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Operation',
        key: 'operationID',
      },
    },
  }, {
    tableName: 'process',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['processID'],
      },
    ],
  });

  return Process;
};