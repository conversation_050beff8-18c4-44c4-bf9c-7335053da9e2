const { sequelize } = require('../index');

const getIncidentsByType = async () => {
  try {
    const query = `
      SELECT 
        "Incident"."incidentTypeID",
        "IncidentType"."name" AS type_name,
        COUNT(*)::integer AS incident_count
      FROM 
        "Incident"
      LEFT JOIN 
        "IncidentType" ON "Incident"."incidentTypeID" = "IncidentType"."incidentTypeID"
      WHERE 
        "Incident"."incidentTypeID" IS NOT NULL
      GROUP BY 
        "Incident"."incidentTypeID", "IncidentType"."name"
      ORDER BY 
        incident_count DESC;
    `;

    const results = await sequelize.query(query, { 
      type: sequelize.QueryTypes.SELECT 
    });
    return results;
  } catch (error) {
    console.error('Error fetching incidents by type:', error);
    throw error;
  }
};

module.exports = { getIncidentsByType };