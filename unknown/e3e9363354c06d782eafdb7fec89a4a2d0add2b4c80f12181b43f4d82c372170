import { useSelector } from 'react-redux';
import { hasPermission } from '@/store/auth-slice';
import { Button } from '@/components/ui/button';
import { 
  Trash2, 
  PlusCircle, 
  Edit, 
  Eye, 
  UserCog 
} from 'lucide-react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

/**
 * Component to demonstrate RBAC functionality
 * Shows different buttons based on user permissions
 */
const RbacDemo = () => {
  const { user } = useSelector(state => state.auth);
  const state = useSelector(state => state);
  
  // Check permissions using the Redux store
  const canCreate = hasPermission(state, 'create');
  const canRead = hasPermission(state, 'read');
  const canUpdate = hasPermission(state, 'update');
  const canDelete = hasPermission(state, 'delete');
  const canManageUsers = hasPermission(state, 'user_management');
  
  // Mock action handlers
  const handleCreate = () => toast.success('Create action would happen here');
  const handleView = () => toast.success('View action would happen here');
  const handleEdit = () => toast.success('Edit action would happen here');
  const handleDelete = () => toast.success('Delete action would happen here');
  const handleManageUsers = () => toast.success('User management would happen here');
  
  return (
    <Card className="w-full max-w-lg mx-auto">
      <CardHeader>
        <CardTitle>Role-Based Access Control Demo</CardTitle>
        <CardDescription>
          Your current role(s): 
          {user?.roles?.map(role => (
            <Badge key={role.id} className="ml-2">{role.name}</Badge>
          ))}
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-2">
          <div className="font-medium">Your permissions allow you to:</div>
          <ul className="list-disc list-inside space-y-1 ml-2">
            {canCreate && <li>Create new items</li>}
            {canRead && <li>View items</li>}
            {canUpdate && <li>Edit existing items</li>}
            {canDelete && <li>Delete items</li>}
            {canManageUsers && <li>Manage user accounts</li>}
          </ul>
        </div>
      </CardContent>
      
      <CardFooter className="flex flex-wrap gap-2">
        {canCreate && (
          <Button 
            variant="default" 
            size="sm" 
            onClick={handleCreate}
            className="flex items-center gap-1"
          >
            <PlusCircle className="h-4 w-4" />
            Create
          </Button>
        )}
        
        {canRead && (
          <Button 
            variant="secondary" 
            size="sm" 
            onClick={handleView}
            className="flex items-center gap-1"
          >
            <Eye className="h-4 w-4" />
            View
          </Button>
        )}
        
        {canUpdate && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleEdit}
            className="flex items-center gap-1"
          >
            <Edit className="h-4 w-4" />
            Edit
          </Button>
        )}
        
        {canDelete && (
          <Button 
            variant="destructive" 
            size="sm" 
            onClick={handleDelete}
            className="flex items-center gap-1"
          >
            <Trash2 className="h-4 w-4" />
            Delete
          </Button>
        )}
        
        {canManageUsers && (
          <Button 
            variant="default" 
            size="sm" 
            onClick={handleManageUsers}
            className="flex items-center gap-1 bg-indigo-600 hover:bg-indigo-700"
          >
            <UserCog className="h-4 w-4" />
            Manage Users
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

export default RbacDemo; 