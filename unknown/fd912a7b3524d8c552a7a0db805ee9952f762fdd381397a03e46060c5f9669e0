import axios from 'axios';
import { getApiBaseUrl } from './api-config';

const API_BASE_URL = getApiBaseUrl();

/**
 * Helper function to make authenticated API requests
 * @param {string} endpoint - API endpoint (without base URL)
 * @param {string} method - HTTP method (GET, POST, PUT, DELETE)
 * @param {object} data - Request body data (for POST, PUT)
 * @param {object} options - Additional axios options
 * @returns {Promise} - Axios response promise
 */
export const apiRequest = async (endpoint, method = 'GET', data = null, options = {}) => {
  // Get token from localStorage
  const token = localStorage.getItem('authToken');
  
  // Prepare headers
  const headers = {
    'Content-Type': 'application/json',
    ...(options.headers || {})
  };
  
  // Add authorization header if token exists
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  // Prepare request config
  const config = {
    method,
    url: `${API_BASE_URL}/${endpoint.replace(/^\//, '')}`,
    headers,
    withCredentials: true,
    ...options
  };
  
  // Add data to request if provided
  if (data) {
    if (method.toUpperCase() === 'GET') {
      config.params = data;
    } else {
      config.data = data;
    }
  }
  
  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API request error (${method} ${endpoint}):`, error);
    throw error;
  }
};

// Convenience methods
export const get = (endpoint, params, options) => 
  apiRequest(endpoint, 'GET', params, options);

export const post = (endpoint, data, options) => 
  apiRequest(endpoint, 'POST', data, options);

export const put = (endpoint, data, options) => 
  apiRequest(endpoint, 'PUT', data, options);

export const del = (endpoint, options) => 
  apiRequest(endpoint, 'DELETE', null, options);