const { sequelize } = require('../index');

const getInherentRiskRapport = async () => {
    const query = `
        SELECT 
            r."impact",
            r."probability",
            COUNT(*) as count
        FROM 
            "Risk" r
        WHERE 
            r."probability" IS NOT NULL AND r."impact" IS NOT NULL
        GROUP BY 
            r."impact", r."probability"
        ORDER BY 
            r."probability" DESC, r."impact" ASC;
    `;
    
    try {
        const results = await sequelize.query(query, { 
            type: sequelize.QueryTypes.SELECT 
        });
        
        // Create a 5x5 matrix initialized with zeros
        const matrix = Array(5).fill().map(() => Array(5).fill(0));
        
        // Fill the matrix with counts (adjusting for 0-based array indices)
        results.forEach(result => {
            if (result.impact && result.probability) {
                matrix[5 - result.probability][result.impact - 1] = result.count;
            }
        });
        
        return matrix;
    } catch (error) {
        console.error('Error executing InherentRiskRapport query:', error);
        throw error;
    }
};

module.exports = { getInherentRiskRapport };