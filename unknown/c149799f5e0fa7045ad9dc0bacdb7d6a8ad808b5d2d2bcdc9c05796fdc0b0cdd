module.exports = (sequelize, DataTypes) => {
  const IncidentActivityLog = sequelize.define('IncidentActivityLog', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    incident_id: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Incident',
        key: 'incidentID'
      }
    },
    timestamp: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    user: {
      type: DataTypes.STRING,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('creation', 'transition', 'update', 'ASSIGN_CONTRIBUTOR', 'REMOVE_CONTRIBUTOR'),
      allowNull: false
    },
    field: {
      type: DataTypes.STRING,
      allowNull: true
    },
    old_value: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    new_value: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    details: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'IncidentActivityLogs',
    timestamps: false
  });

  IncidentActivityLog.associate = (models) => {
    IncidentActivityLog.belongsTo(models.Incident, {
      foreignKey: 'incident_id',
      targetKey: 'incidentID',
      as: 'incident'
    });
  };

  return IncidentActivityLog;
}; 