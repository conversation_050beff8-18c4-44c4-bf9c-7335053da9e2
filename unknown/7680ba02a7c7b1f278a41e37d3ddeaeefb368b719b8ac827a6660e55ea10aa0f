// Mock data for all chart components

// Inherent Risk Matrix data (5x5 matrix)
export const inherentRiskData = [
  [2, 3, 5, 7, 9],   // Certain
  [1, 4, 6, 8, 5],   // Probable
  [3, 5, 7, 4, 2],   // Vraisemblable
  [6, 4, 2, 1, 0],   // Possible
  [8, 5, 3, 1, 0]    // Rare
];

// Residual Risk Matrix data (5x5 matrix)
export const residualRiskData = [
  [1, 3, 6, 8, 10],  // Très élevé
  [2, 4, 7, 9, 5],   // Élevé
  [4, 6, 5, 3, 2],   // Moyen
  [7, 5, 3, 2, 1],   // Bas
  [9, 6, 4, 2, 0]    // Très bas
];

// Loss Distribution data
export const lossDistributionData = [
  { TargetRisk: "Very Low", Probability: 15 },
  { TargetRisk: "Low", Probability: 25 },
  { TargetRisk: "Medium", Probability: 35 },
  { TargetRisk: "High", Probability: 20 },
  { TargetRisk: "Very High", Probability: 5 }
];

// Risk Incident data
export const riskIncidentData = [
  {
    "Code de Risque": "R001",
    "Risque": "Fraude externe",
    "Niveau de Risque net": "Élevé",
    "Nb des incidents": 12,
    "Perte brute": "€45,000",
    "Récupérations": "€15,000",
    "Perte nette": "€30,000"
  },
  {
    "Code de Risque": "R002",
    "Risque": "Fraude interne",
    "Niveau de Risque net": "Moyen",
    "Nb des incidents": 5,
    "Perte brute": "€25,000",
    "Récupérations": "€10,000",
    "Perte nette": "€15,000"
  },
  {
    "Code de Risque": "R003",
    "Risque": "Défaillance système",
    "Niveau de Risque net": "Bas",
    "Nb des incidents": 8,
    "Perte brute": "€12,000",
    "Récupérations": "€2,000",
    "Perte nette": "€10,000"
  },
  {
    "Code de Risque": "R004",
    "Risque": "Erreur d'exécution",
    "Niveau de Risque net": "Très élevé",
    "Nb des incidents": 20,
    "Perte brute": "€80,000",
    "Récupérations": "€20,000",
    "Perte nette": "€60,000"
  },
  {
    "Code de Risque": "R005",
    "Risque": "Risque réglementaire",
    "Niveau de Risque net": "Élevé",
    "Nb des incidents": 7,
    "Perte brute": "€50,000",
    "Récupérations": "€5,000",
    "Perte nette": "€45,000"
  }
];
