const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../../middleware/auth');
const { User } = require('../../models');
const { 
    getAllUsers, 
    getUserStats, 
    createUser, 
    updateUser 
} = require('../../controllers/users/user-controller');

// Get all users
router.get('/', authenticateToken, getAllUsers);

// Get user stats
router.get('/stats', authenticateToken, getUserStats);

// Create new user
router.post('/', authenticateToken, createUser);

// Update user
router.put('/:id', authenticateToken, updateUser);

// Delete user
router.delete('/:id', authenticateToken, async (req, res) => {
    try {
        const user = await User.findByPk(req.params.id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        await user.destroy();
        res.json({
            success: true,
            message: 'User deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete user'
        });
    }
});

module.exports = router;
