export const registerFormControls = [
  {
    name: "username",
    label: "User Name",
    placeholder: "Enter your user name",
    componentType: "input",
    type: "text",
  },
  {
    name: "email",
    label: "Email",
    placeholder: "Enter your email",
    componentType: "input",
    type: "email",
  },
  {
    name: "password",
    label: "Password",
    placeholder: "Enter your password",
    componentType: "input",
    type: "password",
  }
];

export const loginFormControls = [
  {
    name: "email",
    label: "Email",
    placeholder: "Enter your email",
    componentType: "input",
    type: "email",
  },
  {
    name: "password",
    label: "Password",
    placeholder: "Enter your password",
    componentType: "input",
    type: "password",
  },
];

export const mockIncidentData = [
  {
    id: 1,
    name: "System Outage",
    description: "Critical system downtime affecting customer transactions",
    declaredBy: "<PERSON> Smith",
    declarationDate: "2024-03-15",
    declarantEntity: "IT Department",
    detectionDate: "2024-03-15",
    occurrenceDate: "2024-03-14",
    nearMiss: 0,
    nature: "Financial",
    impact: "Very High",
    priority: "High",
    currency: "USD",
    grossLoss: 50000,
    recoveries: 10000,
    provisions: 20000,
    risk: "Operational Risk",
    control: "System Monitoring",
    entity: "Head Office",
    businessLine: "Digital Banking",
    incidentType: "Technology",
    businessProcess: "Payment Processing",
    organizationalProcess: "IT Operations",
    product: "Online Banking",
    application: "Core Banking System"
  },
  {
    id: 2,
    name: "Data Entry Error",
    description: "Incorrect customer data entry in core system",
    declaredBy: "Jane Doe",
    declarationDate: "2024-03-14",
    declarantEntity: "Operations",
    detectionDate: "2024-03-14",
    occurrenceDate: "2024-03-13",
    nearMiss: 1,
    nature: "Non-Financial",
    impact: "Low",
    priority: "Medium",
    currency: "EUR",
    grossLoss: 1000,
    recoveries: 500,
    provisions: 200,
    risk: "Process Risk",
    control: "Data Validation",
    entity: "Branch Office",
    businessLine: "Retail Banking",
    incidentType: "Process",
    businessProcess: "Customer Onboarding",
    organizationalProcess: "Customer Service",
    product: "Savings Account",
    application: "CRM System"
  },
  {
    id: 3,
    name: "Network Outage",
    description: "Complete network failure affecting all branch operations",
    declaredBy: "Michael Chen",
    declarationDate: "2024-03-13",
    declarantEntity: "Network Operations",
    detectionDate: "2024-03-13",
    occurrenceDate: "2024-03-13",
    nearMiss: 0,
    nature: "Financial",
    impact: "High",
    priority: "High",
    currency: "USD",
    grossLoss: 75000,
    recoveries: 15000,
    provisions: 25000,
    risk: "Infrastructure Risk",
    control: "Network Monitoring",
    entity: "Regional Office",
    businessLine: "Branch Operations",
    incidentType: "Infrastructure",
    businessProcess: "Branch Services",
    organizationalProcess: "Network Operations",
    product: "Branch Services",
    application: "Network Infrastructure"
  },
  {
    id: 4,
    name: "Security Breach",
    description: "Unauthorized access attempt detected in payment system",
    declaredBy: "Sarah Johnson",
    declarationDate: "2024-03-12",
    declarantEntity: "Security Team",
    detectionDate: "2024-03-12",
    occurrenceDate: "2024-03-11",
    nearMiss: 1,
    nature: "Non-Financial",
    impact: "Very High",
    priority: "High",
    currency: "USD",
    grossLoss: 0,
    recoveries: 0,
    provisions: 50000,
    risk: "Security Risk",
    control: "Security Monitoring",
    entity: "Head Office",
    businessLine: "Information Security",
    incidentType: "Security",
    businessProcess: "Security Operations",
    organizationalProcess: "Security Management",
    product: "Payment Gateway",
    application: "Security Systems"
  },
  {
    id: 5,
    name: "Compliance Violation",
    description: "Regulatory reporting deadline missed for Q1 reports",
    declaredBy: "Robert Wilson",
    declarationDate: "2024-03-11",
    declarantEntity: "Compliance",
    detectionDate: "2024-03-11",
    occurrenceDate: "2024-03-10",
    nearMiss: 0,
    nature: "Non-Financial",
    impact: "Medium",
    priority: "Medium",
    currency: "EUR",
    grossLoss: 25000,
    recoveries: 0,
    provisions: 10000,
    risk: "Compliance Risk",
    control: "Regulatory Monitoring",
    entity: "Head Office",
    businessLine: "Compliance",
    incidentType: "Regulatory",
    businessProcess: "Regulatory Reporting",
    organizationalProcess: "Compliance Management",
    product: "Regulatory Reports",
    application: "Reporting System"
  },
  {
    id: 6,
    name: "Customer Data Leak",
    description: "Accidental exposure of customer data through email",
    declaredBy: "Emma Davis",
    declarationDate: "2024-03-10",
    declarantEntity: "Customer Service",
    detectionDate: "2024-03-10",
    occurrenceDate: "2024-03-10",
    nearMiss: 0,
    nature: "Financial",
    impact: "High",
    priority: "High",
    currency: "USD",
    grossLoss: 100000,
    recoveries: 20000,
    provisions: 30000,
    risk: "Data Privacy Risk",
    control: "Data Protection",
    entity: "Branch Office",
    businessLine: "Customer Relations",
    incidentType: "Privacy",
    businessProcess: "Customer Communication",
    organizationalProcess: "Data Management",
    product: "Email Service",
    application: "CRM System"
  },
  {
    id: 7,
    name: "Payment Processing Delay",
    description: "Batch payment processing system delay affecting international transfers",
    declaredBy: "Tom Anderson",
    declarationDate: "2024-03-09",
    declarantEntity: "Operations",
    detectionDate: "2024-03-09",
    occurrenceDate: "2024-03-09",
    nearMiss: 0,
    nature: "Financial",
    impact: "Medium",
    priority: "High",
    currency: "EUR",
    grossLoss: 30000,
    recoveries: 5000,
    provisions: 15000,
    risk: "Operational Risk",
    control: "Payment Monitoring",
    entity: "Operations Center",
    businessLine: "Payment Operations",
    incidentType: "Process",
    businessProcess: "Payment Processing",
    organizationalProcess: "Transaction Management",
    product: "International Transfers",
    application: "Payment System"
  },
  {
    id: 8,
    name: "ATM Malfunction",
    description: "Multiple ATMs offline due to software update failure",
    declaredBy: "Lisa Brown",
    declarationDate: "2024-03-08",
    declarantEntity: "ATM Operations",
    detectionDate: "2024-03-08",
    occurrenceDate: "2024-03-08",
    nearMiss: 0,
    nature: "Financial",
    impact: "Medium",
    priority: "Medium",
    currency: "USD",
    grossLoss: 20000,
    recoveries: 5000,
    provisions: 10000,
    risk: "Technical Risk",
    control: "ATM Monitoring",
    entity: "Branch Network",
    businessLine: "ATM Services",
    incidentType: "Technology",
    businessProcess: "Cash Services",
    organizationalProcess: "ATM Management",
    product: "ATM Services",
    application: "ATM Network"
  }
];

export const mockRiskData = [
  {
    id: 1,
    title: "Network Security Breach",
    description: "Potential unauthorized access to network resources",
    severity: "High",
    status: "Active",
    date: "2024-03-15",
  },
  {
    id: 2,
    title: "Data Loss",
    description: "Risk of data corruption or loss during system updates",
    severity: "Medium",
    status: "Pending",
    date: "2024-03-14",
  },
  {
    id: 3,
    title: "Software Vulnerability",
    description: "Critical security patch pending deployment",
    severity: "High",
    status: "Active",
    date: "2024-03-13",
  },
  {
    id: 4,
    title: "Hardware Failure",
    description: "Server hardware showing signs of failure",
    severity: "Medium",
    status: "Pending",
    date: "2024-03-12",
  },
  {
    id: 5,
    title: "Configuration Error",
    description: "Misconfiguration in firewall rules",
    severity: "Low",
    status: "Active",
    date: "2024-03-11",
  },
  {
    id: 6,
    title: "Backup System Issue",
    description: "Backup system not functioning properly",
    severity: "High",
    status: "Active",
    date: "2024-03-10",
  },
  {
    id: 7,
    title: "Database Performance",
    description: "Slow query response times affecting system",
    severity: "Medium",
    status: "Pending",
    date: "2024-03-09",
  },
  {
    id: 8,
    title: "API Security Risk",
    description: "Potential vulnerabilities in API endpoints",
    severity: "High",
    status: "Active",
    date: "2024-03-08",
  },
  {
    id: 9,
    title: "Data Compliance",
    description: "Risk of non-compliance with data protection regulations",
    severity: "High",
    status: "Pending",
    date: "2024-03-07",
  },
  {
    id: 10,
    title: "Infrastructure Scaling",
    description: "Risk of system failure due to increased load",
    severity: "Medium",
    status: "Active",
    date: "2024-03-06",
  }
  ,
  {
    id: 11,
    title: "TEST",
    description: "Risk of system failure due to increased load",
    severity: "Low",
    status: "Active",
    date: "2024-03-06",
  }
];

export const currencies = [
  { code: 'XOF', name: 'West African CFA franc' },
  { code: 'EUR', name: 'Euro' },
  { code: 'USD', name: 'US Dollar' },
  { code: 'GBP', name: 'British Pound' },
  { code: 'JPY', name: 'Japanese Yen' },
  { code: 'CNY', name: 'Chinese Yuan' },
  { code: 'CHF', name: 'Swiss Franc' },
  { code: 'CAD', name: 'Canadian Dollar' },
  { code: 'AUD', name: 'Australian Dollar' },
  { code: 'MAD', name: 'Moroccan Dirham' },
  { code: 'NGN', name: 'Nigerian Naira' },
  { code: 'ZAR', name: 'South African Rand' },
  { code: 'EGP', name: 'Egyptian Pound' },
  { code: 'GHS', name: 'Ghanaian Cedi' },
  { code: 'KES', name: 'Kenyan Shilling' }
];
