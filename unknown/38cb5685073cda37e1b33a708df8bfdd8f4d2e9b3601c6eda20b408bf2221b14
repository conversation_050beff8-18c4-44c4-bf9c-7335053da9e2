// backend/models/rapport/RapportRiskIncident.js
const { sequelize } = require('../index'); // Adjust the path to your Sequelize instance

/**
 * Fetches the Back Testing report data from the database, only including risks with associated incidents.
 * @returns {Promise<Array>} An array of report rows.
 */
const getBackTestingReport = async () => {
    const query = `
      SELECT 
          "Risk"."code" AS "Code de Risque", 
          "Risk"."name" AS "Risque", 
          CASE
              WHEN "Risk"."impact" IS NULL OR "Risk"."probability" IS NULL OR "Risk"."DMR" IS NULL THEN 'N/A'
              WHEN ("Risk"."DMR" * "Risk"."impact" * "Risk"."probability") <= 16 THEN 'Faible'
              WHEN ("Risk"."DMR" * "Risk"."impact" * "Risk"."probability") <= 81 THEN 'Moyen'
              WHEN ("Risk"."DMR" * "Risk"."impact" * "Risk"."probability") <= 256 THEN 'Élevé'
              ELSE 'Très <PERSON>'
          END AS "Niveau de Risque net",
          COUNT("Incident"."incidentID") AS "Nb des incidents",
          SUM("Incident"."grossLoss") AS "Perte brute",
          SUM("Incident"."grossLoss") AS "Perte effective brute",
          SUM("Incident"."recoveries") AS "Récupérations",
          COALESCE(SUM("Incident"."grossLoss"), 0) - COALESCE(SUM("Incident"."recoveries"), 0) AS "Perte nette",
          COALESCE(SUM("Incident"."grossLoss"), 0) - COALESCE(SUM("Incident"."recoveries"), 0) AS "Perte effective net"
      FROM 
          "Risk"
      INNER JOIN 
          "Incident" ON "Risk"."riskID" = "Incident"."riskID"
      GROUP BY 
          "Risk"."riskID", "Risk"."code", "Risk"."name", "Risk"."impact", "Risk"."probability", "Risk"."DMR";
    `;
    try {
        const results = await sequelize.query(query, { 
            type: sequelize.QueryTypes.SELECT 
        });
        return results;
    } catch (error) {
        console.error('Error executing BackTestingReport query:', error);
        throw error; // Re-throw to be handled by the caller
    }
};

module.exports = { getBackTestingReport };