import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { toast } from 'sonner';
import { getApiBaseUrl } from '../../utils/api-config';

const API_URL = `${getApiBaseUrl()}/businessProcesses`;

// Initial state
const initialState = {
  businessProcesses: [],
  currentBusinessProcess: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: ''
};

// Get all business processes
export const getAllBusinessProcesses = createAsyncThunk(
  'businessProcesses/getAll',
  async (_, thunkAPI) => {
    try {
      const response = await axios.get(API_URL, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch business processes';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get business process by ID
export const getBusinessProcessById = createAsyncThunk(
  'businessProcesses/getById',
  async (id, thunkAPI) => {
    try {
      const response = await axios.get(`${API_URL}/${id}`, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch business process';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create new business process
export const createBusinessProcess = createAsyncThunk(
  'businessProcesses/create',
  async (businessProcessData, thunkAPI) => {
    try {
      const response = await axios.post(API_URL, businessProcessData, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to create business process';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update business process
export const updateBusinessProcess = createAsyncThunk(
  'businessProcesses/update',
  async ({ id, businessProcessData }, thunkAPI) => {
    try {
      const response = await axios.put(`${API_URL}/${id}`, businessProcessData, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to update business process';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete business process
export const deleteBusinessProcess = createAsyncThunk(
  'businessProcesses/delete',
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${API_URL}/${id}`, {
        withCredentials: true
      });
      return id;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete business process';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Business process slice
const businessProcessSlice = createSlice({
  name: 'businessProcess',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all business processes
      .addCase(getAllBusinessProcesses.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllBusinessProcesses.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.businessProcesses = action.payload.data || [];
      })
      .addCase(getAllBusinessProcesses.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Get business process by ID
      .addCase(getBusinessProcessById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getBusinessProcessById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentBusinessProcess = action.payload.data;
      })
      .addCase(getBusinessProcessById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Create business process
      .addCase(createBusinessProcess.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createBusinessProcess.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.businessProcesses.push(action.payload.data);
        toast.success('Business process created successfully');
      })
      .addCase(createBusinessProcess.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Update business process
      .addCase(updateBusinessProcess.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateBusinessProcess.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.businessProcesses = state.businessProcesses.map(businessProcess => 
          businessProcess.businessProcessID === action.payload.data.businessProcessID ? action.payload.data : businessProcess
        );
        state.currentBusinessProcess = action.payload.data;
        toast.success('Business process updated successfully');
      })
      .addCase(updateBusinessProcess.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Delete business process
      .addCase(deleteBusinessProcess.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteBusinessProcess.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.businessProcesses = state.businessProcesses.filter(businessProcess => businessProcess.businessProcessID !== action.meta.arg);
        toast.success('Business process deleted successfully');
      })
      .addCase(deleteBusinessProcess.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
  }
});

export const { reset } = businessProcessSlice.actions;
export default businessProcessSlice.reducer;
