const { Activity } = require('../../models');

const getRecentActivities = async (req, res) => {
  try {
    const activities = await Activity.findAll({
      order: [['createdAt', 'DESC']],
      limit: 100 // Limit to most recent 100 activities
    });

    // Process activities to parse details and add timestamp
    const processedActivities = activities.map(activity => {
      const activityData = activity.toJSON();

      // Add timestamp field using createdAt
      activityData.timestamp = activityData.createdAt;

      // Parse details if it's a JSON string
      if (activityData.details && typeof activityData.details === 'string') {
        try {
          activityData.details = JSON.parse(activityData.details);
        } catch (e) {
          console.error('Error parsing activity details:', e);
          // Keep the original string if parsing fails
        }
      }

      return activityData;
    });

    res.json({
      success: true,
      data: processedActivities
    });
  } catch (error) {
    console.error('Error fetching recent activities:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch recent activities',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

const getUserActivities = async (req, res) => {
  try {
    const userId = parseInt(req.params.userId);
    const activities = await Activity.findAll({
      where: { userId },
      order: [['createdAt', 'DESC']],
      limit: 50 // Limit to most recent 50 activities per user
    });

    // Process activities to parse details and add timestamp
    const processedActivities = activities.map(activity => {
      const activityData = activity.toJSON();

      // Add timestamp field using createdAt
      activityData.timestamp = activityData.createdAt;

      // Parse details if it's a JSON string
      if (activityData.details && typeof activityData.details === 'string') {
        try {
          activityData.details = JSON.parse(activityData.details);
        } catch (e) {
          console.error('Error parsing activity details:', e);
          // Keep the original string if parsing fails
        }
      }

      return activityData;
    });

    res.json({
      success: true,
      data: processedActivities
    });
  } catch (error) {
    console.error('Error fetching user activities:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user activities',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getRecentActivities,
  getUserActivities
};
