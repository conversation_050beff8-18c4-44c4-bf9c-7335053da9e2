// backend/models/BusinessLine.js
module.exports = (sequelize, DataTypes) => {
  const BusinessLine = sequelize.define('BusinessLine', {
    businessLineID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: "Name is required"
        }
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'BusinessLine',
    timestamps: false
  });

  return BusinessLine;
};