import axios from 'axios';

/**
 * Configure axios with interceptors to handle authentication
 */
const setupAxiosInterceptors = () => {
  // Request interceptor
  axios.interceptors.request.use(
    (config) => {
      // Get token from localStorage
      const token = localStorage.getItem('authToken');
      
      // If token exists, add it to the Authorization header
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor
  axios.interceptors.response.use(
    (response) => response,
    (error) => {
      // Handle 401 errors (unauthorized)
      if (error.response && error.response.status === 401) {
        console.log('Unauthorized request detected. Redirecting to login...');
        // You could dispatch a logout action here or redirect to login
        // store.dispatch(logout());
        // window.location.href = '/auth/login';
      }
      return Promise.reject(error);
    }
  );
};

export default setupAxiosInterceptors;