module.exports = (sequelize, DataTypes) => {
  const Reference = sequelize.define('Reference', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      defaultValue: () => `REF_${Date.now()}_${Math.floor(Math.random() * 1000)}`
    },
    url: {
      type: DataTypes.STRING,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    incidentID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Incident',
        key: 'incidentID'
      }
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'References'
  });

  Reference.associate = (models) => {
    Reference.belongsTo(models.Incident, {
      foreignKey: 'incidentID',
      as: 'incident'
    });
  };

  return Reference;
}; 