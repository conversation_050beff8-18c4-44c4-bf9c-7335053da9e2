const { Sequelize } = require('sequelize');
require('dotenv').config();

async function dropActionPlanIncidentTable() {
  const sequelize = new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USER,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      dialect: 'postgres',
      logging: console.log
    }
  );

  try {
    // Test the connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Check if ActionPlanIncident table exists
    const [tables] = await sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'ActionPlanIncident';
    `);

    if (tables.length === 0) {
      console.log('ActionPlanIncident table does not exist. No action needed.');
    } else {
      console.log('ActionPlanIncident table found. Dropping it...');
      await sequelize.query(`DROP TABLE "ActionPlanIncident" CASCADE;`);
      console.log('ActionPlanIncident table dropped successfully.');
    }

    console.log('\nOperation completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error dropping ActionPlanIncident table:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

dropActionPlanIncidentTable();

//node scripts/drop-action-plan-incident-table.js