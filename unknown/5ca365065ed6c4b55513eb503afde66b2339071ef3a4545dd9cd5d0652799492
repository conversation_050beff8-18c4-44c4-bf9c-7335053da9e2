module.exports = (sequelize, DataTypes) => {
  const Incident = sequelize.define('Incident', {
    incidentID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    riskID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Risk',
        key: 'riskID'
      }
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    declaredBy: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    declarationDate: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: DataTypes.NOW,
    },
    declarantEntity: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    detectionDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    occurrenceDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    nearMiss: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
    nature: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    impact: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    priority: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    currency: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    grossLoss: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    recoveries: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    provisions: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    controlID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Control',
        key: 'controlID',
      },
    },
    entityID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Entity',
        key: 'entityID',
      },
    },
    businessLineID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'BusinessLine',
        key: 'businessLineID',
      },
    },
    incidentTypeID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'IncidentType',
        key: 'incidentTypeID',
      },
    },
    businessProcessID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'BusinessProcess',
        key: 'businessProcessID',
      },
    },
    organizationalProcessID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'OrganizationalProcess',
        key: 'organizationalProcessID',
      },
    },
    productID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Product',
        key: 'productID',
      },
    },
    applicationID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Application',
        key: 'applicationID',
      },
    },
    actionPlanID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'action_plan',
        key: 'actionPlanID'
      }
    },
    current_state: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'Start'
    }
  }, {
    tableName: 'Incident',
    freezeTableName: true,
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['incidentID'],
      },
    ],
  });

  Incident.associate = (models) => {
    // ActionPlan association (Incident belongs to one ActionPlan)
    Incident.belongsTo(models.ActionPlan, {
      foreignKey: 'actionPlanID',
      as: 'actionPlan'
    });

    // Incident has many ActionPlans
    Incident.hasMany(models.ActionPlan, {
      foreignKey: 'incidentID',
      as: 'actionPlans'
    });

    // Incident has many FinancialEntries (legacy)
    Incident.hasMany(models.FinancialEntry, {
      foreignKey: 'incidentID',
      as: 'financialEntries'
    });

    // Incident has many IncidentLosses
    Incident.hasMany(models.IncidentLoss, {
      foreignKey: 'incidentID',
      as: 'lossEntries'
    });

    // Incident has many IncidentGains
    Incident.hasMany(models.IncidentGain, {
      foreignKey: 'incidentID',
      as: 'gainEntries'
    });

    // Incident has many IncidentRecoveries
    Incident.hasMany(models.IncidentRecovery, {
      foreignKey: 'incidentID',
      as: 'recoveryEntries'
    });

    // Incident has many IncidentProvisions
    Incident.hasMany(models.IncidentProvision, {
      foreignKey: 'incidentID',
      as: 'provisionEntries'
    });
    
    // Incident has many IncidentEvents
    Incident.hasMany(models.IncidentEvent, {
      foreignKey: 'incident_id',
      sourceKey: 'incidentID', 
      as: 'events'
    });

    // New association with contributors through IncidentContributor
    Incident.belongsToMany(models.User, {
      through: models.IncidentContributor,
      foreignKey: 'incident_id',
      otherKey: 'user_id',
      as: 'contributors'
    });

    // IncidentContributor direct association
    Incident.hasMany(models.IncidentContributor, {
      foreignKey: 'incident_id',
      sourceKey: 'incidentID',
      as: 'contributorAssignments'
    });
  };

  return Incident;
};