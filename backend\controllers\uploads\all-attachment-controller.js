const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const db = require('../../models');
const { Op } = require('sequelize');

const AllAttachment = db.AllAttachment;
const Attachment = db.Attachment;
const RiskAttachment = db.RiskAttachment;
const ActionPlanAttachment = db.ActionPlanAttachment;
const FicheDeTestAttachment = db.FicheDeTestAttachment;

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../../uploads');
const docsDir = path.join(uploadsDir, 'all-documents');
const refsDir = path.join(uploadsDir, 'all-references');

// Ensure directories exist
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}
if (!fs.existsSync(docsDir)) {
  fs.mkdirSync(docsDir, { recursive: true });
}
if (!fs.existsSync(refsDir)) {
  fs.mkdirSync(refsDir, { recursive: true });
}

// Upload a file
const uploadFile = async (req, res) => {
  try {
    if (!req.files || !req.files.file) {
      return res.status(400).json({
        success: false,
        message: 'No file was uploaded'
      });
    }

    const {
      type,
      auditActivityID, auditConstatID, auditFicheDeTravailID, auditFicheDeTestID,
      auditMissionID, auditScopeID, actionPlanID, businessProcessID, controlID,
      entityID, incidentID, organizationalProcessID, riskID, questionID,
      campagneID, sampleNumber
    } = req.body;

    // Check if at least one ID is provided
    const hasValidID = auditActivityID || auditConstatID || auditFicheDeTravailID ||
                      auditFicheDeTestID || auditMissionID || auditScopeID ||
                      actionPlanID || businessProcessID || controlID || entityID ||
                      incidentID || organizationalProcessID || riskID || campagneID;

    if (!hasValidID) {
      return res.status(400).json({
        success: false,
        message: 'At least one entity ID is required'
      });
    }

    if (!type || !['business-document', 'external-reference'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Valid attachment type is required'
      });
    }

    const file = req.files.file;
    const fileExtension = path.extname(file.name).toLowerCase();

    // Define allowed file extensions
    const allowedExtensions = [
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.txt', '.csv', '.rtf', '.odt', '.ods', '.odp',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg',
      '.zip', '.rar', '.7z', '.tar', '.gz'
    ];

    // Check if file extension is allowed
    if (!allowedExtensions.includes(fileExtension)) {
      return res.status(400).json({
        success: false,
        message: `File type ${fileExtension} is not allowed`
      });
    }

    const fileName = `${uuidv4()}${fileExtension}`;
    const uploadDir = type === 'business-document' ? docsDir : refsDir;
    const filePath = path.join(uploadDir, fileName);

    // Move the file to the uploads directory
    await file.mv(filePath);

    // Generate a unique ID for the attachment
    const allAttachmentID = `ALL_ATT_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

    // Save file metadata to database
    const attachment = await AllAttachment.create({
      allAttachmentID,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.mimetype,
      filePath: fileName,
      uploadDate: new Date(),
      type,
      auditActivityID: auditActivityID || null,
      auditConstatID: auditConstatID || null,
      auditFicheDeTravailID: auditFicheDeTravailID || null,
      auditFicheDeTestID: auditFicheDeTestID || null,
      questionID: questionID || null,
      auditMissionID: auditMissionID || null,
      auditScopeID: auditScopeID || null,
      actionPlanID: actionPlanID || null,
      businessProcessID: businessProcessID || null,
      controlID: controlID || null,
      entityID: entityID || null,
      incidentID: incidentID || null,
      organizationalProcessID: organizationalProcessID || null,
      riskID: riskID || null,
      campagneID: campagneID || null,
      sampleNumber: sampleNumber || null
    });

    return res.status(201).json({
      success: true,
      message: 'File uploaded successfully',
      data: attachment
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to upload file'
    });
  }
};

// Add a web reference
const addReference = async (req, res) => {
  try {
    const {
      url, fileName, description, type,
      auditActivityID, auditConstatID, auditFicheDeTravailID, auditFicheDeTestID,
      auditMissionID, auditScopeID, actionPlanID, businessProcessID, controlID,
      entityID, incidentID, organizationalProcessID, riskID, questionID,
      campagneID, sampleNumber
    } = req.body;

    if (!url || !fileName) {
      return res.status(400).json({
        success: false,
        message: 'URL and file name are required'
      });
    }

    // Check if at least one ID is provided
    const hasValidID = auditActivityID || auditConstatID || auditFicheDeTravailID ||
                      auditFicheDeTestID || auditMissionID || auditScopeID ||
                      actionPlanID || businessProcessID || controlID || entityID ||
                      incidentID || organizationalProcessID || riskID || campagneID;

    if (!hasValidID) {
      return res.status(400).json({
        success: false,
        message: 'At least one entity ID is required'
      });
    }

    if (!type || type !== 'external-reference') {
      return res.status(400).json({
        success: false,
        message: 'Type must be external-reference'
      });
    }

    // Generate a unique ID for the attachment
    const allAttachmentID = `ALL_REF_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

    // Save reference to database
    const attachment = await AllAttachment.create({
      allAttachmentID,
      fileName,
      fileSize: 0,
      fileType: 'url/link',
      filePath: url,
      uploadDate: new Date(),
      type,
      auditActivityID: auditActivityID || null,
      auditConstatID: auditConstatID || null,
      auditFicheDeTravailID: auditFicheDeTravailID || null,
      auditFicheDeTestID: auditFicheDeTestID || null,
      questionID: questionID || null,
      auditMissionID: auditMissionID || null,
      auditScopeID: auditScopeID || null,
      actionPlanID: actionPlanID || null,
      businessProcessID: businessProcessID || null,
      controlID: controlID || null,
      entityID: entityID || null,
      incidentID: incidentID || null,
      organizationalProcessID: organizationalProcessID || null,
      riskID: riskID || null,
      campagneID: campagneID || null,
      sampleNumber: sampleNumber || null,
      url: url,
      description: description || null
    });

    return res.status(201).json({
      success: true,
      message: 'Reference added successfully',
      data: attachment
    });
  } catch (error) {
    console.error('Error adding reference:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to add reference'
    });
  }
};

// Get all attachments
const getAllAttachments = async (req, res) => {
  try {
    const {
      type,
      auditActivityID, auditConstatID, auditFicheDeTravailID, auditFicheDeTestID,
      auditMissionID, auditScopeID, actionPlanID, businessProcessID, controlID,
      entityID, incidentID, organizationalProcessID, riskID, questionID,
      limit = 100, offset = 0
    } = req.query;

    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Build query conditions
    const where = {};

    if (type && ['business-document', 'external-reference'].includes(type)) {
      where.type = type;
    }

    // Add entity IDs to query if provided
    if (auditActivityID) where.auditActivityID = auditActivityID;
    if (auditConstatID) where.auditConstatID = auditConstatID;
    if (auditFicheDeTravailID) where.auditFicheDeTravailID = auditFicheDeTravailID;
    if (auditFicheDeTestID) where.auditFicheDeTestID = auditFicheDeTestID;
    if (questionID) where.questionID = questionID;
    if (auditMissionID) where.auditMissionID = auditMissionID;
    if (auditScopeID) where.auditScopeID = auditScopeID;
    if (actionPlanID) where.actionPlanID = actionPlanID;
    if (businessProcessID) where.businessProcessID = businessProcessID;
    if (controlID) where.controlID = controlID;
    if (entityID) where.entityID = entityID;
    if (incidentID) where.incidentID = incidentID;
    if (organizationalProcessID) where.organizationalProcessID = organizationalProcessID;
    if (riskID) where.riskID = riskID;

    // Query with pagination
    const attachments = await AllAttachment.findAll({
      where,
      order: [['uploadDate', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
      raw: true
    });

    return res.json({
      success: true,
      count: attachments.length,
      data: attachments
    });
  } catch (error) {
    console.error('Error getting attachments:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get a specific attachment
const getAttachment = async (req, res) => {
  try {
    const { id } = req.params;

    const attachment = await AllAttachment.findByPk(id);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    return res.json({
      success: true,
      data: attachment
    });
  } catch (error) {
    console.error('Error getting attachment:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachment'
    });
  }
};

// Delete an attachment
const deleteAttachment = async (req, res) => {
  try {
    const { id } = req.params;

    const attachment = await AllAttachment.findByPk(id);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // If it's a file (not a reference), delete from filesystem
    if (attachment.type === 'business-document') {
      const uploadDir = docsDir;
      const filePath = path.join(uploadDir, attachment.filePath);

      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }

    // Delete the record from the database
    await attachment.destroy();

    return res.json({
      success: true,
      message: 'Attachment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting attachment:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete attachment'
    });
  }
};

// Download an attachment
const downloadAttachment = async (req, res) => {
  try {
    const { id } = req.params;

    const attachment = await AllAttachment.findByPk(id);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // If it's an external reference, redirect to the URL
    if (attachment.type === 'external-reference') {
      return res.redirect(attachment.filePath);
    }

    // Otherwise, download the file
    const uploadDir = docsDir;
    const filePath = path.join(uploadDir, attachment.filePath);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'File not found on server'
      });
    }

    res.download(filePath, attachment.fileName);
  } catch (error) {
    console.error('Error downloading attachment:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to download attachment'
    });
  }
};

// Sync attachments from all tables
// Sync attachments from all tables
const syncAttachments = async (req, res) => {
  try {
    let syncCount = 0;
    
    // Sync Incident attachments
    if (db.Attachment) {
      const incidentAttachments = await db.Attachment.findAll({ raw: true });
      for (const att of incidentAttachments) {
        const exists = await AllAttachment.findOne({ 
          where: { 
            [Op.or]: [
              { allAttachmentID: `SYNC_INC_${att.attachmentID}` },
              { 
                fileName: att.fileName,
                incidentID: att.incidentID,
                uploadDate: att.uploadDate
              }
            ]
          }
        });
        
        if (!exists) {
          await AllAttachment.create({
            allAttachmentID: `SYNC_INC_${att.attachmentID}`,
            fileName: att.fileName,
            fileSize: att.fileSize,
            fileType: att.fileType,
            filePath: att.filePath,
            uploadDate: att.uploadDate || new Date(),
            type: att.type || 'business-document',
            incidentID: att.incidentID
          });
          syncCount++;
        }
      }
    }
    
    // Sync Risk attachments
    if (db.RiskAttachment) {
      const riskAttachments = await db.RiskAttachment.findAll({ raw: true });
      for (const att of riskAttachments) {
        const exists = await AllAttachment.findOne({ 
          where: { 
            [Op.or]: [
              { allAttachmentID: `SYNC_RISK_${att.attachmentID}` },
              { 
                fileName: att.fileName,
                riskID: att.riskID,
                uploadDate: att.uploadDate
              }
            ]
          }
        });
        
        if (!exists) {
          await AllAttachment.create({
            allAttachmentID: `SYNC_RISK_${att.attachmentID}`,
            fileName: att.fileName,
            fileSize: att.fileSize,
            fileType: att.fileType,
            filePath: att.filePath,
            uploadDate: att.uploadDate || new Date(),
            type: att.type || 'business-document',
            riskID: att.riskID
          });
          syncCount++;
        }
      }
    }
    
    // Sync Action Plan attachments
    if (db.ActionPlanAttachment) {
      const actionPlanAttachments = await db.ActionPlanAttachment.findAll({ raw: true });
      for (const att of actionPlanAttachments) {
        const exists = await AllAttachment.findOne({ 
          where: { 
            [Op.or]: [
              { allAttachmentID: `SYNC_AP_${att.attachmentID}` },
              { 
                fileName: att.fileName,
                actionPlanID: att.actionPlanID,
                uploadDate: att.uploadDate
              }
            ]
          }
        });
        
        if (!exists) {
          await AllAttachment.create({
            allAttachmentID: `SYNC_AP_${att.attachmentID}`,
            fileName: att.fileName,
            fileSize: att.fileSize,
            fileType: att.fileType,
            filePath: att.filePath,
            uploadDate: att.uploadDate || new Date(),
            type: att.type || 'business-document',
            actionPlanID: att.actionPlanID
          });
          syncCount++;
        }
      }
    }
    
    // Sync Audit FicheDeTest attachments
    if (db.FicheDeTestAttachment) {
      const ficheDeTestAttachments = await db.FicheDeTestAttachment.findAll({ raw: true });
      for (const att of ficheDeTestAttachments) {
        const exists = await AllAttachment.findOne({ 
          where: { 
            [Op.or]: [
              { allAttachmentID: `SYNC_FDT_${att.attachmentID}` },
              { 
                fileName: att.fileName,
                auditFicheDeTestID: att.ficheDeTestID,
                uploadDate: att.uploadDate
              }
            ]
          }
        });
        
        if (!exists) {
          await AllAttachment.create({
            allAttachmentID: `SYNC_FDT_${att.attachmentID}`,
            fileName: att.fileName,
            fileSize: att.fileSize,
            fileType: att.fileType,
            filePath: att.filePath,
            uploadDate: att.uploadDate || new Date(),
            type: att.type || 'business-document',
            auditFicheDeTestID: att.ficheDeTestID,
            auditFicheDeTravailID: att.ficheDeTravailID
          });
          syncCount++;
        }
      }
    }
    
    // Add more sync blocks for other attachment types as needed
    
    return res.json({
      success: true,
      message: `Synchronized ${syncCount} attachments successfully`
    });
  } catch (error) {
    console.error('Error syncing attachments:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to sync attachments'
    });
  }
};

// Get attachments by auditActivityID
const getAttachmentsByAuditActivityID = async (req, res) => {
  try {
    const { auditActivityID } = req.params;

    if (!auditActivityID) {
      return res.status(400).json({
        success: false,
        message: 'auditActivityID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        auditActivityID: auditActivityID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by auditActivityID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get attachments by auditConstatID
const getAttachmentsByAuditConstatID = async (req, res) => {
  try {
    const { auditConstatID } = req.params;

    if (!auditConstatID) {
      return res.status(400).json({
        success: false,
        message: 'auditConstatID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        auditConstatID: auditConstatID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by auditConstatID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get attachments by auditFicheDeTravailID
const getAttachmentsByAuditFicheDeTravailID = async (req, res) => {
  try {
    const { auditFicheDeTravailID } = req.params;

    if (!auditFicheDeTravailID) {
      return res.status(400).json({
        success: false,
        message: 'auditFicheDeTravailID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        auditFicheDeTravailID: auditFicheDeTravailID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by auditFicheDeTravailID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get attachments by auditFicheDeTestID
const getAttachmentsByAuditFicheDeTestID = async (req, res) => {
  try {
    const { auditFicheDeTestID } = req.params;

    if (!auditFicheDeTestID) {
      return res.status(400).json({
        success: false,
        message: 'auditFicheDeTestID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        auditFicheDeTestID: auditFicheDeTestID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by auditFicheDeTestID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get attachments by auditMissionID
const getAttachmentsByAuditMissionID = async (req, res) => {
  try {
    const { auditMissionID } = req.params;

    if (!auditMissionID) {
      return res.status(400).json({
        success: false,
        message: 'auditMissionID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        auditMissionID: auditMissionID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by auditMissionID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get attachments by auditScopeID
const getAttachmentsByAuditScopeID = async (req, res) => {
  try {
    const { auditScopeID } = req.params;

    if (!auditScopeID) {
      return res.status(400).json({
        success: false,
        message: 'auditScopeID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        auditScopeID: auditScopeID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by auditScopeID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get attachments by actionPlanID
const getAttachmentsByActionPlanID = async (req, res) => {
  try {
    const { actionPlanID } = req.params;

    if (!actionPlanID) {
      return res.status(400).json({
        success: false,
        message: 'actionPlanID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        actionPlanID: actionPlanID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by actionPlanID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get attachments by businessProcessID
const getAttachmentsByBusinessProcessID = async (req, res) => {
  try {
    const { businessProcessID } = req.params;

    if (!businessProcessID) {
      return res.status(400).json({
        success: false,
        message: 'businessProcessID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        businessProcessID: businessProcessID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by businessProcessID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get attachments by controlID
const getAttachmentsByControlID = async (req, res) => {
  try {
    const { controlID } = req.params;

    if (!controlID) {
      return res.status(400).json({
        success: false,
        message: 'controlID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        controlID: controlID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by controlID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get attachments by entityID
const getAttachmentsByEntityID = async (req, res) => {
  try {
    const { entityID } = req.params;

    if (!entityID) {
      return res.status(400).json({
        success: false,
        message: 'entityID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        entityID: entityID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by entityID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get attachments by incidentID
const getAttachmentsByIncidentID = async (req, res) => {
  try {
    const { incidentID } = req.params;

    if (!incidentID) {
      return res.status(400).json({
        success: false,
        message: 'incidentID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        incidentID: incidentID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by incidentID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get attachments by organizationalProcessID
const getAttachmentsByOrganizationalProcessID = async (req, res) => {
  try {
    const { organizationalProcessID } = req.params;

    if (!organizationalProcessID) {
      return res.status(400).json({
        success: false,
        message: 'organizationalProcessID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        organizationalProcessID: organizationalProcessID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by organizationalProcessID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get attachments by riskID
const getAttachmentsByRiskID = async (req, res) => {
  try {
    const { riskID } = req.params;

    if (!riskID) {
      return res.status(400).json({
        success: false,
        message: 'riskID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        riskID: riskID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by riskID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

// Get attachments by campagneID
const getAttachmentsByCampagneID = async (req, res) => {
  try {
    const { campagneID } = req.params;

    if (!campagneID) {
      return res.status(400).json({
        success: false,
        message: 'campagneID is required'
      });
    }

    const attachments = await AllAttachment.findAll({
      where: {
        campagneID: campagneID
      },
      order: [['uploadDate', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      message: 'Attachments retrieved successfully',
      data: attachments,
      count: attachments.length
    });
  } catch (error) {
    console.error('Error getting attachments by campagneID:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get attachments'
    });
  }
};

module.exports = {
  uploadFile,
  addReference,
  getAllAttachments,
  getAttachment,
  deleteAttachment,
  downloadAttachment,
  syncAttachments,
  getAttachmentsByAuditActivityID,
  getAttachmentsByAuditConstatID,
  getAttachmentsByAuditFicheDeTravailID,
  getAttachmentsByAuditFicheDeTestID,
  getAttachmentsByAuditMissionID,
  getAttachmentsByAuditScopeID,
  getAttachmentsByActionPlanID,
  getAttachmentsByBusinessProcessID,
  getAttachmentsByControlID,
  getAttachmentsByEntityID,
  getAttachmentsByIncidentID,
  getAttachmentsByOrganizationalProcessID,
  getAttachmentsByRiskID,
  getAttachmentsByCampagneID
};