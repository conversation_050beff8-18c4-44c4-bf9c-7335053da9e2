'use strict';

module.exports = (sequelize, DataTypes) => {
  const EquipeIntervenante = sequelize.define('EquipeIntervenante', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    auditMissionId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'AuditMissions',
        key: 'id'
      }
    },
    auditActivityId: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'AuditActivities',
        key: 'id'
      }
    },
    auditConstatId: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'AuditConstats',
        key: 'id'
      }
    },
    auditRecommendationId: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'AuditRecommendations',
        key: 'id'
      }
    },
    chefdemission: {
      type: DataTypes.ENUM('oui', 'non'),
      allowNull: false,
      defaultValue: 'non',
    }
  }, {
    tableName: 'EquipeIntervenantes',
    timestamps: true,
    indexes: [
      { unique: false, fields: ['userId'] },
      { unique: false, fields: ['auditMissionId'] },
      { unique: false, fields: ['auditActivityId'] },
      { unique: false, fields: ['auditConstatId'] },
      { unique: false, fields: ['auditRecommendationId'] },
      { unique: false, fields: ['chefdemission'] },
      { unique: true, fields: ['auditMissionId', 'chefdemission'], where: { chefdemission: 'oui' } }
    ]
  });

  EquipeIntervenante.associate = function(models) {
    EquipeIntervenante.belongsTo(models.User, { foreignKey: 'userId', as: 'user' });
    EquipeIntervenante.belongsTo(models.AuditMission, { foreignKey: 'auditMissionId', as: 'auditMission' });
    EquipeIntervenante.belongsTo(models.AuditActivity, { foreignKey: 'auditActivityId', as: 'auditActivity' });
    EquipeIntervenante.belongsTo(models.AuditConstat, { foreignKey: 'auditConstatId', as: 'auditConstat' });
    EquipeIntervenante.belongsTo(models.AuditRecommendation, { foreignKey: 'auditRecommendationId', as: 'auditRecommendation' });
  };

  return EquipeIntervenante;
}; 