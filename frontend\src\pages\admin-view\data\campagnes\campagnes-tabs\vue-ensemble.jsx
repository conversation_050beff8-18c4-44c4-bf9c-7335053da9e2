import { useState, useEffect } from "react";
import { useOutletContext } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import {
  Activity,
  Calendar,
  Users,
  FileText,
  Settings,
  Clock,
  CheckCircle,
  AlertCircle,
  User,
  Hash,
  ScrollText
} from "lucide-react";
import controlService from "@/services/controlService";

function VueEnsemble() {
  const { campagne } = useOutletContext();
  const [controlName, setControlName] = useState(null);
  const [loadingControl, setLoadingControl] = useState(false);

  // Fetch control name when campagne data is available
  useEffect(() => {
    const fetchControlName = async () => {
      if (campagne?.controlId) {
        setLoadingControl(true);
        try {
          console.log('🔍 [DEBUG] Fetching control with ID:', campagne.controlId);
          const response = await controlService.getControlById(campagne.controlId);
          console.log('🔍 [DEBUG] Control service response:', response);

          if (response.success && response.data) {
            console.log('🔍 [DEBUG] Control data received:', response.data);
            setControlName(response.data.name);
          } else {
            console.log('🔍 [DEBUG] No control data or unsuccessful response');
            setControlName(null);
          }
        } catch (error) {
          console.error('Error fetching control name:', error);
          setControlName(null);
        } finally {
          setLoadingControl(false);
        }
      } else {
        console.log('🔍 [DEBUG] No controlId found in campagne:', campagne?.controlId);
      }
    };

    fetchControlName();
  }, [campagne?.controlId]);

  if (!campagne) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement des informations de la campagne...</p>
      </div>
    );
  }

  // Helper function to get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'en cours':
        return 'bg-gradient-to-br from-green-500 to-green-600';
      case 'planifié':
        return 'bg-gradient-to-br from-blue-500 to-blue-600';
      case 'terminé':
        return 'bg-gradient-to-br from-gray-500 to-gray-600';
      case 'validé':
        return 'bg-gradient-to-br from-purple-500 to-purple-600';
      default:
        return 'bg-gradient-to-br from-gray-500 to-gray-600';
    }
  };

  // Helper function to get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'en cours':
        return <Activity className="h-5 w-5 text-white" />;
      case 'planifié':
        return <Clock className="h-5 w-5 text-white" />;
      case 'terminé':
        return <CheckCircle className="h-5 w-5 text-white" />;
      case 'validé':
        return <CheckCircle className="h-5 w-5 text-white" />;
      default:
        return <AlertCircle className="h-5 w-5 text-white" />;
    }
  };

  // Helper function to format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-8">
      {/* Prominent Status Card */}
      <div className="flex flex-col md:flex-row gap-3">
        <div className={`rounded-lg shadow-md overflow-hidden w-fit ${getStatusColor(campagne.statut)}`}>
          <div className="p-4">
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0 w-10 h-10 rounded-lg bg-white/20 flex items-center justify-center">
                {getStatusIcon(campagne.statut)}
              </div>
              <div className="text-white">
                <p className="text-sm font-medium opacity-90">Statut de la Campagne</p>
                <p className="text-xl font-bold capitalize">{campagne.statut || 'Non défini'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Participants Count Card */}
        <div className="rounded-lg shadow-md overflow-hidden w-fit bg-gradient-to-br from-indigo-500 to-indigo-600">
          <div className="p-4">
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0 w-10 h-10 rounded-lg bg-white/20 flex items-center justify-center">
                <Users className="h-5 w-5 text-white" />
              </div>
              <div className="text-white">
                <p className="text-sm font-medium opacity-90">Participants Assignés</p>
                <p className="text-xl font-bold">{campagne.assignedUsers?.length || 0}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Basic Information Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-500" />
            Informations de la Campagne
          </h3>
        </div>

        <div className="p-6">
          {/* First Row - Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            <div className="flex items-start space-x-3">
              <div className="bg-green-50 p-2 rounded-full">
                <FileText className="h-5 w-5 text-green-500" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Nom</p>
                <p className="text-base font-semibold text-gray-900">{campagne.name || 'N/A'}</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="bg-blue-50 p-2 rounded-full">
                <Hash className="h-5 w-5 text-blue-500" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Code</p>
                <p className="text-base font-semibold text-gray-900">{campagne.code || 'N/A'}</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="bg-purple-50 p-2 rounded-full">
                <Activity className="h-5 w-5 text-purple-500" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Statut</p>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${
                  campagne.statut === 'en cours' ? 'bg-green-100 text-green-800' :
                  campagne.statut === 'planifié' ? 'bg-blue-100 text-blue-800' :
                  campagne.statut === 'terminé' ? 'bg-gray-100 text-gray-800' :
                  campagne.statut === 'validé' ? 'bg-purple-100 text-purple-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {campagne.statut || 'N/A'}
                </span>
              </div>
            </div>
          </div>

          {/* Second Row - Dates (2/3 filled, 1/3 empty) */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="flex items-start space-x-3">
              <div className="bg-green-50 p-2 rounded-full">
                <Calendar className="h-5 w-5 text-green-500" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Date de Création</p>
                <p className="text-base font-semibold text-gray-900">{formatDate(campagne.createdAt)}</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="bg-orange-50 p-2 rounded-full">
                <Clock className="h-5 w-5 text-orange-500" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Dernière Modification</p>
                <p className="text-base font-semibold text-gray-900">{formatDate(campagne.updatedAt)}</p>
              </div>
            </div>

            {/* Empty third column */}
            <div></div>
          </div>

          {/* Third Row - Control (same design as other fields) */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-start space-x-3">
              <div className="bg-indigo-50 p-2 rounded-full">
                <Settings className="h-5 w-5 text-indigo-500" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Contrôle Associé</p>
                {loadingControl ? (
                  <div className="flex items-center space-x-2 mt-1">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-500"></div>
                    <span className="text-sm text-gray-500">Chargement...</span>
                  </div>
                ) : (
                  <p className="text-base font-semibold text-gray-900">
                    {controlName || 'Nom du contrôle non disponible'}
                  </p>
                )}
              </div>
            </div>

            {/* Empty second and third columns */}
            <div></div>
            <div></div>
          </div>

          {/* Description */}
          {campagne.description && (
            <>
              <div className="border-t border-gray-200 my-6"></div>
              <div className="mt-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <ScrollText className="h-4 w-4 mr-1 text-gray-500" /> Description
                </h4>
                <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">{campagne.description}</p>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Participants Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <Users className="h-5 w-5 mr-2 text-indigo-500" />
            Participants Assignés ({campagne.assignedUsers?.length || 0})
          </h3>
        </div>

        <div className="p-6">
          {campagne.assignedUsers && campagne.assignedUsers.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {campagne.assignedUsers.map((user, index) => (
                <div key={user.id || index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="bg-indigo-100 p-2 rounded-full">
                    <User className="h-4 w-4 text-indigo-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {user.username || user.name || 'Utilisateur'}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {user.email || 'Email non disponible'}
                    </p>
                    {user.UserCampagne?.assignedAt && (
                      <p className="text-xs text-gray-400">
                        Assigné le {formatDate(user.UserCampagne.assignedAt)}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-600 mb-2">
                Aucun participant assigné
              </h4>
              <p className="text-gray-500">
                Cette campagne n'a pas encore de participants assignés.
              </p>
            </div>
          )}
        </div>
      </div>


    </div>
  );
}

export default VueEnsemble;