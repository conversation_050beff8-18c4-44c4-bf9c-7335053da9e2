-- Create the RiskAttachment table
CREATE TABLE IF NOT EXISTS "RiskAttachment" (
  "attachmentID" VARCHAR(255) PRIMARY KEY,
  "fileName" VARCHAR(255) NOT NULL,
  "fileSize" INTEGER NOT NULL,
  "fileType" VARCHAR(255) NOT NULL,
  "filePath" VARCHAR(255) NOT NULL,
  "uploadDate" TIMESTAMP NOT NULL,
  "type" VARCHAR(20) NOT NULL CHECK ("type" IN ('business-document', 'external-reference')),
  "riskID" VARCHAR(255) NOT NULL REFERENCES "Risk"("riskID") ON DELETE CASCADE
);
