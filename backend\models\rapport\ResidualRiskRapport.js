const { sequelize } = require('../index');

const getResidualRiskRapport = async () => {
    const query = `
        SELECT 
            CASE 
                WHEN "inherentRisk" <= 4 THEN 1  -- Très bas
                WHEN "inherentRisk" <= 9 THEN 2  -- <PERSON><PERSON>
                WHEN "inherentRisk" <= 15 THEN 3 -- <PERSON><PERSON><PERSON>
                WHEN "inherentRisk" <= 20 THEN 4 -- <PERSON><PERSON>é
                ELSE 5                           -- Très élevé
            END as "inherentRiskLevel",
            "DMR",
            COUNT(*) as count
        FROM 
            (SELECT 
                "impact" * "probability" as "inherentRisk",
                "DMR"
            FROM "Risk"
            WHERE "impact" IS NOT NULL 
            AND "probability" IS NOT NULL 
            AND "DMR" IN (1, 4, 9, 16, 25)) as subquery
        GROUP BY 
            "inherentRiskLevel", "DMR"
        ORDER BY 
            "inherentRiskLevel" DESC, "DMR" ASC;
    `;
    
    try {
        const results = await sequelize.query(query, { 
            type: sequelize.QueryTypes.SELECT 
        });
        
        const dmrValues = [1, 4, 9, 16, 25];
        const matrix = Array(5).fill().map(() => Array(5).fill(0));
        
        results.forEach(result => {
            if (result.inherentRiskLevel && result.DMR) {
                const rowIndex = 5 - result.inherentRiskLevel; // 0 for Très élevé, 4 for Très bas
                const colIndex = dmrValues.indexOf(result.DMR);
                if (colIndex !== -1) {
                    matrix[rowIndex][colIndex] = result.count;
                }
            }
        });
        
        return matrix;
    } catch (error) {
        console.error('Error executing ResidualRiskRapport query:', error);
        throw error;
    }
};

const getRisksByResidualLevel = async (inherentRiskLevel, DMR) => {
    const query = `
        SELECT 
            "riskID",
            "name",
            "comment" as "description",
            "probability",
            "impact"
        FROM "Risk"
        WHERE "impact" IS NOT NULL 
        AND "probability" IS NOT NULL 
        AND "DMR" = :DMR
        AND CASE 
                WHEN ("impact" * "probability") <= 4 THEN 1
                WHEN ("impact" * "probability") <= 9 THEN 2
                WHEN ("impact" * "probability") <= 15 THEN 3
                WHEN ("impact" * "probability") <= 20 THEN 4
                ELSE 5
            END = :inherentRiskLevel;
    `;
    
    try {
        const risks = await sequelize.query(query, {
            replacements: { inherentRiskLevel, DMR },
            type: sequelize.QueryTypes.SELECT
        });
        return risks;
    } catch (error) {
        console.error('Error fetching risks by residual level:', error);
        throw error;
    }
};

module.exports = { getResidualRiskRapport, getRisksByResidualLevel };