module.exports = (sequelize, DataTypes) => {
  const ControlActivityLog = sequelize.define('ControlActivityLog', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    control_id: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Control',
        key: 'controlID'
      }
    },
    timestamp: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    user: {
      type: DataTypes.STRING,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('creation', 'update', 'deletion', 'link', 'unlink'),
      allowNull: false
    },
    field: {
      type: DataTypes.STRING,
      allowNull: true
    },
    old_value: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    new_value: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    details: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'ControlActivityLogs',
    timestamps: false,
    underscored: false, // Keep field names as defined
    indexes: [
      {
        fields: ['control_id']
      },
      {
        fields: ['timestamp']
      },
      {
        fields: ['control_id', 'timestamp']
      }
    ]
  });

  ControlActivityLog.associate = (models) => {
    ControlActivityLog.belongsTo(models.Control, {
      foreignKey: 'control_id',
      targetKey: 'controlID',
      as: 'control'
    });
  };

  return ControlActivityLog;
};
