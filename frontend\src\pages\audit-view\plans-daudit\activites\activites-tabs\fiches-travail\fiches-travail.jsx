import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Edit, Trash2, Plus, ChevronUp, ChevronDown, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useNavigate, useParams, useMatch, useOutletContext } from "react-router-dom";
import {
  getFichesDeTravailByActivityId,
  createFicheDeTravail,
  updateFicheDeTravail,
  deleteFicheDeTravail
} from "@/services/fiche-de-travail-service";

// Import fiche-travail icon
import ficheTravailIcon from '@/assets/fiche-travail.png';

function useSafeOutletContext() {
  try {
    return useOutletContext();
  } catch {
    return undefined;
  }
}

function FichesTravailTab() {
  const [isFicheOpen, setIsFicheOpen] = useState(true);
  const [ficheItems, setFicheItems] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFicheDialogOpen, setIsFicheDialogOpen] = useState(false);
  const [newFicheNom, setNewFicheNom] = useState("");
  const [editingFicheItem, setEditingFicheItem] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deletingId, setDeletingId] = useState(null);
  const navigate = useNavigate();
  const params = useParams();
  const match = useMatch("/audit/plans-daudit/:planId/missions-audits/:missionAuditId/activites/:activiteId/fiches-travail");
  const parentContext = useSafeOutletContext();
  const missionAuditFromContext = parentContext?.missionAudit;
  const effectivePlanId = match?.params?.planId || missionAuditFromContext?.planId;
  const missionAuditId = match?.params?.missionAuditId || params.missionAuditId;
  const activiteId = match?.params?.activiteId || params.activiteId;
  const abortControllerRef = useRef(null);

  // Fetch fiches de travail when component mounts or activityId changes
  useEffect(() => {
    console.log('useEffect activiteId:', activiteId);
    if (activiteId) {
      fetchFichesDeTravail();
    }
  }, [activiteId]);

  const fetchFichesDeTravail = async () => {
    if (!activiteId) return;

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new AbortController
    abortControllerRef.current = new AbortController();

    setIsLoading(true);
    try {
      const response = await getFichesDeTravailByActivityId(activiteId, abortControllerRef.current.signal);
      if (response && response.success) {
        setFicheItems(response.data || []);
      } else {
        setFicheItems([]);
      }
    } catch (error) {
      console.error('Error fetching fiches de travail:', error);
      toast.error('Erreur lors du chargement des fiches de travail');
      setFicheItems([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddFiche = async () => {
    if (!newFicheNom.trim()) {
      toast.error("Veuillez saisir le nom de la fiche de travail");
      return;
    }

    if (!activiteId || !missionAuditId) {
      toast.error("ID d'activité ou de mission manquant");
      return;
    }

    setIsSubmitting(true);
    try {
      const ficheData = {
        name: newFicheNom.trim(),
        auditActivityID: activiteId,
        auditMissionID: missionAuditId
      };

      let response;
      if (editingFicheItem) {
        response = await updateFicheDeTravail(editingFicheItem.id, ficheData);
      } else {
        response = await createFicheDeTravail(ficheData);
      }

      if (response && response.success) {
        toast.success(editingFicheItem ? "Fiche de travail mise à jour" : "Fiche de travail ajoutée");
        await fetchFichesDeTravail(); // Refresh the list
        setNewFicheNom("");
        setEditingFicheItem(null);
        setIsFicheDialogOpen(false);
      } else {
        throw new Error(response?.message || 'Erreur lors de la sauvegarde');
      }
    } catch (error) {
      console.error('Error saving fiche de travail:', error);
      toast.error(error.message || 'Erreur lors de la sauvegarde de la fiche de travail');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditFiche = (item) => {
    setEditingFicheItem(item);
    setNewFicheNom(item.name);
    setIsFicheDialogOpen(true);
  };

  const handleDeleteFiche = async (id) => {
    if (!id) return;

    setIsDeleting(true);
    setDeletingId(id);
    try {
      const response = await deleteFicheDeTravail(id);
      if (response && response.success) {
        toast.success("Fiche de travail supprimée");
        await fetchFichesDeTravail(); // Refresh the list
      } else {
        throw new Error(response?.message || 'Erreur lors de la suppression');
      }
    } catch (error) {
      console.error('Error deleting fiche de travail:', error);
      toast.error(error.message || 'Erreur lors de la suppression de la fiche de travail');
    } finally {
      setIsDeleting(false);
      setDeletingId(null);
    }
  };

  const handleDialogClose = () => {
    setIsFicheDialogOpen(false);
    setEditingFicheItem(null);
    setNewFicheNom("");
    setIsSubmitting(false);
  };

  return (
    <div className="border rounded-lg shadow-sm">
      <button
        type="button"
        className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-t-lg"
        onClick={() => setIsFicheOpen(!isFicheOpen)}
      >
        <div className="flex items-center gap-2">
          {isFicheOpen ? (
            <ChevronUp className="h-5 w-5 text-blue-600" />
          ) : (
            <ChevronDown className="h-5 w-5 text-blue-600" />
          )}
          <span className="text-lg font-medium text-blue-800">Documents de travail</span>
        </div>
      </button>
      {isFicheOpen && (
        <div className="p-5 bg-white">
          <div className="mb-4 flex justify-end">
            <Button 
              className="bg-[#F62D51] hover:bg-[#F62D51]/90" 
              onClick={() => {
                setEditingFicheItem(null);
                setNewFicheNom("");
                setIsFicheDialogOpen(true);
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Nouveau document
            </Button>
          </div>
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table className="min-w-full">
                  <TableHeader>
                    <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                      <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
                      <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody className="divide-y divide-gray-200">
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={2} className="px-4 py-10 text-center">
                          <div className="flex items-center justify-center">
                            <Loader2 className="h-6 w-6 animate-spin text-[#F62D51]" />
                            <span className="ml-2 text-sm text-gray-500">Chargement...</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : ficheItems.length > 0 ? (
                      ficheItems.map((item) => (
                        <TableRow
                          key={item.id}
                          className="hover:bg-gray-50/50 cursor-pointer"
                          onClick={() => {
                            if (effectivePlanId && missionAuditId && item.id) {
                              navigate(`/audit/plans-daudit/${effectivePlanId}/missions-audits/${missionAuditId}/fiches-travail/${item.id}`);
                            } else if (missionAuditId && activiteId && item.id) {
                              navigate(`/audit/missions-audits/${missionAuditId}/activites/${activiteId}/fiches-travail/${item.id}`);
                            } else {
                              toast.error("Impossible d'ouvrir la fiche : planId manquant (navigation non supportée sans planId)");
                            }
                          }}
                        >
                          <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                            <div className="flex items-center">
                              <img src={ficheTravailIcon} alt="Fiche de travail" className="h-4 w-4 mr-2 flex-shrink-0" />
                              <span>{item.name}</span>
                            </div>
                          </TableCell>
                          <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                            <div className="flex justify-end gap-2">
                              <Button 
                                size="sm" 
                                variant="ghost" 
                                className="h-8 w-8 p-0" 
                                onClick={e => { 
                                  e.stopPropagation(); 
                                  handleEditFiche(item); 
                                }}
                              >
                                <Edit className="h-4 w-4 text-blue-600" />
                              </Button>
                              <Button 
                                size="sm" 
                                variant="ghost" 
                                className="h-8 w-8 p-0" 
                                onClick={e => { 
                                  e.stopPropagation(); 
                                  handleDeleteFiche(item.id); 
                                }}
                                disabled={isDeleting && deletingId === item.id}
                              >
                                {isDeleting && deletingId === item.id ? (
                                  <Loader2 className="h-4 w-4 animate-spin text-red-600" />
                                ) : (
                                  <Trash2 className="h-4 w-4 text-red-600" />
                                )}
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={2} className="px-4 py-10 text-center text-gray-500">
                          Aucun document de travail trouvé.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      
      {/* Fiche Dialog */}
      <Dialog open={isFicheDialogOpen} onOpenChange={handleDialogClose}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editingFicheItem ? "Modifier le document" : "Nouveau document"}
            </DialogTitle>
            <DialogDescription>
              {editingFicheItem 
                ? "Modifiez les informations du document" 
                : "Ajoutez un nouveau document à l'activité"
              }
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="fiche-nom">Nom du document *</Label>
              <Input 
                id="fiche-nom" 
                name="nom" 
                value={newFicheNom} 
                onChange={e => setNewFicheNom(e.target.value)} 
                placeholder="Nom du document"
                disabled={isSubmitting}
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={handleDialogClose}
              disabled={isSubmitting}
            >
              Annuler
            </Button>
            <Button 
              className="bg-[#F62D51] hover:bg-[#F62D51]/90" 
              onClick={handleAddFiche}
              disabled={isSubmitting || !newFicheNom.trim()}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {editingFicheItem ? "Mise à jour..." : "Ajout en cours..."}
                </>
              ) : (
                editingFicheItem ? "Mettre à jour" : "Ajouter"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default FichesTravailTab;
