import { Activity, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON><PERSON><PERSON>, Clock, Search, ShieldAlert, Skull, TrendingUp, Loader2, ExternalLink } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend, <PERSON><PERSON><PERSON>, Pie, Cell, <PERSON>Chart as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import axios from 'axios';
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from 'react-i18next';
// Mock data from mock-data.js (translated to English)
const riskIncidentData = [
  {
    "Risk Code": "R001",
    "Risk": "External Fraud",
    "Net Risk Level": "High",
    "Nb of Incidents": 12,
    "Gross Loss": "€45,000",
    "Recoveries": "€15,000",
    "Net Loss": "€30,000"
  },
  {
    "Risk Code": "R002",
    "Risk": "Internal Fraud",
    "Net Risk Level": "Medium",
    "Nb of Incidents": 5,
    "Gross Loss": "€25,000",
    "Recoveries": "€10,000",
    "Net Loss": "€15,000"
  },
  {
    "Risk Code": "R003",
    "Risk": "System Failure",
    "Net Risk Level": "Low",
    "Nb of Incidents": 8,
    "Gross Loss": "€12,000",
    "Recoveries": "€2,000",
    "Net Loss": "€10,000"
  },
  {
    "Risk Code": "R004",
    "Risk": "Execution Error",
    "Net Risk Level": "Very High",
    "Nb of Incidents": 20,
    "Gross Loss": "€80,000",
    "Recoveries": "€20,000",
    "Net Loss": "€60,000"
  },
  {
    "Risk Code": "R005",
    "Risk": "Regulatory Risk",
    "Net Risk Level": "High",
    "Nb of Incidents": 7,
    "Gross Loss": "€50,000",
    "Recoveries": "€5,000",
    "Net Loss": "€45,000"
  }
];
const API_BASE_URL = getApiBaseUrl();
// Existing filler data (kept for remaining charts)
const incidentsOverTimeData = [
  { month: 'Jan', count: 5 },
  { month: 'Feb', count: 3 },
  { month: 'Mar', count: 7 },
  { month: 'Apr', count: 4 },
];

const incidentsByTypeData = [
  { name: 'Security Breach', value: 30 },
  { name: 'Human Error', value: 20 },
  { name: 'Network Outage', value: 25 },
  { name: 'Environmental', value: 15 },
  { name: 'Data Loss updates ', value: 10 },
];

const lossesOverTimeData = [
  { month: 'Jan', loss: 10000 },
  { month: 'Feb', loss: 12000 },
  { month: 'Mar', loss: 8000 },
  { month: 'Apr', loss: 15000 },
];

const lossesByTypeData = [
  { type: 'Security Breach', loss: 5000 },
  { type: 'Environmental', loss: 7000 },
  { type: 'Human Error', loss: 3000 },
  { type: 'Data Loss updates', loss: 4000 },
  { type: 'Network Outage', loss: 2000 },
];

// Chart Components
const DonutChartComponent = ({ data, title }) => {
  const { t } = useTranslation();
  return (
    <div className="bg-white p-6 rounded-xl shadow-sm">
      <h2 className="text-lg font-semibold mb-4">{title}</h2>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              innerRadius={70}
              outerRadius={90}
              paddingAngle={5}
              dataKey="value"
              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={['#3B82F6', '#F59E0B', '#10B981', '#8B5CF6', '#EC4899'][index % 5]} />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

const BarChartComponent = ({ data, title, dataKey, labelKey }) => {
  const { t } = useTranslation();
  return (
    <div className="bg-white p-6 rounded-xl shadow-sm">
      <h2 className="text-lg font-semibold mb-4">{title}</h2>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <ReBarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={labelKey} />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey={dataKey} fill="#3B82F6" />
          </ReBarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

const LineChartComponent = ({ data, title, dataKey }) => {
  const { t } = useTranslation();
  return (
    <div className="bg-white p-6 rounded-xl shadow-sm">
      <h2 className="text-lg font-semibold mb-4">{title}</h2>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey={dataKey} stroke="#3B82F6" />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

const RiskIncidentBarChart = ({ data }) => {
  const { t } = useTranslation();
  return (
    <div className="bg-white p-6 rounded-xl shadow-sm">
      <h2 className="text-lg font-semibold mb-4">{t('admin.dashboard.charts.incidents_overview', 'Incidents Overview')}</h2>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <ReBarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="Risk" />
            <YAxis yAxisId="left" />
            <YAxis yAxisId="right" orientation="right" />
            <Tooltip />
            <Legend />
            <Bar yAxisId="left" dataKey="Nb of Incidents" fill="#3B82F6" />
            <Bar yAxisId="right" dataKey="Net Loss" fill="#F59E0B" />
          </ReBarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

function AdminDashboard() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalRisks, setTotalRisks] = useState(0);
  const [totalIncidents, setTotalIncidents] = useState(0);
  const [highPriorityItems, setHighPriorityItems] = useState(0);
  const [actionPlans, setActionPlans] = useState(0);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);
  const [recentItems, setRecentItems] = useState([]);
  const [statusFilter, setStatusFilter] = useState('All');
  const [priorityFilter, setPriorityFilter] = useState('All');

  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      let risks = [];
      let incidents = [];

      try {
        const risksResponse = await axios.get(`${API_BASE_URL}/risk`, { withCredentials: true });
        risks = risksResponse.data.data || [];
      } catch (riskErr) {
        console.error('Error fetching risks:', riskErr);
      }

      try {
        const incidentsResponse = await axios.get(`${API_BASE_URL}/incidents`, { withCredentials: true });
        incidents = incidentsResponse.data.data || [];
      } catch (incidentErr) {
        console.error('Error fetching incidents:', incidentErr);
      }

      if (risks.length === 0 && incidents.length === 0) {
        throw new Error('Failed to fetch both risks and incidents data');
      }

      setTotalRisks(risks.length);
      setTotalIncidents(incidents.length);

      const highPriorityCount = [
        ...risks.filter(risk => risk.targetRisk === 'High' || risk.targetRisk === 'Very High'),
        ...incidents.filter(incident => incident.priority === 'High')
      ].length;
      setHighPriorityItems(highPriorityCount);

      let actionPlansData = [];
      try {
        const actionPlansResponse = await axios.get(`${API_BASE_URL}/actionPlans`, { withCredentials: true });
        actionPlansData = actionPlansResponse.data.data || [];
        setActionPlans(actionPlansData.length);
      } catch (actionPlanErr) {
        console.error('Error fetching action plans:', actionPlanErr);
      }

      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const recentItemsList = [
        ...risks,
        ...incidents
      ].filter(item => {
        const updatedAt = new Date(item.updatedAt || item.createdAt);
        return updatedAt >= sevenDaysAgo;
      });

      setRecentItems(recentItemsList.sort((a, b) => {
        try {
          const dateA = new Date(a.updatedAt || a.createdAt || 0);
          const dateB = new Date(b.updatedAt || b.createdAt || 0);
          if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) return 0;
          return dateB - dateA;
        } catch (error) {
          console.log('Error sorting dates:', error);
          return 0;
        }
      }).slice(0, 10));

      if (recentItemsList.length > 0) {
        try {
          const mostRecent = recentItemsList.reduce((latest, item) => {
            try {
              if (!item.updatedAt && !item.createdAt) return latest;
              const itemDate = new Date(item.updatedAt || item.createdAt);
              if (isNaN(itemDate.getTime())) return latest;
              return itemDate > latest ? itemDate : latest;
            } catch (error) {
              console.log('Error processing item date:', error);
              return latest;
            }
          }, new Date(0));

          if (mostRecent && mostRecent.getTime() > 0) {
            setLastUpdateTime(mostRecent);
          }
        } catch (error) {
          console.log('Error finding most recent update:', error);
        }
      }

      const safeFormatDate = (dateString) => {
        if (!dateString) return 'N/A';
        try {
          const date = new Date(dateString);
          if (isNaN(date.getTime())) return 'N/A';
          return date.toISOString().split('T')[0];
        } catch (error) {
          console.log('Invalid date:', dateString, error);
          return 'N/A';
        }
      };

      const safeGetTimeAgo = (dateString) => {
        if (!dateString) return 'N/A';
        try {
          const date = new Date(dateString);
          if (isNaN(date.getTime())) return 'N/A';
          return getTimeAgo(date);
        } catch (error) {
          console.log('Invalid date for time ago:', dateString, error);
          return 'N/A';
        }
      };

      const combinedItems = [
        ...risks.map(risk => ({
          ...risk,
          type: 'Risk',
          title: risk.name || 'Unnamed Risk',
          reportedDate: safeFormatDate(risk.createdAt),
          lastUpdated: safeGetTimeAgo(risk.updatedAt || risk.createdAt)
        })),
        ...incidents.map(incident => ({
          ...incident,
          type: 'Incident',
          title: incident.name || 'Unnamed Incident',
          reportedDate: safeFormatDate(incident.createdAt),
          lastUpdated: safeGetTimeAgo(incident.updatedAt || incident.createdAt)
        }))
      ].sort((a, b) => {
        try {
          const dateA = new Date(a.updatedAt || a.createdAt || 0);
          const dateB = new Date(b.updatedAt || b.createdAt || 0);
          if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) return 0;
          return dateB - dateA;
        } catch (error) {
          console.log('Error sorting dates:', error);
          return 0;
        }
      }).slice(0, 10);

      setRecentItems(combinedItems);

      setError(null);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const getTimeAgo = (date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds}s ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return `${Math.floor(diffInSeconds / 604800)}w ago`;
  };

  // Prepare data for charts
  const riskIncidentBarData = riskIncidentData.map(item => ({
    ...item,
    'Net Loss': parseFloat(item['Net Loss'].replace('€', '').replace(',', '')),
  }));

  return (
    <div className="p-6 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-semibold text-[#242A33] pt-5 pl-5">{t('admin.dashboard.title', 'Dashboard')}</h1>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 bg-gray-50 px-4 py-2 rounded-lg">
            <Search className="text-[#555F6D]" size={20} />
            <input
              type="text"
              placeholder={t('common.buttons.search', 'Search risks...')}
              className="bg-transparent outline-none"
            />
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
            <span className="mt-4 text-gray-600">{t('admin.dashboard.loading', 'Loading dashboard data...')}</span>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-50 p-4 rounded-xl text-red-800 text-center">
          {error}
          <button
            className="ml-4 bg-red-100 px-3 py-1 rounded-md hover:bg-red-200"
            onClick={() => window.location.reload()}
          >
            {t('common.buttons.retry', 'Retry')}
          </button>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-[#555F6D]">{t('admin.dashboard.total_risks', 'Total Risks')}</p>
                  <p className="text-2xl font-bold mt-2">{totalRisks}</p>
                </div>
                <div className="bg-red-100 p-3 rounded-full">
                  <ShieldAlert className="text-red-500" size={24} />
                </div>
              </div>
              <div className="flex items-center gap-2 mt-4 text-sm">
                <TrendingUp className="text-green-500" size={16} />
                <span className="text-[#555F6D]">{t('admin.dashboard.active_risk_monitoring', 'Active risk monitoring')}</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-[#555F6D]">{t('admin.dashboard.total_incidents', 'Total Incidents')}</p>
                  <p className="text-2xl font-bold mt-2">{totalIncidents}</p>
                </div>
                <div className="bg-orange-100 p-3 rounded-full">
                  <AlertTriangle className="text-orange-500" size={24} />
                </div>
              </div>
              <div className="flex items-center gap-2 mt-4 text-sm">
                <Clock className="text-blue-500" size={16} />
                <span className="text-[#555F6D]">{t('admin.dashboard.incident_management', 'Incident management')}</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-[#555F6D]">{t('admin.dashboard.high_priority', 'High Priority')}</p>
                  <p className="text-2xl font-bold mt-2">{highPriorityItems}</p>
                </div>
                <div className="bg-purple-100 p-3 rounded-full">
                  <Skull className="text-purple-500" size={24} />
                </div>
              </div>
              <div className="flex items-center gap-2 mt-4 text-sm">
                <Activity className="text-red-500" size={16} />
                <span className="text-red-500">{t('admin.dashboard.critical_attention', 'Critical attention needed')}</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-[#555F6D]">{t('admin.dashboard.action_plans', 'Action Plans')}</p>
                  <p className="text-2xl font-bold mt-2">{actionPlans}</p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <BarChart className="text-blue-500" size={24} />
                </div>
              </div>
              <div className="flex items-center gap-2 mt-4 text-sm">
                <Clock className="text-[#555F6D]" size={16} />
                <span className="text-[#555F6D]">
                  {lastUpdateTime
                    ? t('admin.dashboard.last_update', 'Last update {{time}}', { time: getTimeAgo(lastUpdateTime) })
                    : t('admin.dashboard.no_updates', 'No recent updates')}
                </span>
              </div>
            </div>
          </div>

          {/* Updated Charts Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4 mb-4">
            <RiskIncidentBarChart data={riskIncidentBarData} />
            <LineChartComponent
              data={incidentsOverTimeData}
              title={t('admin.dashboard.charts.incidents_over_time', 'Number of Incidents Over Time')}
              dataKey="count"
            />
            <DonutChartComponent
              data={incidentsByTypeData}
              title={t('admin.dashboard.charts.incidents_by_type', 'Incidents by Incident Type')}
            />
            <LineChartComponent
              data={lossesOverTimeData}
              title={t('admin.dashboard.charts.losses_over_time', 'Losses Over Time')}
              dataKey="loss"
            />
            <BarChartComponent
              data={lossesByTypeData}
              title={t('admin.dashboard.charts.losses_by_type', 'Losses by Incident Type')}
              dataKey="loss"
              labelKey="type"
            />
            <BarChartComponent
              data={riskIncidentBarData}
              title={t('admin.dashboard.charts.net_loss_by_risk', 'Net Loss by Risk Type')}
              dataKey="Net Loss"
              labelKey="Risk"
            />
          </div>

          {/* Recent Risks & Incidents Table */}
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-lg font-semibold">{t('admin.dashboard.recent_items', 'Recent Risks & Incidents')}</h2>
              <div className="flex items-center gap-4">
                <select
                  className="bg-gray-50 px-3 py-2 rounded-lg text-sm"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="All">{t('common.status.all', 'All Status')}</option>
                  <option value="Open">{t('common.status.open', 'Open')}</option>
                  <option value="In Progress">{t('common.status.in_progress', 'In Progress')}</option>
                  <option value="Resolved">{t('common.status.resolved', 'Resolved')}</option>
                </select>
                <select
                  className="bg-gray-50 px-3 py-2 rounded-lg text-sm"
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                >
                  <option value="All">{t('common.priority.all', 'All Priorities')}</option>
                  <option value="High">{t('common.priority.high', 'High')}</option>
                  <option value="Medium">{t('common.priority.medium', 'Medium')}</option>
                  <option value="Low">{t('common.priority.low', 'Low')}</option>
                </select>
              </div>
            </div>

            {recentItems.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                {t('admin.dashboard.no_recent_items', 'No recent risks or incidents found.')}
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">{t('common.fields.title', 'Title')}</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">{t('common.fields.type', 'Type')}</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">{t('common.fields.status', 'Status')}</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">{t('common.fields.priority', 'Priority')}</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">{t('common.fields.reported_date', 'Reported Date')}</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">{t('common.fields.last_updated', 'Last Updated')}</th>
                        <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">{t('common.fields.actions', 'Actions')}</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {recentItems
                        .filter(item => statusFilter === 'All' || item.status === statusFilter)
                        .filter(item => priorityFilter === 'All' || item.priority === priorityFilter)
                        .map((item) => (
                          <tr key={`${item.type}-${item.id || item.riskID || item.incidentID}`}>
                            <td className="px-6 py-4 text-sm font-medium text-[#242A33]">{item.title}</td>
                            <td className="px-6 py-4">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${item.type === 'Risk' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}`}>
                                {item.type}
                              </span>
                            </td>
                            <td className="px-6 py-4">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                item.status === 'Open' ? 'bg-blue-100 text-blue-800' :
                                item.status === 'In Progress' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-green-100 text-green-800'
                              }`}>
                                {item.status}
                              </span>
                            </td>
                            <td className="px-6 py-4">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                item.priority === 'High' ? 'bg-red-100 text-red-800' :
                                item.priority === 'Medium' ? 'bg-orange-100 text-orange-800' :
                                'bg-green-100 text-green-800'
                              }`}>
                                {item.priority}
                              </span>
                            </td>
                            <td className="px-6 py-4 text-sm text-[#555F6D]">{item.reportedDate}</td>
                            <td className="px-6 py-4 text-sm text-[#555F6D]">{item.lastUpdated}</td>
                            <td className="px-6 py-4">
                              <button
                                className="text-[#F62D51] hover:text-red-700 text-sm font-medium flex items-center gap-1"
                                onClick={() => {
                                  if (item.type === 'Risk') {
                                    navigate(`/admin/risks/edit/${item.riskID}`);
                                  } else {
                                    navigate(`/admin/incident/edit/${item.incidentID}`);
                                  }
                                }}
                              >
                                {t('common.buttons.view', 'View')} <ExternalLink className="h-3 w-3" />
                              </button>
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>

                <div className="flex items-center justify-between px-6 py-4 border-t">
                  <span className="text-sm text-[#555F6D]">
                    {t('admin.dashboard.showing_items', 'Showing {{count}} recent items', { count: recentItems.length })}
                  </span>
                </div>
              </>
            )}
          </div>
        </>
      )}
    </div>
  );
}

export default AdminDashboard;