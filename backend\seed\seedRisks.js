const { Risk } = require('../models');

const riskLevels = ["Very Low", "Low", "Medium", "High", "Very High"];

// Helper function to get random boolean
const getRandomBoolean = () => Math.random() < 0.5;

async function seedRisks() {
  try {
    const risks = [];

    for (let i = 0; i < 40; i++) {
      const risk = {
        riskID: `RISK-${String(i + 1).padStart(3, '0')}`,
        name: `Risk ${i + 1}`,
        code: `R${String(i + 1).padStart(3, '0')}`,
        // Remove targetRisk field
        accept: getRandomBoolean(),
        avoid: getRandomBoolean(),
        insurance: getRandomBoolean(),
        reduction: getRandomBoolean(),
        comment: `This is a test risk entry ${i + 1}. This risk was automatically generated for testing purposes.`
      };

      risks.push(risk);
    }

    await Risk.bulkCreate(risks);
    console.log('Successfully seeded 40 risks');
  } catch (error) {
    console.error('Error seeding risks:', error);
  }
}

// Run the seeding function if this file is executed directly
if (require.main === module) {
  seedRisks();
}

module.exports = seedRisks;
