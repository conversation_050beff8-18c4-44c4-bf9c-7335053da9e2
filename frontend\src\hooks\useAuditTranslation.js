import { useTranslation } from 'react-i18next';
import { translateText } from '../utils/translateText';

/**
 * Custom hook for audit-specific translations
 * @returns {Object} - Translation utilities
 */
const useAuditTranslation = () => {
  const { t, i18n } = useTranslation();

  /**
   * Translate text using the audit mapping
   * @param {string} text - The text to translate (usually English)
   * @returns {string} - The translated text
   */
  const translateAudit = (text) => {
    return translateText(text, t);
  };

  /**
   * Get a translation directly using a key
   * @param {string} key - The translation key
   * @param {Object} options - Options for the translation
   * @returns {string} - The translated text
   */
  const tAudit = (key, options) => {
    return t(`audit.${key}`, options);
  };

  return {
    t,
    i18n,
    translateAudit,
    tAudit
  };
};

export default useAuditTranslation;
