const { AuditSkill, AuditorSkill, User } = require('../../models');
const { Op, sequelize } = require('sequelize');

// Get all audit skills
const getAllAuditSkills = async (req, res) => {
  try {
    console.log('[AUDIT_SKILLS] Getting all audit skills');
    
    const skills = await AuditSkill.findAll({
      where: { isActive: true },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: AuditorSkill,
          as: 'auditorSkills',
          where: { isActive: true },
          required: false,
          include: [
            {
              model: User,
              as: 'auditor',
              attributes: ['id', 'username', 'email']
            }
          ]
        }
      ],
      order: [['name', 'ASC']]
    });

    console.log(`[AUDIT_SKILLS] Found ${skills.length} skills`);
    
    res.status(200).json({
      success: true,
      data: skills
    });
  } catch (error) {
    console.error('[AUDIT_SKILLS] Error getting skills:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des compétences'
    });
  }
};

// Get audit skill by ID
const getAuditSkillById = async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`[AUDIT_SKILLS] Getting skill with ID: ${id}`);
    
    const skill = await AuditSkill.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: AuditorSkill,
          as: 'auditorSkills',
          where: { isActive: true },
          required: false,
          include: [
            {
              model: User,
              as: 'auditor',
              attributes: ['id', 'username', 'email']
            },
            {
              model: User,
              as: 'rater',
              attributes: ['id', 'username', 'email']
            }
          ]
        }
      ]
    });

    if (!skill) {
      return res.status(404).json({
        success: false,
        message: 'Compétence non trouvée'
      });
    }

    console.log(`[AUDIT_SKILLS] Found skill: ${skill.name}`);
    
    res.status(200).json({
      success: true,
      data: skill
    });
  } catch (error) {
    console.error('[AUDIT_SKILLS] Error getting skill:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la compétence'
    });
  }
};

// Create new audit skill
const createAuditSkill = async (req, res) => {
  try {
    const { name, description } = req.body;
    console.log('[AUDIT_SKILLS] Creating new skill:', { name });
    
    // Check if skill name already exists
    const existingSkill = await AuditSkill.findOne({
      where: { 
        name: { [Op.iLike]: name.trim() },
        isActive: true 
      }
    });

    if (existingSkill) {
      return res.status(400).json({
        success: false,
        message: 'Une compétence avec ce nom existe déjà'
      });
    }

    const skill = await AuditSkill.create({
      name: name.trim(),
      description: description?.trim() || null,
      createdBy: req.user.userId
    });

    console.log(`[AUDIT_SKILLS] Created skill with ID: ${skill.id}`);
    
    res.status(201).json({
      success: true,
      data: skill,
      message: 'Compétence créée avec succès'
    });
  } catch (error) {
    console.error('[AUDIT_SKILLS] Error creating skill:', error);
    
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({
        success: false,
        message: 'Une compétence avec ce nom existe déjà'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la compétence'
    });
  }
};

// Update audit skill
const updateAuditSkill = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;
    console.log(`[AUDIT_SKILLS] Updating skill with ID: ${id}`);
    
    const skill = await AuditSkill.findByPk(id);
    
    if (!skill) {
      return res.status(404).json({
        success: false,
        message: 'Compétence non trouvée'
      });
    }

    // Check if new name conflicts with existing skill (excluding current skill)
    if (name && name.trim() !== skill.name) {
      const existingSkill = await AuditSkill.findOne({
        where: { 
          name: { [Op.iLike]: name.trim() },
          id: { [Op.ne]: id },
          isActive: true 
        }
      });

      if (existingSkill) {
        return res.status(400).json({
          success: false,
          message: 'Une compétence avec ce nom existe déjà'
        });
      }
    }

    await skill.update({
      name: name?.trim() || skill.name,
      description: description?.trim() || skill.description,
      updatedBy: req.user.userId
    });

    console.log(`[AUDIT_SKILLS] Updated skill: ${skill.name}`);
    
    res.status(200).json({
      success: true,
      data: skill,
      message: 'Compétence mise à jour avec succès'
    });
  } catch (error) {
    console.error('[AUDIT_SKILLS] Error updating skill:', error);
    
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({
        success: false,
        message: 'Une compétence avec ce nom existe déjà'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de la compétence'
    });
  }
};

// Delete audit skill (soft delete)
const deleteAuditSkill = async (req, res) => {
  try {
    const { id } = req.params;
    const { force } = req.query; // Check if force delete is requested
    console.log(`[AUDIT_SKILLS] Deleting skill with ID: ${id}, force: ${force}`);

    const skill = await AuditSkill.findByPk(id);

    if (!skill) {
      return res.status(404).json({
        success: false,
        message: 'Compétence non trouvée'
      });
    }

    // Check if skill is assigned to any auditors
    const assignments = await AuditorSkill.findAll({
      where: {
        skillId: id,
        isActive: true
      },
      include: [
        {
          model: User,
          as: 'auditor',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    const assignedCount = assignments.length;

    if (assignedCount > 0 && force !== 'true') {
      // Return assignment details for confirmation
      return res.status(409).json({
        success: false,
        requiresConfirmation: true,
        assignedCount,
        assignments: assignments.map(a => ({
          id: a.id,
          auditor: a.auditor,
          rating: a.rating
        })),
        message: `Cette compétence est assignée à ${assignedCount} auditeur(s). Voulez-vous supprimer la compétence et toutes ses assignations ?`
      });
    }

    // If force delete or no assignments, proceed with deletion
    if (assignedCount > 0 && force === 'true') {
      // Permanently delete all assignments first
      await AuditorSkill.destroy({
        where: {
          skillId: id,
          isActive: true
        }
      });
      console.log(`[AUDIT_SKILLS] Permanently deleted ${assignedCount} assignments for skill: ${skill.name}`);
    }

    // Permanently delete the skill
    await skill.destroy();

    console.log(`[AUDIT_SKILLS] Permanently deleted skill: ${skill.name}`);

    res.status(200).json({
      success: true,
      message: assignedCount > 0
        ? `Compétence et ${assignedCount} assignation(s) supprimée(s) définitivement`
        : 'Compétence supprimée définitivement'
    });
  } catch (error) {
    console.error('[AUDIT_SKILLS] Error deleting skill:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de la compétence'
    });
  }
};

// Get skills statistics
const getSkillsStatistics = async (req, res) => {
  try {
    console.log('[AUDIT_SKILLS] Getting skills statistics');
    
    const totalSkills = await AuditSkill.count({
      where: { isActive: true }
    });

    const totalAssignments = await AuditorSkill.count({
      where: { isActive: true }
    });

    const averageRating = await AuditorSkill.findOne({
      where: { isActive: true },
      attributes: [
        [sequelize.fn('AVG', sequelize.col('rating')), 'avgRating']
      ],
      raw: true
    });

    const topSkills = await AuditSkill.findAll({
      where: { isActive: true },
      include: [
        {
          model: AuditorSkill,
          as: 'auditorSkills',
          where: { isActive: true },
          required: false
        }
      ],
      order: [
        [sequelize.literal('(SELECT COUNT(*) FROM "AuditorSkills" WHERE "skillId" = "AuditSkill"."id" AND "isActive" = true)'), 'DESC']
      ],
      limit: 5
    });

    const statistics = {
      totalSkills,
      totalAssignments,
      averageRating: averageRating?.avgRating ? parseFloat(averageRating.avgRating).toFixed(2) : 0,
      topSkills: topSkills.map(skill => ({
        id: skill.id,
        name: skill.name,
        assignmentCount: skill.auditorSkills?.length || 0
      }))
    };

    console.log('[AUDIT_SKILLS] Statistics calculated');
    
    res.status(200).json({
      success: true,
      data: statistics
    });
  } catch (error) {
    console.error('[AUDIT_SKILLS] Error getting statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques'
    });
  }
};

module.exports = {
  getAllAuditSkills,
  getAuditSkillById,
  createAuditSkill,
  updateAuditSkill,
  deleteAuditSkill,
  getSkillsStatistics
};
