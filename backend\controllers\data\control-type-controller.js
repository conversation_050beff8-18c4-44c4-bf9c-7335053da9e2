const { ControlType } = require('../../models');

// Get all control types
const getAllControlTypes = async (req, res) => {
  try {
    const controlTypes = await ControlType.findAll();
    res.json({
      success: true,
      data: controlTypes
    });
  } catch (error) {
    console.error('Error fetching control types:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch control types'
    });
  }
};

// Create new control type
const createControlType = async (req, res) => {
  try {
    const {
      controlTypeID,
      name,
      code,
      comment,
      parentControlTypeID
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name'].filter(field => !req.body[field])
      });
    }

    const controlType = await ControlType.create({
      controlTypeID: controlTypeID || `CT_${Date.now()}`,
      name,
      code: code || null,
      comment: comment || null,
      parentControlTypeID: parentControlTypeID || null
    });

    return res.status(201).json({
      success: true,
      message: 'Control type created successfully',
      data: controlType
    });
  } catch (error) {
    console.error('Error creating control type:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create control type'
    });
  }
};

// Get control type by ID
const getControlTypeById = async (req, res) => {
  try {
    const { id } = req.params;
    const controlType = await ControlType.findByPk(id);
    
    if (!controlType) {
      return res.status(404).json({
        success: false,
        message: 'Control type not found'
      });
    }
    
    res.json({
      success: true,
      data: controlType
    });
  } catch (error) {
    console.error('Error fetching control type:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch control type'
    });
  }
};

// Update control type
const updateControlType = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      code,
      comment,
      parentControlTypeID
    } = req.body;

    const controlType = await ControlType.findByPk(id);
    
    if (!controlType) {
      return res.status(404).json({
        success: false,
        message: 'Control type not found'
      });
    }

    // Update fields
    await controlType.update({
      name: name || controlType.name,
      code: code !== undefined ? code : controlType.code,
      comment: comment !== undefined ? comment : controlType.comment,
      parentControlTypeID: parentControlTypeID !== undefined ? parentControlTypeID : controlType.parentControlTypeID
    });

    res.json({
      success: true,
      message: 'Control type updated successfully',
      data: controlType
    });
  } catch (error) {
    console.error('Error updating control type:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update control type'
    });
  }
};

// Delete control type
const deleteControlType = async (req, res) => {
  try {
    const { id } = req.params;
    const controlType = await ControlType.findByPk(id);
    
    if (!controlType) {
      return res.status(404).json({
        success: false,
        message: 'Control type not found'
      });
    }
    
    await controlType.destroy();
    
    res.json({
      success: true,
      message: 'Control type deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting control type:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete control type'
    });
  }
};

module.exports = {
  getAllControlTypes,
  createControlType,
  getControlTypeById,
  updateControlType,
  deleteControlType
};
