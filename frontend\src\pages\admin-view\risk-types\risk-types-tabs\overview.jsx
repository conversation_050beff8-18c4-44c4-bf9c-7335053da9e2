import { useOutletContext } from "react-router-dom";
import { Tag, Calendar, Hash, FileText, AlertTriangle } from "lucide-react";
import { useEffect, useState } from "react";

function RiskTypesOverview() {
  const { riskType, riskTypes } = useOutletContext();
  const [parentRiskType, setParentRiskType] = useState(null);

  // Find parent risk type
  useEffect(() => {
    if (riskType?.parentRiskType && riskTypes?.length) {
      const parent = riskTypes.find(
        type => type.riskTypeID === riskType.parentRiskType
      );
      setParentRiskType(parent);
    } else {
      setParentRiskType(null);
    }
  }, [riskType, riskTypes]);

  // Helper function to format dates
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-GB", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6">
        <div>
          <h3 className="text-lg font-semibold text-[#242A33] mb-4">Risk Type Details</h3>
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Tag className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Name</p>
                <p className="font-medium">{riskType.name || "N/A"}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Hash className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Code</p>
                <p className="font-medium">{riskType.code || "N/A"}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Parent Risk Type</p>
                <p className="font-medium">{parentRiskType ? parentRiskType.name : "None"}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <FileText className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Comment</p>
                <p className="font-medium">{riskType.comment || "N/A"}</p>
              </div>
            </div>

            {riskType.createdAt && (
              <div className="flex items-start gap-4">
                <div className="bg-gray-50 p-2 rounded-lg">
                  <Calendar className="h-5 w-5 text-[#555F6D]" />
                </div>
                <div>
                  <p className="text-sm text-[#555F6D]">Created At</p>
                  <p className="font-medium">{formatDate(riskType.createdAt)}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default RiskTypesOverview;
