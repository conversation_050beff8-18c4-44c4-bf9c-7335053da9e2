import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { Plus, ChevronLeft, Trash2, Loader2, ArrowUpDown, Briefcase } from 'lucide-react'; // Added Briefcase icon
import { useSelector } from 'react-redux';
import { hasPermission } from '@/store/auth-slice';
import PageHeader from "@/components/ui/page-header"; // Added PageHeader
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from "react-i18next";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import TablePagination from "@/components/ui/table-pagination";

export default function BusinessLinesManagement() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const canCreate = useSelector(state => hasPermission(state, 'create'));
  const canDelete = useSelector(state => hasPermission(state, 'delete'));
  const [searchQuery, setSearchQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [selectedLines, setSelectedLines] = useState([]);
  const [currentBusinessLines, setCurrentBusinessLines] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentBusinessLine, setCurrentBusinessLine] = useState(null);
  const API_BASE_URL = getApiBaseUrl();
  const columns = [
    { key: 'name', label: t('admin.business_lines.columns.name', 'Name'), sortable: true },
    { key: 'description', label: t('admin.business_lines.columns.description', 'Description'), sortable: true },
  ];

  const [newBusinessLine, setNewBusinessLine] = useState({
    name: '',
    description: '',
  });

  useEffect(() => {
    fetchBusinessLines();
  }, []);

  const fetchBusinessLines = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/businessLines`, {
        withCredentials: true,
      });
      if (response.data.success) {
        setCurrentBusinessLines(response.data.data);
      }
    } catch (error) {
      console.error(t('admin.business_lines.error.fetch_failed', 'Error fetching business lines:'), error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      const businessLineToCreate = {
        ...newBusinessLine,
        businessLineID: `BL_${Date.now()}`,
      };

      const response = await axios.post(
        `${API_BASE_URL}/businessLines`,
        businessLineToCreate,
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
        }
      );

      if (response.data.success) {
        setIsOpen(false);
        setNewBusinessLine({ name: '', description: '' });
        await fetchBusinessLines();
      }
    } catch (error) {
      console.error(t('admin.business_lines.error.create_failed', 'Error creating business line:'), error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });

    const sortedLines = [...currentBusinessLines].sort((a, b) => {
      if (a[key] < b[key]) return direction === 'asc' ? -1 : 1;
      if (a[key] > b[key]) return direction === 'asc' ? 1 : -1;
      return 0;
    });
    setCurrentBusinessLines(sortedLines);
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedLines(currentBusinessLines.map(line => line.businessLineID));
    } else {
      setSelectedLines([]);
    }
  };

  const handleSelectLine = (businessLineID) => {
    setSelectedLines(prev =>
      prev.includes(businessLineID)
        ? prev.filter(id => id !== businessLineID)
        : [...prev, businessLineID]
    );
  };

  const handleDeleteSelected = async () => {
    if (selectedLines.length === 0) {
      return;
    }

    const confirmDelete = window.confirm(
      t('admin.business_lines.confirm.delete', 'Are you sure you want to delete {{count}} selected business line(s)?', { count: selectedLines.length })
    );

    if (!confirmDelete) {
      return;
    }

    try {
      setSubmitting(true);
      for (const businessLineID of selectedLines) {
        await axios.delete(`${API_BASE_URL}/businessLines/${businessLineID}`, {
          withCredentials: true,
        });
      }
      setSelectedLines([]);
      await fetchBusinessLines();
    } catch (error) {
      console.error(t('admin.business_lines.error.delete_failed', 'Error deleting business lines:'), error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleRowClick = (id) => {
    navigate(`/admin/data/business-lines/${id}`);
  };

  const handleUpdateSubmit = async (e) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      const response = await axios.put(
        `${API_BASE_URL}/businessLines/${currentBusinessLine.businessLineID}`,
        {
          name: currentBusinessLine.name,
          description: currentBusinessLine.description,
        },
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
        }
      );

      if (response.data.success) {
        setIsEditModalOpen(false);
        setCurrentBusinessLine(null);
        await fetchBusinessLines();
      }
    } catch (error) {
      console.error(t('admin.business_lines.error.update_failed', 'Error updating business line:'), error);
    } finally {
      setSubmitting(false);
    }
  };

  const filteredBusinessLines = currentBusinessLines.filter(line =>
    line.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    line.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalPages = Math.ceil(filteredBusinessLines.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredBusinessLines.slice(indexOfFirstItem, indexOfLastItem);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      <PageHeader
        title={t('admin.business_lines.title', 'Business Lines Management')}
        description={t('admin.business_lines.description', 'Define and manage business lines within your organization.')}
        section={t('admin.sidebar.data', 'Data')}
        currentPage={t('admin.sidebar.business_lines', 'Business Lines')}
        searchPlaceholder={t('admin.business_lines.search_placeholder', 'Search business lines...')}
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
        icon={Briefcase} // Using Briefcase icon for Business Lines
      />

      <div className="flex justify-between items-center gap-3 mb-4">
        <Button variant="ghost" className="p-2" onClick={() => navigate('/admin/team')}>
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <div className="flex items-center gap-3">
          {selectedLines.length > 0 && (
            <Button
              variant="outline"
              className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
                canDelete && !submitting
                  ? 'bg-[#F62D51]/90 text-white hover:bg-[#F62D51] hover:shadow-md'
                  : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
              } flex items-center gap-2 px-6 py-2 font-semibold`}
              onClick={handleDeleteSelected}
              disabled={!canDelete || submitting}
            >
              {submitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  {t('common.deleting', 'Deleting...')}
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  {t('common.buttons.delete', 'Delete')} ({selectedLines.length})
                </>
              )}
            </Button>
          )}
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            {canCreate && (
              <DialogTrigger asChild>
                <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  {t('admin.business_lines.buttons.add', 'Add Business Line')}
                </Button>
              </DialogTrigger>
            )}
            <DialogContent className="max-w-3xl p-8">
              <DialogHeader>
                <DialogTitle>{t('admin.business_lines.dialog.title', 'Add New Business Line')}</DialogTitle>
                <DialogDescription>
                  {t('admin.business_lines.dialog.description', 'Fill in the details to create a new business line.')}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-8">
                <div className="flex flex-col">
                  <Label htmlFor="name" className="mb-2">{t('admin.business_lines.form.name', 'Name')} *</Label>
                  <Input
                    id="name"
                    value={newBusinessLine.name}
                    onChange={(e) => setNewBusinessLine({ ...newBusinessLine, name: e.target.value })}
                    placeholder={t('admin.business_lines.form.name_placeholder', 'Enter business line name')}
                    required
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="description" className="mb-2">{t('admin.business_lines.form.description', 'Description')}</Label>
                  <Textarea
                    id="description"
                    value={newBusinessLine.description}
                    onChange={(e) => setNewBusinessLine({ ...newBusinessLine, description: e.target.value })}
                    placeholder={t('admin.business_lines.form.description_placeholder', 'Enter description')}
                    className="w-full h-24 resize-y"
                  />
                </div>
                <div className="flex justify-end gap-4">
                  <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                    {t('common.buttons.cancel', 'Cancel')}
                  </Button>
                  <Button type="submit" className="bg-[#F62D51] hover:bg-red-700" disabled={submitting}>
                    {submitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t('common.creating', 'Creating...')}
                      </>
                    ) : (
                      t('admin.business_lines.buttons.create', 'Create Business Line')
                    )}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                      <Checkbox
                        checked={selectedLines.length === currentBusinessLines.length && currentBusinessLines.length > 0}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all"
                      />
                    </th>
                    {columns.map((column) => (
                      <th
                        key={column.key}
                        className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100"
                        onClick={() => column.sortable && handleSort(column.key)}
                      >
                        <div className="flex items-center gap-2">
                          {column.label}
                          {column.sortable && <ArrowUpDown className="h-4 w-4" />}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {currentItems.map((line, index) => (
                    <tr
                      key={line.businessLineID}
                      className={`hover:bg-gray-50 cursor-pointer ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                      onClick={() => handleRowClick(line.businessLineID)}
                    >
                      <td className="px-6 py-4">
                        <Checkbox
                          checked={selectedLines.includes(line.businessLineID)}
                          onCheckedChange={() => handleSelectLine(line.businessLineID)}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </td>

                      <td className="px-6 py-4 text-sm font-bold text-[#242A33] whitespace-nowrap">
                        {line.name}
                      </td>
                      <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                        {line.description || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={filteredBusinessLines.length}
            onPageChange={handlePageChange}
            onItemsPerPageChange={(value) => {
              setItemsPerPage(value);
              setCurrentPage(1);
            }}
            startIndex={indexOfFirstItem}
            endIndex={indexOfLastItem}
          />
        </>
      )}

      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-3xl p-8">
          <DialogHeader>
            <DialogTitle>{t('admin.business_lines.dialog.edit_title', 'Edit Business Line')}</DialogTitle>
            <DialogDescription>{t('admin.business_lines.dialog.edit_description', 'Update the business line details.')}</DialogDescription>
          </DialogHeader>
          {currentBusinessLine && (
            <form onSubmit={handleUpdateSubmit} className="space-y-8">
              <div className="flex flex-col">
                <Label htmlFor="edit-name" className="mb-2">{t('admin.business_lines.form.name', 'Name')} *</Label>
                <Input
                  id="edit-name"
                  value={currentBusinessLine.name}
                  onChange={(e) => setCurrentBusinessLine({ ...currentBusinessLine, name: e.target.value })}
                  placeholder={t('admin.business_lines.form.name_placeholder', 'Enter business line name')}
                  required
                  className="w-full"
                />
              </div>
              <div className="flex flex-col">
                <Label htmlFor="edit-description" className="mb-2">{t('admin.business_lines.form.description', 'Description')}</Label>
                <Textarea
                  id="edit-description"
                  value={currentBusinessLine.description || ''}
                  onChange={(e) => setCurrentBusinessLine({ ...currentBusinessLine, description: e.target.value })}
                  placeholder={t('admin.business_lines.form.description_placeholder', 'Enter description')}
                  className="w-full h-24 resize-y"
                />
              </div>
              <div className="flex justify-end gap-4">
                <Button type="button" variant="outline" onClick={() => setIsEditModalOpen(false)}>
                  {t('common.buttons.cancel', 'Cancel')}
                </Button>
                <Button type="submit" className="bg-[#F62D51] hover:bg-red-700" disabled={submitting}>
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('common.updating', 'Updating...')}
                    </>
                  ) : (
                    t('admin.business_lines.buttons.update', 'Update Business Line')
                  )}
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}