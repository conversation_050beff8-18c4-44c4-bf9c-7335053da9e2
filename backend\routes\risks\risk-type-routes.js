const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllRiskTypes,
  createRiskType,
  getRiskTypeById,
  updateRiskType,
  deleteRiskType
} = require('../../controllers/risks/risk-type-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all risk types
router.get('/', getAllRiskTypes);

// Create new risk type
router.post('/', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), createRiskType);

// Get risk type by ID
router.get('/:id', getRiskTypeById);

// Update risk type
router.put('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), updateRiskType);

// Delete risk type
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteRiskType);

module.exports = router;
