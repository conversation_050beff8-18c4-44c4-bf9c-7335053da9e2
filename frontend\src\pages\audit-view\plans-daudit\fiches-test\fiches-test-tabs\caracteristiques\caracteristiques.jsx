import React, { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Paperclip, Upload, Trash2, ChevronUp, ChevronDown, Save, Loader2, FileText } from "lucide-react";
import { useCustomOutletContext } from "../../edit-fiches-test";
import { updateFicheDeTest } from "@/services/fiche-de-test-service";
import { toast } from "sonner";
import { getQuestionsByFicheId } from '@/services/fiche-de-travail-service';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { DateInput } from "@/components/ui/date-input";
import { AuditTestFicheAttachmentsSection } from "@/components/attachments_audit/AuditTestFicheAttachmentsSection";

function FichesTestCaracteristiquesTab() {
  const context = useCustomOutletContext();
  const ficheTest = context?.ficheTest;
  const abortControllerRef = useRef(null);

  // State for loading questions
  const [questions, setQuestions] = useState([]);
  const [questionsLoading, setQuestionsLoading] = useState(false);

  // Use real ficheTest from context
  const activeFicheTest = ficheTest;

  // Debugging log to confirm activeFicheTest
  useEffect(() => {
    console.log("activeFicheTest:", activeFicheTest);
    console.log("ficheTest from context:", ficheTest);
    console.log("Questions:", activeFicheTest?.ficheDeTravail?.questions);
  }, [activeFicheTest, ficheTest]);

  // Section toggles
  const [isCaracteristiquesOpen, setIsCaracteristiquesOpen] = useState(true);
  const [isQuestionnaireOpen, setIsQuestionnaireOpen] = useState(true);
  const [isPiecesJointesOpen, setIsPiecesJointesOpen] = useState(true);

  // Loading states
  const [isSaving, setIsSaving] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    titre: "",
    elementTrouve: false,
    commentaire: "",
    preuve: "",
    dateSignature: "",
    signe: false,
    responsable: "",
    answers: {}
  });

  // State for quiz answers
  const [answers, setAnswers] = useState({});

  // Helper function to format date for input (ISO format)
  const formatDateForInput = (dateString) => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error("Error formatting date for input:", error);
      return dateString;
    }
  };

  // Initialize form data when activeFicheTest changes
  useEffect(() => {
    if (activeFicheTest) {
      setFormData({
        titre: activeFicheTest.titre || "",
        elementTrouve: activeFicheTest.elementTrouve || false,
        commentaire: activeFicheTest.commentaire || "",
        preuve: activeFicheTest.preuve || "",
        dateSignature: formatDateForInput(activeFicheTest.dateSignature),
        signe: activeFicheTest.signe || false,
        responsable: activeFicheTest.responsable || "",
        answers: activeFicheTest.answers || {}
      });
      setAnswers(activeFicheTest.answers || {});
    }
  }, [activeFicheTest]);

  // Fetch questions for ficheDeTravailID if not present
  useEffect(() => {
    const ficheDeTravailId = ficheTest?.ficheDeTravail?.id || ficheTest?.ficheDeTravailID;
    console.log('ficheTest:', ficheTest);
    console.log('ficheTest.ficheDeTravail:', ficheTest?.ficheDeTravail);
    console.log('ficheTest.ficheDeTravailID:', ficheTest?.ficheDeTravailID);
    async function fetchQuestions() {
      if (ficheDeTravailId) {
        setQuestionsLoading(true);
        try {
          const res = await getQuestionsByFicheId(ficheDeTravailId);
          if (res && res.success && Array.isArray(res.data)) {
            setQuestions(res.data);
          } else {
            setQuestions([]);
          }
        } catch (error) {
          console.error("Error fetching questions:", error);
          setQuestions([]);
        } finally {
          setQuestionsLoading(false);
        }
      }
    }
    fetchQuestions();
  }, [ficheTest]);

  // Cleanup on unmount
  useEffect(() => {
    const controller = abortControllerRef.current;
    return () => {
      if (controller) {
        controller.abort();
      }
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Handlers
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleAnswerChange = (questionId, value) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: value
    }));
    setFormData((prev) => ({
      ...prev,
      answers: {
        ...prev.answers,
        [questionId]: value
      }
    }));
  };

  // Validation for answers
  const validateAnswers = () => {
    const errors = [];
    if (!questions) return errors;
    questions.forEach((q) => {
      const value = answers[q.id];
      if (q.input_type === 'text' && (!value || value.trim() === '')) {
        errors.push(`La question "${q.question_text}" est requise.`);
      }
      if (q.input_type === 'number' && (value === undefined || value === '' || isNaN(Number(value)))) {
        errors.push(`La question "${q.question_text}" doit être un nombre.`);
      }
      if (q.input_type === 'date' && (!value || isNaN(Date.parse(value)))) {
        errors.push(`La question "${q.question_text}" doit être une date valide.`);
      }
      if ((q.input_type === 'radio' || q.input_type === 'select') && (!value || value === '')) {
        errors.push(`La question "${q.question_text}" est requise.`);
      }
    });
    return errors;
  };

  const handleValidate = async () => {
    const errors = validateAnswers();
    if (errors.length > 0) {
      errors.forEach((err) => toast.error(err));
      return;
    }
    if (!ficheTest?.id) {
      toast.error("ID de fiche de test manquant");
      return;
    }
    setIsSaving(true);
    try {
      const response = await updateFicheDeTest(
        ficheTest.id,
        { ...formData, answers },
        abortControllerRef.current?.signal
      );
      if (!response) return;
      if (response.success) {
        toast.success("Réponses validées et sauvegardées avec succès");
        if (context?.setFicheTest) {
          context.setFicheTest(response.data);
        }
      } else {
        throw new Error(response?.message || "Erreur lors de la validation");
      }
    } catch (error) {
      if (error.name === 'CanceledError') return;
      console.error('Error validating fiche de test:', error);
      toast.error(error.message || "Erreur lors de la validation de la fiche de test");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6 py-4">
      {/* Section 1: Caractéristiques */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsCaracteristiquesOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isCaracteristiquesOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <span className="text-lg font-medium text-blue-800">Caractéristiques</span>
          </div>
        </button>
        {isCaracteristiquesOpen && (
          <div className="p-5 bg-white space-y-6">
            <div className="w-full space-y-2">
              <Label htmlFor="titre">Titre *</Label>
              <Input 
                id="titre" 
                name="titre" 
                value={formData.titre} 
                onChange={handleInputChange} 
                placeholder="Titre de la fiche de test" 
                className="w-full" 
                required 
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ficheDeTravail">Fiche de travail</Label>
                <Input 
                  id="ficheDeTravail" 
                  name="ficheDeTravail" 
                  value={activeFicheTest?.ficheDeTravail?.name || 'N/A'} 
                  className="w-full bg-gray-50" 
                  readOnly 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="responsable">Responsable</Label>
                <Input 
                  id="responsable" 
                  name="responsable" 
                  value={formData.responsable} 
                  onChange={handleInputChange} 
                  placeholder="Responsable" 
                  className="w-full" 
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="elementTrouve">Élément trouvé</Label>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="elementTrouve"
                    name="elementTrouve"
                    checked={formData.elementTrouve}
                    onCheckedChange={val => handleInputChange({ target: { name: 'elementTrouve', type: 'checkbox', checked: val } })}
                  />
                  <Label htmlFor="elementTrouve" className="text-sm font-medium text-gray-700">
                    L'élément a été trouvé
                  </Label>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="signe">Signé</Label>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="signe"
                    name="signe"
                    checked={formData.signe}
                    onCheckedChange={val => handleInputChange({ target: { name: 'signe', type: 'checkbox', checked: val } })}
                  />
                  <Label htmlFor="signe" className="text-sm font-medium text-gray-700">
                    La fiche est signée
                  </Label>
                </div>
              </div>
            </div>
            <div className="w-full space-y-2">
              <Label htmlFor="dateSignature">Date de signature</Label>
              <Input 
                id="dateSignature" 
                name="dateSignature" 
                type="date" 
                value={formData.dateSignature} 
                onChange={handleInputChange} 
                className="w-full" 
              />
            </div>
            <div className="w-full space-y-2">
              <Label htmlFor="commentaire">Commentaires</Label>
              <Textarea 
                id="commentaire" 
                name="commentaire" 
                value={formData.commentaire} 
                onChange={handleInputChange} 
                placeholder="Commentaires" 
                rows={3} 
                className="w-full" 
              />
            </div>
            <div className="w-full space-y-2">
              <Label htmlFor="preuve">Preuve</Label>
              <Textarea 
                id="preuve" 
                name="preuve" 
                value={formData.preuve} 
                onChange={handleInputChange} 
                placeholder="Preuve" 
                rows={3} 
                className="w-full" 
              />
            </div>
            <div className="flex justify-end pt-4">
              <Button 
                onClick={async () => {
                  setIsSaving(true);
                  try {
                    const response = await updateFicheDeTest(
                      ficheTest.id,
                      { ...formData, answers },
                      abortControllerRef.current?.signal
                    );
                    if (!response) return;
                    if (response.success) {
                      toast.success("Fiche de test sauvegardée avec succès");
                      if (context?.setFicheTest) {
                        context.setFicheTest(response.data);
                      }
                    } else {
                      throw new Error(response?.message || "Erreur lors de la sauvegarde");
                    }
                  } catch (error) {
                    if (error.name === 'CanceledError') return;
                    console.error('Error saving fiche de test:', error);
                    toast.error(error.message || "Erreur lors de la sauvegarde de la fiche de test");
                  } finally {
                    setIsSaving(false);
                  }
                }}
                className="bg-[#F62D51] hover:bg-[#F62D51]/90"
                disabled={isSaving}
              >
                {isSaving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
                {isSaving ? "Sauvegarde..." : "Sauvegarder"}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Section 2: Questionnaire */}
      <div className="border rounded-lg shadow-sm mt-6">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-green-50 to-teal-50 rounded-t-lg"
          onClick={() => setIsQuestionnaireOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isQuestionnaireOpen ? (
              <ChevronUp className="h-5 w-5 text-green-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-green-600" />
            )}
            <span className="text-lg font-medium text-green-800">Questionnaire</span>
          </div>
        </button>
        {isQuestionnaireOpen && (
          <div className="p-5 bg-white space-y-6">
            {questionsLoading ? (
              <p className="text-sm text-gray-500 text-center">Chargement des questions...</p>
            ) : questions.length > 0 ? (
              <div className="space-y-4">
                {questions.map((question, index) => (
                  <div key={question.id} className="space-y-2">
                    <Label htmlFor={`question-${question.id}`} className="font-semibold">
                      {index + 1}. {question.question_text}
                    </Label>
                    {question.input_type === "text" && (
                      <Input
                        id={`question-${question.id}`}
                        value={answers[question.id] || ""}
                        onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                        placeholder="Entrez votre réponse"
                        className="w-full"
                      />
                    )}
                    {question.input_type === "number" && (
                      <Input
                        id={`question-${question.id}`}
                        type="number"
                        value={answers[question.id] || ""}
                        onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                        placeholder="Entrez un nombre"
                        className="w-full"
                      />
                    )}
                    {question.input_type === "date" && (
                      <DateInput
                          id={`question-${question.id}`}
                          value={answers[question.id] || ""}
                          onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                        name={`question-${question.id}`}
                          className="w-full"
                        />
                    )}
                    {question.input_type === "radio" && question.options && (
                      <RadioGroup
                        value={answers[question.id] || ""}
                        onValueChange={(value) => handleAnswerChange(question.id, value)}
                        className="flex flex-col gap-2"
                      >
                        {question.options.map((option, optIndex) => (
                          <div key={optIndex} className="flex items-center space-x-2">
                            <RadioGroupItem value={option} id={`question-${question.id}-option-${optIndex}`} />
                            <Label htmlFor={`question-${question.id}-option-${optIndex}`} className="text-sm">
                              {option}
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    )}
                    {question.input_type === "select" && question.options && (
                      <Select
                        value={answers[question.id] || ""}
                        onValueChange={(value) => handleAnswerChange(question.id, value)}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Sélectionnez une option" />
                        </SelectTrigger>
                        <SelectContent>
                        {question.options.map((option, optIndex) => (
                            <SelectItem key={optIndex} value={option}>{option}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                    {question.input_type === "multi-select" && question.options && (
                      <div className="w-full">
                        <label className="block text-sm font-medium mb-1">Sélectionnez une ou plusieurs options</label>
                        <div className="flex flex-col gap-2">
                          {question.options.map((option, optIndex) => {
                            const checked = Array.isArray(answers[question.id]) && answers[question.id].includes(option);
                            return (
                              <label key={optIndex} className="flex items-center gap-2 cursor-pointer">
                                <Checkbox
                                  checked={checked}
                                  onCheckedChange={val => {
                                    let selected = Array.isArray(answers[question.id]) ? [...answers[question.id]] : [];
                                    if (val) {
                                      selected.push(option);
                                    } else {
                                      selected = selected.filter(v => v !== option);
                                    }
                                    handleAnswerChange(question.id, selected);
                                  }}
                                />
                                <span>{option}</span>
                              </label>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
                {/* Valider button */}
                <div className="flex justify-end pt-4">
                  <Button
                    onClick={handleValidate}
                    className="bg-[#F62D51] hover:bg-[#F62D51]/90"
                    disabled={isSaving}
                  >
                    {isSaving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
                    {isSaving ? "Validation..." : "Valider"}
                  </Button>
                </div>
              </div>
            ) : (
              <p className="text-sm text-gray-500 text-center">Aucune question disponible dans la fiche de travail associée.</p>
            )}
          </div>
        )}
      </div>

      {/* Section 3: Pièces jointes */}
      <div className="border rounded-lg shadow-sm mt-6">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-t-lg"
          onClick={() => setIsPiecesJointesOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isPiecesJointesOpen ? (
              <ChevronUp className="h-5 w-5 text-purple-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-purple-600" />
            )}
            <Paperclip className="h-5 w-5 text-purple-600" />
            <span className="text-lg font-medium text-purple-800">Pièces jointes</span>
          </div>
        </button>
        {isPiecesJointesOpen && (
          <div className="p-5 bg-white space-y-6">
            <AuditTestFicheAttachmentsSection ficheTest={ficheTest} />
          </div>
        )}
      </div>
    </div>
  );
}

export default FichesTestCaracteristiquesTab;