module.exports = (sequelize, DataTypes) => {
  const RiskAttachment = sequelize.define('RiskAttachment', {
    // Use UUID for primary key
    attachmentID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: false
    },
    filePath: {
      type: DataTypes.STRING,
      allowNull: false
    },
    uploadDate: {
      type: DataTypes.DATE,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('business-document', 'external-reference'),
      allowNull: false
    },
    riskID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Risk',
        key: 'riskID'
      }
    }
  }, {
    tableName: 'RiskAttachment',
    timestamps: false
  });

  RiskAttachment.associate = (models) => {
    RiskAttachment.belongsTo(models.Risk, {
      foreignKey: 'riskID',
      targetKey: 'riskID',
      as: 'relatedRisk'
    });
  };

  return RiskAttachment;
};
