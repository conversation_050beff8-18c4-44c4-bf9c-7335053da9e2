import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft, AlertTriangle } from 'lucide-react';
import PageHeader from '@/components/ui/page-header';
import axios from 'axios';
import { toast } from 'react-hot-toast';
import { Button } from "../../../components/ui/button";
import { Input } from "../../../components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../../components/ui/select";
import { Textarea } from "../../../components/ui/textarea";
import { Combobox } from "@/components/ui/combobox";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from "react-i18next";

function AddIncident() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const API_BASE_URL = getApiBaseUrl();
  // State for the new incident form with required fields
  const [newIncident, setNewIncident] = useState({
    name: '',
    entityID: '',
    detectionDate: '',
    occurrenceDate: '',
    description: ''
  });

  // State for dropdown options fetched from APIs
  const [entities, setEntities] = useState([]);

  // Fetch data for dropdowns on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        const entitiesRes = await axios.get(`${API_BASE_URL}/entities`, { 
          withCredentials: true, 
          headers: { 'Content-Type': 'application/json' } 
        });

        setEntities(entitiesRes.data.success ? entitiesRes.data.data || [] : []);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error(t('admin.incidents.add.failed_to_fetch_entity_data', 'Failed to fetch entity data. Please refresh the page.'));
      }
    };

    fetchData();
  }, [t]);

  // Handle input changes for all fields
  const handleInputChange = (field, value) => {
    setNewIncident((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    const incidentID = `INC_${Date.now()}`;
    const declarationDate = new Date().toISOString(); // Auto-fill current date

    const formattedIncident = {
      ...newIncident,
      incidentID,
      declarationDate,
      detectionDate: newIncident.detectionDate ? new Date(newIncident.detectionDate).toISOString() : null,
      occurrenceDate: newIncident.occurrenceDate ? new Date(newIncident.occurrenceDate).toISOString() : null,
      nearMiss: false,
      // Adding empty fields for backend compatibility
      declaredBy: '',
      declarantEntity: '',
      nature: '',
      impact: '',
      priority: '',
      currency: '',
      grossLoss: 0,
      recoveries: 0,
      provisions: 0,
      riskID: null,
      controlID: null,
      businessLineID: null,
      incidentTypeID: null,
      businessProcessID: null,
      organizationalProcessID: null,
      productID: null,
      applicationID: null
    };

    // Only name and entity are required
    const requiredFields = ['name', 'entityID'];
    const missingFields = requiredFields.filter(field => !formattedIncident[field]);
    if (missingFields.length > 0) {
      toast.error(t('admin.incidents.add.missing_fields', 'Missing required fields: ' + missingFields.join(', ')));
      return;
    }

    try {
      console.log('Submitting incident data:', formattedIncident);

      const response = await axios.post(`${API_BASE_URL}/incidents`, formattedIncident, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' },
      });

      console.log('API response:', response.data);

      if (response.data.success) {
        toast.success(t('admin.incidents.add.incident_created_successfully', 'Incident created successfully'));
        navigate('/admin/incident');
      } else {
        // Handle case where API returns success: false
        toast.error(response.data.message || t('admin.incidents.add.failed_to_create_incident', 'Failed to create incident'));
      }
    } catch (error) {
      console.error('Error creating incident:', error);
      console.error('Error details:', error.response?.data);
      const errorMessage = error.response?.data?.message || error.response?.data?.error || t('admin.incidents.add.failed_to_create_incident', 'Failed to create incident');
      toast.error(errorMessage);
    }
  };

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      <PageHeader
        title={t('admin.incidents.add.title', 'Add New Incident')}
        description={t('admin.incidents.add.description', 'Create a new incident record with essential information')}
        section="Incident"
        currentPage="Add New"
        icon={AlertTriangle}
      />

      <div className="flex justify-between items-center mb-4">
        <Button variant="outline" className="flex items-center gap-2" onClick={() => navigate('/admin/incident')}>
          <ChevronLeft className="h-4 w-4" />
          {t('admin.incidents.add.back_to_incidents', 'Back to Incidents')}
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8 bg-white rounded-lg p-6 shadow">
        <div className="grid grid-cols-2 gap-x-8 gap-y-6">
          {/* Name */}
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('admin.incidents.add.name', 'Name *')}</label>
            <Input
              value={newIncident.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder={t('admin.incidents.add.incident_name', 'Incident name')}
              required
              className="w-full"
            />
          </div>

          {/* Entity */}
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('admin.incidents.add.entity', 'Entity *')}</label>
            <Combobox
              options={entities.map(entity => ({
                label: entity.name,
                value: entity.entityID
              }))}
              value={newIncident.entityID}
              onChange={(value) => handleInputChange('entityID', value)}
              placeholder={t('admin.incidents.add.select_entity', 'Select entity')}
              emptyMessage={t('admin.incidents.add.no_entities_found', 'No entities found.')}
              searchPlaceholder={t('admin.incidents.add.search_entities', 'Search entities...')}
              className="w-full"
              popoverClassName="w-full"
            />
          </div>

          {/* Detection Date */}
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('admin.incidents.add.detection_date', 'Detection Date')}</label>
            <Input
              type="date"
              value={newIncident.detectionDate}
              onChange={(e) => handleInputChange('detectionDate', e.target.value)}
              className="w-full"
            />
          </div>

          {/* Occurrence Date */}
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('admin.incidents.add.occurrence_date', 'Occurrence Date')}</label>
            <Input
              type="date"
              value={newIncident.occurrenceDate}
              onChange={(e) => handleInputChange('occurrenceDate', e.target.value)}
              className="w-full"
            />
          </div>

          {/* Description */}
          <div className="space-y-2 col-span-2">
            <label className="text-sm font-medium">{t('admin.incidents.add.description', 'Description')}</label>
            <Textarea
              value={newIncident.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder={t('admin.incidents.add.incident_description', 'Incident description')}
              className="min-h-[100px] w-full"
            />
          </div>
        </div>

        <div className="flex justify-end gap-4 pt-6">
          <Button type="button" variant="outline" onClick={() => navigate('/admin/incident')} className="px-6">
            {t('admin.incidents.add.cancel', 'Cancel')}
          </Button>
          <Button type="submit" className="bg-[#F62D51] hover:bg-red-700 px-6">
            {t('admin.incidents.add.add_incident', 'Add Incident')}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default AddIncident;