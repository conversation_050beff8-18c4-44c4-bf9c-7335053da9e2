import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Users,
  Search,
  Filter,
  Plus,
  Award,
  Star,
  TrendingUp,
  User,
  BookOpen,
  BarChart3,
  Loader2,
  Mail,
  Phone,
  Edit,
  Trash2
} from 'lucide-react';
import { toast } from 'sonner';
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

const CompetencesTeam = () => {
  const [users, setUsers] = useState([]);
  const [competences, setCompetences] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [competenceFilter, setCompetenceFilter] = useState('all');
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedSkillAssignment, setSelectedSkillAssignment] = useState(null);
  const [assignFormData, setAssignFormData] = useState({
    skillId: '',
    rating: 3,
    comments: ''
  });
  const [editFormData, setEditFormData] = useState({
    rating: 3,
    comments: ''
  });
  const [selectedUserForDetail, setSelectedUserForDetail] = useState(null);

  // Fetch data from API
  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch auditors with their skills and available skills in parallel
      const [auditorsResponse, skillsResponse] = await Promise.all([
        axios.get(`${getApiBaseUrl()}/audit/auditors`, { withCredentials: true }),
        axios.get(`${getApiBaseUrl()}/audit/skills`, { withCredentials: true })
      ]);

      if (auditorsResponse.data.success) {
        const newUsers = auditorsResponse.data.data;
        setUsers(newUsers);

        // Update selected user with fresh data if one is selected
        if (selectedUserForDetail) {
          const updatedSelectedUser = newUsers.find(user => user.id === selectedUserForDetail.id);
          if (updatedSelectedUser) {
            setSelectedUserForDetail(updatedSelectedUser);
          }
        }
      }

      if (skillsResponse.data.success) {
        setCompetences(skillsResponse.data.data);
      }

    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  // Get user's competences and evaluations
  const getUserCompetenceData = (user) => {
    const userSkills = user.auditorSkills || [];

    const userCompetencesList = userSkills.map(auditorSkill => ({
      id: auditorSkill.id, // Include the assignment ID for edit/delete
      skillId: auditorSkill.skillId,
      skill: auditorSkill.skill,
      rating: parseFloat(auditorSkill.rating),
      comments: auditorSkill.comments,
      ratedAt: auditorSkill.ratedAt,
      ratedBy: auditorSkill.rater
    }));

    const averageScore = userCompetencesList.length > 0
      ? userCompetencesList.reduce((sum, comp) => sum + comp.rating, 0) / userCompetencesList.length
      : 0;

    return {
      competences: userCompetencesList,
      averageScore: averageScore.toFixed(1),
      totalCompetences: userCompetencesList.length,
      excellentCompetences: userCompetencesList.filter(comp => comp.rating >= 4).length
    };
  };

  // Get user role display
  const getUserRoleDisplay = (user) => {
    const auditRoles = user.userRoles?.filter(userRole =>
      userRole.role?.code === 'auditor' || userRole.role?.code === 'audit_director'
    );

    if (auditRoles?.length > 0) {
      return auditRoles.map(userRole =>
        userRole.role.code === 'audit_director' ? 'Directeur d\'Audit' : 'Auditeur'
      ).join(', ');
    }

    return 'Utilisateur';
  };

  // Get user initials
  const getUserInitials = (user) => {
    const username = user.username || '';
    return username.length >= 2 ? username.substring(0, 2).toUpperCase() : username.toUpperCase();
  };

  // Filter users
  const filteredUsers = users.filter(user => {
    const matchesSearch = searchTerm === '' ||
      user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = roleFilter === 'all' ||
      user.userRoles?.some(userRole => userRole.role?.code === roleFilter);

    if (competenceFilter === 'all') {
      return matchesSearch && matchesRole;
    }

    // Filter by specific competence
    const hasCompetence = user.auditorSkills?.some(auditorSkill =>
      auditorSkill.skillId === competenceFilter
    );

    return matchesSearch && matchesRole && hasCompetence;
  });

  // Calculate team statistics
  const teamStats = {
    totalMembers: users.length,
    auditDirectors: users.filter(user =>
      user.userRoles?.some(userRole => userRole.role?.code === 'audit_director')
    ).length,
    auditors: users.filter(user =>
      user.userRoles?.some(userRole => userRole.role?.code === 'auditor')
    ).length,
    averageCompetences: users.length > 0
      ? (users.reduce((sum, user) => {
          const userData = getUserCompetenceData(user);
          return sum + userData.totalCompetences;
        }, 0) / users.length).toFixed(1)
      : 0
  };

  // Assign skill to auditor
  const handleAssignSkill = async () => {
    if (!assignFormData.skillId) {
      toast.error('Veuillez sélectionner une compétence');
      return;
    }

    try {
      const response = await axios.post(`${getApiBaseUrl()}/audit/assignments`, {
        auditorId: selectedUser.id,
        skillId: assignFormData.skillId,
        rating: parseFloat(assignFormData.rating),
        comments: assignFormData.comments.trim() || null
      }, {
        withCredentials: true
      });

      if (response.data.success) {
        // Get the skill name for the success message
        const assignedSkill = competences.find(skill => skill.id === parseInt(assignFormData.skillId));
        const skillName = assignedSkill ? assignedSkill.name : 'Compétence';

        toast.success(`${skillName} assignée avec succès à ${selectedUser.username}`);

        // Close modal and reset form
        setIsAssignModalOpen(false);
        setSelectedUser(null);
        setAssignFormData({ skillId: '', rating: 3, comments: '' });

        // Refresh data to get updated information
        await fetchData();
      }
    } catch (error) {
      console.error('Error assigning skill:', error);
      toast.error(error.response?.data?.message || 'Erreur lors de l\'assignation de la compétence');
    }
  };

  // Edit skill assignment
  const handleEditSkillAssignment = async () => {
    try {
      const response = await axios.put(`${getApiBaseUrl()}/audit/assignments/${selectedSkillAssignment.id}`, {
        rating: parseFloat(editFormData.rating),
        comments: editFormData.comments.trim() || null
      }, {
        withCredentials: true
      });

      if (response.data.success) {
        const skillName = selectedSkillAssignment.skill?.name || 'Compétence';
        toast.success(`Évaluation de "${skillName}" mise à jour avec succès`);

        // Close modal and reset form
        setIsEditModalOpen(false);
        setSelectedSkillAssignment(null);
        setEditFormData({ rating: 3, comments: '' });

        // Refresh data to get updated information
        await fetchData();
      }
    } catch (error) {
      console.error('Error updating skill assignment:', error);
      toast.error(error.response?.data?.message || 'Erreur lors de la mise à jour de l\'évaluation');
    }
  };

  // Delete skill assignment
  const handleDeleteSkillAssignment = async (assignmentId) => {
    if (!confirm('Êtes-vous sûr de vouloir retirer cette compétence ?')) {
      return;
    }

    try {
      const response = await axios.delete(`${getApiBaseUrl()}/audit/assignments/${assignmentId}`, {
        withCredentials: true
      });

      if (response.data.success) {
        toast.success('Compétence retirée avec succès');

        // Refresh data to get updated information
        await fetchData();
      }
    } catch (error) {
      console.error('Error deleting skill assignment:', error);
      toast.error(error.response?.data?.message || 'Erreur lors du retrait de la compétence');
    }
  };

  // Open assign modal
  const openAssignModal = (user) => {
    setSelectedUser(user);
    setAssignFormData({ skillId: '', rating: 3, comments: '' });
    setIsAssignModalOpen(true);
  };

  // Open edit modal
  const openEditModal = (user, skillAssignment) => {
    setSelectedUser(user);
    setSelectedSkillAssignment(skillAssignment);
    setEditFormData({
      rating: skillAssignment.rating,
      comments: skillAssignment.comments || ''
    });
    setIsEditModalOpen(true);
  };

  // Get available skills for assignment (skills not already assigned to the user)
  const getAvailableSkills = (user) => {
    const assignedSkillIds = user.auditorSkills?.map(as => as.skillId) || [];
    return competences.filter(skill => !assignedSkillIds.includes(skill.id));
  };

  // Load data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Auto-select first user when users are loaded
  useEffect(() => {
    if (users.length > 0 && !selectedUserForDetail) {
      setSelectedUserForDetail(users[0]);
    }
  }, [users, selectedUserForDetail]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
        <span className="ml-2 text-gray-600">Chargement de l'équipe...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-100">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Users className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Compétences d'équipe
            </h1>
            <p className="text-gray-600 text-sm">
              Vue d'ensemble des compétences de votre équipe d'audit
            </p>
          </div>
        </div>
      </div>

      {/* Team Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
        <Card className="shadow-sm">
          <CardContent className="p-3">
            <div className="flex items-center">
              <Users className="h-5 w-5 text-blue-600" />
              <div className="ml-2">
                <p className="text-xs font-medium text-gray-600">Total Membres</p>
                <p className="text-base font-bold text-gray-900">{teamStats.totalMembers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardContent className="p-3">
            <div className="flex items-center">
              <Award className="h-5 w-5 text-purple-600" />
              <div className="ml-2">
                <p className="text-xs font-medium text-gray-600">Directeurs</p>
                <p className="text-base font-bold text-gray-900">{teamStats.auditDirectors}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardContent className="p-3">
            <div className="flex items-center">
              <User className="h-5 w-5 text-green-600" />
              <div className="ml-2">
                <p className="text-xs font-medium text-gray-600">Auditeurs</p>
                <p className="text-base font-bold text-gray-900">{teamStats.auditors}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardContent className="p-3">
            <div className="flex items-center">
              <BarChart3 className="h-5 w-5 text-orange-600" />
              <div className="ml-2">
                <p className="text-xs font-medium text-gray-600">Moy. Compétences</p>
                <p className="text-base font-bold text-gray-900">{teamStats.averageCompetences}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content - Split Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-[600px]">
        {/* Left Panel - Users List */}
        <div className="lg:col-span-1">
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Équipe d'Audit
              </CardTitle>
              <CardDescription>
                Sélectionnez un membre pour voir ses compétences
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              {/* Search and Filters */}
              <div className="p-4 border-b space-y-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Rechercher un membre..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <div className="flex flex-col gap-2">
                  <Select value={roleFilter} onValueChange={setRoleFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Rôle" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tous les rôles</SelectItem>
                      <SelectItem value="audit_director">Directeurs d'Audit</SelectItem>
                      <SelectItem value="auditor">Auditeurs</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={competenceFilter} onValueChange={setCompetenceFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Compétence" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Toutes compétences</SelectItem>
                      {competences.map((competence) => (
                        <SelectItem key={competence.id} value={competence.id}>
                          {competence.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Users List */}
              <div className="max-h-[500px] overflow-y-auto">
                {loading ? (
                  <div className="flex items-center justify-center p-8">
                    <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                    <span className="ml-2 text-gray-500">Chargement...</span>
                  </div>
                ) : filteredUsers.length > 0 ? (
                  <div className="space-y-1 p-2">
                    {filteredUsers.map((user) => {
                      const userData = getUserCompetenceData(user);
                      const isSelected = selectedUserForDetail?.id === user.id;

                      return (
                        <div
                          key={user.id}
                          onClick={() => setSelectedUserForDetail(user)}
                          className={`p-3 rounded-lg cursor-pointer transition-colors ${
                            isSelected
                              ? 'bg-blue-50 border-2 border-blue-200'
                              : 'hover:bg-gray-50 border-2 border-transparent'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-10 w-10">
                              <AvatarFallback className="bg-blue-100 text-blue-600">
                                {getUserInitials(user)}
                              </AvatarFallback>
                            </Avatar>

                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-gray-900 truncate">
                                {user.username}
                              </div>
                              <div className="text-sm text-gray-500 truncate">
                                {getUserRoleDisplay(user)}
                              </div>
                              <div className="flex items-center gap-2 mt-1">
                                <Badge variant="outline" className="text-xs">
                                  {userData.totalCompetences} compétences
                                </Badge>
                                {userData.averageScore > 0 && (
                                  <Badge
                                    className={`text-xs ${
                                      userData.averageScore >= 4 ? 'bg-green-100 text-green-800' :
                                      userData.averageScore >= 3 ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-orange-100 text-orange-800'
                                    }`}
                                  >
                                    ⭐ {userData.averageScore}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center p-8">
                    <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun membre trouvé</h3>
                    <p className="text-gray-600">
                      {searchTerm || roleFilter !== 'all' || competenceFilter !== 'all'
                        ? 'Aucun membre ne correspond à vos critères de recherche.'
                        : 'Aucun membre de l\'équipe d\'audit trouvé.'}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Panel - User Details */}
        <div className="lg:col-span-2">
          {selectedUserForDetail ? (
            <Card className="h-full">
              <CardHeader>
                <div className="flex items-center gap-4">
                  <Avatar className="h-16 w-16">
                    <AvatarFallback className="bg-blue-100 text-blue-600 text-lg">
                      {getUserInitials(selectedUserForDetail)}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1">
                    <CardTitle className="text-2xl">
                      {selectedUserForDetail.username}
                    </CardTitle>
                    <CardDescription className="flex items-center gap-4 mt-1">
                      <span className="text-base">{getUserRoleDisplay(selectedUserForDetail)}</span>
                      <div className="flex items-center gap-1">
                        <Mail className="h-4 w-4" />
                        <span>{selectedUserForDetail.email}</span>
                      </div>
                    </CardDescription>
                  </div>
                </div>

                <div className="flex justify-end mt-4">
                  <Button
                    onClick={() => openAssignModal(selectedUserForDetail)}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Assigner une compétence
                  </Button>
                </div>

                {/* User Statistics */}
                <div className="grid grid-cols-3 gap-6 mt-6 pt-6 border-t">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">
                      {getUserCompetenceData(selectedUserForDetail).averageScore}
                    </div>
                    <div className="text-sm text-gray-500">Note Moyenne</div>
                  </div>

                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">
                      {getUserCompetenceData(selectedUserForDetail).excellentCompetences}
                    </div>
                    <div className="text-sm text-gray-500">Excellentes (≥4)</div>
                  </div>

                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-600">
                      {getUserCompetenceData(selectedUserForDetail).totalCompetences}
                    </div>
                    <div className="text-sm text-gray-500">Total Compétences</div>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <BookOpen className="h-5 w-5" />
                      Compétences Évaluées
                    </h3>
                    <Badge variant="outline">
                      {getUserCompetenceData(selectedUserForDetail).totalCompetences} compétences
                    </Badge>
                  </div>

                  <div className="max-h-[400px] overflow-y-auto space-y-2">
                    {getUserCompetenceData(selectedUserForDetail).competences.length > 0 ? (
                      getUserCompetenceData(selectedUserForDetail).competences.map((comp, index) => (
                        <div key={index} className="border rounded-md p-3 bg-gray-50 hover:bg-gray-100 transition-colors">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex-1">
                              <div className="text-sm font-medium text-gray-900">
                                {comp.skill?.name || 'Compétence inconnue'}
                              </div>
                              <div className="text-xs text-gray-500">
                                Évalué le {comp.ratedAt ? new Date(comp.ratedAt).toLocaleDateString('fr-FR') : 'Date inconnue'}
                              </div>
                            </div>
                            <div className="flex items-center gap-1">
                              <Button
                                onClick={() => openEditModal(selectedUserForDetail, comp)}
                                size="sm"
                                variant="ghost"
                                className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800"
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button
                                onClick={() => handleDeleteSkillAssignment(comp.id)}
                                size="sm"
                                variant="ghost"
                                className="h-6 w-6 p-0 text-red-600 hover:text-red-800"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Progress
                              value={(comp.rating / 5) * 100}
                              className="w-24 h-2"
                            />
                            <Badge
                              className={`text-xs px-2 py-0.5 ${
                                comp.rating >= 4 ? 'bg-green-100 text-green-800' :
                                comp.rating >= 3 ? 'bg-yellow-100 text-yellow-800' :
                                comp.rating >= 2 ? 'bg-orange-100 text-orange-800' :
                                'bg-red-100 text-red-800'
                              }`}
                            >
                              {comp.rating}/5
                            </Badge>
                          </div>

                          {comp.comments && (
                            <div className="mt-2 p-2 bg-white rounded border-l-2 border-blue-200">
                              <div className="text-xs text-gray-600 italic">
                                "{comp.comments}"
                              </div>
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-12">
                        <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                        <h4 className="text-lg font-medium text-gray-900 mb-2">Aucune compétence évaluée</h4>
                        <p className="text-gray-600 mb-4">
                          Ce membre n'a pas encore de compétences évaluées.
                        </p>
                        <Button
                          onClick={() => openAssignModal(selectedUserForDetail)}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Assigner la première compétence
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card className="h-full">
              <CardContent className="flex items-center justify-center h-full p-12">
                <div className="text-center">
                  <User className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-medium text-gray-900 mb-2">Sélectionnez un membre</h3>
                  <p className="text-gray-600">
                    Choisissez un membre de l'équipe dans la liste de gauche pour voir ses compétences détaillées.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Assign Skill Modal */}
      <Dialog open={isAssignModalOpen} onOpenChange={setIsAssignModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Assigner une compétence</DialogTitle>
            <DialogDescription>
              Assigner une nouvelle compétence à {selectedUser?.username}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="skill" className="text-right">Compétence</Label>
              <Select
                value={assignFormData.skillId}
                onValueChange={(value) => setAssignFormData({ ...assignFormData, skillId: value })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Sélectionner une compétence" />
                </SelectTrigger>
                <SelectContent>
                  {getAvailableSkills(selectedUser || {}).map((skill) => (
                    <SelectItem key={skill.id} value={skill.id}>
                      {skill.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="rating" className="text-right">Note</Label>
              <div className="col-span-3">
                <Select
                  value={assignFormData.rating.toString()}
                  onValueChange={(value) => setAssignFormData({ ...assignFormData, rating: parseFloat(value) })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">0 - Non évalué</SelectItem>
                    <SelectItem value="1">1 - Insuffisant</SelectItem>
                    <SelectItem value="2">2 - Faible</SelectItem>
                    <SelectItem value="3">3 - Satisfaisant</SelectItem>
                    <SelectItem value="4">4 - Bon</SelectItem>
                    <SelectItem value="5">5 - Excellent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="comments" className="text-right">Commentaires</Label>
              <Textarea
                id="comments"
                value={assignFormData.comments}
                onChange={(e) => setAssignFormData({ ...assignFormData, comments: e.target.value })}
                className="col-span-3"
                placeholder="Commentaires sur l'évaluation (optionnel)"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAssignModalOpen(false)}>
              Annuler
            </Button>
            <Button onClick={handleAssignSkill}>
              Assigner
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Skill Assignment Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Modifier l'évaluation</DialogTitle>
            <DialogDescription>
              Modifier l'évaluation de {selectedSkillAssignment?.skill?.name} pour {selectedUser?.username}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-rating" className="text-right">Note</Label>
              <div className="col-span-3">
                <Select
                  value={editFormData.rating.toString()}
                  onValueChange={(value) => setEditFormData({ ...editFormData, rating: parseFloat(value) })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">0 - Non évalué</SelectItem>
                    <SelectItem value="1">1 - Insuffisant</SelectItem>
                    <SelectItem value="2">2 - Faible</SelectItem>
                    <SelectItem value="3">3 - Satisfaisant</SelectItem>
                    <SelectItem value="4">4 - Bon</SelectItem>
                    <SelectItem value="5">5 - Excellent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="edit-comments" className="text-right">Commentaires</Label>
              <Textarea
                id="edit-comments"
                value={editFormData.comments}
                onChange={(e) => setEditFormData({ ...editFormData, comments: e.target.value })}
                className="col-span-3"
                placeholder="Commentaires sur l'évaluation (optionnel)"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              Annuler
            </Button>
            <Button onClick={handleEditSkillAssignment}>
              Mettre à jour
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CompetencesTeam;
