const { Sequelize } = require('sequelize');
const config = require('../config/database');

// Create Sequelize instance
const sequelize = new Sequelize(
  config.development.database,
  config.development.username,
  config.development.password,
  {
    host: config.development.host,
    port: config.development.port,
    dialect: config.development.dialect,
    logging: console.log
  }
);

async function runMigration() {
  try {
    console.log('Starting Control Activity Logs table migration...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Check if table already exists
    const tableExists = await sequelize.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'ControlActivityLogs'
      );
    `, { type: sequelize.QueryTypes.SELECT });

    if (tableExists[0].exists) {
      console.log('✅ ControlActivityLogs table already exists');
    } else {
      console.log('📋 ControlActivityLogs table does not exist. Creating...');
      
      // Import and run the migration
      const migration = require('../migrations/20240630000001-create-control-activity-logs.js');
      
      console.log('Running migration UP...');
      await migration.up(sequelize.getQueryInterface(), Sequelize);
      
      console.log('✅ Migration completed successfully!');
    }
    
    // Verify the table structure
    console.log('📋 Verifying table structure...');
    const tableInfo = await sequelize.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'ControlActivityLogs' 
      ORDER BY ordinal_position;
    `, { type: sequelize.QueryTypes.SELECT });

    console.log('✅ ControlActivityLogs table columns:');
    tableInfo.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });

    // Check ENUM values
    try {
      const enumValues = await sequelize.query(`
        SELECT unnest(enum_range(NULL::enum_ControlActivityLogs_type)) AS enum_value;
      `, { type: sequelize.QueryTypes.SELECT });
      
      console.log('✅ ENUM values for type column:');
      enumValues.forEach(row => {
        console.log(`   - ${row.enum_value}`);
      });
    } catch (enumError) {
      console.log('⚠️  ENUM type might not exist yet. This is normal if the ENUM migration hasn\'t been run.');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

// Run the migration
runMigration();
