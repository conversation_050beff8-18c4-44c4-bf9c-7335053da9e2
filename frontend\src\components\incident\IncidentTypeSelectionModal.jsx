import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
import { Search } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export function IncidentTypeSelectionModal({ open, onClose, onSelect, incidentTypes = [] }) {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");

  const filteredIncidentTypes = incidentTypes.filter((type) =>
    Object.values(type).some((value) =>
      value && value.toString().toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-[90vw] md:max-w-[900px] max-h-[80vh] flex flex-col" aria-describedby="incident-type-selection-description">
        <DialogHeader>
          <DialogTitle>{t('admin.incidents.incident_type_selection.select_incident_type')}</DialogTitle>
          <p id="incident-type-selection-description" className="text-sm text-gray-500">
            {t('admin.incidents.incident_type_selection.select_an_incident_type_to_associate_with_this_incident')}
          </p>
          <div className="relative mt-4">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder={t('admin.incidents.incident_type_selection.search_incident_types')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:border-[#F62D51]"
            />
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto mt-4 pr-2">
          <div className="space-y-2">
            {filteredIncidentTypes.length > 0 ? (
              filteredIncidentTypes.map((type) => (
                <div
                  key={type.incidentTypeID || type.id || type._id}
                  className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    onSelect(type);
                    onClose();
                  }}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex-1">
                      <h3 className="font-medium">{type.name || type.title}</h3>
                      <p className="text-sm text-gray-500">{type.comment || type.description || t('admin.incidents.incident_type_selection.no_description')}</p>
                    </div>
                    <div className="flex gap-3 text-sm">
                      {type.code && (
                        <span className="px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          {t('admin.incidents.incident_type_selection.code')} {type.code}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-4 border rounded-lg text-center text-gray-500">
                {t('admin.incidents.incident_type_selection.no_incident_types_found_matching_your_search_criteria')}
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="mt-4">
          <Button type="button" variant="outline" onClick={onClose}>
            {t('admin.incidents.incident_type_selection.cancel')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
