const express = require('express');
const router = express.Router();
const actionPlanUploadController = require('../controllers/uploads/action-plan-upload-controller');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(authenticateToken);

// File upload route
router.post('/upload', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager','audit_director', 'auditor']), actionPlanUploadController.uploadFile);

// Web reference route
router.post('/reference', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager','audit_director', 'auditor']), actionPlanUploadController.addReference);

// Get all attachments (across all action plans)
router.get('/', actionPlanUploadController.getAllAttachments);

// Get all attachments for a specific action plan
router.get('/:actionPlanID', actionPlanUploadController.getAttachments);

// Delete an attachment
router.delete('/:attachmentID', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager','audit_director', 'auditor']), actionPlanUploadController.deleteAttachment);

module.exports = router;

