const { Sequelize } = require('sequelize');
const config = require('../config/database');

// Create Sequelize instance
const sequelize = new Sequelize(
  config.development.database,
  config.development.username,
  config.development.password,
  {
    host: config.development.host,
    port: config.development.port,
    dialect: config.development.dialect,
    logging: console.log
  }
);

async function runMigration() {
  try {
    console.log('Starting Control Activity Log ENUM migration...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Import and run the migration
    const migration = require('../migrations/20241230000001-update-control-activity-log-enum.js');
    
    console.log('Running migration UP...');
    await migration.up(sequelize.getQueryInterface(), Sequelize);
    
    console.log('✅ Migration completed successfully!');
    console.log('Control Activity Log ENUM type has been updated with link and unlink values.');
    
    // Verify the migration by checking the ENUM values
    const [results] = await sequelize.query(`
      SELECT unnest(enum_range(NULL::enum_ControlActivityLogs_type)) AS enum_value;
    `);
    
    console.log('✅ Current ENUM values:');
    results.forEach(row => {
      console.log(`   - ${row.enum_value}`);
    });
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

// Run the migration
runMigration();
