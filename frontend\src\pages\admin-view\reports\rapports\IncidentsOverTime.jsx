import React, { useState, useEffect, useRef } from 'react';
import { Bar } from 'react-chartjs-2';
import axios from 'axios';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { jsPDF } from "jspdf";
import * as XLSX from "https://cdn.jsdelivr.net/npm/xlsx@0.18.5/xlsx.mjs";
import { Button } from "@/components/ui/button";
import { getApiBaseUrl } from "@/utils/api-config";
import EmailReportModal from '@/components/reports/EmailReportModal';
// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const IncidentsOverTime = (props) => {
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timePeriod, setTimePeriod] = useState('month');
  const chartRef = useRef(null); // Reference to the chart instance
  const API_BASE_URL = getApiBaseUrl();
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [emailAttachment, setEmailAttachment] = useState(null);
  
  const handleEmailReport = async () => {
    if (!chartRef.current) {
      alert('Le graphique n\'est pas prêt.');
      return;
    }
    try {
      // Generate chart image
      const chartImage = chartRef.current.toBase64Image();
      const pdf = new jsPDF({ orientation: 'landscape', unit: 'in', format: 'letter' });
      const imgProps = pdf.getImageProperties(chartImage);
      const pdfWidth = pdf.internal.pageSize.getWidth() - 1;
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
      pdf.addImage(chartImage, 'JPEG', 0.5, 0.5, pdfWidth, pdfHeight);
      let yPosition = pdfHeight + 1;
      pdf.setFontSize(16);
      pdf.setTextColor(45, 55, 72);
      pdf.text('Details', 0.5, yPosition);
      yPosition += 0.5;
      pdf.setFontSize(12);
      pdf.setTextColor(113, 128, 150);
      pdf.text('Total Number of Incidents:', 0.5, yPosition);
      pdf.setTextColor(26, 41, 66);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${totalIncidents}`, 3.5, yPosition);
      yPosition += 0.5;
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(113, 128, 150);
      pdf.text('Period with Most Incidents:', 0.5, yPosition);
      pdf.setTextColor(26, 41, 66);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${maxIncidentsPeriod}`, 3.5, yPosition);
      // Get PDF as base64
      const pdfBase64 = btoa(
        new Uint8Array(pdf.output('arraybuffer'))
          .reduce((data, byte) => data + String.fromCharCode(byte), '')
      );
      setEmailAttachment({
        base64: pdfBase64,
        filename: 'incidents_over_time.pdf',
        contentType: 'application/pdf',
      });
      setIsEmailModalOpen(true);
    } catch (err) {
      alert('Erreur lors de la génération du PDF.');
    }
  };
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${API_BASE_URL}/incidentsOverTime?timePeriod=${timePeriod}`, {
          withCredentials: true,
          headers: { 'x-user-id': '1' },
        });
        const data = response.data.data;

        const labels = data.map(item => {
          const date = new Date(item.period);
          if (timePeriod === 'day') {
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
          } else if (timePeriod === 'month') {
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
          } else {
            return `${date.getFullYear()}`;
          }
        });
        const counts = data.map(item => item.incident_count);

        setChartData({
          labels,
          datasets: [
            {
              label: "Number of Incidents",
              data: counts,
              backgroundColor: '#FF0000',
              borderColor: '#FF0000',
              borderWidth: 1,
              categoryPercentage: 0.5,
              barPercentage: 0.5,
            },
          ],
        });
      } catch (err) {
        console.error('Error fetching incidents data:', err);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timePeriod]);

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: "Number of Incidents Over Time",
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: "Number of Incidents",
        },
      },
      x: {
        title: {
          display: true,
          text: timePeriod === 'day' ? 'Day' : timePeriod === 'month' ? 'Month' : 'Year',
        },
      },
    },
  };

  const handleTimePeriodChange = (e) => {
    setTimePeriod(e.target.value);
  };

  const downloadPDF = async () => {
    try {
      if (!chartRef.current) {
        console.error("Chart reference is not available.");
        alert("Error: Unable to generate PDF. The chart is not ready.");
        return;
      }

      // Get the chart as a base64 image
      const chartImage = chartRef.current.toBase64Image();
      console.log("Chart image generated successfully.");

      // Create a new jsPDF instance
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'in',
        format: 'letter'
      });

      // Calculate the dimensions for the chart image
      const imgProps = pdf.getImageProperties(chartImage);
      const pdfWidth = pdf.internal.pageSize.getWidth() - 1; // 1 inch margin
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

      // Add the chart image to the PDF
      pdf.addImage(chartImage, 'JPEG', 0.5, 0.5, pdfWidth, pdfHeight);

      // Add the Details section as text (avoiding html2canvas)
      let yPosition = pdfHeight + 1; // Start below the chart
      pdf.setFontSize(16);
      pdf.setTextColor(45, 55, 72); // RGB equivalent of #2D3748
      pdf.text('Details', 0.5, yPosition);
      yPosition += 0.5;

      pdf.setFontSize(12);
      pdf.setTextColor(113, 128, 150); // RGB equivalent of #718096
      pdf.text('Total Number of Incidents:', 0.5, yPosition);
      pdf.setTextColor(26, 41, 66); // RGB equivalent of #1A2942
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${totalIncidents}`, 3.5, yPosition);
      yPosition += 0.5;

      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(113, 128, 150); // RGB equivalent of #718096
      pdf.text('Period with Most Incidents:', 0.5, yPosition);
      pdf.setTextColor(26, 41, 66); // RGB equivalent of #1A2942
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${maxIncidentsPeriod}`, 3.5, yPosition);

      // Save the PDF
      pdf.save('incidents_over_time.pdf');
      console.log("PDF generated and downloaded successfully.");
    } catch (err) {
      console.error("Error generating PDF:", err);
      alert("Error generating PDF. Please check the console for details.");
    }
  };

  const downloadExcel = () => {
    try {
      if (!chartData) {
        console.error("Chart data is not available.");
        alert("Error: Unable to generate Excel file. The data is not ready.");
        return;
      }

      if (!XLSX) {
        console.error("XLSX library is not loaded.");
        alert("Error: Excel library is not loaded.");
        return;
      }

      const excelData = chartData.labels.map((label, index) => ({
        Period: label,
        Incidents: chartData.datasets[0].data[index],
      }));
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "IncidentsOverTime");
      XLSX.writeFile(workbook, "incidents_over_time.xlsx");
      console.log("Excel file generated successfully.");
    } catch (err) {
      console.error("Error generating Excel file:", err);
      alert("Error generating Excel file. Please check the console for details.");
    }
  };

  const totalIncidents = chartData ? chartData.datasets[0].data.reduce((sum, count) => sum + count, 0) : 0;
  const maxIncidentsPeriod = chartData && chartData.datasets[0].data.length > 0
    ? chartData.labels[chartData.datasets[0].data.indexOf(Math.max(...chartData.datasets[0].data))]
    : "N/A";

  if (loading) return <div className="p-6 text-center">Loading...</div>;
  if (error) return <div className="p-6 text-center text-red-500">{error}</div>;

  return (
    <div className="p-6 bg-white rounded-lg shadow border border-gray-200">
      <div>
        <div className="mb-4">
          <label htmlFor="timePeriod" className="mr-2">Group by:</label>
          <select
            id="timePeriod"
            value={timePeriod}
            onChange={handleTimePeriodChange}
            className="border rounded p-1"
          >
            <option value="day">Day</option>
            <option value="month">Month</option>
            <option value="year">Year</option>
          </select>
        </div>
        <Bar ref={chartRef} data={chartData} options={options} />
      </div>
      <div className="flex gap-4 mt-4">
        <Button onClick={downloadPDF} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">
          Download PDF
        </Button>
        <Button onClick={downloadExcel} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">
          Download Excel
        </Button>
        <Button
          onClick={handleEmailReport}
          className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
          </svg>
          Send via Email
        </Button>
      </div>
      <div className="mt-6 bg-gray-50 rounded-xl p-4 border border-gray-200">
        <h3 className="text-lg font-semibold mb-3 text-gray-800">Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <p className="text-sm text-gray-500 mb-1">Total Number of Incidents</p>
            <p className="text-xl font-bold text-[#1A2942]">{totalIncidents}</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <p className="text-sm text-gray-500 mb-1">Period with Most Incidents</p>
            <p className="text-xl font-bold text-[#1A2942]">{maxIncidentsPeriod}</p>
          </div>
        </div>
      </div>
      <EmailReportModal
        isOpen={isEmailModalOpen}
        onClose={() => setIsEmailModalOpen(false)}
        reportType="incidents-over-time"
        reportTitle="Number of Incidents Over Time"
        defaultAttachment={emailAttachment}
      />
    </div>
  );
};

export default IncidentsOverTime;
