const { User } = require('../models');
const bcrypt = require('bcryptjs');

async function seedUsers() {
  try {
    // Create admin user
    const adminPassword = await bcrypt.hash('<EMAIL>', 10);
    const admin = {
      username: 'admin',
      email: '<EMAIL>',
      password: adminPassword,
      role: 'admin'
    };

    // Create superadmin user
    const superadminPassword = await bcrypt.hash('<EMAIL>', 10);
    const superadmin = {
      username: 'superadmin',
      email: '<EMAIL>',
      password: superadminPassword,
      role: 'super_admin'
    };

    await User.bulkCreate([admin, superadmin]);
    console.log('Successfully seeded admin and superadmin users');

    // Log credentials for reference
    console.log('\nCreated users:');
    console.log('Admin credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: <EMAIL>');
    console.log('\nSuperadmin credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: <EMAIL>');

  } catch (error) {
    console.error('Error seeding users:', error);
  } finally {
    process.exit();
  }
}

// Run the seeding function
seedUsers();