const { User, RiskContributor, IncidentContributor } = require('../models');

/**
 * Middleware to check if a user is a contributor to a risk or has a higher role
 *
 * @param {Array} higherRoles - Array of role codes that bypass contributor check
 * @returns {Function} Express middleware
 */
exports.authorizeRiskContributor = (higherRoles = ['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']) => {
  return async (req, res, next) => {
    try {
      // Get user ID from multiple possible sources
      const userId = req.user.id || req.user.userId || req.user._id;

      if (!userId) {
        console.error('User ID not found in request:', req.user);
        return res.status(400).json({
          success: false,
          message: 'User ID not found in request'
        });
      }

      const riskId = req.params.riskId;

      // Ensure user roles are loaded
      if (!req.user.roles) {
        console.log(`User ${userId} roles not loaded, loading now...`);
        // Load user with roles
        const { User, Role } = require('../models');
        const user = await User.findByPk(userId, {
          include: [
            {
              model: Role,
              as: 'roles',
              through: { attributes: [] }
            }
          ]
        });

        if (user && user.roles) {
          req.user.roles = user.roles.map(role => ({
            id: role.id,
            name: role.name,
            code: role.code
          }));
          console.log(`Loaded ${req.user.roles.length} roles for user ${userId}`);
        } else {
          console.log(`No roles found for user ${userId}`);
          req.user.roles = [];
        }
      }

      // Check for legacy role
      if (req.user.role === 'super_admin' || req.user.role === 'admin') {
        console.log(`User ${userId} has legacy admin role (${req.user.role}), bypassing contributor check for risk`);
        return next();
      }

      // Skip check for higher roles
      if (req.user.roles && req.user.roles.length > 0) {
        // Check if user has any of the higher roles
        const hasHigherRole = req.user.roles.some(role => {
          // Check role.code directly
          if (higherRoles.includes(role.code)) {
            console.log(`User ${userId} has admin role code: ${role.code}`);
            return true;
          }

          // Also check role.name for backward compatibility
          if (role.name) {
            const normalizedRoleName = role.name.toLowerCase().replace(/\s+/g, '_');
            const hasMatchingRole = higherRoles.some(higherRole =>
              normalizedRoleName.includes(higherRole) ||
              higherRole.includes(normalizedRoleName)
            );

            if (hasMatchingRole) {
              console.log(`User ${userId} has admin role name: ${role.name}`);
              return true;
            }
          }

          return false;
        });

        if (hasHigherRole) {
          console.log(`User ${userId} has admin role, bypassing contributor check for risk`);
          return next();
        }
      }

      // Check if user is a contributor to this risk
      const isContributor = await RiskContributor.findOne({
        where: {
          risk_id: riskId,
          user_id: userId
        }
      });

      if (isContributor) {
        return next();
      }

      return res.status(403).json({
        success: false,
        message: 'Access denied: You are not authorized as a contributor for this risk'
      });
    } catch (error) {
      console.error('Error in authorizeRiskContributor middleware:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  };
};

/**
 * Middleware to check if a user is a contributor to an incident or has a higher role
 *
 * @param {Array} higherRoles - Array of role codes that bypass contributor check
 * @returns {Function} Express middleware
 */
exports.authorizeIncidentContributor = (higherRoles = ['grc_admin', 'grc_manager', 'incident_manager', 'risk_manager']) => {
  return async (req, res, next) => {
    try {
      // Get user ID from multiple possible sources
      const userId = req.user.id || req.user.userId || req.user._id;

      if (!userId) {
        console.error('User ID not found in request:', req.user);
        return res.status(400).json({
          success: false,
          message: 'User ID not found in request'
        });
      }

      const incidentId = req.params.incidentId;

      // Ensure user roles are loaded
      if (!req.user.roles) {
        console.log(`User ${userId} roles not loaded, loading now...`);
        // Load user with roles
        const { User, Role } = require('../models');
        const user = await User.findByPk(userId, {
          include: [
            {
              model: Role,
              as: 'roles',
              through: { attributes: [] }
            }
          ]
        });

        if (user && user.roles) {
          req.user.roles = user.roles.map(role => ({
            id: role.id,
            name: role.name,
            code: role.code
          }));
          console.log(`Loaded ${req.user.roles.length} roles for user ${userId}`);
        } else {
          console.log(`No roles found for user ${userId}`);
          req.user.roles = [];
        }
      }

      // Check for legacy role
      if (req.user.role === 'super_admin' || req.user.role === 'admin') {
        console.log(`User ${userId} has legacy admin role (${req.user.role}), bypassing contributor check`);
        return next();
      }

      // Skip check for higher roles
      if (req.user.roles && req.user.roles.length > 0) {
        // Check if user has any of the higher roles
        const hasHigherRole = req.user.roles.some(role => {
          // Check role.code directly
          if (higherRoles.includes(role.code)) {
            console.log(`User ${userId} has admin role code: ${role.code}`);
            return true;
          }

          // Also check role.name for backward compatibility
          if (role.name) {
            const normalizedRoleName = role.name.toLowerCase().replace(/\s+/g, '_');
            const hasMatchingRole = higherRoles.some(higherRole =>
              normalizedRoleName.includes(higherRole) ||
              higherRole.includes(normalizedRoleName)
            );

            if (hasMatchingRole) {
              console.log(`User ${userId} has admin role name: ${role.name}`);
              return true;
            }
          }

          return false;
        });

        if (hasHigherRole) {
          console.log(`User ${userId} has admin role, bypassing contributor check`);
          return next();
        }
      }

      // Check if user is a contributor to this incident
      const isContributor = await IncidentContributor.findOne({
        where: {
          incident_id: incidentId,
          user_id: userId
        }
      });

      if (isContributor) {
        return next();
      }

      return res.status(403).json({
        success: false,
        message: 'Access denied: You are not authorized as a contributor for this incident'
      });
    } catch (error) {
      console.error('Error in authorizeIncidentContributor middleware:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  };
};