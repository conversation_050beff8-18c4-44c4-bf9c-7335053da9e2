import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, ArrowUpDown, Trash2, Loader2, Shield<PERSON><PERSON>ck } from 'lucide-react';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { hasPermission } from '@/store/auth-slice';
import PageHeader from "@/components/ui/page-header";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import TablePagination from "@/components/ui/table-pagination";
import FilterPanel from "@/components/ui/filter-panel";
import { toast } from "react-hot-toast";
import axios from 'axios';
import { getApiBaseUrl } from "@/utils/api-config";
import controlIcon from '@/assets/control.png';

function ControlsManagement() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const canCreate = useSelector(state => hasPermission(state, 'create'));
  const canDelete = useSelector(state => hasPermission(state, 'delete'));
  const [searchQuery, setSearchQuery] = useState('');
  const [controls, setControls] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [selectedControls, setSelectedControls] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [isOpen, setIsOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [activeFilters, setActiveFilters] = useState({
    testingFrequency: "all",
    sampleType: "all",
    testingMethod: "all"
  });
  const API_BASE_URL = getApiBaseUrl();
  const initialFormState = {
    name: '',
    code: '',
    controlKey: '',
    controlExecutionMethod: '',
  };

  const [newControl, setNewControl] = useState(initialFormState);

  // Translation mappings for dropdown values
  const testingFrequencyMap = {
    "Quarterly": "Trimestriel",
    "Bi-Yearly": "Semestriel",
    "Yearly": "Annuel"
  };

  const sampleTypeMap = {
    "Command": "Commande",
    "Bill": "Facture",
    "Contract": "Contrat"
  };

  const testingMethodMap = {
    "Observation": "Observation",
    "Inquiry": "Enquête",
    "Inspection": "Inspection",
    "Re-Performance": "Re-exécution"
  };

  // Helper function to get French display name
  const getDisplayName = (value, mapping) => {
    return mapping[value] || value || '-';
  };

  const fetchControls = useCallback(async () => {
    try {
      setLoading(true);
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);

      const response = await axios.get(`${API_BASE_URL}/controls`, {
        withCredentials: true,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.data.success) {
        setControls(response.data.data || []);
        setError(null);
      } else {
        throw new Error(response.data.message || t('admin.controls.management.error', 'Failed to fetch controls'));
      }
    } catch (error) {
      console.error('Error fetching controls:', error);
      if (error.code !== 'ERR_CANCELED' && error.code !== 'ECONNABORTED') {
        toast.error(error.message || t('admin.controls.management.error', 'Failed to fetch controls. Please refresh the page.'));
      }
      if (controls.length === 0) {
        setControls([]);
      }
      setError(error.message || t('admin.controls.management.error', 'An error occurred while fetching controls'));
    } finally {
      setLoading(false);
    }
  }, [API_BASE_URL, controls.length, t]);

  useEffect(() => {
    fetchControls();
  }, [fetchControls]);

  const handleSort = (key) => {
    setSortConfig((prevConfig) => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedControls(currentControls.map(control => control.controlID));
    } else {
      setSelectedControls([]);
    }
  };

  const handleSelectControl = (id) => {
    setSelectedControls(prev => prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]);
  };

  const handleRowClick = (id) => {
    const path = window.location.pathname;
    if (path.includes('/audit')) {
      navigate(`/audit/controls/edit/${id}/overview`);
    } else {
      navigate(`/admin/controls/edit/${id}/overview`);
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedControls.length === 0) {
      toast.error(t('admin.controls.management.no_controls', 'No controls selected'));
      return;
    }

    if (window.confirm(t('admin.controls.management.delete_confirm', { count: selectedControls.length }))) {
      try {
        setLoading(true);
        let failedDeletions = [];

        const attemptDelete = async (id, retries = 2) => {
          for (let attempt = 0; attempt <= retries; attempt++) {
            try {
              await axios.delete(`${API_BASE_URL}/controls/${id}`, { withCredentials: true, timeout: 30000 });
              return { success: true };
            } catch (error) {
              if (attempt === retries) {
                let errorMessage = error.response?.data?.message || error.message || 'Unknown error';
                return { success: false, error: errorMessage };
              }
              await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
            }
          }
        };

        const results = await Promise.all(selectedControls.map(id => attemptDelete(id)));
        failedDeletions = results.filter(r => !r.success).map((r, idx) => ({ id: selectedControls[idx], error: r.error }));

        await fetchControls();
        setSelectedControls([]);

        if (failedDeletions.length > 0) {
          failedDeletions.forEach(item => {
            toast.error(t('admin.controls.management.delete_failed', { id: item.id, error: item.error }), {
              duration: 5000,
              position: "top-center",
              style: { background: '#f8d7da', color: '#721c24', border: '1px solid #f5c6cb', padding: '12px' },
            });
          });
        } else {
          toast.success(t('admin.controls.management.delete_success', 'Selected controls deleted successfully'));
        }
      } catch (error) {
        console.error("Error in deletion process:", error);
        toast.error(error.message || t('admin.controls.management.delete_error', 'An error occurred during the deletion process'));
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!newControl.name) {
      toast.error('Le nom est requis');
      return;
    }
    if (!newControl.controlKey) {
      toast.error('Le contrôle clé est requis');
      return;
    }
    if (!newControl.controlExecutionMethod) {
      toast.error('La méthode d\'exécution est requise');
      return;
    }

    try {
      setSubmitting(true);
      const controlToCreate = {
        controlID: `CTL_${Date.now()}`,
        name: newControl.name.trim(),
        code: newControl.code ? newControl.code.trim() : null,
        controlKey: parseInt(newControl.controlKey, 10),
        controlExecutionMethod: newControl.controlExecutionMethod,
      };

      const response = await axios.post(`${API_BASE_URL}/controls`, controlToCreate, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
        timeout: 30000,
      });

      if (response.data.success) {
        setControls(prevControls => [...prevControls, response.data.data]);
        setNewControl(initialFormState);
        setIsOpen(false);
        toast.success('Contrôle créé avec succès');
      }
    } catch (error) {
      console.error('Error creating control:', error);
      let errorMessage = error.response?.data?.message || error.message || 'Échec de la création du contrôle';
      if (errorMessage.includes('invalid input syntax for type integer')) {
        toast.error('Le contrôle clé doit être 0 ou 1.', {
          duration: 6000,
          position: "top-center",
          style: { background: '#f8d7da', color: '#721c24', border: '1px solid #f5c6cb', padding: '16px', maxWidth: '80%', textAlign: 'center' },
        });
      } else {
        toast.error(errorMessage);
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleFilterChange = (filterName, value) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleClearFilters = () => {
    setActiveFilters({
      testingFrequency: "all",
      sampleType: "all",
      testingMethod: "all"
    });
  };

  const filteredControls = controls.filter(control => {
    // Apply search filter
    const matchesSearch = Object.values(control).some(value =>
      value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
    );

    // Apply active filters
    const matchesTestingFrequency = activeFilters.testingFrequency === "all" ||
      control.testingFrequency === activeFilters.testingFrequency;
    const matchesSampleType = activeFilters.sampleType === "all" ||
      control.sampleType === activeFilters.sampleType;
    const matchesTestingMethod = activeFilters.testingMethod === "all" ||
      control.testingMethod === activeFilters.testingMethod;

    return matchesSearch && matchesTestingFrequency && matchesSampleType && matchesTestingMethod;
  });

  const sortedControls = [...filteredControls].sort((a, b) => {
    if (sortConfig.key) {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];
      if (sortConfig.direction === 'asc') return aValue > bValue ? 1 : -1;
      return aValue < bValue ? 1 : -1;
    }
    return 0;
  });

  const totalPages = Math.ceil(sortedControls.length / itemsPerPage);
  const indexOfFirstItem = (currentPage - 1) * itemsPerPage;
  const indexOfLastItem = indexOfFirstItem + itemsPerPage;
  const currentControls = sortedControls.slice(indexOfFirstItem, indexOfLastItem);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) setCurrentPage(page);
  };

  if (loading) return (
    <div className="p-6 flex justify-center items-center h-screen">
      <div className="flex flex-col items-center">
        <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
        <span className="mt-4 text-gray-600">{t('admin.controls.management.loading', 'Loading controls...')}</span>
      </div>
    </div>
  );
  if (error) return <div className="p-6 text-red-500">{t('admin.controls.management.error', { message: error })}</div>;

  const columns = [
    { key: 'name', label: t('admin.controls.management.columns.name', 'Name'), sortable: true },
    { key: 'code', label: t('admin.controls.management.columns.code', 'Code'), sortable: true },
    { key: 'controlKey', label: t('admin.controls.management.columns.control_key', 'Control Key'), sortable: true },
    { key: 'testingFrequency', label: t('admin.controls.management.columns.testing_frequency', 'Testing Frequency'), sortable: true },
    { key: 'sampleType', label: t('admin.controls.management.columns.sample_type', 'Sample Type'), sortable: true },
    { key: 'testingMethod', label: t('admin.controls.management.columns.testing_method', 'Testing Method'), sortable: true },
  ];

  const filters = [
    {
      id: 'testingFrequency',
      label: t('admin.controls.management.filters.testing_frequency', 'Testing Frequency'),
      component: (
        <Select
          value={activeFilters.testingFrequency}
          onValueChange={(value) => handleFilterChange('testingFrequency', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder={t('admin.controls.management.filters.select_frequency', 'Select frequency')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="Quarterly">Quarterly</SelectItem>
            <SelectItem value="Bi-yearly">Bi-yearly</SelectItem>
            <SelectItem value="Yearly">Yearly</SelectItem>
          </SelectContent>
        </Select>
      )
    },
    {
      id: 'sampleType',
      label: t('admin.controls.management.filters.sample_type', 'Sample Type'),
      component: (
        <Select
          value={activeFilters.sampleType}
          onValueChange={(value) => handleFilterChange('sampleType', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder={t('admin.controls.management.filters.select_type', 'Select type')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="Command">Command</SelectItem>
            <SelectItem value="Bill">Bill</SelectItem>
            <SelectItem value="Contract">Contract</SelectItem>
          </SelectContent>
        </Select>
      )
    },
    {
      id: 'testingMethod',
      label: t('admin.controls.management.filters.testing_method', 'Testing Method'),
      component: (
        <Select
          value={activeFilters.testingMethod}
          onValueChange={(value) => handleFilterChange('testingMethod', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder={t('admin.controls.management.filters.select_method', 'Select method')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="Observation">Observation</SelectItem>
            <SelectItem value="Inquiry">Inquiry</SelectItem>
            <SelectItem value="Inspection">Inspection</SelectItem>
            <SelectItem value="Re-performance">Re-performance</SelectItem>
          </SelectContent>
        </Select>
      )
    }
  ];

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      <PageHeader
        title={t('admin.controls.management.title', 'Controls Management')}
        description={t('admin.controls.management.description', 'Define and manage controls within your organization.')}
        section={t('admin.controls.title', 'Controls')}
        currentPage={t('admin.controls.title', 'Controls')}
        searchPlaceholder={t('admin.controls.management.search_placeholder', 'Search controls...')}
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
        icon={ShieldCheck}
      />

      <FilterPanel
        filters={filters}
        activeFilters={activeFilters}
        onClearFilters={handleClearFilters}
        className="mb-4"
      />

      <div className="flex justify-end items-center gap-3 mb-4">
        {selectedControls.length > 0 && (
          <Button
            variant="outline"
            className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
              canDelete && !loading
                ? 'bg-[#F62D51]/90 text-white hover:bg-[#F62D51] hover:shadow-md'
                : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
            } flex items-center gap-2 px-6 py-2 font-semibold`}
            onClick={handleDeleteSelected}
            disabled={!canDelete || loading}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                {t('admin.controls.management.buttons.deleting', 'Deleting...')}
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                {t('admin.controls.management.buttons.delete', 'Delete')} ({selectedControls.length})
              </>
            )}
          </Button>
        )}
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          {canCreate && (
            <DialogTrigger asChild>
              <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2">
                <Plus className="h-4 w-4" />
                {t('admin.controls.management.buttons.add', 'Add Control')}
              </Button>
            </DialogTrigger>
          )}
          <DialogContent className="sm:max-w-[600px] p-6">
            <DialogHeader>
              <DialogTitle>Créer un Nouveau Contrôle</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nom <span className="text-red-500">*</span></Label>
                <Input
                  required
                  id="name"
                  value={newControl.name}
                  onChange={(e) => setNewControl({ ...newControl, name: e.target.value })}
                  className="h-9 text-sm"
                  placeholder="Entrer le nom du contrôle"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="code">Code</Label>
                <Input
                  id="code"
                  value={newControl.code}
                  onChange={(e) => setNewControl({ ...newControl, code: e.target.value })}
                  className="h-9 text-sm"
                  placeholder="Entrer le code du contrôle"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="controlKey">Contrôle Clé <span className="text-red-500">*</span></Label>
                <Select
                  value={newControl.controlKey}
                  onValueChange={(value) => setNewControl({ ...newControl, controlKey: value })}
                  required
                >
                  <SelectTrigger className="h-9 text-sm">
                    <SelectValue placeholder="Sélectionner le contrôle clé" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Non</SelectItem>
                    <SelectItem value="1">Oui</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="controlExecutionMethod">Méthode d'Exécution <span className="text-red-500">*</span></Label>
                <Select
                  value={newControl.controlExecutionMethod}
                  onValueChange={(value) => setNewControl({ ...newControl, controlExecutionMethod: value })}
                  required
                >
                  <SelectTrigger className="h-9 text-sm">
                    <SelectValue placeholder="Sélectionner la méthode d'exécution" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Observation">Observation</SelectItem>
                    <SelectItem value="Exhaustive">Exhaustif</SelectItem>
                    <SelectItem value="Exception Control">Contrôle d'Exception</SelectItem>
                    <SelectItem value="By Sample">Par Échantillon</SelectItem>
                    <SelectItem value="Standard Control">Contrôle Standard</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-4 mt-6">
                <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                  Annuler
                </Button>
                <Button type="submit" className="bg-[#F62D51] hover:bg-red-700" disabled={submitting}>
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Création...
                    </>
                  ) : (
                    "Créer le Contrôle"
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                  <Checkbox
                    checked={selectedControls.length === currentControls.length && currentControls.length > 0}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all"
                  />
                </th>
                {columns.map(column => (
                  <th
                    key={column.key}
                    className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100"
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      {sortConfig.key === column.key && <ArrowUpDown className="w-4 h-4" />}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {currentControls.map((control, index) => (
                <tr
                  key={control.controlID}
                  className={`hover:bg-gray-50 cursor-pointer ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                  onClick={() => handleRowClick(control.controlID)}
                >
                  <td className="px-6 py-4">
                    <Checkbox
                      checked={selectedControls.includes(control.controlID)}
                      onCheckedChange={() => handleSelectControl(control.controlID)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-[#242A33] whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <img src={controlIcon} alt="Control" className="w-5 h-5" />
                      {control.name}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">{control.code || '-'}</td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">{control.controlKey === 1 ? 'Oui' : control.controlKey === 0 ? 'Non' : '-'}</td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">{getDisplayName(control.testingFrequency, testingFrequencyMap)}</td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">{getDisplayName(control.sampleType, sampleTypeMap)}</td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">{getDisplayName(control.testingMethod, testingMethodMap)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <TablePagination
        currentPage={currentPage}
        totalPages={totalPages}
        itemsPerPage={itemsPerPage}
        totalItems={sortedControls.length}
        onPageChange={handlePageChange}
        onItemsPerPageChange={(value) => {
          setItemsPerPage(value);
          setCurrentPage(1);
        }}
        startIndex={indexOfFirstItem}
        endIndex={indexOfLastItem}
      />
    </div>
  );
}

export default ControlsManagement;