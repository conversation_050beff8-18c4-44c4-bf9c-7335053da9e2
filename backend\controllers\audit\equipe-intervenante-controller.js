const db = require('../../models');
const EquipeIntervenante = db.EquipeIntervenante;
const User = db.User;
const AuditMission = db.AuditMission;
const AuditActivity = db.AuditActivity;
const AuditConstat = db.AuditConstat;
const AuditRecommendation = db.AuditRecommendation;
const { v4: uuidv4 } = require('uuid');

// Create a new team member
const createEquipeIntervenante = async (req, res) => {
  try {
    const { userId, auditMissionId, auditActivityId, auditConstatId, auditRecommendationId, chefdemission } = req.body;
    if (!userId || !auditMissionId) {
      return res.status(400).json({ success: false, message: 'userId and auditMissionId are required' });
    }
    if (chefdemission === 'oui') {
      // Ensure only one chef per mission
      const existingChef = await EquipeIntervenante.findOne({ where: { auditMissionId, chefdemission: 'oui' } });
      if (existingChef) {
        return res.status(400).json({ success: false, message: 'There is already a chef de mission for this mission' });
      }
    }
    const equipe = await EquipeIntervenante.create({
      id: `EQI_${uuidv4().substring(0, 8)}`,
      userId,
      auditMissionId,
      auditActivityId: auditActivityId || null,
      auditConstatId: auditConstatId || null,
      auditRecommendationId: auditRecommendationId || null,
      chefdemission: chefdemission || 'non'
    });
    return res.status(201).json({ success: true, data: equipe });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Failed to create equipe intervenante', error: error.message });
  }
};

// Get all team members (optionally filter by mission/activity/constat/recommendation)
const getEquipeIntervenantes = async (req, res) => {
  console.log('EquipeIntervenante GET called', req.query);
  try {
    const { auditMissionId, auditActivityId, auditConstatId, auditRecommendationId } = req.query;
    if (!auditMissionId) {
      return res.status(400).json({ success: false, message: 'auditMissionId is required' });
    }
    const where = { auditMissionId };
    if (auditActivityId) where.auditActivityId = auditActivityId;
    if (auditConstatId) where.auditConstatId = auditConstatId;
    if (auditRecommendationId) where.auditRecommendationId = auditRecommendationId;
    const equipes = await db.EquipeIntervenante.findAll({
      where,
      include: [
        { model: db.User, as: 'user', attributes: ['id', 'username', 'email'] }
      ]
    });
    return res.status(200).json({ success: true, data: equipes });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Failed to fetch equipe intervenante', error: error.message });
  }
};

// Update a team member (e.g., change chefdemission)
const updateEquipeIntervenante = async (req, res) => {
  try {
    const { id } = req.params;
    const { chefdemission, auditActivityId, auditConstatId, auditRecommendationId } = req.body;
    const equipe = await db.EquipeIntervenante.findByPk(id);
    if (!equipe) return res.status(404).json({ success: false, message: 'EquipeIntervenante not found' });

    // Update fields if provided
    if (chefdemission !== undefined) equipe.chefdemission = chefdemission;
    if (auditActivityId !== undefined) equipe.auditActivityId = auditActivityId;
    if (auditConstatId !== undefined) equipe.auditConstatId = auditConstatId;
    if (auditRecommendationId !== undefined) equipe.auditRecommendationId = auditRecommendationId;

    await equipe.save();
    return res.status(200).json({ success: true, data: equipe });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Failed to update equipe intervenante', error: error.message });
  }
};

// Delete a team member
const deleteEquipeIntervenante = async (req, res) => {
  try {
    const { id } = req.params;
    const equipe = await EquipeIntervenante.findByPk(id);
    if (!equipe) return res.status(404).json({ success: false, message: 'EquipeIntervenante not found' });
    await equipe.destroy();
    return res.status(200).json({ success: true, message: 'EquipeIntervenante deleted' });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Failed to delete equipe intervenante', error: error.message });
  }
};

module.exports = {
  createEquipeIntervenante,
  getEquipeIntervenantes,
  updateEquipeIntervenante,
  deleteEquipeIntervenante
}; 