import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import {
  LayoutDashboard,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Home,
  ShieldCheck,
  FileText,
  ChevronsUpDown,
  Zap,
  AlertTriangle,
  List,
  Plus,
  FileType,
  Sparkles,
  Shield,
  FolderTree,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import logo2 from "../../assets/whitelogovitalis.png";
import bpsIcon from '@/assets/BPS.png';
import orgIcon from '@/assets/org.png';
import operationIcon from '@/assets/operation.png';

// Import translation hook
import { useTranslation } from 'react-i18next';

// Define the audit sidebar menu items as a function to use translations
const getAuditSidebarMenuItems = (t) => [
  {
    id: "home",
    label: t("audit.sidebar.home", "Home"),
    path: "/audit/welcome",
    icon: <Home className="h-5 w-5" />,
  },
  {
    id: "dashboard",
    label: t("audit.sidebar.dashboard", "Dashboard"),
    path: "/audit/dashboard",
    icon: <LayoutDashboard className="h-5 w-5" />,
  },
  {
    id: "audit",
    label: t("audit.sidebar.audit", "Audit"),
    icon: <ShieldCheck className="h-5 w-5" />,
    submenu: [
      {
        id: "plans-daudit",
        label: t("audit.sidebar.plans_daudit", "Plans d'audit"),
        path: "/audit/plans-daudit",
        icon: <FileText className="h-4 w-4" />,
      },
    ],
  },
  {
    id: "incidents",
    label: t("audit.sidebar.incidents", "Incidents"),
    icon: <Zap className="h-5 w-5" />,
    submenu: [
      {
        id: "incidents-list",
        label: t("audit.sidebar.incidents_list", "Incidents List"),
        path: "/audit/incident",
        icon: <List className="h-4 w-4" />,
      },
      {
        id: "create-incident",
        label: t("audit.sidebar.create_incident", "Create Incident"),
        path: "/audit/incident/add",
        icon: <Plus className="h-4 w-4" />,
      },
    ],
  },
  {
    id: "risks",
    label: t("audit.sidebar.risks", "Risks"),
    icon: <AlertTriangle className="h-5 w-5" />,
    submenu: [
      {
        id: "risks-list",
        label: t("audit.sidebar.risks_list", "Risks List"),
        path: "/audit/risks",
        icon: <List className="h-4 w-4" />,
      },
    ],
  },
  {
    id: "controls",
    label: t("audit.sidebar.controls", "Controls"),
    path: "/audit/controls",
    icon: <Shield className="h-5 w-5" />,
  },
  {
    id: "processes",
    label: t("audit.sidebar.processes", "Processes"),
    icon: <img src={bpsIcon} className="sidebar-icon" alt="processes" />,
    submenu: [
      {
        id: "tree-view",
        label: t("audit.sidebar.tree_view", "Tree View"),
        path: "/audit/processes/tree-view",
        icon: <FolderTree className="h-4 w-4" />,
      },
      {
        id: "business-processes",
        label: t("audit.sidebar.business_processes", "Business Processes"),
        path: "/audit/processes/business-processes",
        icon: <img src={bpsIcon} className="sidebar-icon-sm" alt="business processes" />,
      },
      {
        id: "organizational-processes",
        label: t("audit.sidebar.organizational_processes", "Org. Processes"),
        path: "/audit/processes/organizational-processes",
        icon: <img src={orgIcon} className="sidebar-icon-sm" alt="organizational processes" />,
      },
      {
        id: "operations",
        label: t("audit.sidebar.operations", "Operations"),
        path: "/audit/processes/operations",
        icon: <img src={operationIcon} className="sidebar-icon-sm" alt="operations" />,
      },
    ],
  },
];

function MenuItem({ item, isActive, onClick, isCollapsed }) {
  const [isOpen, setIsOpen] = React.useState(false);
  const hasSubmenu = item.submenu && item.submenu.length > 0;
  const location = useLocation();

  const isSubmenuActive = hasSubmenu && item.submenu.some(subItem =>
    location.pathname.startsWith(subItem.path)
  );

  React.useEffect(() => {
    if (isSubmenuActive) {
      setIsOpen(true);
    }
  }, [isSubmenuActive]);

  if (hasSubmenu) {
    if (isCollapsed) {
      return (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-center px-2 text-white hover:bg-white hover:text-[#242A33]",
                isSubmenuActive && "bg-red-500 text-white hover:bg-red-500 hover:text-white"
              )}
              title={item.label}
            >
              {item.icon}
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="bg-[#242A33] border-[#3A424E] p-2 w-48"
            side="right"
            align="start"
          >
            <div className="flex flex-col space-y-1">
              <div className="text-white font-medium px-2 py-1 border-b border-[#3A424E] mb-1">
                {item.label}
              </div>
              {item.submenu.map((subItem) => (
                <Button
                  key={subItem.id}
                  variant="ghost"
                  className={cn(
                    "w-full justify-start text-white hover:bg-white hover:text-[#242A33]",
                    isActive(subItem.path) && "bg-red-500 text-white hover:bg-red-500 hover:text-white"
                  )}
                  onClick={() => onClick(subItem.path)}
                >
                  {subItem.icon && <span className="mr-2">{subItem.icon}</span>}
                  {subItem.label}
                </Button>
              ))}
            </div>
          </PopoverContent>
        </Popover>
      );
    }

    return (
      <Collapsible
        open={isOpen}
        onOpenChange={setIsOpen}
        className="w-full"
      >
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-between text-white hover:bg-white hover:text-[#242A33]"
            )}
          >
            <div className="flex items-center gap-2">
              {item.icon}
              <span>{item.label}</span>
            </div>
            <ChevronDown
              className={cn("h-4 w-4 transition-transform", {
                "transform rotate-180": isOpen || isSubmenuActive,
              })}
            />
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="transition-all duration-200 ease-in-out">
          <div className="pt-1 pb-2">
            {item.submenu.map((subItem) => (
              <Button
                key={subItem.id}
                variant="ghost"
                className={cn(
                  "w-full pl-9 justify-start text-white hover:bg-white hover:text-[#242A33]",
                  isActive(subItem.path) && "bg-red-500 text-white hover:bg-red-500 hover:text-white"
                )}
                onClick={() => onClick(subItem.path)}
              >
                {subItem.icon && <span className="mr-2">{subItem.icon}</span>}
                {subItem.label}
              </Button>
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    );
  }

  return (
    <Button
      variant="ghost"
      className={cn(
        "w-full text-white hover:bg-white hover:text-[#242A33]",
        isActive(item.path) && "bg-red-500 text-white hover:bg-red-500 hover:text-white",
        isCollapsed ? "justify-center px-2" : "justify-start"
      )}
      onClick={() => onClick(item.path)}
      title={isCollapsed ? item.label : undefined}
    >
      {item.icon}
      {!isCollapsed && <span className="ml-2">{item.label}</span>}
    </Button>
  );
}

function AuditSideBar({ open, setOpen, onCollapseChange }) {
  const navigate = useNavigate();
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Load collapsed state from localStorage on component mount
  useEffect(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState !== null) {
      setIsCollapsed(savedState === 'true');
    }
  }, []);

  // Save collapsed state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', isCollapsed);
    // Notify parent component about the collapse state change
    if (onCollapseChange) {
      onCollapseChange(isCollapsed);
    }
  }, [isCollapsed, onCollapseChange]);

  const toggleCollapse = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);

    // Update localStorage directly for immediate effect
    localStorage.setItem('sidebarCollapsed', newState);

    // Dispatch custom event to notify other components
    window.dispatchEvent(new Event('sidebarStateChanged'));

    // Notify parent component directly if callback exists
    if (onCollapseChange) {
      onCollapseChange(newState);
    }
  };

  // Check if a path is active
  const isActive = (path) => {
    // Exact match for dashboard
    if (path === "/audit/dashboard") {
      return location.pathname === path;
    }

    // Special case for welcome page
    if (path === "/audit/welcome") {
      return location.pathname === path;
    }

    // For other pages, check if the current path starts with the menu path
    return location.pathname.startsWith(path);
  };

  return (
    <>
      {/* Mobile Sidebar */}
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetContent
          side="left"
          className="w-64 bg-[#242A33] text-white border-[#3A424E] p-0 overflow-hidden flex flex-col"
        >
          <SidebarContent
            navigate={navigate}
            isActive={isActive}
            setOpen={setOpen}
            isCollapsed={false} // Always expanded in mobile view
          />
        </SheetContent>
      </Sheet>

      {/* Desktop Sidebar */}
      <aside
        className={cn(
          "hidden lg:flex flex-col border-r border-[#3A424E] bg-[#242A33] h-screen fixed transition-all duration-300 overflow-visible",
          isCollapsed ? "w-16" : "w-64"
        )}
        style={{ zIndex: 30 }}
      >
        <SidebarContent
          navigate={navigate}
          isActive={isActive}
          isCollapsed={isCollapsed}
          toggleCollapse={toggleCollapse}
        />
      </aside>
    </>
  );
}

function SidebarContent({ navigate, isActive, isCollapsed, toggleCollapse }) {
  // Get user data from Redux store
  const user = useSelector((state) => state.auth.user);
  // Get translation function
  const { t, i18n } = useTranslation();
  // Get translated menu items
  const [sidebarMenuItems, setSidebarMenuItems] = useState(getAuditSidebarMenuItems(t));

  // Update menu items when language changes
  useEffect(() => {
    setSidebarMenuItems(getAuditSidebarMenuItems(t));
  }, [t, i18n.language]);

  return (
    <div className="flex flex-col h-full">
      {/* Logo and Collapse Toggle */}
      <div className="p-3 border-b border-[#3A424E] flex justify-between items-center h-[60px]">
        <div
          onClick={() => navigate("/audit/dashboard")}
          className="cursor-pointer flex items-center justify-center w-full"
        >
          <img src={logo2} alt="Logo" className="h-auto w-auto max-h-10" />
        </div>
        {toggleCollapse && (
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleCollapse}
            className={cn(
              "absolute text-white rounded-full shadow-md",
              "w-8 h-8 flex items-center justify-center",
              "bg-[#1E2329] hover:bg-[#2A3038] border border-[#3A424E]",
              "transition-all duration-200 hover:scale-110",
              "right-[-15px]"
            )}
            style={{
              transform: "translateX(75%)",
              zIndex: 999, // Ensure it's above everything
              top: "calc(30px - 1rem)" // Center vertically
            }}
          >
            {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        )}
      </div>

      {/* Menu Items */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className={cn("space-y-1", isCollapsed ? "px-1 py-4" : "px-3 py-4")}>
            {sidebarMenuItems.map((item) => (
              <MenuItem
                key={item.id}
                item={item}
                isActive={isActive}
                onClick={navigate}
                isCollapsed={isCollapsed}
              />
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Profile Section */}
      <div className="border-t border-[#3A424E] h-[72px] p-2 flex items-center justify-center">
        {/* When collapsed, use a simpler button structure */}
        {isCollapsed ? (
          <div
            className="w-10 h-10 flex items-center justify-center cursor-pointer"
            onClick={() => navigate("/audit/profile")}
            title={`${user?.username || 'Audit User'} (${user?.email || '<EMAIL>'})`}
          >
            <div className="w-8 h-8 rounded-full bg-gray-800 border border-gray-600 flex items-center justify-center">
              <span className="text-white font-medium text-sm">
                {user?.username?.charAt(0)?.toUpperCase() || 'A'}
              </span>
            </div>
          </div>
        ) : (
          <Button
            variant="ghost"
            className="flex items-center text-white hover:bg-gray-700 hover:text-white p-3 rounded-lg justify-start gap-3 w-full h-[56px]"
            onClick={() => navigate("/audit/profile")}
            title="Profile"
          >
            <div className="w-8 h-8 rounded-full bg-gray-800 border border-gray-600 flex items-center justify-center mr-2">
              <span className="text-white font-medium text-sm">
                {user?.username?.charAt(0)?.toUpperCase() || 'A'}
              </span>
            </div>
            <div className="flex flex-col items-start">
              <span className="text-sm font-medium text-white">{user?.username || 'Audit User'}</span>
              <span className="text-xs text-gray-400">
                {user?.email || '<EMAIL>'}
              </span>
            </div>
            <ChevronsUpDown className="h-5 w-5 ml-auto" />
          </Button>
        )}
      </div>
    </div>
  );
}

export default AuditSideBar;
