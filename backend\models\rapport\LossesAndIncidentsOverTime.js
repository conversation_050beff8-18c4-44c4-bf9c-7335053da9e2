const { sequelize } = require('../index');

// Add this function to filter by incident type name
const getLossesAndIncidentsByTypeName = async (typeName, timePeriod = 'month', start, end) => {
  try {
    let groupByClause;
    switch (timePeriod) {
      case 'day':
        groupByClause = "DATE_TRUNC('day', i.\"occurrenceDate\")";
        break;
      case 'year':
        groupByClause = "DATE_TRUNC('year', i.\"occurrenceDate\")";
        break;
      case 'month':
      default:
        groupByClause = "DATE_TRUNC('month', i.\"occurrenceDate\")";
    }

    // Build query with JOIN to IncidentType
    let query = `
      SELECT 
        ${groupByClause} AS period,
        COUNT(*)::integer AS incident_count,
        SUM(COALESCE(i."grossLoss", 0) - COALESCE(i."recoveries", 0)) AS net_loss
      FROM 
        "Incident" i
      JOIN 
        "IncidentType" it ON i."incidentTypeID" = it."incidentTypeID"
      LEFT JOIN 
        "Risk" r ON i."riskID" = r."riskID"
      WHERE 
        i."occurrenceDate" IS NOT NULL
        AND it."name" = $1
    `;
    
    const params = [typeName];
    let paramIndex = 2;
    
    // Add date filters
    if (start) {
      query += ` AND i."occurrenceDate" >= $${paramIndex}`;
      params.push(start);
      paramIndex++;
    }
    if (end) {
      query += ` AND i."occurrenceDate" <= $${paramIndex}`;
      params.push(end);
      paramIndex++;
    }
    
    query += `
      GROUP BY 
        ${groupByClause}
      ORDER BY 
        period;
    `;

    const results = await sequelize.query(query, {
      replacements: params,
      type: sequelize.QueryTypes.SELECT
    });
    
    return results;
  } catch (error) {
    console.error('Error fetching losses and incidents by type name:', error);
    throw error;
  }
};

const getLossesAndIncidentsOverTime = async (timePeriod = 'month', start, end, filters = {}) => {
  try {
    let groupByClause;
    switch (timePeriod) {
      case 'day':
        groupByClause = "DATE_TRUNC('day', i.\"occurrenceDate\")";
        break;
      case 'year':
        groupByClause = "DATE_TRUNC('year', i.\"occurrenceDate\")";
        break;
      case 'month':
      default:
        groupByClause = "DATE_TRUNC('month', i.\"occurrenceDate\")";
    }

    // Start building the query with proper joins
    let query = `
      SELECT 
        ${groupByClause} AS period,
        COUNT(*)::integer AS incident_count,
        SUM(COALESCE(i."grossLoss", 0) - COALESCE(i."recoveries", 0)) AS net_loss
      FROM 
        "Incident" i
      LEFT JOIN 
        "Risk" r ON i."riskID" = r."riskID"
    `;
    
    // Add JOIN to IncidentType if we're filtering by incident type name
    if (filters.incidentTypeName) {
      query += `JOIN "IncidentType" it ON i."incidentTypeID" = it."incidentTypeID" `;
    }
    
    query += `WHERE i."occurrenceDate" IS NOT NULL `;
    
    const params = [];
    let paramIndex = 1;
    
    // Add incident name filter if provided
    if (filters.incidentName) {
      query += ` AND i."name" = $${paramIndex}`;
      params.push(filters.incidentName);
      paramIndex++;
    }
    
    // Add incident type name filter if provided
    if (filters.incidentTypeName) {
      query += ` AND it."name" = $${paramIndex}`;
      params.push(filters.incidentTypeName);
      paramIndex++;
    }
    
    // Add date filters
    if (start) {
      query += ` AND i."occurrenceDate" >= $${paramIndex}`;
      params.push(start);
      paramIndex++;
    }
    if (end) {
      query += ` AND i."occurrenceDate" <= $${paramIndex}`;
      params.push(end);
      paramIndex++;
    }
    
    // Add DMR filter
    if (filters.DMR && filters.DMR.length > 0) {
      const dmrValues = Array.isArray(filters.DMR) ? filters.DMR : [filters.DMR];
      
      // Convert to integers and filter out invalid values
      const validDmrValues = dmrValues
        .map(dmr => parseInt(dmr, 10))
        .filter(dmr => !isNaN(dmr));
      
      if (validDmrValues.length > 0) {
        const placeholders = validDmrValues.map((_, idx) => `$${paramIndex + idx}`).join(', ');
        query += ` AND r."DMR" IN (${placeholders})`;
        params.push(...validDmrValues);
        paramIndex += validDmrValues.length;
      }
    }
    
    // Add inherent risk level filter if provided
    if (filters.riskLevels && filters.riskLevels.length > 0) {
      const riskLevels = Array.isArray(filters.riskLevels) ? filters.riskLevels : [filters.riskLevels];
      
      // Convert to integers and filter out invalid values
      const validRiskLevels = riskLevels
        .map(level => parseInt(level, 10))
        .filter(level => !isNaN(level));
      
      if (validRiskLevels.length > 0) {
        query += ` AND (
          CASE 
            WHEN (r."impact" * r."probability") <= 4 THEN 1
            WHEN (r."impact" * r."probability") <= 9 THEN 2
            WHEN (r."impact" * r."probability") <= 15 THEN 3
            WHEN (r."impact" * r."probability") <= 20 THEN 4
            ELSE 5
          END
        ) IN (`;
        
        const placeholders = validRiskLevels.map((_, idx) => `$${paramIndex + idx}`).join(', ');
        query += placeholders + ')';
        params.push(...validRiskLevels);
        paramIndex += validRiskLevels.length;
      }
    }
    
    // Add incidentTypes filter - modified to support both ID and name filtering
    if (filters.incidentTypes && filters.incidentTypes.length > 0) {
      const placeholders = filters.incidentTypes.map((_, idx) => `$${paramIndex + idx}`).join(', ');
      query += ` AND i."incidentTypeID" IN (${placeholders})`;
      params.push(...filters.incidentTypes);
      paramIndex += filters.incidentTypes.length;
    }
    
    // Add incidents filter by ID
    if (filters.incidents && filters.incidents.length > 0) {
      const placeholders = filters.incidents.map((_, idx) => `$${paramIndex + idx}`).join(', ');
      query += ` AND i."incidentID" IN (${placeholders})`;
      params.push(...filters.incidents);
      paramIndex += filters.incidents.length;
    }
    
    if (filters.entities && filters.entities.length > 0) {
      const placeholders = filters.entities.map((_, idx) => `$${paramIndex + idx}`).join(', ');
      query += ` AND i."entityID" IN (${placeholders})`;
      params.push(...filters.entities);
      paramIndex += filters.entities.length;
    }
    
    if (filters.businessProcesses && filters.businessProcesses.length > 0) {
      const placeholders = filters.businessProcesses.map((_, idx) => `$${paramIndex + idx}`).join(', ');
      query += ` AND i."businessProcessID" IN (${placeholders})`;
      params.push(...filters.businessProcesses);
      paramIndex += filters.businessProcesses.length;
    }
    
    if (filters.organizationalProcesses && filters.organizationalProcesses.length > 0) {
      const placeholders = filters.organizationalProcesses.map((_, idx) => `$${paramIndex + idx}`).join(', ');
      query += ` AND i."organizationalProcessID" IN (${placeholders})`;
      params.push(...filters.organizationalProcesses);
      paramIndex += filters.organizationalProcesses.length;
    }

    // Complete the query with GROUP BY and ORDER BY
    query += `
      GROUP BY 
        ${groupByClause}
      ORDER BY 
        period;
    `;

    console.log('Executing query:', query);
    console.log('With parameters:', params);

    const results = await sequelize.query(query, { 
      bind: params,
      type: sequelize.QueryTypes.SELECT 
    });
    
    return results;
  } catch (error) {
    console.error('Error fetching losses and incidents over time:', error);
    throw error;
  }
};

module.exports = { getLossesAndIncidentsOverTime, getLossesAndIncidentsByTypeName };
