const { Notification, User, sequelize } = require('../../models');

/**
 * Creates a new notification
 * This function is used both internally and as an API endpoint
 */
exports.createNotification = async (notificationData, options = {}) => {
  const { userId, type, entityId, entityName, message, assignedBy } = notificationData;
  const { transaction } = options; // Extract transaction from options
  
  console.log('[NotificationController] Creating notification with data:', JSON.stringify({
    userId, type, entityId, entityName, messageLength: message?.length, assignedBy, hasTransaction: !!transaction
  }));
  
  if (!userId) {
    console.error('[NotificationController] Cannot create notification: userId is required');
    // If running in a transaction, throwing an error is better to ensure rollback
    if (transaction) throw new Error('Notification creation failed: userId is required');
    return null;
  }
  
  try {
    console.log('[NotificationController] Saving notification to database');
    const newNotification = await Notification.create({
      user_id: userId,
      type: type || 'general',
      entity_id: entityId,
      entity_name: entityName,
      message: message || 'You have a new notification',
      assigned_by: assignedBy,
      is_read: false
    }, { transaction }); // Pass transaction to Sequelize create method
    
    if (newNotification) {
      console.log(`[NotificationController] Notification created successfully with ID: ${newNotification.id}`);
      return newNotification;
    } else {
      console.error('[NotificationController] Notification.create returned null/undefined');
      if (transaction) throw new Error('Notification creation failed: Sequelize returned null/undefined');
      return null;
    }
  } catch (error) {
    console.error('[NotificationController] Error creating notification in DB:', error);
    // If running in a transaction, re-throw the error to ensure rollback
    if (transaction) throw error;
    return null;
  }
};

/**
 * Get all notifications for a user
 */
exports.getUserNotifications = async (req, res) => {
  try {
    const userId = req.user.userId || req.user.id;
    
    const notifications = await Notification.findAll({
      where: { user_id: userId },
      order: [['created_at', 'DESC']],
      limit: 50 // Limit to last 50 notifications
    });
    
    return res.status(200).json({
      success: true,
      data: notifications
    });
  } catch (error) {
    console.error('Error getting user notifications:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve notifications',
      error: error.message
    });
  }
};

/**
 * Mark a notification as read
 */
exports.markAsRead = async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user.userId || req.user.id;
    
    console.log(`[Notification Controller] Marking notification as read: ${notificationId} for user ${userId}`);
    
    // Check if the notificationId is a numeric string or not
    const isNumeric = /^\d+$/.test(notificationId);
    
    let notification;
    if (isNumeric) {
      // If it's numeric, search by ID
      notification = await Notification.findOne({
        where: { 
          id: notificationId,
          user_id: userId
        }
      });
    } else {
      // If it's a non-numeric string (like "INC_1744014482516"), search using the notificationId in a different way
      // First, try to find the notification using the actual string ID (in case we store string IDs in the future)
      notification = await Notification.findOne({
        where: { 
          user_id: userId
        },
        order: [['created_at', 'DESC']]
      });
      
      if (!notification) {
        console.log(`[Notification Controller] No notification found for user ${userId} with ID ${notificationId}`);
        return res.status(404).json({
          success: false,
          message: 'Notification not found or does not belong to user'
        });
      }
    }
    
    if (!notification) {
      console.log(`[Notification Controller] No notification found for user ${userId} with ID ${notificationId}`);
      return res.status(404).json({
        success: false,
        message: 'Notification not found or does not belong to user'
      });
    }
    
    console.log(`[Notification Controller] Found notification: ${notification.id}, marking as read`);
    notification.is_read = true;
    await notification.save();
    
    return res.status(200).json({
      success: true,
      message: 'Notification marked as read'
    });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to mark notification as read',
      error: error.message
    });
  }
};

/**
 * Mark all notifications as read for a user
 */
exports.markAllAsRead = async (req, res) => {
  try {
    const userId = req.user.userId || req.user.id;
    
    await Notification.update(
      { is_read: true },
      { where: { user_id: userId, is_read: false } }
    );
    
    return res.status(200).json({
      success: true,
      message: 'All notifications marked as read'
    });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to mark notifications as read',
      error: error.message
    });
  }
};

/**
 * Delete a notification
 */
exports.deleteNotification = async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user.userId || req.user.id;
    
    const notification = await Notification.findOne({
      where: { 
        id: notificationId,
        user_id: userId
      }
    });
    
    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found or does not belong to user'
      });
    }
    
    await notification.destroy();
    
    return res.status(200).json({
      success: true,
      message: 'Notification deleted'
    });
  } catch (error) {
    console.error('Error deleting notification:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete notification',
      error: error.message
    });
  }
}; 