import React, { useState, useEffect, useCallback, useRef } from 'react';

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { FileText, FileDown, Loader2, AlertTriangle, RefreshCw, Mail, Eye, Volume2, Pause, Play, Square, MessageCircle } from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';
import EmailReportModal from '@/components/reports/EmailReportModal';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from '@/components/ui/dialog';
import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, <PERSON> } from 'chart.js';
ChartJS.register(<PERSON><PERSON><PERSON>, Toolt<PERSON>, Legend);
import { jsPDF } from "jspdf";
import * as XLSX from "https://cdn.jsdelivr.net/npm/xlsx@0.18.5/xlsx.mjs";

function IndicateursPerformanceTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;


  // State for report functionality
  const [reportData, setReportData] = useState(null);
  const [loadingReport, setLoadingReport] = useState(false);
  const [downloadingPdf, setDownloadingPdf] = useState(false);
  const [downloadingDocx, setDownloadingDocx] = useState(false);
  const [reportError, setReportError] = useState(null);
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [isAudioModalOpen, setIsAudioModalOpen] = useState(false);
  const [isCommentModalOpen, setIsCommentModalOpen] = useState(false);
  const [audioUrl, setAudioUrl] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioCurrentTime, setAudioCurrentTime] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);
  const audioRef = useRef(null);
  const [reportComments, setReportComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [loadingComments, setLoadingComments] = useState(false);
  const [addingComment, setAddingComment] = useState(false);

  // Fetch report data
  const fetchReportData = useCallback(async () => {
    if (!missionAudit?.id) return;

    setLoadingReport(true);
    setReportError(null);

    try {
      const response = await axios.get(
        `${getApiBaseUrl()}/audit-missions/report/${missionAudit.id}`,
        { withCredentials: true }
      );

      if (response.data.success) {
        setReportData(response.data.data);
      } else {
        setReportError(response.data.message || 'Erreur lors du chargement du rapport');
      }
    } catch (error) {
      console.error('Error fetching report:', error);
      setReportError(error.response?.data?.message || 'Erreur lors du chargement du rapport');
    } finally {
      setLoadingReport(false);
    }
  }, [missionAudit?.id]);

  // Fetch comments
  const fetchComments = useCallback(async () => {
    if (!missionAudit?.id) return;

    setLoadingComments(true);
    try {
      const response = await axios.get(
        `${getApiBaseUrl()}/audit-missions/report/${missionAudit.id}/comments`,
        { withCredentials: true }
      );

      if (response.data.success) {
        setReportComments(response.data.data || []);
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
    } finally {
      setLoadingComments(false);
    }
  }, [missionAudit?.id]);

  useEffect(() => {
    fetchReportData();
    fetchComments();
  }, [fetchReportData, fetchComments]);

  // Generate PDF report
  const generatePdfReport = async () => {
    if (!reportData) return;

    setDownloadingPdf(true);
    try {
      const pdf = new jsPDF();
      
      // Add title
      pdf.setFontSize(20);
      pdf.text('Indicateurs de Performance - Mission d\'Audit', 20, 30);
      
      // Add mission info
      pdf.setFontSize(12);
      pdf.text(`Mission: ${missionAudit.name}`, 20, 50);
      pdf.text(`Date: ${new Date().toLocaleDateString('fr-FR')}`, 20, 60);
      
      // Add statistics
      let yPosition = 80;
      pdf.setFontSize(14);
      pdf.text('Statistiques:', 20, yPosition);
      yPosition += 20;
      
      pdf.setFontSize(10);
      if (reportData.statistics) {
        Object.entries(reportData.statistics).forEach(([key, value]) => {
          pdf.text(`${key}: ${value}`, 20, yPosition);
          yPosition += 10;
        });
      }
      
      pdf.save(`indicateurs-performance-${missionAudit.name}-${new Date().toISOString().split('T')[0]}.pdf`);
      toast.success('Rapport PDF généré avec succès');
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Erreur lors de la génération du PDF');
    } finally {
      setDownloadingPdf(false);
    }
  };

  // Generate Excel report
  const generateExcelReport = async () => {
    if (!reportData) return;

    setDownloadingDocx(true);
    try {
      const workbook = XLSX.utils.book_new();
      
      // Create summary sheet
      const summaryData = [
        ['Mission', missionAudit.name],
        ['Date', new Date().toLocaleDateString('fr-FR')],
        ['', ''],
        ['Indicateurs de Performance', '']
      ];
      
      if (reportData.statistics) {
        Object.entries(reportData.statistics).forEach(([key, value]) => {
          summaryData.push([key, value]);
        });
      }
      
      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Résumé');
      
      // Save file
      XLSX.writeFile(workbook, `indicateurs-performance-${missionAudit.name}-${new Date().toISOString().split('T')[0]}.xlsx`);
      toast.success('Rapport Excel généré avec succès');
    } catch (error) {
      console.error('Error generating Excel:', error);
      toast.error('Erreur lors de la génération du fichier Excel');
    } finally {
      setDownloadingDocx(false);
    }
  };

  // Audio functions
  const generateAudioReport = async () => {
    if (!reportData) return;

    try {
      const response = await axios.post(
        `${getApiBaseUrl()}/audit-missions/report/${missionAudit.id}/audio`,
        {},
        { 
          withCredentials: true,
          responseType: 'blob'
        }
      );

      const audioBlob = new Blob([response.data], { type: 'audio/mpeg' });
      const url = URL.createObjectURL(audioBlob);
      setAudioUrl(url);
      setIsAudioModalOpen(true);
    } catch (error) {
      console.error('Error generating audio:', error);
      toast.error('Erreur lors de la génération audio');
    }
  };

  const togglePlayPause = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const stopAudio = () => {
    if (!audioRef.current) return;
    audioRef.current.pause();
    audioRef.current.currentTime = 0;
    setIsPlaying(false);
    setAudioCurrentTime(0);
  };

  // Comment functions
  const addComment = async () => {
    if (!newComment.trim() || !missionAudit?.id) return;

    setAddingComment(true);
    try {
      const response = await axios.post(
        `${getApiBaseUrl()}/audit-missions/report/${missionAudit.id}/comments`,
        { comment: newComment },
        { withCredentials: true }
      );

      if (response.data.success) {
        setNewComment('');
        fetchComments();
        toast.success('Commentaire ajouté');
      }
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error('Erreur lors de l\'ajout du commentaire');
    } finally {
      setAddingComment(false);
    }
  };

  if (!missionAudit) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">Chargement des informations de la mission...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Indicateurs de Performance</h2>
          <p className="text-gray-600 mt-1">Mission: {missionAudit.name}</p>
        </div>
        <Button
          onClick={fetchReportData}
          disabled={loadingReport}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loadingReport ? 'animate-spin' : ''}`} />
          Actualiser
        </Button>
      </div>

      {/* Loading State */}
      {loadingReport && (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600 mr-3" />
            <p className="text-gray-600">Chargement des indicateurs de performance...</p>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {reportError && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="flex items-center py-4">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-3" />
            <p className="text-red-800">{reportError}</p>
          </CardContent>
        </Card>
      )}

      {/* Report Content */}
      {reportData && !loadingReport && (
        <>
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={() => setIsPreviewModalOpen(true)}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              Aperçu
            </Button>
            <Button
              onClick={generatePdfReport}
              disabled={downloadingPdf}
              variant="outline"
              className="flex items-center gap-2"
            >
              {downloadingPdf ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <FileDown className="h-4 w-4" />
              )}
              PDF
            </Button>
            <Button
              onClick={generateExcelReport}
              disabled={downloadingDocx}
              variant="outline"
              className="flex items-center gap-2"
            >
              {downloadingDocx ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <FileDown className="h-4 w-4" />
              )}
              Excel
            </Button>
            <Button
              onClick={generateAudioReport}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Volume2 className="h-4 w-4" />
              Audio
            </Button>
            <Button
              onClick={() => setIsEmailModalOpen(true)}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Mail className="h-4 w-4" />
              Envoyer
            </Button>
            <Button
              onClick={() => setIsCommentModalOpen(true)}
              variant="outline"
              className="flex items-center gap-2"
            >
              <MessageCircle className="h-4 w-4" />
              Commentaires ({reportComments.length})
            </Button>
          </div>

          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Activités Complétées</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {reportData.statistics?.completedActivities || 0}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  sur {reportData.statistics?.totalActivities || 0} activités
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Constats Identifiés</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {reportData.statistics?.totalConstats || 0}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {reportData.statistics?.criticalConstats || 0} critiques
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Recommandations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {reportData.statistics?.totalRecommendations || 0}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {reportData.statistics?.implementedRecommendations || 0} mises en œuvre
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Taux d'Avancement</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  {reportData.statistics?.progressPercentage || 0}%
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  de la mission
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          {reportData.charts && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {reportData.charts.activitiesChart && (
                <Card>
                  <CardHeader>
                    <CardTitle>Répartition des Activités</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      <Doughnut
                        data={reportData.charts.activitiesChart}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'bottom',
                            },
                          },
                        }}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {reportData.charts.constatsChart && (
                <Card>
                  <CardHeader>
                    <CardTitle>Répartition des Constats</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      <Doughnut
                        data={reportData.charts.constatsChart}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'bottom',
                            },
                          },
                        }}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </>
      )}

      {/* Modals */}
      {/* Preview Modal */}
      <Dialog open={isPreviewModalOpen} onOpenChange={setIsPreviewModalOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Aperçu des Indicateurs de Performance</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {reportData && (
              <div className="prose max-w-none">
                <h3>Mission: {missionAudit.name}</h3>
                <h4>Résumé des Indicateurs</h4>
                <ul>
                  <li>Activités complétées: {reportData.statistics?.completedActivities || 0}/{reportData.statistics?.totalActivities || 0}</li>
                  <li>Constats identifiés: {reportData.statistics?.totalConstats || 0}</li>
                  <li>Recommandations: {reportData.statistics?.totalRecommendations || 0}</li>
                  <li>Taux d'avancement: {reportData.statistics?.progressPercentage || 0}%</li>
                </ul>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Audio Modal */}
      <Dialog open={isAudioModalOpen} onOpenChange={setIsAudioModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Lecture Audio des Indicateurs</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {audioUrl && (
              <>
                <audio
                  ref={audioRef}
                  src={audioUrl}
                  onTimeUpdate={(e) => setAudioCurrentTime(e.target.currentTime)}
                  onLoadedMetadata={(e) => setAudioDuration(e.target.duration)}
                />
                <div className="flex items-center gap-4">
                  <Button onClick={togglePlayPause} variant="outline">
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  <Button onClick={stopAudio} variant="outline">
                    <Square className="h-4 w-4" />
                  </Button>
                </div>
                <div className="text-sm text-gray-600">
                  {Math.floor(audioCurrentTime / 60)}:{Math.floor(audioCurrentTime % 60).toString().padStart(2, '0')} / {Math.floor(audioDuration / 60)}:{Math.floor(audioDuration % 60).toString().padStart(2, '0')}
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Comments Modal */}
      <Dialog open={isCommentModalOpen} onOpenChange={setIsCommentModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Commentaires sur les Indicateurs</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {loadingComments ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : reportComments.length === 0 ? (
              <p className="text-gray-500 text-center py-4">Aucun commentaire</p>
            ) : (
              reportComments.map((comment, index) => (
                <div key={index} className="border-b pb-2">
                  <p className="text-sm">{comment.text}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {comment.author} - {new Date(comment.createdAt).toLocaleDateString('fr-FR')}
                  </p>
                </div>
              ))
            )}
          </div>
          <div className="flex gap-2">
            <input
              type="text"
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Ajouter un commentaire..."
              className="flex-1 px-3 py-2 border rounded-md"
            />
            <Button onClick={addComment} disabled={addingComment || !newComment.trim()}>
              {addingComment ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Ajouter'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Email Modal */}
      {isEmailModalOpen && (
        <EmailReportModal
          isOpen={isEmailModalOpen}
          onClose={() => setIsEmailModalOpen(false)}
          reportType="indicateurs-performance"
          missionId={missionAudit.id}
          missionName={missionAudit.name}
        />
      )}
    </div>
  );
}

export default IndicateursPerformanceTab;
