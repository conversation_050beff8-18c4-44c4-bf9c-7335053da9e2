const fs = require('fs');
const path = require('path');
const { Sequelize } = require('sequelize');
require('dotenv').config();

async function runMigration() {
  try {
    console.log('Starting AllAttachment table migration...');
    
    // Create a new Sequelize instance
    const sequelize = new Sequelize(
      process.env.DB_NAME,
      process.env.DB_USER,
      process.env.DB_PASSWORD,
      {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        dialect: 'postgres',
        logging: console.log
      }
    );

    // Test the connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Read the SQL migration file
    const migrationPath = path.join(__dirname, '../migrations/create-all-attachment-table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the SQL
    console.log('Executing migration SQL...');
    await sequelize.query(migrationSQL);
    
    console.log('Migration completed successfully!');
    
    // Create uploads directories if they don't exist
    const uploadsDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir);
    }
    
    const dirs = [
      'business-documents',
      'external-references'
    ];
    
    dirs.forEach(dir => {
      const dirPath = path.join(uploadsDir, dir);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath);
        console.log(`Created directory: ${dirPath}`);
      }
    });
    
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

runMigration();