/**
 * Format a date string to French format (dd/mm/yyyy)
 * @param {string} dateString - The date string to format (ISO format or French format)
 * @returns {string} The date in French format (dd/mm/yyyy)
 */
export const formatDateToFrench = (dateString) => {
  if (!dateString) return '';
  
  try {
    // If the date is already in French format, return it
    if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateString)) {
      return dateString;
    }

    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date');
    }

    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Parse a French date string (dd/mm/yyyy) to a Date object
 * @param {string} frenchDate - The date string in French format (dd/mm/yyyy)
 * @returns {Date|null} The parsed Date object or null if invalid
 */
export const parseFrenchDate = (frenchDate) => {
  if (!frenchDate) return null;

  try {
    // If the date is already a Date object, return it
    if (frenchDate instanceof Date) {
      return frenchDate;
    }

    // If the date is in ISO format, parse it directly
    if (/^\d{4}-\d{2}-\d{2}/.test(frenchDate)) {
      const date = new Date(frenchDate);
      if (!isNaN(date.getTime())) {
        return date;
      }
    }

    // Parse French format (dd/mm/yyyy)
    const [day, month, year] = frenchDate.split('/').map(Number);
    const date = new Date(year, month - 1, day);
    
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date');
    }

    return date;
  } catch (error) {
    console.error('Error parsing date:', error);
    return null;
  }
};

/**
 * Convert a date to ISO format (YYYY-MM-DD)
 * @param {string|Date} date - The date to convert
 * @returns {string} The date in ISO format
 */
export const toISODate = (date) => {
  if (!date) return '';
  
  try {
    const parsedDate = parseFrenchDate(date);
    if (!parsedDate) return '';
    
    return parsedDate.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error converting to ISO date:', error);
    return '';
  }
};

/**
 * Check if a date is valid
 * @param {string|Date} date - The date to check
 * @returns {boolean} True if the date is valid
 */
export const isValidDate = (date) => {
  if (!date) return false;
  
  try {
    const parsedDate = parseFrenchDate(date);
    return parsedDate !== null && !isNaN(parsedDate.getTime());
  } catch (error) {
    return false;
  }
};

/**
 * Compare two dates
 * @param {string|Date} date1 - First date
 * @param {string|Date} date2 - Second date
 * @returns {number} -1 if date1 < date2, 0 if equal, 1 if date1 > date2
 */
export const compareDates = (date1, date2) => {
  const d1 = parseFrenchDate(date1);
  const d2 = parseFrenchDate(date2);
  
  if (!d1 || !d2) return 0;
  
  return d1.getTime() - d2.getTime();
}; 