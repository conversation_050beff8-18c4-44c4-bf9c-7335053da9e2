const express = require('express');
const router = express.Router();
const {
  getAllCampagnes,
  getCampagneById,
  createCampagne,
  updateCampagne,
  deleteCampagne,
  assignUsersToCampagne,
  getCampagneUsers,
  removeUserFromCampagne,
  createCampagneWithUsers,
  findOrCreateCampagneForControl
} = require('../controllers/campagne-controller');
const { authenticateToken } = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(authenticateToken);

// CRUD Operations for campagnes
router.get('/', getAllCampagnes);
router.get('/:id', getCampagneById);
router.post('/', createCampagne);
router.put('/:id', updateCampagne);
router.delete('/:id', deleteCampagne);

// User Assignment Operations
router.post('/:id/assign-users', assignUsersToCampagne);
router.get('/:id/users', getCampagneUsers);
router.delete('/:id/users/:userId', removeUserFromCampagne);

// Create campagne with users (for "Lancer la Campagne")
router.post('/create-with-users', createCampagneWithUsers);

// Find or create campagne for control (for "Lancer la Campagne" with update logic)
router.post('/find-or-create-for-control', findOrCreateCampagneForControl);

module.exports = router;
