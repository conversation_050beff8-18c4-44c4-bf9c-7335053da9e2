import { cn } from "@/lib/utils";

/**
 * TabContent component for displaying tab content with consistent styling
 *
 * @param {Object} props
 * @param {React.ReactNode} props.children - The content to display
 * @param {string} props.className - Additional classes for the container
 */
function TabContent({ children, className }) {
  return (
    <div
      className={cn("bg-white rounded-lg shadow-sm p-6", className)}
      style={{
        backgroundColor: "white",
        boxShadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"
      }}
    >
      {children}
    </div>
  );
}

export default TabContent;
