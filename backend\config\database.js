const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Afficher les variables d'environnement (temporaire pour debug)
console.log('Database Config:', {
  database: process.env.DB_NAME,
  username: process.env.DB_USER,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT
});

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: false, // Désactive les logs SQL
    pool: {
      max: 20, // Increased from 5 to handle more concurrent connections
      min: 5,  // Increased from 0 to maintain minimum connections
      acquire: 60000, // Increased timeout for connection acquisition
      idle: 30000,    // Increased idle timeout
      evict: 1000     // Run eviction every 1 second
    },
    dialectOptions: {
      // Optimize PostgreSQL settings
      statement_timeout: 30000, // 30 seconds timeout for queries
      idle_in_transaction_session_timeout: 30000,
      // Enable connection pooling optimization
      keepAlive: true,
      // Optimize for performance
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
    },
    // Query optimization
    benchmark: false, // Disable query benchmarking in production
    // Optimize for bulk operations
    define: {
      timestamps: true,
      underscored: false,
      freezeTableName: true
    }
  }
);

module.exports = sequelize;
