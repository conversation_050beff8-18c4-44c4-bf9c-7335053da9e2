import { useCallback, useEffect } from 'react'; // Added useCallback for stable reload function
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchAllReferenceData,
  fetchBusinessProcesses,
  fetchOrganizationalProcesses,
  fetchOperations,
  fetchApplications,
  fetchEntities,
  fetchRiskTypes,
  fetchControls
} from '../store/reference-data-slice';

/**
 * Custom hook to access and load reference data from Redux store
 * @param {Object} options - Configuration options
 * @param {boolean} options.loadOnMount - Whether to load all reference data when the component mounts
 * @param {Array<string>} options.include - Specific data types to load (e.g., ['businessProcesses', 'entities'])
 * @returns {Object} Reference data and loading states
 */
const useReferenceData = (options = {}) => {
  const { loadOnMount = false, include = [] } = options; // Default to false
  const dispatch = useDispatch();
  const referenceData = useSelector((state) => state.referenceData);

  // Load data on mount if specified - with a flag to prevent multiple loads
  useEffect(() => {
    // Create a flag to track if this is the first load
    let isFirstLoad = true;

    if (loadOnMount && isFirstLoad) {
      isFirstLoad = false; // Prevent subsequent loads

      if (include.length === 0) {
        dispatch(fetchAllReferenceData());
      } else {
        include.forEach(dataType => {
          switch (dataType) {
            case 'businessProcesses':
              dispatch(fetchBusinessProcesses());
              break;
            case 'organizationalProcesses':
              dispatch(fetchOrganizationalProcesses());
              break;
            case 'operations':
              dispatch(fetchOperations());
              break;
            case 'applications':
              dispatch(fetchApplications());
              break;
            case 'entities':
              dispatch(fetchEntities());
              break;
            case 'riskTypes':
              dispatch(fetchRiskTypes());
              break;
            case 'controls':
              dispatch(fetchControls());
              break;
            default:
              console.warn(`Unknown reference data type: ${dataType}`);
          }
        });
      }
    }

    // Cleanup function
    return () => {
      isFirstLoad = false;
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array to ensure it only runs once

  // Manual reload function with useCallback for stability
  const reload = useCallback((dataType) => {
    switch (dataType) {
      case 'businessProcesses':
        dispatch(fetchBusinessProcesses());
        break;
      case 'organizationalProcesses':
        dispatch(fetchOrganizationalProcesses());
        break;
      case 'operations':
        dispatch(fetchOperations());
        break;
      case 'applications':
        dispatch(fetchApplications());
        break;
      case 'entities':
        dispatch(fetchEntities());
        break;
      case 'riskTypes':
        dispatch(fetchRiskTypes());
        break;
      case 'controls':
        dispatch(fetchControls());
        break;
      case 'all':
        dispatch(fetchAllReferenceData());
        break;
      default:
        // Silently ignore unknown data types
    }
  }, [dispatch]);

  const isLoading =
    referenceData.businessProcesses.loading ||
    referenceData.organizationalProcesses.loading ||
    referenceData.operations.loading ||
    referenceData.applications.loading ||
    referenceData.entities.loading ||
    referenceData.riskTypes.loading ||
    referenceData.controls.loading;

  const hasErrors =
    referenceData.businessProcesses.error ||
    referenceData.organizationalProcesses.error ||
    referenceData.operations.error ||
    referenceData.applications.error ||
    referenceData.entities.error ||
    referenceData.riskTypes.error ||
    referenceData.controls.error;

  return {
    businessProcesses: referenceData.businessProcesses.data,
    organizationalProcesses: referenceData.organizationalProcesses.data,
    operations: referenceData.operations.data,
    applications: referenceData.applications.data,
    entities: referenceData.entities.data,
    riskTypes: referenceData.riskTypes.data,
    controls: referenceData.controls.data,
    isLoading,
    businessProcessesLoading: referenceData.businessProcesses.loading,
    organizationalProcessesLoading: referenceData.organizationalProcesses.loading,
    operationsLoading: referenceData.operations.loading,
    applicationsLoading: referenceData.applications.loading,
    entitiesLoading: referenceData.entities.loading,
    riskTypesLoading: referenceData.riskTypes.loading,
    controlsLoading: referenceData.controls.loading,
    hasErrors,
    businessProcessesError: referenceData.businessProcesses.error,
    organizationalProcessesError: referenceData.organizationalProcesses.error,
    operationsError: referenceData.operations.error,
    applicationsError: referenceData.applications.error,
    entitiesError: referenceData.entities.error,
    riskTypesError: referenceData.riskTypes.error,
    controlsError: referenceData.controls.error,
    reload,
  };
};

export default useReferenceData;