import { useEffect, useRef, useCallback } from 'react';
import axios from 'axios';

/**
 * Custom hook for handling API requests with automatic cancellation
 * Prevents ECONNABORTED errors and handles component unmounting gracefully
 */
export const useApiRequest = () => {
  const cancelTokensRef = useRef(new Set());

  // Create a new cancel token for each request
  const createCancelToken = useCallback(() => {
    const source = axios.CancelToken.source();
    cancelTokensRef.current.add(source);
    return source;
  }, []);

  // Make an API request with automatic cancellation
  const makeRequest = useCallback(async (requestConfig, options = {}) => {
    const {
      retries = 2,
      retryDelay = 1000,
      onError = null,
      silentError = false,
      skipCancellation = false
    } = options;

    // Use AbortController instead of axios cancel token for better control
    const abortController = new AbortController();

    if (!skipCancellation) {
      cancelTokensRef.current.add({ cancel: () => abortController.abort() });
    }

    const configWithCancel = {
      ...requestConfig,
      signal: abortController.signal,
      timeout: 15000, // Reduced timeout to 15 seconds
    };

    let lastError = null;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await axios(configWithCancel);

        // Remove successful request from cancel tokens
        if (!skipCancellation) {
          cancelTokensRef.current.delete({ cancel: () => abortController.abort() });
        }

        return response;
      } catch (error) {
        lastError = error;

        // Check if request was aborted
        if (error.name === 'AbortError' || error.code === 'ECONNABORTED' || abortController.signal.aborted) {
          if (!silentError) {
            console.warn('Request was cancelled or aborted');
          }
          return null; // Return null for cancelled requests
        }

        // Don't retry if it's the last attempt
        if (attempt === retries) {
          break;
        }

        // Don't retry on 4xx errors (client errors)
        if (error.response && error.response.status >= 400 && error.response.status < 500) {
          break;
        }

        // Wait before retrying with exponential backoff
        if (attempt < retries) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));
        }
      }
    }

    // Remove failed request from cancel tokens
    if (!skipCancellation) {
      cancelTokensRef.current.delete({ cancel: () => abortController.abort() });
    }

    // Handle the error
    if (lastError && (lastError.name === 'AbortError' || lastError.code === 'ECONNABORTED')) {
      if (!silentError) {
        console.warn('Request was cancelled or timed out');
      }
      return null; // Return null for cancelled/aborted requests
    }

    // Call custom error handler if provided
    if (onError && lastError) {
      onError(lastError);
    } else if (!silentError && lastError) {
      console.error('API request failed:', lastError);
    }

    // Don't throw error for cancelled requests, return null instead
    if (lastError && (lastError.name === 'AbortError' || lastError.code === 'ECONNABORTED')) {
      return null;
    }

    if (lastError) {
      throw lastError;
    }

    return null;
  }, []);

  // Cancel all pending requests
  const cancelAllRequests = useCallback(() => {
    cancelTokensRef.current.forEach(source => {
      try {
        source.cancel('Component unmounted or navigation occurred');
      } catch {
        // Ignore errors when cancelling
      }
    });
    cancelTokensRef.current.clear();
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cancelAllRequests();
    };
  }, [cancelAllRequests]);

  return {
    makeRequest,
    cancelAllRequests,
    createCancelToken
  };
};

/**
 * Higher-order function to wrap API calls with error handling and retries
 */
export const withApiErrorHandling = (apiCall, options = {}) => {
  const { 
    fallbackValue = null, 
    autoRefresh = false, 
    refreshDelay = 3000,
    onError = null 
  } = options;

  return async (...args) => {
    try {
      return await apiCall(...args);
    } catch (error) {
      if (axios.isCancel(error)) {
        console.warn('Request cancelled');
        return fallbackValue;
      }

      console.error('API call failed:', error);
      
      if (onError) {
        onError(error);
      }

      // Auto refresh page if specified and it's a critical error
      if (autoRefresh && error.code === 'ECONNABORTED') {
        console.log(`Auto-refreshing page in ${refreshDelay}ms due to connection issues...`);
        setTimeout(() => {
          window.location.reload();
        }, refreshDelay);
      }

      return fallbackValue;
    }
  };
};

export default useApiRequest;
