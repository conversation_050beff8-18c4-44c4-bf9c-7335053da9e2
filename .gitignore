# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build
/dist

# Misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Uploads directory (except .gitkeep files)
/backend/uploads/*
!/backend/uploads/.gitkeep
!/backend/uploads/business-documents/.gitkeep
!/backend/uploads/external-references/.gitkeep
!/backend/uploads/risk-documents/.gitkeep
!/backend/uploads/risk-references/.gitkeep

# Do NOT ignore the uploads routes and controllers
!/backend/routes/uploads/
!/backend/controllers/uploads/
