import React, { useState, useEffect, useRef } from 'react';
import { Line } from 'react-chartjs-2';
import axios from 'axios';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, TimeScale, Title, Tooltip, Legend } from 'chart.js';
import { jsPDF } from "jspdf";
import * as XLSX from "https://cdn.jsdelivr.net/npm/xlsx@0.18.5/xlsx.mjs";
import { Button } from "@/components/ui/button";
import { DialogTitle, DialogDescription } from "@/components/ui/dialog";
import * as VisuallyHidden from "@radix-ui/react-visually-hidden";
import zoomPlugin from 'chartjs-plugin-zoom';
import 'chartjs-adapter-date-fns';
import { enUS } from 'date-fns/locale';
import { getApiBaseUrl } from "@/utils/api-config";
import { useLocation } from 'react-router-dom';
import { format } from 'date-fns';
import { toast } from 'react-hot-toast';
import EmailReportModal from '@/components/reports/EmailReportModal';

// Add this helper function at the top (or wherever you need it)
const getDMRLabel = (dmr) => {
  const dmrValue = parseInt(dmr, 10);
  switch (dmrValue) {
    case 1: return 'Very Strong (1)';
    case 2: return 'Very Strong (2)';
    case 3: return 'Very Strong (3)';
    case 4: return 'Strong (4)';
    case 5: return 'Strong (5)';
    case 6: return 'Strong (6)';
    case 8: return 'Medium (8)';
    case 9: return 'Medium (9)';
    case 10: return 'Medium (10)';
    case 12: return 'Medium (12)';
    case 15: return 'Weak (15)';
    case 16: return 'Weak (16)';
    case 20: return 'Weak (20)';
    case 25: return 'Very Weak (25)';
    default: return `DMR ${dmrValue}`;
  }
};

// Register Chart.js components and plugins
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, TimeScale, Title, Tooltip, Legend, zoomPlugin);

// Function to get filters from URL
const getFiltersFromURL = () => {
  const queryParams = new URLSearchParams(window.location.search);
  const filters = {};
  
  // Get single value parameters
  if (queryParams.has('startDate')) filters.startDate = queryParams.get('startDate');
  if (queryParams.has('endDate')) filters.endDate = queryParams.get('endDate');
  
  // Get array parameters
  if (queryParams.has('DMR')) filters.DMR = queryParams.getAll('DMR');
  if (queryParams.has('incidentTypes')) filters.incidentTypes = queryParams.getAll('incidentTypes');
  if (queryParams.has('incidents')) filters.incidents = queryParams.getAll('incidents');
  if (queryParams.has('entities')) filters.entities = queryParams.getAll('entities');
  if (queryParams.has('businessProcesses')) filters.businessProcesses = queryParams.getAll('businessProcesses');
  if (queryParams.has('organizationalProcesses')) filters.organizationalProcesses = queryParams.getAll('organizationalProcesses');
  
  return filters;
};

// Function to get filters from sessionStorage
const getFiltersFromSessionStorage = () => {
  const storedFilters = sessionStorage.getItem('reportFilters');
  if (storedFilters) {
    try {
      return JSON.parse(storedFilters);
    } catch (e) {
      console.error("Error parsing stored filters:", e);
    }
  }
  return {};
};

// Add this helper function to export chart with white background
function getChartImageWithWhiteBg(chartRef) {
  if (!chartRef.current) return null;
  const canvas = chartRef.current.canvas;
  // Create a new canvas
  const tempCanvas = document.createElement('canvas');
  tempCanvas.width = canvas.width;
  tempCanvas.height = canvas.height;
  const ctx = tempCanvas.getContext('2d');
  // Fill with white
  ctx.fillStyle = '#fff';
  ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
  // Draw the chart on top
  ctx.drawImage(canvas, 0, 0);
  // Return the data URL
  return tempCanvas.toDataURL('image/jpeg', 1.0);
}

const EvolutionOfLossesAndIncidents = (props) => {
  const location = useLocation();
  
  // Extract filters from URL, sessionStorage, and props, with fallback to empty object
  const [effectiveFilters, setEffectiveFilters] = useState({});
  
  // Add state for email modal
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  
  // Add state for filter options
  const [filterOptions, setFilterOptions] = useState({
    dmrOptions: [],
    incidentTypeOptions: [],
    incidentOptions: [],
    entityOptions: [],
    businessProcessOptions: [],
    organizationalProcessOptions: []
  });
  
  // Add useEffect to get filter options from props
  useEffect(() => {
    if (props.filterOptions) {
      setFilterOptions(props.filterOptions);
    }
  }, [props.filterOptions]);
  
  useEffect(() => {
    console.log("Component received props:", props);
    console.log("Component received filters:", props.filters || {});
    
    // Try to get filters from different sources in order of priority
    const urlFilters = getFiltersFromURL();
    const sessionFilters = getFiltersFromSessionStorage();
    const propsFilters = props.filters || {};
    
    // Use filters in order of priority: URL > sessionStorage > props
    let finalFilters = {};
    
    if (Object.keys(urlFilters).length > 0) {
      console.log("Using filters from URL:", urlFilters);
      finalFilters = urlFilters;
    } else if (Object.keys(sessionFilters).length > 0) {
      console.log("Using filters from sessionStorage:", sessionFilters);
      finalFilters = sessionFilters;
    } else if (Object.keys(propsFilters).length > 0) {
      console.log("Using filters from props:", propsFilters);
      finalFilters = propsFilters;
    } else {
      console.log("No filters found, using defaults");
    }
    
    setEffectiveFilters(finalFilters);
  }, [location.search, props]);
  
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timePeriod, setTimePeriod] = useState('month');
  const [totalNetLosses, setTotalNetLosses] = useState(0);
  const [totalIncidents, setTotalIncidents] = useState(0);
  const chartRef = useRef(null);
  const API_BASE_URL = getApiBaseUrl();
  
  // Add this state to track if we're using mock data
  const [usingMockData, setUsingMockData] = useState(false);
  
  // Add this state to track email attachment
  const [emailAttachment, setEmailAttachment] = useState(null);
  
  // Move processChartData inside the component
  const processChartData = (data) => {
    // Extract periods and format them based on time period
    const periods = data.map(item => {
      const date = new Date(item.period);
      return date;
    });

    // Extract net losses and incident counts
    const netLosses = data.map(item => parseFloat(item.net_loss) || 0);
    const incidentCounts = data.map(item => parseInt(item.incident_count) || 0);

    // Calculate totals
    const netLossesTotal = netLosses.reduce((sum, value) => sum + value, 0);
    const incidentsTotal = incidentCounts.reduce((sum, value) => sum + value, 0);
    
    // Update state with totals
    setTotalNetLosses(netLossesTotal);
    setTotalIncidents(incidentsTotal);

    // Set chart data
    setChartData({
      labels: periods,
      datasets: [
        {
          label: 'Net Losses',
          data: netLosses,
          borderColor: 'rgb(255, 99, 132)',
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          yAxisID: 'y',
          tension: 0.1
        },
        {
          label: 'Incident Count',
          data: incidentCounts,
          borderColor: 'rgb(53, 162, 235)',
          backgroundColor: 'rgba(53, 162, 235, 0.5)',
          yAxisID: 'y1',
          tension: 0.1
        }
      ]
    });
  };

  // Log filters when component receives them
  useEffect(() => {
    console.log("Component received props:", props);
    console.log("Component received filters:", effectiveFilters);
  }, [props, effectiveFilters]);
  useEffect(() => {
    console.log("DMR filter changed:", effectiveFilters?.DMR);
    
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Create params object with proper validation
        const params = {
          timePeriod,
          start: effectiveFilters?.startDate || undefined,
          end: effectiveFilters?.endDate || undefined
        };
        
        // Only include parameters that have valid values
        if (!params.start) delete params.start;
        if (!params.end) delete params.end;
        
        // Track which filters are applied for better error messages
        const appliedFilters = [];
        
        // Add DMR to params
        if (effectiveFilters?.DMR && effectiveFilters.DMR.length > 0) {
          // Check if DMR is an array of objects with value property (from React-Select)
          if (typeof effectiveFilters.DMR[0] === 'object' && effectiveFilters.DMR[0].value) {
            params.DMR = effectiveFilters.DMR.map(item => item.value);
          } else {
            params.DMR = Array.isArray(effectiveFilters.DMR) ? effectiveFilters.DMR : [effectiveFilters.DMR];
          }
          appliedFilters.push(`DMR (${params.DMR.map(dmr => getDMRLabel(dmr)).join(', ')})`);
        }
        
        // Add other filters with tracking
        if (effectiveFilters?.incidentTypes && effectiveFilters.incidentTypes.length > 0) {
          params.incidentTypes = effectiveFilters.incidentTypes;
          appliedFilters.push('Incident Types');
        }
        
        if (effectiveFilters?.incidents && effectiveFilters.incidents.length > 0) {
          params.incidents = effectiveFilters.incidents;
          appliedFilters.push('Incidents');
        }
        
        if (effectiveFilters?.entities && effectiveFilters.entities.length > 0) {
          params.entities = effectiveFilters.entities;
          appliedFilters.push('Entities');
        }
        
        if (effectiveFilters?.businessProcesses && effectiveFilters.businessProcesses.length > 0) {
          params.businessProcesses = effectiveFilters.businessProcesses;
          appliedFilters.push('Business Processes');
        }
        
        if (effectiveFilters?.organizationalProcesses && effectiveFilters.organizationalProcesses.length > 0) {
          params.organizationalProcesses = effectiveFilters.organizationalProcesses;
          appliedFilters.push('Organizational Processes');
        }
        
        console.log("Sending request with params:", params);
        
        // Check if any filters are applied beyond date range
        const hasFilters = appliedFilters.length > 0;
        
        try {
          const response = await axios.get(`${API_BASE_URL}/lossesAndIncidentsOverTime`, {
            params,
            withCredentials: true
          });
          
          let data = response.data.data;
          console.log("Data from API:", data);
          
          if (!data || data.length === 0) {
            if (hasFilters) {
              // Show "no data available" message with specific filters mentioned
              const filterMessage = appliedFilters.length > 1 
                ? `filters (${appliedFilters.join(', ')})` 
                : `filter ${appliedFilters[0]}`;
              setError(`No data available for the selected ${filterMessage}. Try adjusting your criteria.`);
              setUsingMockData(false);
              return;
            } else {
              console.log("No data from API, using mock data");
              data = generateMockData(timePeriod, params.start, params.end);
              toast.success("No data available from API. Showing mock data for demonstration purposes.");
              setUsingMockData(true);
            }
          } else {
            setUsingMockData(false);
          }
          
          console.log("Final data for chart:", data);
          processChartData(data);
        } catch (apiError) {
          console.error('API error:', apiError);
          
          // Check if filters are applied and show no data message instead of mock data
          if (hasFilters) {
            console.log("API error with filters applied, showing no data message");
            const filterMessage = appliedFilters.length > 1 
              ? `filters (${appliedFilters.join(', ')})` 
              : `filter ${appliedFilters[0]}`;
            setError(`No data available for the selected ${filterMessage}. Try adjusting your criteria.`);
            setUsingMockData(false);
            return;
          }
          
          console.log("Using mock data due to API error");
          const mockData = generateMockData(timePeriod, params.start, params.end);
          processChartData(mockData);
          toast.error("API endpoint not available. Showing mock data for demonstration purposes.");
          setUsingMockData(true);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to fetch data. Using mock data instead.');
        const mockData = generateMockData(timePeriod, 
          effectiveFilters?.startDate, 
          effectiveFilters?.endDate);
        processChartData(mockData);
      } finally {
        setLoading(false);
      }
    };

    if (effectiveFilters) {
      fetchData();
    }
  }, [timePeriod, effectiveFilters]);

  const zoomIn = () => {
    if (chartRef.current) {
      const chart = chartRef.current;
      const xAxis = chart.scales.x;
      const min = xAxis.min;
      const max = xAxis.max;
      const range = max - min;
      const newRange = range * 0.8; // Zoom in by 20%
      const center = (min + max) / 2;
      const newMin = center - newRange / 2;
      const newMax = center + newRange / 2;

      chart.zoomScale('x', { min: newMin, max: newMax }, 'default');
    }
  };

  const zoomOut = () => {
    if (chartRef.current) {
      const chart = chartRef.current;
      const xAxis = chart.scales.x;
      const min = xAxis.min;
      const max = xAxis.max;
      const range = max - min;
      const newRange = range * 1.25; // Zoom out by 25%
      const center = (min + max) / 2;
      const newMin = center - newRange / 2;
      const newMax = center + newRange / 2;

      const dataMin = effectiveFilters?.startDate ? new Date(effectiveFilters.startDate).getTime() : new Date(chart.data.labels[0]).getTime();
      const dataMax = effectiveFilters?.endDate ? new Date(effectiveFilters.endDate).getTime() : new Date(chart.data.labels[chart.data.labels.length - 1]).getTime();

      chart.zoomScale('x', {
        min: Math.max(newMin, dataMin),
        max: Math.min(newMax, dataMax)
      }, 'default');
    }
  };

  const panLeft = () => {
    if (!chartRef.current) {
      console.error('Pan Left: chartRef.current is not defined');
      return;
    }
    try {
      const chart = chartRef.current;
      const xAxis = chart.scales.x;
      if (!xAxis) {
        console.error('Pan Left: xAxis is not defined');
        return;
      }
      const min = xAxis.min;
      const max = xAxis.max;
      const range = max - min;
      const currentCenter = (min + max) / 2;

      const dataMin = effectiveFilters?.startDate ? new Date(effectiveFilters.startDate).getTime() : new Date(chart.data.labels[0]).getTime();
      const dataMax = effectiveFilters?.endDate ? new Date(effectiveFilters.endDate).getTime() : new Date(chart.data.labels[chart.data.labels.length - 1]).getTime();
      console.log(`Pan Left: dataMin=${new Date(dataMin).toISOString()}, dataMax=${new Date(dataMax).toISOString()}`);

      // Calculate new center by subtracting 1 month, ensuring proper date handling
      const currentCenterDate = new Date(currentCenter);
      const newCenterDate = new Date(currentCenterDate);
      newCenterDate.setUTCHours(0, 0, 0, 0); // Normalize to midnight UTC
      currentCenterDate.setUTCHours(0, 0, 0, 0);
      newCenterDate.setUTCFullYear(currentCenterDate.getUTCFullYear());
      newCenterDate.setUTCMonth(currentCenterDate.getUTCMonth() - 1);
      newCenterDate.setUTCDate(currentCenterDate.getUTCDate());
      let shift = currentCenter - newCenterDate.getTime(); // Positive shift for left movement

      const newCenter = currentCenter - shift;
      const newMin = newCenter - range / 2;

      // Adjust shift if hitting the left bound (based on center)
      let adjustedShift = shift;
      if (newMin < dataMin) {
        const newCenterAtBound = dataMin + range / 2;
        adjustedShift = currentCenter - newCenterAtBound;
        console.log(`Pan Left: Adjusted shift to align center at ${new Date(newCenterAtBound).toISOString()}`);
      }

      if (adjustedShift <= 0) {
        console.log('Pan Left: Reached start bound');
        return;
      }

      console.log(`Pan Left: Current center=${new Date(currentCenter).toISOString()}, Shifting by 1 month (${adjustedShift}ms), New center=${new Date(currentCenter - adjustedShift).toISOString()}`);

      chart.pan({ x: adjustedShift }, undefined, 'default'); // Positive shift moves left
    } catch (err) {
      console.error('Pan Left Error:', err);
    }
  };

  const panRight = () => {
    if (!chartRef.current) {
      console.error('Pan Right: chartRef.current is not defined');
      return;
    }
    try {
      const chart = chartRef.current;
      const xAxis = chart.scales.x;
      if (!xAxis) {
        console.error('Pan Right: xAxis is not defined');
        return;
      }
      const min = xAxis.min;
      const max = xAxis.max;
      const range = max - min;
      const currentCenter = (min + max) / 2;

      const dataMin = effectiveFilters?.startDate ? new Date(effectiveFilters.startDate).getTime() : new Date(chart.data.labels[0]).getTime();
      const dataMax = effectiveFilters?.endDate ? new Date(effectiveFilters.endDate).getTime() : new Date(chart.data.labels[chart.data.labels.length - 1]).getTime();
      console.log(`Pan Right: dataMin=${new Date(dataMin).toISOString()}, dataMax=${new Date(dataMax).toISOString()}`);

      // Calculate new center by adding 1 month, ensuring proper date handling
      const currentCenterDate = new Date(currentCenter);
      const newCenterDate = new Date(currentCenterDate);
      newCenterDate.setUTCHours(0, 0, 0, 0); // Normalize to midnight UTC
      currentCenterDate.setUTCHours(0, 0, 0, 0);
      newCenterDate.setUTCFullYear(currentCenterDate.getUTCFullYear());
      newCenterDate.setUTCMonth(currentCenterDate.getUTCMonth() + 1);
      newCenterDate.setUTCDate(currentCenterDate.getUTCDate());
      let shift = newCenterDate.getTime() - currentCenter; // Positive shift, but we need negative for right movement

      const newCenter = currentCenter + shift;
      const newMax = newCenter + range / 2;

      // Adjust shift if hitting the right bound (based on center)
      let adjustedShift = shift;
      if (newMax > dataMax) {
        const newCenterAtBound = dataMax - range / 2;
        adjustedShift = newCenterAtBound - currentCenter;
        console.log(`Pan Right: Adjusted shift to align center at ${new Date(newCenterAtBound).toISOString()}`);
      }

      if (adjustedShift <= 0) {
        console.log('Pan Right: Reached end bound');
        return;
      }

      console.log(`Pan Right: Current center=${new Date(currentCenter).toISOString()}, Shifting by 1 month (${adjustedShift}ms), New center=${new Date(currentCenter + adjustedShift).toISOString()}`);

      chart.pan({ x: -adjustedShift }, undefined, 'default'); // Negative shift moves right
    } catch (err) {
      console.error('Pan Right Error:', err);
    }
  };

  const resetZoom = () => {
    if (chartRef.current) {
      chartRef.current.resetZoom();
    }
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Evolution of Losses and Incidents Over Time',
      },
      zoom: {
        zoom: {
          wheel: {
            enabled: false,
          },
          pinch: {
            enabled: true
          },
          mode: 'x',
        },
        pan: {
          enabled: true,
          mode: 'x',
        },
        limits: {
          x: {
            min: effectiveFilters?.startDate ? new Date(effectiveFilters.startDate).getTime() : null,
            max: effectiveFilters?.endDate ? new Date(effectiveFilters.endDate).getTime() : null
          }
        }
      }
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Net Losses',
        },
        beginAtZero: true,
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: 'Number of Incidents',
        },
        beginAtZero: true,
        grid: {
          drawOnChartArea: false,
        },
      },
      x: {
        type: 'time',
        time: {
          unit: timePeriod,
          tooltipFormat: timePeriod === 'day' ? 'yyyy-MM-dd' : timePeriod === 'month' ? 'yyyy-MM' : 'yyyy',
          displayFormats: {
            day: 'yyyy-MM-dd',
            month: 'yyyy-MM',
            year: 'yyyy'
          }
        },
        title: {
          display: true,
          text: 'Time',
        },
        adapters: {
          date: {
            locale: enUS
          }
        },
        min: effectiveFilters?.startDate ? new Date(effectiveFilters.startDate).getTime() : undefined,
        max: effectiveFilters?.endDate ? new Date(effectiveFilters.endDate).getTime() : undefined,
      },
    },
  };

  const handleTimePeriodChange = (e) => {
    setTimePeriod(e.target.value);
  };

  const downloadPDF = async () => {
    try {
      if (!chartRef.current) {
        alert("Error: Unable to generate PDF. The chart is not ready.");
        return;
      }

      // Use helper to get chart image with white background
      const chartImage = getChartImageWithWhiteBg(chartRef);
      
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'in',
        format: 'letter'
      });
      
      // Add title
      pdf.setFontSize(18);
      pdf.setTextColor(0, 0, 0);
      pdf.text('Evolution of Losses and Incidents Over Time', 0.5, 0.5);
      
      // Add date range
      pdf.setFontSize(12);
      pdf.setTextColor(100, 100, 100);
      const dateRange = effectiveFilters?.startDate && effectiveFilters?.endDate 
        ? `${format(new Date(effectiveFilters.startDate), 'MMM d, yyyy')} - ${format(new Date(effectiveFilters.endDate), 'MMM d, yyyy')}` 
        : 'All Time';
      pdf.text(`Date Range: ${dateRange}`, 0.5, 0.8);
      
      // Add chart
      const imgProps = pdf.getImageProperties(chartImage);
      const pdfWidth = pdf.internal.pageSize.getWidth() - 1;
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
      
      pdf.addImage(chartImage, 'JPEG', 0.5, 1, pdfWidth, pdfHeight);
      
      let yPosition = pdfHeight + 1.5;
      
      // Add filter details
      pdf.setFontSize(14);
      pdf.setTextColor(45, 55, 72);
      pdf.text('Applied Filters', 0.5, yPosition);
      yPosition += 0.3;
      
      pdf.setFontSize(10);
      pdf.setTextColor(100, 100, 100);
      
      // Add each filter
      const addFilterLine = (label, value) => {
        pdf.text(`${label}:`, 0.5, yPosition);
        pdf.setTextColor(0, 0, 0);
        pdf.text(value, 2.5, yPosition);
        pdf.setTextColor(100, 100, 100);
        yPosition += 0.2;
      };
      
      addFilterLine('Time Period', timePeriod.charAt(0).toUpperCase() + timePeriod.slice(1));
      addFilterLine('Risk Control Level (DMR)', displayFilterValue(effectiveFilters?.DMR, filterOptions.dmrOptions, getDMRLabel));
      addFilterLine('Incident Types', displayFilterValue(effectiveFilters?.incidentTypes, filterOptions.incidentTypeOptions));
      addFilterLine('Incidents', displayFilterValue(effectiveFilters?.incidents, filterOptions.incidentOptions));
      addFilterLine('Entities', displayFilterValue(effectiveFilters?.entities, filterOptions.entityOptions));
      addFilterLine('Business Processes', displayFilterValue(effectiveFilters?.businessProcesses, filterOptions.businessProcessOptions));
      addFilterLine('Organizational Processes', displayFilterValue(effectiveFilters?.organizationalProcesses, filterOptions.organizationalProcessOptions));
      
      yPosition += 0.3;
      
      // Add metrics
      pdf.setFontSize(14);
      pdf.setTextColor(45, 55, 72);
      pdf.text('Summary', 0.5, yPosition);
      yPosition += 0.3;
      
      pdf.setFontSize(10);
      pdf.setTextColor(100, 100, 100);
      
      addFilterLine('Total Net Losses', totalNetLosses.toFixed(2));
      addFilterLine('Total Number of Incidents', totalIncidents.toString());
      
      if (totalIncidents > 0) {
        addFilterLine('Average Loss per Incident', (totalNetLosses / totalIncidents).toFixed(2));
      }
      
      // Add mock data notice if applicable
      if (usingMockData) {
        yPosition += 0.3;
        pdf.setFontSize(9);
        pdf.setTextColor(150, 150, 150);
        pdf.text('Note: This report contains mock data for demonstration purposes.', 0.5, yPosition);
      }
      
      pdf.save('losses_and_incidents_over_time.pdf');
    } catch (err) {
      console.error("Error generating PDF:", err);
      alert("Error generating PDF.");
    }
  };

  const downloadExcel = () => {
    try {
      if (!chartData) {
        alert("Error: Unable to generate Excel file. The data is not ready.");
        return;
      }

      const excelData = chartData.labels.map((label, index) => ({
        Period: new Date(label).toLocaleDateString('en-US', {
          year: 'numeric',
          month: timePeriod === 'month' ? '2-digit' : 'numeric',
          day: timePeriod === 'day' ? '2-digit' : undefined
        }),
        'Net Losses': chartData.datasets[0].data[index],
        Incidents: chartData.datasets[1].data[index],
      }));
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "LossesAndIncidents");
      XLSX.writeFile(workbook, "losses_and_incidents_over_time.xlsx");
    } catch (err) {
      console.error("Error generating Excel file:", err);
      alert("Error generating Excel file.");
    }
  };

  const handleEmailReport = async () => {
    if (!chartRef.current) {
      alert('Le graphique n\'est pas prêt.');
      return;
    }
    try {
      // Use helper to get chart image with white background
      const chartImage = getChartImageWithWhiteBg(chartRef);
      const pdf = new jsPDF({ orientation: 'portrait', unit: 'in', format: 'letter' });
      pdf.setFontSize(18);
      pdf.setTextColor(0, 0, 0);
      pdf.text('Evolution of Losses and Incidents Over Time', 0.5, 0.5);
      pdf.setFontSize(12);
      pdf.setTextColor(100, 100, 100);
      const dateRange = effectiveFilters?.startDate && effectiveFilters?.endDate 
        ? `${format(new Date(effectiveFilters.startDate), 'MMM d, yyyy')} - ${format(new Date(effectiveFilters.endDate), 'MMM d, yyyy')}` 
        : 'All Time';
      pdf.text(`Date Range: ${dateRange}`, 0.5, 0.8);
      const imgProps = pdf.getImageProperties(chartImage);
      const pdfWidth = pdf.internal.pageSize.getWidth() - 1;
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
      pdf.addImage(chartImage, 'JPEG', 0.5, 1, pdfWidth, pdfHeight);
      let yPosition = pdfHeight + 1.5;
      pdf.setFontSize(14);
      pdf.setTextColor(45, 55, 72);
      pdf.text('Applied Filters', 0.5, yPosition);
      yPosition += 0.3;
      pdf.setFontSize(10);
      pdf.setTextColor(100, 100, 100);
      const addFilterLine = (label, value) => {
        pdf.text(`${label}:`, 0.5, yPosition);
        pdf.setTextColor(0, 0, 0);
        pdf.text(value, 2.5, yPosition);
        pdf.setTextColor(100, 100, 100);
        yPosition += 0.2;
      };
      addFilterLine('Time Period', timePeriod.charAt(0).toUpperCase() + timePeriod.slice(1));
      addFilterLine('Risk Control Level (DMR)', displayFilterValue(effectiveFilters?.DMR, filterOptions.dmrOptions, getDMRLabel));
      addFilterLine('Incident Types', displayFilterValue(effectiveFilters?.incidentTypes, filterOptions.incidentTypeOptions));
      addFilterLine('Incidents', displayFilterValue(effectiveFilters?.incidents, filterOptions.incidentOptions));
      addFilterLine('Entities', displayFilterValue(effectiveFilters?.entities, filterOptions.entityOptions));
      addFilterLine('Business Processes', displayFilterValue(effectiveFilters?.businessProcesses, filterOptions.businessProcessOptions));
      addFilterLine('Organizational Processes', displayFilterValue(effectiveFilters?.organizationalProcesses, filterOptions.organizationalProcessOptions));
      yPosition += 0.3;
      pdf.setFontSize(14);
      pdf.setTextColor(45, 55, 72);
      pdf.text('Summary', 0.5, yPosition);
      yPosition += 0.3;
      pdf.setFontSize(10);
      pdf.setTextColor(100, 100, 100);
      addFilterLine('Total Net Losses', totalNetLosses.toFixed(2));
      addFilterLine('Total Number of Incidents', totalIncidents.toString());
      if (totalIncidents > 0) {
        addFilterLine('Average Loss per Incident', (totalNetLosses / totalIncidents).toFixed(2));
      }
      if (usingMockData) {
        yPosition += 0.3;
        pdf.setFontSize(9);
        pdf.setTextColor(150, 150, 150);
        pdf.text('Note: This report contains mock data for demonstration purposes.', 0.5, yPosition);
      }
      const pdfBase64 = btoa(
        new Uint8Array(pdf.output('arraybuffer'))
          .reduce((data, byte) => data + String.fromCharCode(byte), '')
      );
      setEmailAttachment({
        base64: pdfBase64,
        filename: 'losses_and_incidents_over_time.pdf',
        contentType: 'application/pdf',
      });
      setIsEmailModalOpen(true);
    } catch (err) {
      alert('Erreur lors de la génération du PDF.');
    }
  };

  if (loading) return (
    <div className="p-6 text-center">
      <div className="animate-pulse flex flex-col items-center">
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-2.5"></div>
        <div className="h-64 bg-gray-200 rounded w-full mb-2.5"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    </div>
  );

  if (error) {
    // Check if the error is about no data being available
    const isNoDataError = error.includes('No data available for the selected');
    
    return (
      <div className="p-6 text-center">
        {isNoDataError ? (
          <div className="bg-gray-50 border border-gray-200 text-gray-700 px-4 py-8 rounded relative mb-4" role="alert">
            <div className="flex flex-col items-center justify-center">
              <svg className="w-16 h-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span className="block text-lg font-medium mb-2">{error}</span>
              <p className="text-sm text-gray-500 mt-2">
                The selected combination of filters returned no data. Try:
              </p>
              <ul className="text-sm text-gray-500 list-disc list-inside mt-1 mb-3">
                <li>Selecting a different time period</li>
                <li>Broadening your date range</li>
                <li>Removing some filters</li>
              </ul>
              <div className="mt-4">
                <Button 
                  onClick={() => window.location.reload()} 
                  className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded transition duration-150 ease-in-out"
                >
                  Reset Filters
                </Button>
              </div>
            </div>
          </div>
        ) : (
          // Keep existing error handling for other types of errors
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error}</span>
            <p className="mt-2 text-sm">
              Please check if the API endpoints are properly configured and the backend server is running.
            </p>
          </div>
        )}
    </div>
  );}

  return (
    <div id="losses-and-incidents-chart" className="p-6 bg-white rounded-lg shadow border border-gray-200">
      <div className="relative">
        <div className="absolute top-0 right-0 flex gap-2">
          <Button
            onClick={zoomIn}
            className="bg-[#22c55e] hover:bg-[#16a34a] text-white w-8 h-8 flex items-center justify-center"
            title="Zoom In"
          >
            +
          </Button>
          <Button
            onClick={zoomOut}
            className="bg-[#22c55e] hover:bg-[#16a34a] text-white w-8 h-8 flex items-center justify-center"
            title="Zoom Out"
          >
            -
          </Button>
          <Button
            onClick={panLeft}
            className="bg-[#22c55e] hover:bg-[#16a34a] text-white w-8 h-8 flex items-center justify-center"
            title="Pan Left"
          >
            ←
          </Button>
          <Button
            onClick={panRight}
            className="bg-[#22c55e] hover:bg-[#16a34a] text-white w-8 h-8 flex items-center justify-center"
            title="Pan Right"
          >
            →
          </Button>
          <Button
            onClick={resetZoom}
            className="bg-[#22c55e] hover:bg-[#16a34a] text-white"
          >
            Reset Zoom
          </Button>
        </div>
        <div className="mb-4 flex items-center gap-4">
          <div>
            <label htmlFor="timePeriod" className="mr-2">Group by:</label>
            <select
              id="timePeriod"
              value={timePeriod}
              onChange={handleTimePeriodChange}
              className="border rounded p-1"
            >
              <option value="day">Day</option>
              <option value="month">Month</option>
              <option value="year">Year</option>
            </select>
          </div>
        </div>
        <Line ref={chartRef} data={chartData} options={options} />
      </div>
      <div className="flex gap-4 mt-4">
        <Button onClick={downloadPDF} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">
          Download PDF
        </Button>
        <Button onClick={downloadExcel} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">
          Download Excel
        </Button>
        <Button
          onClick={handleEmailReport}
          className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
          </svg>
          Send via Email
        </Button>
      </div>
      <div className="mt-6 bg-gray-50 rounded-xl p-4 border border-gray-200">
        <h3 className="text-lg font-semibold mb-3 text-gray-800">Details</h3>
        
        {/* Existing metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <p className="text-sm text-gray-500 mb-1">Total Net Losses</p>
            <p className="text-xl font-bold text-[#1A2942]">{totalNetLosses.toFixed(2)}</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <p className="text-sm text-gray-500 mb-1">Total Number of Incidents</p>
            <p className="text-xl font-bold text-[#1A2942]">{totalIncidents}</p>
          </div>
        </div>
        
        {/* Filter details */}
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-4">
          <h4 className="font-medium text-gray-700 mb-2">Applied Filters</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2 text-sm">
            {/* Date Range - always show */}
            <div className="flex justify-between">
              <span className="text-gray-500">Date Range:</span>
              <span className="font-medium">
                {effectiveFilters?.startDate && effectiveFilters?.endDate ? 
                  `${format(new Date(effectiveFilters.startDate), 'MMM d, yyyy')} - ${format(new Date(effectiveFilters.endDate), 'MMM d, yyyy')}` : 
                  'All Time'}
              </span>
            </div>
            
            {/* Time Period - always show */}
            <div className="flex justify-between">
              <span className="text-gray-500">Time Period:</span>
              <span className="font-medium capitalize">{timePeriod}</span>
            </div>
            
            {/* DMR - always show */}
            <div className="flex justify-between">
              <span className="text-gray-500">Risk Control Level (DMR):</span>
              <span className="font-medium">
                {displayFilterValue(effectiveFilters?.DMR, filterOptions.dmrOptions, getDMRLabel)}
              </span>
            </div>
            
            {/* Incident Types - always show */}
            <div className="flex justify-between">
              <span className="text-gray-500">Incident Types:</span>
              <span className="font-medium">
                {displayFilterValue(effectiveFilters?.incidentTypes, filterOptions.incidentTypeOptions)}
              </span>
            </div>
            
            {/* Incidents - always show */}
            <div className="flex justify-between">
              <span className="text-gray-500">Incidents:</span>
              <span className="font-medium">
                {displayFilterValue(effectiveFilters?.incidents, filterOptions.incidentOptions)}
              </span>
            </div>
            
            {/* Entities - always show */}
            <div className="flex justify-between">
              <span className="text-gray-500">Entities:</span>
              <span className="font-medium">
                {displayFilterValue(effectiveFilters?.entities, filterOptions.entityOptions)}
              </span>
            </div>
            
            {/* Business Processes - always show */}
            <div className="flex justify-between">
              <span className="text-gray-500">Business Processes:</span>
              <span className="font-medium">
                {displayFilterValue(effectiveFilters?.businessProcesses, filterOptions.businessProcessOptions)}
              </span>
            </div>
            
            {/* Organizational Processes - always show */}
            <div className="flex justify-between">
              <span className="text-gray-500">Organizational Processes:</span>
              <span className="font-medium">
                {displayFilterValue(effectiveFilters?.organizationalProcesses, filterOptions.organizationalProcessOptions)}
              </span>
            </div>
          </div>
        </div>
        
        {/* Additional statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <p className="text-sm text-gray-500 mb-1">Average Loss per Incident</p>
            <p className="text-xl font-bold text-[#1A2942]">
              {totalIncidents > 0 ? (totalNetLosses / totalIncidents).toFixed(2) : '0.00'}
            </p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <p className="text-sm text-gray-500 mb-1">Highest Loss Period</p>
            <p className="text-xl font-bold text-[#1A2942]">
              {chartData && chartData.datasets[0].data.length > 0 ? 
                format(new Date(chartData.labels[chartData.datasets[0].data.indexOf(Math.max(...chartData.datasets[0].data))]), 
                  timePeriod === 'day' ? 'MMM d, yyyy' : timePeriod === 'month' ? 'MMM yyyy' : 'yyyy') : 
                'N/A'}
            </p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <p className="text-sm text-gray-500 mb-1">Most Incidents Period</p>
            <p className="text-xl font-bold text-[#1A2942]">
              {chartData && chartData.datasets[1].data.length > 0 ? 
                format(new Date(chartData.labels[chartData.datasets[1].data.indexOf(Math.max(...chartData.datasets[1].data))]), 
                  timePeriod === 'day' ? 'MMM d, yyyy' : timePeriod === 'month' ? 'MMM yyyy' : 'yyyy') : 
                'N/A'}
            </p>
          </div>
        </div>
      </div>
      <EmailReportModal
        isOpen={isEmailModalOpen}
        onClose={() => setIsEmailModalOpen(false)}
        reportType="evolution-losses-incidents"
        reportTitle="Evolution of Losses and Incidents Over Time"
        defaultAttachment={emailAttachment}
      />
    </div>
  );
};

// Add this function to format filter values for display
const formatFilterValue = (value, options) => {
  // If value is undefined, null, empty array, or empty string, return "All"
  if (value === undefined || value === null || 
      (Array.isArray(value) && value.length === 0) || 
      value === '') {
    return 'All';
  }
  
  if (Array.isArray(value)) {
    // Handle objects with label/value properties (from React-Select)
    if (typeof value[0] === 'object' && value[0].label) {
      return value.map(v => v.label).join(', ');
    }
    
    // Try to match values with options to get labels
    if (options && options.length > 0) {
      const labels = value.map(val => {
        const option = options.find(opt => 
          (typeof opt === 'object' && opt.value === val) || opt === val
        );
        return option ? (typeof option === 'object' ? option.label : option) : val;
      });
      
      return labels.join(', ');
    }
    
    // If no options or matching failed, just join the values
    return value.join(', ');
  }
  
  // Single value
  if (options && options.length > 0) {
    const option = options.find(opt => 
      (typeof opt === 'object' && opt.value === value) || opt === value
    );
    return option ? (typeof option === 'object' ? option.label : option) : value;
  }
  
  return value;
};

// Add this function to generate mock data
const generateMockData = (timePeriod, startDate, endDate) => {
  const data = [];
  const start = startDate ? new Date(startDate) : new Date(new Date().setFullYear(new Date().getFullYear() - 1));
  const end = endDate ? new Date(endDate) : new Date();
  
  let current = new Date(start);
  while (current <= end) {
    const randomLoss = Math.floor(Math.random() * 10000);
    const randomIncidents = Math.floor(Math.random() * 10);
    
    data.push({
      period: current.toISOString(),
      net_loss: randomLoss,
      incident_count: randomIncidents
    });
    
    // Increment based on time period
    if (timePeriod === 'day') {
      current.setDate(current.getDate() + 1);
    } else if (timePeriod === 'year') {
      current.setFullYear(current.getFullYear() + 1);
    } else { // month
      current.setMonth(current.getMonth() + 1);
    }
  }
  
  return data;
};

// Add this helper function to display filter values consistently
const displayFilterValue = (filterValue, options, formatter) => {
  if (!filterValue || (Array.isArray(filterValue) && filterValue.length === 0)) {
    return 'All';
  }
  
  if (Array.isArray(filterValue)) {
    return filterValue.map(item => {
      if (typeof item === 'object' && item.label) {
        return item.label;
      } else if (formatter) {
        return formatter(item);
      } else if (options) {
        return formatFilterValue(item, options);
      } else {
        return item;
      }
    }).join(', ');
  }
  
  if (typeof filterValue === 'object' && filterValue.label) {
    return filterValue.label;
  } else if (formatter) {
    return formatter(filterValue);
  } else if (options) {
    return formatFilterValue(filterValue, options);
  } else {
    return filterValue;
  }
};

export default EvolutionOfLossesAndIncidents;
