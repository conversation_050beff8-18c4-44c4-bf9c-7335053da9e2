import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import actionService from '../../services/actionService';
import { toast } from 'sonner';

// Initial state
const initialState = {
  actions: [],
  currentAction: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: ''
};

// Get all actions
export const getAllActions = createAsyncThunk(
  'actions/getAll',
  async (_, thunkAPI) => {
    try {
      return await actionService.getAllActions();
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch actions';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get all actions for an action plan
export const getActionsByActionPlanId = createAsyncThunk(
  'actions/getByActionPlanId',
  async (actionPlanId, thunkAPI) => {
    try {
      return await actionService.getActionsByActionPlanId(actionPlanId);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch actions';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get action by ID
export const getActionById = createAsyncThunk(
  'actions/getById',
  async (id, thunkAPI) => {
    try {
      return await actionService.getActionById(id);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch action';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create new action
export const createAction = createAsyncThunk(
  'actions/create',
  async (actionData, thunkAPI) => {
    try {
      return await actionService.createAction(actionData);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to create action';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update action
export const updateAction = createAsyncThunk(
  'actions/update',
  async ({ id, actionData }, thunkAPI) => {
    try {
      return await actionService.updateAction(id, actionData);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to update action';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete action
export const deleteAction = createAsyncThunk(
  'actions/delete',
  async (id, thunkAPI) => {
    try {
      return await actionService.deleteAction(id);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete action';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete multiple actions
export const deleteMultipleActions = createAsyncThunk(
  'actions/deleteMultiple',
  async (ids, thunkAPI) => {
    try {
      return await actionService.deleteMultipleActions(ids);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete actions';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

const actionSlice = createSlice({
  name: 'action',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    },
    clearActions: (state) => {
      state.actions = [];
      state.currentAction = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all actions
      .addCase(getAllActions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllActions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.actions = action.payload.data;
      })
      .addCase(getAllActions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Get actions by action plan ID
      .addCase(getActionsByActionPlanId.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getActionsByActionPlanId.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.actions = action.payload.data;
      })
      .addCase(getActionsByActionPlanId.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Get action by ID
      .addCase(getActionById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getActionById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentAction = action.payload.data;
      })
      .addCase(getActionById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Create action
      .addCase(createAction.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createAction.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.actions.push(action.payload.data);
        toast.success('Action created successfully');
      })
      .addCase(createAction.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Update action
      .addCase(updateAction.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateAction.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.actions = state.actions.map(item =>
          item.actionID === action.payload.data.actionID ? action.payload.data : item
        );
        state.currentAction = action.payload.data;
        toast.success('Action updated successfully');
      })
      .addCase(updateAction.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Delete action
      .addCase(deleteAction.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteAction.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.actions = state.actions.filter(item => item.actionID !== action.meta.arg);
        toast.success('Action deleted successfully');
      })
      .addCase(deleteAction.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Delete multiple actions
      .addCase(deleteMultipleActions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteMultipleActions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.actions = state.actions.filter(item => !action.meta.arg.includes(item.actionID));
        toast.success('Actions deleted successfully');
      })
      .addCase(deleteMultipleActions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      });
  }
});

export const { reset, clearActions } = actionSlice.actions;
export default actionSlice.reducer;
