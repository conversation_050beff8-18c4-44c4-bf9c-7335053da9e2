const db = require('../../models');
const { Op } = require('sequelize');

/**
 * Get all activities for a specific control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getControlActivities = async (req, res) => {
  try {
    const { id: controlId } = req.params;

    // Validate control exists
    const control = await db.Control.findByPk(controlId);
    if (!control) {
      return res.status(404).json({
        success: false,
        message: 'Control not found'
      });
    }

    // Get activity logs from ControlActivityLog table
    const activityLogs = await db.ControlActivityLog.findAll({
      where: { control_id: controlId },
      attributes: ['id', 'timestamp', 'user', 'type', 'field', 'old_value', 'new_value', 'details'],
      order: [['timestamp', 'DESC']]
    });

    // Format activity logs as activity items
    const logActivities = activityLogs.map(log => ({
      id: `log-${log.id}`,
      type: log.type,
      timestamp: log.timestamp,
      user: log.user,
      field: log.field,
      oldValue: log.old_value,
      newValue: log.new_value,
      details: log.details
    }));

    // Sort all activities by timestamp (newest first)
    const allActivities = logActivities
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    return res.status(200).json({
      success: true,
      data: allActivities
    });
  } catch (error) {
    console.error('Error in getControlActivities:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve control activities',
      error: error.message
    });
  }
};

/**
 * Log control creation activity
 * @param {Object} control - The created control object
 * @param {string} username - Username of the user who created the control
 */
exports.logCreationActivity = async (control, username) => {
  try {
    const activityLog = await db.ControlActivityLog.create({
      control_id: control.controlID,
      user: username,
      type: 'creation',
      details: `Control "${control.name}" was created`
    }, {
      returning: ['id', 'control_id', 'timestamp', 'user', 'type', 'field', 'old_value', 'new_value', 'details']
    });

    return true;
  } catch (error) {
    console.error('Error logging control creation activity:', error);
    return false;
  }
};

/**
 * Log control deletion activity
 * @param {Object} control - The control object before deletion
 * @param {string} username - Username of the user who deleted the control
 */
exports.logDeletionActivity = async (control, username) => {
  try {
    await db.ControlActivityLog.create({
      control_id: control.controlID,
      user: username,
      type: 'deletion',
      details: `Control "${control.name}" was deleted`
    });
    return true;
  } catch (error) {
    console.error('Error logging control deletion activity:', error);
    return false;
  }
};

/**
 * Log control update activities by comparing old and new data
 * @param {Object} oldControl - The control object before update
 * @param {Object} newData - The new data being updated
 * @param {string} username - Username of the user making the update
 */
exports.logUpdateActivities = async (oldControl, newData, username) => {
  try {
    console.log('🔍 [DEBUG] logUpdateActivities called:', {
      controlID: oldControl?.controlID,
      username,
      fieldsToUpdate: Object.keys(newData)
    });

    const changes = [];

    // Define which fields to track and their display names (in French)
    const trackableFields = {
      name: 'Nom',
      code: 'Code',
      controlKey: 'Contrôle Clé',
      controlExecutionMethod: 'Méthode d\'Exécution du Contrôle',
      objective: 'Objectif',
      executionProcedure: 'Procédure d\'Exécution',
      operationalCost: 'Coût Opérationnel',
      organizationalLevel: 'Niveau Organisationnel',
      sampleType: 'Type d\'Échantillon',
      testingFrequency: 'Fréquence des Tests',
      testingMethod: 'Méthode de Test',
      testingPopulationSize: 'Taille de la Population de Test',
      testingProcedure: 'Procédure de Test',
      implementingActionPlan: 'Plan d\'Action de Mise en Œuvre',
      designQuality: 'Qualité de Conception',
      effectivenessLevel: 'Niveau d\'Efficacité',
      businessProcess: 'Processus Métier',
      organizationalProcess: 'Processus Organisationnel',
      operation: 'Opération',
      application: 'Application',
      entity: 'Entité',
      controlType: 'Type de Contrôle',
      risk: 'Risque',
      comment: 'Commentaire'
    };
    
    // Check each trackable field for changes
    for (const [field, displayName] of Object.entries(trackableFields)) {
      if (newData[field] !== undefined && oldControl[field] !== newData[field]) {
        console.log(`🔍 [DEBUG] Field change detected: ${field}`, {
          oldValue: oldControl[field],
          newValue: newData[field],
          displayName
        });

        try {
          const activityLog = await db.ControlActivityLog.create({
            control_id: oldControl.controlID,
            user: username,
            type: 'update',
            field: field,
            old_value: oldControl[field] || '',
            new_value: newData[field] || '',
            details: `${displayName} modifié`
          }, {
            returning: ['id', 'control_id', 'timestamp', 'user', 'type', 'field', 'old_value', 'new_value', 'details']
          });

          console.log(`✅ [DEBUG] Activity log created: ${activityLog.id}`);
          changes.push(field);
        } catch (createError) {
          console.error('❌ [DEBUG] Error creating activity log:', createError);
        }
      }
    }

    console.log(`🔍 [DEBUG] Total changes logged: ${changes.length}`, changes);
    
    return changes;
  } catch (error) {
    console.error('Error logging control update activities:', error);
    return [];
  }
};

/**
 * Log linking/unlinking activity for reference data
 * @param {string} controlId - The control ID
 * @param {string} username - Username of the user
 * @param {string} fieldName - The field that was linked/unlinked
 * @param {string} linkedValue - The value that was linked
 * @param {string} action - 'link' or 'unlink'
 */
exports.logLinkingActivity = async (controlId, username, fieldName, linkedValue, action = 'link') => {
  try {
    console.log('🔍 [DEBUG] logLinkingActivity called:', {
      controlId,
      username,
      fieldName,
      linkedValue,
      action
    });

    const fieldDisplayNames = {
      'controlType': 'Type de Contrôle',
      'businessProcess': 'Processus Métier',
      'organizationalProcess': 'Processus Organisationnel',
      'operation': 'Opération',
      'application': 'Application',
      'entity': 'Entité',
      'risk': 'Risque Associé',
      'actionPlan': 'Plan d\'Action',
      'campagne': 'Campagne'
    };

    const displayName = fieldDisplayNames[fieldName] || fieldName;
    const actionText = action === 'link' ? 'lié' : 'délié';

    const activityLog = await db.ControlActivityLog.create({
      control_id: controlId,
      user: username,
      type: action,
      field: fieldName,
      new_value: action === 'link' ? linkedValue : null,
      old_value: action === 'unlink' ? linkedValue : null,
      details: `${displayName} ${actionText}: ${linkedValue}`
    }, {
      returning: ['id', 'control_id', 'timestamp', 'user', 'type', 'field', 'old_value', 'new_value', 'details']
    });

    console.log(`✅ [DEBUG] Linking activity log created: ${activityLog.id}`);
    return true;
  } catch (error) {
    console.error('❌ [DEBUG] Error logging linking activity:', error);
    return false;
  }
};

/**
 * Log a general activity for a control
 * @param {Object} activityData - Activity data including userId, action, entityType, entityId, details
 * @param {Object} options - Additional options like transaction
 */
exports.logActivity = async (activityData, options = {}) => {
  try {
    const { userId, action, entityType, entityId, details } = activityData;

    // Find username if userId is provided
    let username = 'System';
    if (userId) {
      const user = await db.User.findByPk(userId, { transaction: options.transaction });
      if (user) {
        username = user.username || user.email || `User ID: ${userId}`;
      }
    }

    // Map custom actions to existing enum values
    let type = action.toLowerCase();
    if (!['creation', 'update', 'deletion', 'link', 'unlink'].includes(type)) {
      type = 'update'; // Map to an existing enum value
    }

    // Create activity log entry for controls
    await db.ControlActivityLog.create({
      control_id: entityId,
      user: username,
      type: type,
      details: details || `${action} performed`,
      timestamp: new Date()
    }, { transaction: options.transaction });

    return true;
  } catch (error) {
    console.error('Error logging activity:', error);
    return false;
  }
};
