# Template Files for New Features

This folder contains template files that you can copy and paste to quickly create new features in the application. These templates follow the established UI patterns and code structure of the application.

## Available Templates

1. **edit-template.jsx** - Template for edit pages with tabs
2. **overview-template.jsx** - Template for overview tabs
3. **features-template.jsx** - Template for features tabs
4. **slice-template.js** - Template for Redux slice

## How to Use

### 1. Create a New Feature

To create a new feature (e.g., "products"):

1. Create the necessary folders:
   ```
   frontend/src/pages/admin-view/data/products
   frontend/src/pages/admin-view/data/products/products-tabs
   ```

2. Copy the templates to their respective locations:
   - Copy `edit-template.jsx` to `frontend/src/pages/admin-view/data/products/edit-product.jsx`
   - Copy `overview-template.jsx` to `frontend/src/pages/admin-view/data/products/products-tabs/overview.jsx`
   - Copy `features-template.jsx` to `frontend/src/pages/admin-view/data/products/products-tabs/features.jsx`
   - Copy `slice-template.js` to `frontend/src/store/slices/productSlice.js`

3. Create a list page for the feature (e.g., `products.jsx`)

### 2. Modify the Templates

Search for "MODIFY" comments in the templates to find the sections that need to be updated for your specific feature.

#### Common modifications:

1. **Redux Slice**:
   - Update API URL
   - Update action names
   - Update state property names

2. **Edit Page**:
   - Update imports for your specific slice
   - Update route paths
   - Update tab definitions
   - Update context property names

3. **Overview Tab**:
   - Update context property name
   - Update displayed fields

4. **Features Tab**:
   - Update context property name
   - Update form fields
   - Update form submission logic

### 3. Update Routes

Add the new routes to `App.jsx`:

```jsx
// Products routes
{
  path: "products",
  element: <Products />,
},
{
  path: "products/:id",
  element: <EditProduct />,
  children: [
    {
      index: true,
      element: <ProductsOverview />,
    },
    {
      path: "features",
      element: <ProductsFeatures />,
    },
  ],
},
```

### 4. Add to Store

Add your slice to the Redux store in `store.js`:

```js
import productReducer from './slices/productSlice';

export const store = configureStore({
  reducer: {
    // Other reducers...
    product: productReducer,
  },
});
```

## Best Practices

1. Keep the same structure and naming conventions
2. Maintain consistent UI patterns
3. Use the DetailHeader component for all edit pages
4. Use TabBar and TabContent components for tab navigation
5. Follow the same error handling patterns
6. Use the same loading indicators

## Example Workflow

1. Copy templates to new locations
2. Search for "MODIFY" and update all relevant sections
3. Update routes in App.jsx
4. Add slice to store.js
5. Test the new feature

By following these templates, you can quickly add new features to the application while maintaining a consistent UI and code structure.
