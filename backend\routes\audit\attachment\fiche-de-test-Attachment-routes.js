const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../../middleware/auth');
const {
  uploadFile,
  addReference,
  getAttachments,
  getAllAttachments,
  downloadAttachment,
  deleteAttachment
} = require('../../../controllers/audit/attachment/fiche-de-test-Attachment-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// File upload route
router.post('/upload', authorizeRoles(['audit_director', 'auditor']), uploadFile);

// Web reference route
router.post('/reference', authorizeRoles(['audit_director', 'auditor']), addReference);

// Get all attachments (across all fiches de test)
router.get('/', authorizeRoles(['audit_director', 'auditor']), getAllAttachments);

// Get all attachments for a specific fiche de test
router.get('/fiche-de-test/:ficheDeTestID', authorizeRoles(['audit_director', 'auditor']), getAttachments);

// Get all attachments for a specific fiche de travail
router.get('/fiche-de-travail/:ficheDeTravailID', authorizeRoles(['audit_director', 'auditor']), getAttachments);

// Download an attachment
router.get('/download/:attachmentID', authorizeRoles(['audit_director', 'auditor']), downloadAttachment);

// Delete an attachment
router.delete('/:attachmentID', authorizeRoles(['audit_director', 'auditor']), deleteAttachment);

module.exports = router;
