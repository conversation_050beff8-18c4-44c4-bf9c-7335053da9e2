'use strict';

module.exports = (sequelize, DataTypes) => {
  const FicheDeTestAttachment = sequelize.define('FicheDeTestAttachment', {
    attachmentID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    filePath: {
      type: DataTypes.STRING,
      allowNull: true
    },
    url: {
      type: DataTypes.STRING,
      allowNull: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    uploadDate: {
      type: DataTypes.DATE,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('business-document', 'external-reference'),
      allowNull: false
    },
    ficheDeTestID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    ficheDeTravailID: {
      type: DataTypes.STRING,
      allowNull: false
    }
  }, {
    tableName: 'FicheDeTestAttachments',
    timestamps: true
  });

  FicheDeTestAttachment.associate = function(models) {
    // Association with FicheDeTest
    FicheDeTestAttachment.belongsTo(models.FicheDeTest, {
      foreignKey: 'ficheDeTestID',
      as: 'ficheDeTest',
      constraints: false // Avoid circular dependency issues
    });
    
    // Association with FicheDeTravail
    FicheDeTestAttachment.belongsTo(models.FicheDeTravail, {
      foreignKey: 'ficheDeTravailID',
      as: 'ficheDeTravail'
    });
  };

  return FicheDeTestAttachment;
};



