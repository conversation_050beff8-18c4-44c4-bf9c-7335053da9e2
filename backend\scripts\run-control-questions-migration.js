const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Create a direct pg pool connection
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
  ssl: false // Disable SSL for local development
});

async function runMigration() {
  try {
    console.log('Starting control questions migration...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../migrations/add_controlid_to_questions.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    // Execute each statement
    for (const statement of statements) {
      console.log('Executing:', statement.substring(0, 100) + '...');
      try {
        await pool.query(statement);
        console.log('✓ Statement executed successfully');
      } catch (error) {
        // Check if it's a "column already exists" error
        if (error.message.includes('already exists')) {
          console.log('⚠ Column already exists, skipping...');
        } else {
          throw error;
        }
      }
    }
    
    console.log('✓ Migration completed successfully!');
    
    // Verify the migration by checking if the column exists
    const checkResult = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'Questions' 
      AND column_name = 'controlID'
    `);
    
    if (checkResult.rows.length > 0) {
      console.log('✓ controlID column verified in Questions table');
    } else {
      console.log('✗ controlID column not found in Questions table');
    }
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

runMigration();
