const sequelize = require('../config/database');

async function createActionPlanControlRelation() {
  console.log('Creating ActionPlan-Control many-to-many relationship...');

  try {
    await sequelize.authenticate();
    console.log('Connection has been established successfully.');

    // Create junction table for many-to-many relationship
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS "ActionPlanControl" (
        "actionPlanID" VARCHAR(255) NOT NULL,
        "controlID" VARCHAR(255) NOT NULL,
        "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY ("actionPlanID", "controlID"),
        CONSTRAINT "fk_action_plan_control_action_plan" 
          FOREIGN KEY ("actionPlanID") 
          REFERENCES "action_plan" ("actionPlanID")
          ON DELETE CASCADE,
        CONSTRAINT "fk_action_plan_control_control" 
          FOREIGN KEY ("controlID") 
          REFERENCES "Control" ("controlID")
          ON DELETE CASCADE
      );
    `);

    console.log('ActionPlanControl junction table created successfully.');

    // Create indexes for better performance
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS "idx_action_plan_control_action_plan_id" 
      ON "ActionPlanControl" ("actionPlanID");
    `);

    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS "idx_action_plan_control_control_id" 
      ON "ActionPlanControl" ("controlID");
    `);

    console.log('Indexes created successfully.');

    console.log('ActionPlan-Control many-to-many relationship setup completed successfully.');

  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await sequelize.close();
  }
}

createActionPlanControlRelation();

//node backend/scripts/create-action-plan-control-relation.js
