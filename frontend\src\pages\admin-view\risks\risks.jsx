import { useState, useEffect, use<PERSON><PERSON><PERSON> } from "react";
import { Search, Plus, ArrowUpDown, Trash2, Loader2, AlertCircle, Filter, X, ChevronUp } from "lucide-react";
import PageHeader from '@/components/ui/page-header';
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "../../../components/ui/button";
import { Checkbox } from "../../../components/ui/checkbox";
import { Input } from "../../../components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../../components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "../../../components/ui/popover";
import { Calendar } from "../../../components/ui/calendar";
import { Badge } from "../../../components/ui/badge";
import { <PERSON>readcrumb, <PERSON><PERSON><PERSON>rumb<PERSON><PERSON>, BreadcrumbLink, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BreadcrumbSeparator } from "../../../components/ui/breadcrumb";
import { toast } from 'sonner';
import TablePagination from "../../../components/ui/table-pagination";
import FilterPanel from "../../../components/ui/filter-panel";
import LoadingProgress from "../../../components/ui/loading-progress";
import { DateRangePicker } from "../../../components/ui/date-range-picker";
import { useDispatch, useSelector } from "react-redux";
import { getAllActionPlans } from "../../../store/slices/actionPlanSlice";
import { fetchPaginatedRisks, deleteMultipleRisks, setFilters as setReduxFilters, clearFilters as clearReduxFilters, createRisk } from "../../../store/slices/riskSlice";
import riskIcon from '../../../assets/risk.png';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "../../../components/ui/dialog";
import { Label } from "../../../components/ui/label";
import { Textarea } from "../../../components/ui/textarea";
import { getApiBaseUrl } from "@/utils/api-config";
import { hasPermission } from '@/store/auth-slice';
import { useTranslation } from 'react-i18next';
import useReferenceData from '../../../hooks/useReferenceData';

// Custom toast functions for consistent styling
const showErrorToast = (message) => {
  toast.error(message, {
    duration: 6000,
    style: {
      background: '#f8d7da',
      color: '#721c24',
      border: '1px solid #f5c6cb',
      padding: '16px',
    },
  });
};

const showSuccessToast = (message) => {
  toast.success(message, {
    duration: 3000,
    style: {
      background: '#d4edda',
      color: '#155724',
      border: '1px solid #c3e6cb',
      padding: '16px',
    },
  });
};

const API_BASE_URL = getApiBaseUrl();
// Helper functions for risk treatment labels
const getAcceptanceLabel = (value) => {
  return value
    ? { label: 'Yes', color: 'bg-green-100 text-green-800' }
    : { label: 'No', color: 'bg-gray-100 text-gray-500' };
};

const getAvoidanceLabel = (value) => {
  return value
    ? { label: 'Yes', color: 'bg-green-100 text-green-800' }
    : { label: 'No', color: 'bg-gray-100 text-gray-500' };
};

const getInsuranceLabel = (value) => {
  return value
    ? { label: 'Yes', color: 'bg-green-100 text-green-800' }
    : { label: 'No', color: 'bg-gray-100 text-gray-500' };
};

const getReductionLabel = (value) => {
  return value
    ? { label: 'Yes', color: 'bg-green-100 text-green-800' }
    : { label: 'No', color: 'bg-gray-100 text-gray-500' };
};

// Add this helper function near the top of the file, with other helper functions
const truncateText = (text, maxLength = 50) => {
  if (!text) return '-';
  return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
};

// Add utility functions for inherent/residual risk color/label and date formatting
const getRiskLevelLabel = (riskLabel) => {
  if (!riskLabel) return { label: '-', color: 'bg-gray-100 text-gray-500' };
  const map = {
    'Très faible': { label: 'Très faible', color: 'bg-blue-500 text-white' },
    'Faible': { label: 'Faible', color: 'bg-green-500 text-white' },
    'Moyen': { label: 'Moyen', color: 'bg-yellow-400 text-white' },
    'Élevé': { label: 'Élevé', color: 'bg-orange-500 text-white' },
    'Très élevé': { label: 'Très élevé', color: 'bg-red-500 text-white' },
    'Very Low': { label: 'Très faible', color: 'bg-blue-500 text-white' },
    'Low': { label: 'Faible', color: 'bg-green-500 text-white' },
    'Medium': { label: 'Moyen', color: 'bg-yellow-400 text-white' },
    'High': { label: 'Élevé', color: 'bg-orange-500 text-white' },
    'Very High': { label: 'Très élevé', color: 'bg-red-500 text-white' },
    '-': { label: '-', color: 'bg-gray-100 text-gray-500' },
  };
  return map[riskLabel] || { label: riskLabel, color: 'bg-gray-100 text-gray-500' };
};

const formatDateFR = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', year: 'numeric' });
};

function Risks() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedRisks, setSelectedRisks] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newRisk, setNewRisk] = useState({
    name: "",
    code: "",
    comment: ""
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Permission check
  const state = useSelector(state => state);
  const canDelete = hasPermission(state, 'delete');

  // Get risk data from Redux store with default values
  const { risks = [], isLoading: loading, isError, message, filters = {} } = useSelector(state => state.risk);

  // Remove loading indicator state since we'll handle it differently
  const [showLoadingIndicator, setShowLoadingIndicator] = useState(false);

  // Handle filter change
  const handleFilterChange = (filterName, value) => {
    const newFilters = { ...filters, [filterName]: value };
    dispatch(setReduxFilters(newFilters));
  };

  // Clear all filters
  const clearFilters = () => {
    dispatch(clearReduxFilters());
  };

  // Only show loading indicator for initial load
  useEffect(() => {
    if (loading && !Object.keys(filters).length) {
      setShowLoadingIndicator(true);
    } else {
      setShowLoadingIndicator(false);
    }
  }, [loading, filters]);

  // Get reference data using the hook
  const {
    businessProcesses,
    organizationalProcesses,
    operations,
    applications,
    entities,
    riskTypes,
    controls,
    isLoading: referenceDataLoading,
    reload: reloadReferenceData,
  } = useReferenceData({ loadOnMount: true });

  const actionPlans = useSelector(state => state.actionPlan?.actionPlans || []);

  // Helper functions for impact, Control Level, and probability labels
  const getImpactLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const impactLabels = {
      '1': { label: 'Very Low', color: 'bg-blue-100 text-blue-800' },
      '2': { label: 'Low', color: 'bg-green-100 text-green-800' },
      '3': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
      '4': { label: 'High', color: 'bg-orange-100 text-orange-800' },
      '5': { label: 'Very High', color: 'bg-red-100 text-red-800' }
    };
    return impactLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const getControlLevelLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const controlLevelLabels = {
      '1': { label: 'Very Strong', color: 'bg-green-100 text-green-800' },
      '4': { label: 'Strong', color: 'bg-blue-100 text-blue-800' },
      '9': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
      '16': { label: 'Weak', color: 'bg-orange-100 text-orange-800' },
      '25': { label: 'Very Weak', color: 'bg-red-100 text-red-800' }
    };
    return controlLevelLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const getProbabilityLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const probabilityLabels = {
      '1': { label: 'Very Low', color: 'bg-blue-100 text-blue-800' },
      '2': { label: 'Low', color: 'bg-green-100 text-green-800' },
      '3': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
      '4': { label: 'High', color: 'bg-orange-100 text-orange-800' },
      '5': { label: 'Very High', color: 'bg-red-100 text-red-800' }
    };
    return probabilityLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const getAppetiteLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const appetiteLabels = {
      '1': { label: 'Very Low', color: 'bg-blue-100 text-blue-800' },
      '2': { label: 'Low', color: 'bg-green-100 text-green-800' },
      '3': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
      '4': { label: 'High', color: 'bg-orange-100 text-orange-800' },
      '5': { label: 'Very High', color: 'bg-red-100 text-red-800' }
    };
    return appetiteLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  // Helper functions to map IDs to names with better loading state handling
  const getBusinessProcessName = (id) => {
    if (!id) return '-';
    if (!businessProcesses || businessProcesses.length === 0) {
      return referenceDataLoading ? '...' : id; // Show loading indicator or fallback to ID
    }
    const process = businessProcesses.find(p => p.businessProcessID === id);
    return process ? process.name : id;
  };

  const getOrganizationalProcessName = (id) => {
    if (!id) return '-';
    if (!organizationalProcesses || organizationalProcesses.length === 0) {
      return referenceDataLoading ? '...' : id;
    }
    const process = organizationalProcesses.find(p => p.organizationalProcessID === id);
    return process ? process.name : id;
  };

  const getOperationName = (id) => {
    if (!id) return '-';
    if (!operations || operations.length === 0) {
      return referenceDataLoading ? '...' : id;
    }
    const operation = operations.find(o => o.operationID === id);
    return operation ? operation.name : id;
  };

  const getApplicationName = (id) => {
    if (!id) return '-';
    if (!applications || applications.length === 0) {
      return referenceDataLoading ? '...' : id;
    }
    const app = applications.find(a => a.applicationID === id);
    return app ? app.name : id;
  };

  const getEntityName = (id) => {
    if (!id) return '-';
    if (!entities || entities.length === 0) {
      return referenceDataLoading ? '...' : id;
    }
    const entity = entities.find(e => e.entityID === id);
    return entity ? entity.name : id;
  };

  const getRiskTypeName = (id) => {
    if (!id) return '-';
    if (!riskTypes || riskTypes.length === 0) {
      return referenceDataLoading ? '...' : id;
    }
    const riskType = riskTypes.find(rt => rt.riskTypeID === id);
    return riskType ? riskType.name : id;
  };

  const getControlName = (id) => {
    if (!id) return '-';
    if (!controls || controls.length === 0) {
      return referenceDataLoading ? '...' : id;
    }
    const control = controls.find(c => c.controlID === id);
    return control ? control.name : id;
  };

  // Helper function to get action plan name from ID
  const getActionPlanName = (id) => {
    if (!id) return '-';
    const actionPlan = actionPlans.find(plan => plan.actionPlanID === id);
    return actionPlan ? actionPlan.name : id;
  };

  // Fetch action plans only
  useEffect(() => {
    // Fetch action plans
    dispatch(getAllActionPlans());
  }, [dispatch]);

  // Fetch risks when pagination or filters change
  useEffect(() => {
    dispatch(fetchPaginatedRisks({ page: currentPage, pageSize: itemsPerPage, filters }));
  }, [dispatch, currentPage, itemsPerPage, filters]);

  const handleSort = (key) => {
    setSortConfig((prevConfig) => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedRisks(risks.map(risk => risk.riskID));
    } else {
      setSelectedRisks([]);
    }
  };

  const handleSelectRisk = (id) => {
    setSelectedRisks(prev => prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]);
  };

  const handleRowClick = (id) => {
    const path = window.location.pathname;
    if (path.includes('/audit')) {
      navigate(`/audit/risks/edit/${id}`);
    } else if (path.includes('/super-admin')) {
      navigate(`/super-admin/risks/edit/${id}`);
    } else {
      navigate(`/admin/risks/edit/${id}`);
    }
  };

  const handleWorkflowClick = (id, e) => {
    e.stopPropagation();
    const path = window.location.pathname;
    if (path.includes('/audit')) {
      navigate(`/audit/risks/edit/${id}/workflow`);
    } else if (path.includes('/super-admin')) {
      navigate(`/super-admin/risks/edit/${id}/workflow`);
    } else {
      navigate(`/admin/risks/edit/${id}/workflow`);
    }
  };

  // Handle create risk form input changes
  const handleCreateInputChange = (e) => {
    const { name, value } = e.target;
    setNewRisk(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle create risk form submission
  const handleCreateRisk = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Prepare the data for submission
      const riskData = {
        ...newRisk,
        riskID: `RISK_${Date.now()}`, // Generate ID if not provided
      };

      // Dispatch the create risk action
      await dispatch(createRisk(riskData)).unwrap();

      // Show success message
      toast.success(t('admin.risks.management.create_dialog.success', "Risk created successfully"));

      // Reset form and close modal
      setNewRisk({
        name: "",
        code: "",
        comment: ""
      });
      setIsCreateModalOpen(false);

      // Refresh the list
      dispatch(fetchPaginatedRisks({ page: currentPage, pageSize: itemsPerPage, filters }));
    } catch (error) {
      console.error('Error creating risk:', error);
      toast.error(error?.message || t('admin.risks.management.create_dialog.error', "Failed to create risk"));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteSelected = async () => {
    if (!canDelete) {
      showErrorToast(t('admin.risks.edit.delete_permission', "You don't have permission to delete risks"));
      return;
    }

    if (selectedRisks.length === 0) {
      showErrorToast(t('admin.risks.management.no_selection', "No risks selected"));
      return;
    }

    const dependentRisks = [];
    selectedRisks.forEach(selectedId => {
      const dependents = risks.filter(risk => risk.parentRiskID === selectedId);
      if (dependents.length > 0) {
        const riskName = risks.find(r => r.riskID === selectedId)?.name || selectedId;
        dependentRisks.push({
          id: selectedId,
          name: riskName,
          dependents: dependents.map(dep => dep.name || dep.riskID),
        });
      }
    });

    if (dependentRisks.length > 0) {
      const message = dependentRisks.map(dep =>
        `Cannot delete "${dep.name}" (ID: ${dep.id}) because it is referenced by: ${dep.dependents.join(', ')}.`
      ).join(' ');
      showErrorToast(message);
      return;
    }

    if (window.confirm(t('admin.risks.management.delete_confirm', { count: selectedRisks.length }))) {
      try {
        await dispatch(deleteMultipleRisks(selectedRisks)).unwrap();
        showSuccessToast(t('admin.risks.management.delete_success', { count: selectedRisks.length }));

        // Clear the selection
        setSelectedRisks([]);

        // Refresh the list with current pagination and filters
        dispatch(fetchPaginatedRisks({ page: currentPage, pageSize: itemsPerPage, filters }));
      } catch (error) {
        console.error('Error in bulk delete operation:', error);
        showErrorToast(error || t('admin.risks.management.delete_error', 'An unexpected error occurred during deletion'));
      }
    }
  };

  // Filter risks based on searchQuery and filters using useMemo for performance
  const filteredRisks = useMemo(() => {
    return risks.filter(risk => {
      // Apply search query filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch = Object.values(risk).some(value =>
          value?.toString().toLowerCase().includes(query)
        );
        if (!matchesSearch) return false;
      }

      // Impact filter
      if (filters.impact && filters.impact !== 'all') {
        if (risk.impact?.toString() !== filters.impact) return false;
      }

      // Control Level filter
      if (filters.DMR && filters.DMR !== 'all') {
        if (risk.DMR?.toString() !== filters.DMR) return false;
      }

      // Probability filter
      if (filters.probability && filters.probability !== 'all') {
        if (risk.probability?.toString() !== filters.probability) return false;
      }

      // Mitigating Action Plan filter
      if (filters.mitigatingActionPlan) {
        const query = filters.mitigatingActionPlan.toLowerCase();
        if (!risk.mitigatingActionPlan || !risk.mitigatingActionPlan.toLowerCase().includes(query)) return false;
      }

      // Business Process filter
      if (filters.businessProcessID && filters.businessProcessID !== 'all') {
        if (risk.businessProcessID !== filters.businessProcessID) return false;
      }

      // Organizational Process filter
      if (filters.organizationalProcessID && filters.organizationalProcessID !== 'all') {
        if (risk.organizationalProcessID !== filters.organizationalProcessID) return false;
      }

      // Operation filter
      if (filters.operationID && filters.operationID !== 'all') {
        if (risk.operationID !== filters.operationID) return false;
      }

      // Application filter
      if (filters.applicationID && filters.applicationID !== 'all') {
        if (risk.applicationID !== filters.applicationID) return false;
      }

      // Entity filter
      if (filters.entityID && filters.entityID !== 'all') {
        if (risk.entityID !== filters.entityID) return false;
      }

      // Risk Type filter
      if (filters.riskTypeID && filters.riskTypeID !== 'all') {
        if (risk.riskTypeID !== filters.riskTypeID) return false;
      }

      // Control filter
      if (filters.controlID && filters.controlID !== 'all') {
        if (risk.controlID !== filters.controlID) return false;
      }

      return true;
    });
  }, [risks, searchQuery, filters]);

  // Sorting
  const sortedRisks = useMemo(() => {
    if (!sortConfig.key) return filteredRisks;
    return [...filteredRisks].sort((a, b) => {
      const aValue = a[sortConfig.key] || '';
      const bValue = b[sortConfig.key] || '';
      return sortConfig.direction === 'asc'
        ? aValue.toString().localeCompare(bValue.toString(), undefined, { numeric: true })
        : bValue.toString().localeCompare(aValue.toString(), undefined, { numeric: true });
    });
  }, [filteredRisks, sortConfig]);

  // Pagination calculations
  const totalPages = Math.ceil(sortedRisks.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentRisks = sortedRisks.slice(indexOfFirstItem, indexOfLastItem);

  // Handlers
  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  if (loading || referenceDataLoading) {
    return (
      <div className="p-6 flex justify-center items-center h-screen">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
          <span className="mt-4 text-gray-600">
            {referenceDataLoading
              ? t('admin.risks.management.loading_reference_data', 'Loading reference data...')
              : t('admin.risks.management.loading', 'Loading risks...')
            }
          </span>
        </div>
      </div>
    );
  }

  if (isError) return <div className="p-6 text-red-500">{t('admin.risks.management.error', { message })}</div>;

  // Table columns for new design
  const columns = [
    { key: 'name', label: 'Nom' },
    { key: 'code', label: 'Code' },
    { key: 'majeur', label: 'Majeur' },
    { key: 'evaluationDate', label: 'Date d\'évaluation' },
    { key: 'inherentRisk', label: 'Risque inhérent' },
    { key: 'residualRisk', label: 'Risque résiduel' },
    { key: 'entity', label: 'Entité' },
    { key: 'incidents', label: 'Incidents' },
    { key: 'appetite', label: 'Appétence' },
    { key: 'actionPlan', label: 'Plan d\'actions' },
  ];

  return (
    <div className="p-6">
      {/* Only show loading indicator for initial load */}
      {showLoadingIndicator && (
        <LoadingProgress
          isLoading={loading}
          duration={1500}
          message={t('admin.risks.management.loading', "Loading risks data...")}
          onComplete={() => setShowLoadingIndicator(false)}
        />
      )}
      <PageHeader
        title={t('admin.risks.management.title', "Risk Management")}
        description={t('admin.risks.management.description', "Identify, assess, and manage risks across your organization. Track risk status and implement mitigation strategies.")}
        section={t('admin.risks.title', "Risk")}
        icon={AlertCircle}
        searchPlaceholder={t('admin.risks.management.search_placeholder', "Search risks...")}
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
      />

      {/* Filter Panel */}
      <div className="mb-6 filter-panel-container">
        <FilterPanel
          filters={[
            {
              id: "impact",
              label: t('admin.risks.management.filters.impact', "Impact"),
              component: (
                <Select
                  value={filters.impact}
                  onValueChange={(value) => handleFilterChange("impact", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.risks.management.filters.select_impact', "Select impact")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('admin.risks.management.filters.all', 'All')}</SelectItem>
                    <SelectItem value="1">{t('admin.risks.management.filters.very_low', 'Very Low')}</SelectItem>
                    <SelectItem value="2">{t('admin.risks.management.filters.low', 'Low')}</SelectItem>
                    <SelectItem value="3">{t('admin.risks.management.filters.medium', 'Medium')}</SelectItem>
                    <SelectItem value="4">{t('admin.risks.management.filters.high', 'High')}</SelectItem>
                    <SelectItem value="5">{t('admin.risks.management.filters.very_high', 'Very High')}</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "DMR",
              label: t('admin.risks.management.filters.control_level', "Control Level"),
              component: (
                <Select
                  value={filters.DMR}
                  onValueChange={(value) => handleFilterChange("DMR", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.risks.management.filters.select_control_level', "Select Control Level")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('admin.risks.management.filters.all', 'All')}</SelectItem>
                    <SelectItem value="1">{t('admin.risks.management.filters.very_strong', 'Very Strong')}</SelectItem>
                    <SelectItem value="4">{t('admin.risks.management.filters.strong', 'Strong')}</SelectItem>
                    <SelectItem value="9">{t('admin.risks.management.filters.medium', 'Medium')}</SelectItem>
                    <SelectItem value="16">{t('admin.risks.management.filters.weak', 'Weak')}</SelectItem>
                    <SelectItem value="25">{t('admin.risks.management.filters.very_weak', 'Very Weak')}</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "probability",
              label: t('admin.risks.management.filters.probability', "Probability"),
              component: (
                <Select
                  value={filters.probability}
                  onValueChange={(value) => handleFilterChange("probability", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.risks.management.filters.select_probability', "Select probability")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('admin.risks.management.filters.all', 'All')}</SelectItem>
                    <SelectItem value="1">{t('admin.risks.management.filters.very_low', 'Very Low')}</SelectItem>
                    <SelectItem value="2">{t('admin.risks.management.filters.low', 'Low')}</SelectItem>
                    <SelectItem value="3">{t('admin.risks.management.filters.medium', 'Medium')}</SelectItem>
                    <SelectItem value="4">{t('admin.risks.management.filters.high', 'High')}</SelectItem>
                    <SelectItem value="5">{t('admin.risks.management.filters.very_high', 'Very High')}</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "appetite",
              label: t('admin.risks.management.filters.appetite', "Appetite"),
              component: (
                <Select
                  value={filters.appetite}
                  onValueChange={(value) => handleFilterChange("appetite", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.risks.management.filters.select_appetite', "Select appetite")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('admin.risks.management.filters.all', 'All')}</SelectItem>
                    <SelectItem value="1">{t('admin.risks.management.filters.very_low', 'Very Low')}</SelectItem>
                    <SelectItem value="2">{t('admin.risks.management.filters.low', 'Low')}</SelectItem>
                    <SelectItem value="3">{t('admin.risks.management.filters.medium', 'Medium')}</SelectItem>
                    <SelectItem value="4">{t('admin.risks.management.filters.high', 'High')}</SelectItem>
                    <SelectItem value="5">{t('admin.risks.management.filters.very_high', 'Very High')}</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "mitigatingActionPlan",
              label: t('admin.risks.management.filters.mitigating_action_plan', "Mitigating Action Plan"),
              component: (
                <Input
                  type="text"
                  placeholder={t('admin.risks.management.filters.search_action_plan', "Search action plan...")}
                  value={filters.mitigatingActionPlan}
                  onChange={(e) => handleFilterChange("mitigatingActionPlan", e.target.value)}
                  className="h-9"
                />
              ),
            },
            {
              id: "businessProcessID",
              label: t('admin.risks.management.filters.business_process', "Business Process"),
              component: (
                <Select
                  value={filters.businessProcessID}
                  onValueChange={(value) => handleFilterChange("businessProcessID", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.risks.management.filters.select_business_process', "Select business process")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('admin.risks.management.filters.all', 'All')}</SelectItem>
                    {businessProcesses && businessProcesses.map((process) => (
                      <SelectItem key={process.businessProcessID} value={process.businessProcessID}>
                        {process.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "organizationalProcessID",
              label: t('admin.risks.management.filters.organizational_process', "Organizational Process"),
              component: (
                <Select
                  value={filters.organizationalProcessID}
                  onValueChange={(value) => handleFilterChange("organizationalProcessID", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.risks.management.filters.select_org_process', "Select org. process")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('admin.risks.management.filters.all', 'All')}</SelectItem>
                    {organizationalProcesses && organizationalProcesses.map((process) => (
                      <SelectItem key={process.organizationalProcessID} value={process.organizationalProcessID}>
                        {process.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "operationID",
              label: t('admin.risks.management.filters.operation', "Operation"),
              component: (
                <Select
                  value={filters.operationID}
                  onValueChange={(value) => handleFilterChange("operationID", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.risks.management.filters.select_operation', "Select operation")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('admin.risks.management.filters.all', 'All')}</SelectItem>
                    {operations && operations.map((operation) => (
                      <SelectItem key={operation.operationID} value={operation.operationID}>
                        {operation.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "applicationID",
              label: t('admin.risks.management.filters.application', "Application"),
              component: (
                <Select
                  value={filters.applicationID}
                  onValueChange={(value) => handleFilterChange("applicationID", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.risks.management.filters.select_application', "Select application")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('admin.risks.management.filters.all', 'All')}</SelectItem>
                    {applications && applications.map((app) => (
                      <SelectItem key={app.applicationID} value={app.applicationID}>
                        {app.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "entityID",
              label: t('admin.risks.management.filters.entity', "Entity"),
              component: (
                <Select
                  value={filters.entityID}
                  onValueChange={(value) => handleFilterChange("entityID", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.risks.management.filters.select_entity', "Select entity")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('admin.risks.management.filters.all', 'All')}</SelectItem>
                    {entities && entities.map((entity) => (
                      <SelectItem key={entity.entityID} value={entity.entityID}>
                        {entity.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "riskTypeID",
              label: t('admin.risks.management.filters.risk_type', "Risk Type"),
              component: (
                <Select
                  value={filters.riskTypeID}
                  onValueChange={(value) => handleFilterChange("riskTypeID", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.risks.management.filters.select_risk_type', "Select risk type")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('admin.risks.management.filters.all', 'All')}</SelectItem>
                    {riskTypes && riskTypes.map((type) => (
                      <SelectItem key={type.riskTypeID} value={type.riskTypeID}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "controlID",
              label: t('admin.risks.management.filters.control', "Control"),
              component: (
                <Select
                  value={filters.controlID}
                  onValueChange={(value) => handleFilterChange("controlID", value)}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.risks.management.filters.select_control', "Select control")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('admin.risks.management.filters.all', 'All')}</SelectItem>
                    {controls && controls.map((control) => (
                      <SelectItem key={control.controlID} value={control.controlID}>
                        {control.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
          ]}
          activeFilters={filters}
          onClearFilters={clearFilters}
        />
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end items-center gap-3 mb-4">
        {selectedRisks.length > 0 && (
          <Button
            variant="outline"
            className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
              canDelete && !loading
                ? 'text-red-500 hover:bg-red-50 hover:shadow-md hover:border-red-600'
                : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
            } flex items-center gap-2 px-4 py-2 font-semibold`}
            onClick={handleDeleteSelected}
            disabled={!canDelete || loading || selectedRisks.length === 0}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                {t('admin.risks.management.buttons.deleting', 'Deleting...')}
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                {t('admin.risks.management.buttons.delete', 'Delete')} ({selectedRisks.length})
              </>
            )}
          </Button>
        )}
        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button
              className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              {t('admin.risks.management.buttons.add', 'Add Risk')}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>{t('admin.risks.management.create_dialog.title', 'Create New Risk')}</DialogTitle>
              <DialogDescription>
                {t('admin.risks.management.create_dialog.description', 'Fill in the details below to create a new risk. Fields marked with * are required.')}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleCreateRisk} className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="name">{t('admin.risks.management.create_dialog.name', 'Name *')}</Label>
                <Input
                  id="name"
                  name="name"
                  value={newRisk.name}
                  onChange={handleCreateInputChange}
                  placeholder={t('admin.risks.management.create_dialog.name_placeholder', 'Enter risk name')}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="code">{t('admin.risks.management.create_dialog.code', 'Code')}</Label>
                <Input
                  id="code"
                  name="code"
                  value={newRisk.code}
                  onChange={handleCreateInputChange}
                  placeholder={t('admin.risks.management.create_dialog.code_placeholder', 'Enter risk code')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="comment">{t('admin.risks.management.create_dialog.comment', 'Comment')}</Label>
                <Textarea
                  id="comment"
                  name="comment"
                  value={newRisk.comment}
                  onChange={handleCreateInputChange}
                  placeholder={t('admin.risks.management.create_dialog.comment_placeholder', 'Enter comment')}
                  rows={3}
                />
              </div>
              <div className="flex justify-end gap-3 mt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateModalOpen(false)}
                >
                  {t('admin.risks.management.buttons.cancel', 'Cancel')}
                </Button>
                <Button
                  type="submit"
                  className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('admin.risks.management.buttons.creating', 'Creating...')}
                    </>
                  ) : (
                    t('admin.risks.management.buttons.create', 'Create Risk')
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
        <div className="overflow-x-auto" style={{ maxWidth: '100%', overflowX: 'auto' }}>
          <table className="w-full" style={{ minWidth: '1200px' }}>
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                  <Checkbox
                    checked={selectedRisks.length === currentRisks.length && currentRisks.length > 0}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all"
                  />
                </th>
                {columns.map((column) => (
                    <th
                      key={column.key}
                    className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]"
                    >
                        {column.label}
                    </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {currentRisks.map((risk, index) => {
                // Get latest evaluation date
                let latestEvalDate = '-';
                if (risk.evaluations && risk.evaluations.length > 0 && risk.evaluations[0].date) {
                  latestEvalDate = formatDateFR(risk.evaluations[0].date);
                }
                // Majeur: show Oui/Non if risk.major exists, red if true
                const majeur = risk.major !== undefined ? (risk.major ? 'Oui' : 'Non') : '-';
                // Inherent/residual risk: use label from API
                const inherentLabel = getRiskLevelLabel(risk.inherentRiskLabel);
                const residualLabel = getRiskLevelLabel(risk.residualRiskLabel);
                // Appétence: use same color mapping as risk level
                const appetiteLabel = getRiskLevelLabel(risk.appetiteLabel);
                // Incidents: show count if available
                const incidentsCount = Array.isArray(risk.incidents) ? risk.incidents.length : (risk.incidents || '-');
                // Action plan: show count of action plans
                const actionPlanCount = Array.isArray(risk.actionPlans) ? risk.actionPlans.length : 0;
                // Entity: show name
                const entityName = getEntityName(risk.entityID);
                return (
                <tr
                  key={risk.riskID}
                  className={`hover:bg-gray-50 cursor-pointer ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                  onClick={() => handleRowClick(risk.riskID)}
                >
                  <td className="px-6 py-4">
                    <Checkbox
                      checked={selectedRisks.includes(risk.riskID)}
                      onCheckedChange={() => handleSelectRisk(risk.riskID)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-[#242A33] whitespace-nowrap">
                    <span className="flex items-center gap-2">
                        <img src={riskIcon} alt="risk" className="w-5 h-5 object-contain" />
                      {truncateText(risk.name)}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">{truncateText(risk.code)}</td>
                    <td className={`px-6 py-4 text-sm whitespace-nowrap font-bold`}>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${risk.major ? 'bg-red-500 text-white' : 'bg-green-500 text-white'}`}>{majeur}</span>
                  </td>
                    <td className="px-6 py-4 text-sm whitespace-nowrap">{latestEvalDate}</td>
                    <td className={`px-6 py-4 text-sm whitespace-nowrap`}>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${inherentLabel.color}`}>{inherentLabel.label}</span>
                  </td>
                    <td className={`px-6 py-4 text-sm whitespace-nowrap`}>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${residualLabel.color}`}>{residualLabel.label}</span>
                  </td>
                    <td className="px-6 py-4 text-sm whitespace-nowrap">{entityName}</td>
                    <td className="px-6 py-4 text-sm whitespace-nowrap">{incidentsCount}</td>
                    <td className={`px-6 py-4 text-sm whitespace-nowrap`}>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${appetiteLabel.color}`}>{appetiteLabel.label}</span>
                  </td>
                    <td className="px-6 py-4 text-sm whitespace-nowrap">{actionPlanCount}</td>
                </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Replace custom pagination with TablePagination component */}
      <TablePagination
        currentPage={currentPage}
        totalPages={totalPages}
        itemsPerPage={itemsPerPage}
        totalItems={sortedRisks.length}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        startIndex={indexOfFirstItem}
        endIndex={indexOfLastItem}
      />
    </div>
  );
}

export default Risks;