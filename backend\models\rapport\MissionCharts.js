const Sequelize = require('sequelize');
require('dotenv').config({ path: require('path').resolve(__dirname, '../../.env') });
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: false
  }
);

// 1. Activities progression by status for a mission
async function getActivitiesProgressionByMission(missionId) {
  const query = `
    SELECT status, COUNT(*)::integer AS count
    FROM "AuditActivities"
    WHERE "auditMissionID" = :missionId
    GROUP BY status
    ORDER BY status;
  `;
  return sequelize.query(query, {
    replacements: { missionId },
    type: sequelize.QueryTypes.SELECT
  });
}

// 2. Recommendations progression by Action.status for a mission
async function getRecommendationsProgressionByMission(missionId) {
  const query = `
    SELECT a.status AS recommendation_status, COUNT(DISTINCT a."actionID")::integer AS count
    FROM "AuditRecommendations" ar
    JOIN "ConstatRecommendation" cr ON cr."recommendationId" = ar.id
    JOIN "AuditConstats" ac ON ac.id = cr."constatId"
    JOIN "AuditActivities" aa ON aa.id = ac."auditActivityID"
    LEFT JOIN "action_plan" ap ON ap."actionPlanID" = ar."actionPlanID"
    LEFT JOIN "ActionActionPlan" aap ON aap."actionPlanID" = ap."actionPlanID"
    LEFT JOIN "action" a ON a."actionID" = aap."actionID"
    WHERE aa."auditMissionID" = :missionId
    GROUP BY a.status
    ORDER BY a.status;
  `;
  return sequelize.query(query, {
    replacements: { missionId },
    type: sequelize.QueryTypes.SELECT
  });
}

// 3. Constats breakdown by type for a mission
async function getConstatsBreakdownByMission(missionId) {
  const query = `
    SELECT ac.type, COUNT(*)::integer AS count
    FROM "AuditConstats" ac
    JOIN "AuditActivities" aa ON aa.id = ac."auditActivityID"
    WHERE aa."auditMissionID" = :missionId
    GROUP BY ac.type
    ORDER BY ac.type;
  `;
  return sequelize.query(query, {
    replacements: { missionId },
    type: sequelize.QueryTypes.SELECT
  });
}

// DEBUG: Get all recommendations, their action plans, and actions for a mission
async function getRecommendationsActionPlansActionsByMission(missionId) {
  const query = `
    SELECT ar.id AS recommendation_id, ar.name AS recommendation_name, ar."actionPlanID", ap.name AS action_plan_name, a."actionID", a.name AS action_name, a.status AS action_status
    FROM "AuditRecommendations" ar
    JOIN "ConstatRecommendation" cr ON cr."recommendationId" = ar.id
    JOIN "AuditConstats" ac ON ac.id = cr."constatId"
    JOIN "AuditActivities" aa ON aa.id = ac."auditActivityID"
    LEFT JOIN "action_plan" ap ON ap."actionPlanID" = ar."actionPlanID"
    LEFT JOIN "ActionActionPlan" aap ON aap."actionPlanID" = ap."actionPlanID"
    LEFT JOIN "action" a ON a."actionID" = aap."actionID"
    WHERE aa."auditMissionID" = :missionId
    ORDER BY ar.id, ap."actionPlanID", a."actionID";
  `;
  return sequelize.query(query, {
    replacements: { missionId },
    type: sequelize.QueryTypes.SELECT
  });
}

module.exports = {
  getActivitiesProgressionByMission,
  getRecommendationsProgressionByMission,
  getConstatsBreakdownByMission,
  getRecommendationsActionPlansActionsByMission // debug
}; 