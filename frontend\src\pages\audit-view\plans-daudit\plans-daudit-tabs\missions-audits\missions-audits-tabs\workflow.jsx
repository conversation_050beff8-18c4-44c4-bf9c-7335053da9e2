import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Check<PERSON>ircle, XCircle, Loader, AlertTriangle, User, ChevronLeft, ChevronRight } from 'lucide-react';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { toast } from 'sonner';
import { getApiBaseUrl } from "@/utils/api-config";
import { useMissionAuditContext } from '@/utils/context-helpers';
import userService from "@/services/userService";

// API Base URL
const API_BASE_URL = getApiBaseUrl();

export default function MissionAuditWorkflow(props) {
  // Get mission audit data from context or props
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const { user: currentUser } = useSelector((state) => state.auth);

  // State for workflow data
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);
  const [error, setError] = useState(null);
  const [currentStep, setCurrentStep] = useState(null);
  const [events, setEvents] = useState([]);
  const [participants, setParticipants] = useState([]);
  const [userRoles, setUserRoles] = useState({});
  const [availableTransitions, setAvailableTransitions] = useState({});
  const [missionCreator, setMissionCreator] = useState(null);

  // State for dialogs and modals
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [rejectMessage, setRejectMessage] = useState('');

  // State for active tab
  const [activeTab, setActiveTab] = useState('Activity');

  // 9-step workflow for mission audit
  const workflowSteps = [
    'A valider',
    'A publier',
    'Programme de travail a soumettre',
    'Programme de travail a valider',
    'Rapport final a soumettre',
    'Rapport final a valider',
    'Rapport final a envoyer',
    'A fermer',
    'Fermé'
  ];

  // Map of transition to etat
  const transitionToEtat = useMemo(() => ({
    'A valider': 'En préparation',
    'A publier': 'En préparation',
    'Programme de travail a soumettre': 'Publié',
    'Programme de travail a valider': 'En cours',
    'Rapport final a soumettre': 'En cours',
    'Rapport final a valider': 'En cours',
    'Rapport final a envoyer': 'Terminée',
    'A fermer': 'Terminée',
    'Fermé': 'Fermée'
  }), []);

  // Add pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  // Fetch user roles from backend
  const fetchUserRoles = useCallback(async () => {
    setIsLoadingRoles(true);
    try {
      const users = await userService.fetchUsers({ silent: true });
      const rolesMap = {};
      users.forEach(user => {
        const identifier = user.username || user.email;
        const role = user.roles && user.roles.length > 0
          ? user.roles[0].name
          : 'User';
        rolesMap[identifier] = role;
      });
      setUserRoles(rolesMap);
    } catch (err) {
      console.error('Error fetching user roles:', err);
    } finally {
      setIsLoadingRoles(false);
    }
  }, []);

  // Initialize workflow data from mission audit
  const initializeWorkflowData = useCallback(() => {
    if (!missionAudit) return;
    
    setIsLoading(true);
    
    // Get current transition or default to first step
    const currentTransition = missionAudit.transition || 'A valider';
    setCurrentStep(currentTransition);

    // Create mock events based on current transition
    const mockEvents = [];
    
    // Always have creation event
    mockEvents.push({
      timestamp: missionAudit.createdAt || new Date().toISOString(),
      step: 'A valider',
      user: missionAudit.missionChief?.username || 'Chef de mission',
      transition: 'Create',
      message: 'Mission d\'audit créée'
    });

    // Add progression events based on current transition
    const currentIndex = workflowSteps.indexOf(currentTransition);
    
    // Add events for completed steps
    for (let i = 1; i <= currentIndex; i++) {
      const step = workflowSteps[i];
      mockEvents.push({
        timestamp: missionAudit.updatedAt || new Date().toISOString(),
        step: step,
        user: missionAudit.missionChief?.username || 'Chef de mission',
        transition: 'Advance',
        message: `Progression vers: ${step}`
      });
    }

    // Sort events in reverse chronological order
    const sortedEvents = mockEvents.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    setEvents(sortedEvents);

    // Set creator
    setMissionCreator(missionAudit.missionChief?.username || 'Chef de mission');

    // Extract participants
    const participantsMap = new Map();
    mockEvents.forEach(event => {
      if (!participantsMap.has(event.user)) {
        participantsMap.set(event.user, {
          name: event.user,
          actions: [],
          lastAction: null
        });
      }

      const participant = participantsMap.get(event.user);
      participant.actions.push({
        transition: event.transition,
        timestamp: event.timestamp,
        step: event.step
      });

      if (!participant.lastAction || new Date(event.timestamp) > new Date(participant.lastAction.timestamp)) {
        participant.lastAction = {
          transition: event.transition,
          timestamp: event.timestamp,
          step: event.step
        };
      }
    });

    const participantsList = Array.from(participantsMap.values())
      .sort((a, b) => new Date(b.lastAction.timestamp) - new Date(a.lastAction.timestamp));

    setParticipants(participantsList);

    // Set available transitions based on current transition
    const transitions = {};
    const currentStepIndex = workflowSteps.indexOf(currentTransition);
    
    // Can advance to next step if not at the end
    if (currentStepIndex < workflowSteps.length - 1) {
      const nextStep = workflowSteps[currentStepIndex + 1];
      transitions['Advance'] = `Avancer vers: ${nextStep}`;
    }
    
    // Can reject if not at first step
    if (currentStepIndex > 0) {
      transitions['Reject'] = 'Rejeter et revenir à l\'étape précédente';
    }
    
    setAvailableTransitions(transitions);
    setIsLoading(false);
  }, [missionAudit, workflowSteps]);

  // Initialize workflow data when mission changes
  useEffect(() => {
    if (missionAudit && missionAudit.id) {
      initializeWorkflowData();
      fetchUserRoles();
    }
  }, [missionAudit, initializeWorkflowData, fetchUserRoles]);

  // If no mission data is available yet
  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">Chargement du workflow de la mission...</p>
      </div>
    );
  }

  // Helper function to get role display
  const getUserRoleDisplay = (userName) => {
    if (isLoadingRoles) {
      return <span className="flex items-center"><Loader className="h-3 w-3 animate-spin mr-1" /> Chargement des rôles</span>;
    }
    return userRoles[userName] || 'Rôle inconnu';
  };

  // Handle workflow transition
  const handleTransition = async (transition, message = '') => {
    if (!currentUser?.id) {
      setError('Utilisateur manquant');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const currentStepIndex = workflowSteps.indexOf(currentStep);
      let newTransition = currentStep;

      if (transition === 'Advance') {
        // Move to next step
        if (currentStepIndex < workflowSteps.length - 1) {
          newTransition = workflowSteps[currentStepIndex + 1];
        }
      } else if (transition === 'Reject') {
        // Move to previous step
        if (currentStepIndex > 0) {
          newTransition = workflowSteps[currentStepIndex - 1];
        }
      }

      console.log('Updating mission workflow:', { 
        missionId: missionAudit.id, 
        newTransition, 
        message 
      });

      // Update mission workflow via API
      const response = await axios.put(`${API_BASE_URL}/audit-missions/${missionAudit.id}/workflow`, {
        transition: newTransition,
        workflowMessage: message
      }, {
        withCredentials: true
      });

      if (response.data && response.data.success) {
        // Update local mission data
        missionAudit.transition = newTransition;
        missionAudit.etat = transitionToEtat[newTransition];
        
        // Reinitialize workflow data
        initializeWorkflowData();

        // Reset dialog states
        setShowRejectDialog(false);
        setRejectMessage('');

        toast.success(`Mission ${transition === 'Advance' ? 'avancée' : 'rejetée'} avec succès`);
      } else {
        setError(response.data?.message || 'Erreur lors de la mise à jour du workflow');
      }
    } catch (err) {
      console.error('Error updating workflow:', err);
      
      if (err.response?.status === 401) {
        setError('Non autorisé: Veuillez vous reconnecter');
      } else if (err.response?.status === 403) {
        toast.error(err.response.data?.message || 'Vous n\'avez pas la permission d\'effectuer cette action');
      } else if (err.response?.status === 400) {
        setError(err.response.data?.message || 'Transition invalide demandée');
      } else {
        setError(err.response?.data?.message || 'Erreur lors de la mise à jour du workflow');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle rejection with message
  const handleReject = () => {
    if (rejectMessage.trim() === '') {
      setError('Une raison de rejet est requise');
      return;
    }

    handleTransition('Reject', rejectMessage);
    setShowRejectDialog(false);
    setRejectMessage('');
  };

  // Helper function to get the current step index
  const getCurrentIndex = () => {
    if (!currentStep) return -1;
    return workflowSteps.indexOf(currentStep);
  };

  // Function to format date nicely
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Helper functions for pagination
  const indexOfLastEvent = currentPage * itemsPerPage;
  const indexOfFirstEvent = indexOfLastEvent - itemsPerPage;
  const currentEvents = events.slice(indexOfFirstEvent, indexOfLastEvent);
  const totalPages = Math.ceil(events.length / itemsPerPage);

  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  if (isLoading && !currentStep) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader className="h-8 w-8 animate-spin" />
        <span className="ml-2">Chargement des données du workflow...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-red-500">
        <p>Erreur: {error}</p>
        <button
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={initializeWorkflowData}
        >
          Réessayer
        </button>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Enhanced Status Indicator */}
      <div className="mb-6 flex items-start">
        <div className="flex items-center bg-blue-100 px-4 py-2 rounded-lg">
          <CheckCircle className="h-6 w-6 text-blue-500 mr-2" />
          <span className="text-lg font-medium text-blue-700">{currentStep}</span>
        </div>
        <div className="ml-4 flex items-center bg-gray-100 px-3 py-2 rounded-lg">
          <span className="text-sm font-medium text-gray-700">État: {transitionToEtat[currentStep]}</span>
        </div>
        {isLoading &&
          <div className="flex items-center bg-blue-50 px-3 py-1 rounded ml-4">
            <Loader className="h-4 w-4 mr-2 animate-spin text-blue-500" />
            <span className="text-blue-500 text-sm">Mise à jour...</span>
          </div>
        }
      </div>

      {/* Horizontal Step Progress Bar with Multi-line Support */}
      <div className="mb-6 overflow-x-auto">
        <div className="flex items-start justify-between min-w-full space-x-2">
          {workflowSteps.map((stepLabel, index) => (
            <React.Fragment key={index}>
              <div className="flex flex-col items-center min-w-0 flex-1">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  index === getCurrentIndex()
                    ? 'bg-blue-500' :
                  index < getCurrentIndex()
                    ? 'bg-green-500' :
                  'bg-gray-300'
                }`}>
                  <span className="text-white text-xs">{index + 1}</span>
                </div>
                <p className="mt-2 text-xs text-center px-1 leading-tight max-w-20 break-words">
                  {stepLabel}
                </p>
              </div>
              {index < workflowSteps.length - 1 && <div className="flex-1 h-1 bg-gray-300 mx-1 max-w-8 mt-4"></div>}
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* Action Buttons - Based on available transitions */}
      <div className="flex justify-start space-x-4 mb-6">
        {Object.entries(availableTransitions).map(([action, label]) => (
          <button
            key={action}
            className={`px-4 py-2 rounded ${
              action === "Advance"
                ? "border border-blue-500 text-blue-500 bg-white hover:bg-blue-50"
                : action === "Reject"
                  ? "bg-red-500 text-white hover:bg-red-600"
                  : "bg-gray-300 text-gray-700 hover:bg-gray-400"
            }`}
            onClick={() => {
              if (action === "Reject") {
                setShowRejectDialog(true);
              } else {
                handleTransition(action);
              }
            }}
            disabled={isLoading}
          >
            {label}
          </button>
        ))}
      </div>

      {/* Tab Buttons */}
      <div className="flex space-x-4 mb-6">
        <button
          className={`px-4 py-2 rounded ${
            activeTab === 'Activity' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
          }`}
          onClick={() => setActiveTab('Activity')}
        >
          Activité
        </button>
        <button
          className={`px-4 py-2 rounded ${
            activeTab === 'Participants' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700'
          }`}
          onClick={() => setActiveTab('Participants')}
        >
          Participants
        </button>
      </div>

      {/* Activity Timeline */}
      {activeTab === 'Activity' && (
        <div className="mt-4 space-y-6">
          {missionCreator && (
            <div className="mb-4 p-3 bg-blue-50 rounded-md border border-blue-100">
              <p className="font-medium">Créateur de la mission: <span className="font-normal">{missionCreator}</span></p>
            </div>
          )}

          {events.length === 0 ? (
            <div className="text-gray-500">Aucune activité enregistrée</div>
          ) : (
            <>
              {currentEvents.map((event, index) => (
                <div key={index} className="flex">
                  <div className="flex flex-col items-center mr-4">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    {index < currentEvents.length - 1 && <div className="w-0.5 h-full bg-gray-300"></div>}
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">
                      {formatDate(event.timestamp)}
                    </p>
                    <p className="font-medium">Étape atteinte: {event.step}</p>
                    <p className="text-sm">Effectué par: {event.user}</p>
                    {event.transition && (
                      <p className="text-sm text-gray-500">Utilisant la transition: '{event.transition}'</p>
                    )}
                    {event.message && (
                      <p className="text-sm mt-1 italic bg-gray-50 p-2 rounded">"{event.message}"</p>
                    )}
                  </div>
                </div>
              ))}

              {/* Pagination Controls */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
                  <button
                    onClick={prevPage}
                    disabled={currentPage === 1}
                    className={`flex items-center ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-blue-600 hover:text-blue-800'}`}
                  >
                    <ChevronLeft className="h-5 w-5" />
                    <span>Précédent</span>
                  </button>

                  <span className="text-sm text-gray-600">Page {currentPage} sur {totalPages}</span>

                  <button
                    onClick={nextPage}
                    disabled={currentPage === totalPages}
                    className={`flex items-center ${currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-blue-600 hover:text-blue-800'}`}
                  >
                    <span>Suivant</span>
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      )}

      {/* Participants tab content */}
      {activeTab === 'Participants' && (
        <div className="mt-4">
          {participants.length === 0 ? (
            <div className="text-gray-500">Aucun participant enregistré</div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nom
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rôle
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Dernière action
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {participants.map((participant, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {participant.name}
                        {participant.name === missionCreator && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            Créateur
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {getUserRoleDisplay(participant.name)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {participant.lastAction?.transition} ({participant.lastAction?.step})
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {participant.lastAction?.timestamp ? formatDate(participant.lastAction.timestamp) : ''}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {/* Reject Dialog with Message Field */}
      {showRejectDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <div className="flex items-center mb-4">
              <AlertTriangle className="h-6 w-6 text-red-500 mr-2" />
              <h3 className="text-lg font-medium">Rejeter la mission</h3>
            </div>
            <p className="mb-4">Êtes-vous sûr de vouloir rejeter cette mission ? Veuillez fournir une raison.</p>
            <textarea
              className="w-full border border-gray-300 rounded p-2 mb-4"
              rows="3"
              placeholder="Raison du rejet..."
              value={rejectMessage}
              onChange={(e) => setRejectMessage(e.target.value)}
            ></textarea>
            <div className="flex justify-end space-x-2">
              <button
                className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-100"
                onClick={() => {
                  setShowRejectDialog(false);
                  setRejectMessage('');
                }}
              >
                Annuler
              </button>
              <button
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                onClick={handleReject}
                disabled={rejectMessage.trim() === ''}
              >
                Rejeter
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
