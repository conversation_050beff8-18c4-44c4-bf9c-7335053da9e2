import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import axios from 'axios';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ist,
  <PERSON><PERSON>hart,
  Loader2,
  RefreshCw,
  FileText,
  Bell,
  HelpCircle,
  Settings,
  Mail
} from 'lucide-react';
import { useSelector } from "react-redux";
import { getApiBaseUrl } from '../../utils/api-config';
import { useNotifications } from '@/hooks/useNotifications';
import { Button } from '@/components/ui/button';
const API_BASE_URL = getApiBaseUrl();
const AdminWelcome = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    risks: {
      total: 0,
      major: 0,
      highImpact: 0
    },
    incidents: {
      total: 0,
      highPriority: 0
    },
    controls: {
      total: 0,
      actionPlans: 0
    },
    processes: {
      total: 0,
      business: 0,
      organizational: 0,
      operations: 0
    }
  });

  // Use the notifications hook directly in this component for debug purposes
  const { 
    notifications, 
    unreadCount, 
    isLoading, 
    error, 
    fetchNotifications 
  } = useNotifications();

  // Fetch real statistics
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        
        // Get token from localStorage
        const token = localStorage.getItem('authToken');
        const headers = {
          'Content-Type': 'application/json'
        };
        
        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }

        // Fetch risks count
        const risksResponse = await axios.get(`${API_BASE_URL}/risk`, {
          withCredentials: true,
          headers
        });

        // Fetch incidents count
        const incidentsResponse = await axios.get(`${API_BASE_URL}/incidents`, {
          withCredentials: true,
          headers
        });

        // Fetch controls count
        const controlsResponse = await axios.get(`${API_BASE_URL}/controls`, {
          withCredentials: true,
          headers
        });

        // Fetch processes count (combining business and organizational processes)
        const businessProcessesResponse = await axios.get(`${API_BASE_URL}/businessProcesses`, {
          withCredentials: true,
          headers
        });

        const orgProcessesResponse = await axios.get(`${API_BASE_URL}/organizationalProcesses`, {
          withCredentials: true,
          headers
        });

        // Get action plans count
        const actionPlansResponse = await axios.get(`${API_BASE_URL}/actionPlans`, {
          withCredentials: true,
          headers
        });

        // Get operations count
        const operationsResponse = await axios.get(`${API_BASE_URL}/operations`, {
          withCredentials: true,
          headers
        });

        // Process the data for more detailed statistics
        const risks = risksResponse.data.data || [];
        const incidents = incidentsResponse.data.data || [];
        const controls = controlsResponse.data.data || [];
        const businessProcesses = businessProcessesResponse.data.data || [];
        const orgProcesses = orgProcessesResponse.data.data || [];
        const operations = operationsResponse.data.data || [];
        const actionPlans = actionPlansResponse.data.data || [];

        // Calculate detailed statistics
        // For risks, check both string values and numeric values (4-5 are high/very high)
        const majorRisks = risks.filter(risk => {
          // Check for major flag
          if (risk.major === true) return true;

          // Check for string impact values
          if (typeof risk.impact === 'string') {
            return risk.impact === 'VERY_HIGH' || risk.impact === 'HIGH' ||
                   risk.impact === 'Very High' || risk.impact === 'High';
          }

          // Check for numeric impact values (4-5 are high/very high)
          if (typeof risk.impact === 'number') {
            return risk.impact >= 4;
          }

          // Check for impact label
          if (risk.impactLabel) {
            return risk.impactLabel === 'Élevé' || risk.impactLabel === 'Très élevé';
          }

          return false;
        }).length;

        // Calculate high impact risks (based on impact field)
        const highImpactRisks = risks.filter(risk => {
          // Check for string impact values
          if (typeof risk.impact === 'string') {
            return risk.impact === 'VERY_HIGH' || risk.impact === 'HIGH' ||
                   risk.impact === 'Very High' || risk.impact === 'High';
          }

          // Check for numeric impact values (4-5 are high/very high)
          if (typeof risk.impact === 'number') {
            return risk.impact >= 4;
          }

          // Check for impact label
          if (risk.impactLabel) {
            return risk.impactLabel === 'Élevé' || risk.impactLabel === 'Très élevé';
          }

          return false;
        }).length;

        // Calculate high probability risks
        const highProbabilityRisks = risks.filter(risk => {
          // Check for string probability values
          if (typeof risk.probability === 'string') {
            return risk.probability === 'VERY_HIGH' || risk.probability === 'HIGH' ||
                   risk.probability === 'Very High' || risk.probability === 'High';
          }

          // Check for numeric probability values (4-5 are high/very high)
          if (typeof risk.probability === 'number') {
            return risk.probability >= 4;
          }

          return false;
        }).length;

        const highPriorityIncidents = incidents.filter(incident => {
          if (typeof incident.priority === 'string') {
            return incident.priority === 'HIGH' || incident.priority === 'CRITICAL' ||
                   incident.priority === 'High' || incident.priority === 'Critical';
          }
          return false;
        }).length;

        const highImpactIncidents = incidents.filter(incident => {
          if (typeof incident.impact === 'string') {
            return incident.impact === 'VERY_HIGH' || incident.impact === 'HIGH' ||
                   incident.impact === 'Very High' || incident.impact === 'High';
          }
          return false;
        }).length;

        // Update stats with detailed data
        setStats({
          risks: {
            total: risks.length,
            major: majorRisks,
            highImpact: highImpactRisks,
            highPriority: highProbabilityRisks // Use the high probability calculation
          },
          incidents: {
            total: incidents.length,
            highPriority: highPriorityIncidents,
            highImpact: highImpactIncidents // Add high impact for incidents
          },
          controls: {
            total: controls.length,
            actionPlans: actionPlans.length
          },
          processes: {
            total: businessProcesses.length + orgProcesses.length + operations.length,
            business: businessProcesses.length,
            organizational: orgProcesses.length,
            operations: operations.length
          }
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
        // Set default values in case of error
        setStats({
          risks: {
            total: 0,
            major: 0,
            highImpact: 0,
            highPriority: 0
          },
          incidents: {
            total: 0,
            highPriority: 0,
            highImpact: 0
          },
          controls: {
            total: 0,
            actionPlans: 0
          },
          processes: {
            total: 0,
            business: 0,
            organizational: 0,
            operations: 0
          }
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="h-full w-full flex items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Hero Section with Feature Cards */}
      <section className="relative py-8 bg-gradient-to-r from-[#1E2329] to-[#2A3038] text-white overflow-hidden rounded-lg">
        <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-1.2.1&auto=format&fit=crop&w=1567&q=80')] opacity-20 bg-cover bg-center"></div>

        {/* Main Content */}
        <div className="container mx-auto px-2 relative z-10">
          <div className="text-center mb-6">
            <motion.h1
              className="text-3xl md:text-5xl font-bold leading-tight mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Governance, Risk & Compliance
            </motion.h1>
            <motion.p
              className="text-xl text-gray-300 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Welcome, {user?.firstName || 'User'}! Here's an overview of your GRC metrics
            </motion.p>
          </div>

          {/* Feature Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            <motion.div
              className="bg-[#F62D51]/90 rounded-lg p-6 shadow-lg transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              onClick={() => navigate('/admin/risks')}
            >
              <div className="flex flex-col">
                <div className="flex items-center mb-4">
                  <div className="bg-white/10 p-3 rounded-full mr-3">
                    <ShieldAlert className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white">Risk Management</h3>
                </div>
                <div className="space-y-3 mt-2">
                  <div className="flex justify-between items-center p-2">
                    <span className="text-sm text-gray-200">Total Risks</span>
                    <span className="text-lg font-bold text-white">{stats.risks.total}</span>
                  </div>
                  <div className="flex justify-between items-center p-2">
                    <span className="text-sm text-gray-200">Major Risks</span>
                    <span className="text-lg font-bold text-white">{stats.risks.major}</span>
                  </div>
                  <div className="flex justify-between items-center p-2">
                    <span className="text-sm text-gray-200">High Impact</span>
                    <span className="text-lg font-bold text-white">{stats.risks.highImpact}</span>
                  </div>
                  <div className="flex justify-between items-center p-2">
                    <span className="text-sm text-gray-200">High Probability</span>
                    <span className="text-lg font-bold text-white">{stats.risks.highPriority}</span>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-purple-600/90 rounded-lg p-6 shadow-lg transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              onClick={() => navigate('/admin/incident')}
            >
              <div className="flex flex-col">
                <div className="flex items-center mb-4">
                  <div className="bg-white/10 p-3 rounded-full mr-3">
                    <AlertTriangle className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white">Incident Management</h3>
                </div>
                <div className="space-y-3 mt-2">
                  <div className="flex justify-between items-center p-2">
                    <span className="text-sm text-gray-200">Total Incidents</span>
                    <span className="text-lg font-bold text-white">{stats.incidents.total}</span>
                  </div>
                  <div className="flex justify-between items-center p-2">
                    <span className="text-sm text-gray-200">High Priority</span>
                    <span className="text-lg font-bold text-white">{stats.incidents.highPriority}</span>
                  </div>
                  <div className="flex justify-between items-center p-2">
                    <span className="text-sm text-gray-200">High Impact</span>
                    <span className="text-lg font-bold text-white">{stats.incidents.highImpact}</span>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-blue-500/90 rounded-lg p-6 shadow-lg transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              onClick={() => navigate('/admin/controls')}
            >
              <div className="flex flex-col">
                <div className="flex items-center mb-4">
                  <div className="bg-white/10 p-3 rounded-full mr-3">
                    <ClipboardList className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white">Controls & Plans</h3>
                </div>
                <div className="space-y-3 mt-2">
                  <div className="flex justify-between items-center p-2">
                    <span className="text-sm text-gray-200">Total Controls</span>
                    <span className="text-lg font-bold text-white">{stats.controls.total}</span>
                  </div>
                  <div className="flex justify-between items-center p-2">
                    <span className="text-sm text-gray-200">Action Plans</span>
                    <span className="text-lg font-bold text-white">{stats.controls.actionPlans}</span>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-green-600/90 rounded-lg p-6 shadow-lg transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              onClick={() => navigate('/admin/processes/business-processes')}
            >
              <div className="flex flex-col">
                <div className="flex items-center mb-4">
                  <div className="bg-white/10 p-3 rounded-full mr-3">
                    <BarChart className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white">Processes</h3>
                </div>
                <div className="space-y-3 mt-2">
                  <div className="flex justify-between items-center p-2">
                    <span className="text-sm text-gray-200">Total Processes</span>
                    <span className="text-lg font-bold text-white">{stats.processes.total}</span>
                  </div>
                  <div className="flex justify-between items-center p-2">
                    <span className="text-sm text-gray-200">Business</span>
                    <span className="text-lg font-bold text-white">{stats.processes.business}</span>
                  </div>
                  <div className="flex justify-between items-center p-2">
                    <span className="text-sm text-gray-200">Organizational</span>
                    <span className="text-lg font-bold text-white">{stats.processes.organizational}</span>
                  </div>
                  <div className="flex justify-between items-center p-2">
                    <span className="text-sm text-gray-200">Operations</span>
                    <span className="text-lg font-bold text-white">{stats.processes.operations}</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* White Section */}
      <section className="bg-white py-12 px-4">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">Recent System Updates</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Update Card 1 */}
              <motion.div
                className="bg-gray-50 rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-300"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.1 }}
              >
                <div className="flex items-start mb-4">
                  <div className="bg-blue-100 p-2 rounded-full mr-3">
                    <RefreshCw className="h-5 w-5 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800">System Update</h3>
                </div>
                <p className="text-gray-600 text-sm mb-3">
                  The GRC platform has been updated to version 2.5 with improved risk assessment capabilities.
                </p>
                <p className="text-gray-400 text-xs">Updated 3 days ago</p>
              </motion.div>

              {/* Update Card 2 */}
              <motion.div
                className="bg-gray-50 rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-300"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.2 }}
              >
                <div className="flex items-start mb-4">
                  <div className="bg-green-100 p-2 rounded-full mr-3">
                    <FileText className="h-5 w-5 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800">New Reports</h3>
                </div>
                <p className="text-gray-600 text-sm mb-3">
                  New regulatory compliance reports have been added to the reporting module.
                </p>
                <p className="text-gray-400 text-xs">Updated 1 week ago</p>
              </motion.div>

              {/* Update Card 3 */}
              <motion.div
                className="bg-gray-50 rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-300"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.3 }}
              >
                <div className="flex items-start mb-4">
                  <div className="bg-purple-100 p-2 rounded-full mr-3">
                    <Bell className="h-5 w-5 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800">Notifications</h3>
                </div>
                <p className="text-gray-600 text-sm mb-3">
                  Enhanced notification system for risk and incident alerts has been implemented.
                </p>
                <p className="text-gray-400 text-xs">Updated 2 weeks ago</p>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Debug section for notifications */}
      <div className="mt-10 p-6 bg-white rounded-md shadow">
        <h2 className="text-xl font-semibold mb-4">Notifications Debug</h2>
        
        <div className="space-y-4">
          <p><strong>Notification count:</strong> {notifications?.length || 0}</p>
          <p><strong>Unread count:</strong> {unreadCount}</p>
          <p><strong>Is Loading:</strong> {isLoading ? 'True' : 'False'}</p>
          <p><strong>Error:</strong> {error ? JSON.stringify(error) : 'None'}</p>
          
          <Button 
            onClick={() => fetchNotifications()} 
            variant="outline"
            className="bg-blue-50 text-blue-700 hover:bg-blue-100"
          >
            Refresh Notifications
          </Button>
          
          <div className="mt-4">
            <h3 className="text-lg font-medium mb-2">Recent Notifications:</h3>
            {notifications && notifications.length > 0 ? (
              <div className="border rounded-md">
                {notifications.slice(0, 5).map((notification) => (
                  <div key={notification.notificationId} className="border-b p-3 last:border-b-0">
                    <p className="font-medium">{notification.message}</p>
                    <p className="text-sm text-gray-500">
                      Type: {notification.type} | 
                      Read: {notification.read ? 'Yes' : 'No'} | 
                      ID: {notification.notificationId}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No notifications found.</p>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-100 py-8 px-4 border-t border-gray-200">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-4 md:mb-0">
                <h3 className="text-lg font-semibold text-gray-700 mb-2">Vitalis • GRC</h3>
                <p className="text-gray-500 text-sm">Governance, Risk & Compliance Platform</p>
              </div>

              <div className="flex space-x-6">
                <a href="#" className="text-gray-500 hover:text-gray-700 transition-colors">
                  <HelpCircle className="h-5 w-5" />
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700 transition-colors">
                  <Settings className="h-5 w-5" />
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700 transition-colors">
                  <Mail className="h-5 w-5" />
                </a>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200 flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-500 text-sm mb-4 md:mb-0">
                &copy; {new Date().getFullYear()} Vitalis GRC. All rights reserved.
              </p>

              <div className="flex space-x-4">
                <a href="#" className="text-gray-500 hover:text-gray-700 text-sm transition-colors">Privacy Policy</a>
                <a href="#" className="text-gray-500 hover:text-gray-700 text-sm transition-colors">Terms of Service</a>
                <a href="#" className="text-gray-500 hover:text-gray-700 text-sm transition-colors">Contact</a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default AdminWelcome;
