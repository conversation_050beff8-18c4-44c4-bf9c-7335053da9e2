const express = require('express');
const router = express.Router();
const db = require('../models');
const ControlMethodeExecution = db.ControlMethodeExecution;

// Get execution method for a specific control
router.get('/controls/:controlId/methode-execution', async (req, res) => {
  try {
    const { controlId } = req.params;
    
    console.log('[Control Methode Execution] Getting execution method for control:', controlId);

    if (!ControlMethodeExecution) {
      return res.status(500).json({
        success: false,
        message: 'ControlMethodeExecution model not found'
      });
    }

    const executionMethod = await ControlMethodeExecution.findOne({
      where: { controlID: controlId }
    });

    res.json({
      success: true,
      data: executionMethod
    });
  } catch (error) {
    console.error('Error fetching control execution method:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching control execution method',
      error: error.message
    });
  }
});

// Create or update execution method for a control
router.post('/controls/:controlId/methode-execution', async (req, res) => {
  try {
    const { controlId } = req.params;
    const {
      frequenceExecution,
      methode,
      calendrierPilotage,
      taillePopulationTotale,
      tailleEchantillon,
      seuilTauxConformite,
      procedureExecution
    } = req.body;

    console.log('[Control Methode Execution] Creating/updating execution method for control:', controlId);

    if (!ControlMethodeExecution) {
      return res.status(500).json({
        success: false,
        message: 'ControlMethodeExecution model not found'
      });
    }

    // Convert percentage strings to integers
    const tailleEchantillonInt = tailleEchantillon ? parseInt(tailleEchantillon) : null;
    const seuilTauxConformiteInt = seuilTauxConformite ? parseInt(seuilTauxConformite) : null;
    const taillePopulationTotaleInt = taillePopulationTotale ? parseInt(taillePopulationTotale) : null;

    // Use upsert to create or update
    const [executionMethod, created] = await ControlMethodeExecution.upsert({
      controlID: controlId,
      frequenceExecution,
      methode,
      calendrierPilotage,
      taillePopulationTotale: taillePopulationTotaleInt,
      tailleEchantillon: tailleEchantillonInt,
      seuilTauxConformite: seuilTauxConformiteInt,
      procedureExecution
    }, {
      returning: true
    });

    res.status(created ? 201 : 200).json({
      success: true,
      data: executionMethod,
      message: created ? 'Execution method created successfully' : 'Execution method updated successfully'
    });
  } catch (error) {
    console.error('Error creating/updating control execution method:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating/updating control execution method',
      error: error.message
    });
  }
});

// Update execution method for a control
router.put('/controls/:controlId/methode-execution', async (req, res) => {
  try {
    const { controlId } = req.params;
    const {
      frequenceExecution,
      methode,
      calendrierPilotage,
      taillePopulationTotale,
      tailleEchantillon,
      seuilTauxConformite,
      procedureExecution
    } = req.body;

    console.log('[Control Methode Execution] Updating execution method for control:', controlId);

    if (!ControlMethodeExecution) {
      return res.status(500).json({
        success: false,
        message: 'ControlMethodeExecution model not found'
      });
    }

    // Convert percentage strings to integers
    const tailleEchantillonInt = tailleEchantillon ? parseInt(tailleEchantillon) : null;
    const seuilTauxConformiteInt = seuilTauxConformite ? parseInt(seuilTauxConformite) : null;
    const taillePopulationTotaleInt = taillePopulationTotale ? parseInt(taillePopulationTotale) : null;

    const [updatedRowsCount, updatedExecutionMethods] = await ControlMethodeExecution.update(
      {
        frequenceExecution,
        methode,
        calendrierPilotage,
        taillePopulationTotale: taillePopulationTotaleInt,
        tailleEchantillon: tailleEchantillonInt,
        seuilTauxConformite: seuilTauxConformiteInt,
        procedureExecution
      },
      {
        where: { controlID: controlId },
        returning: true
      }
    );

    if (updatedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Execution method not found for this control'
      });
    }

    res.json({
      success: true,
      data: updatedExecutionMethods[0],
      message: 'Execution method updated successfully'
    });
  } catch (error) {
    console.error('Error updating control execution method:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating control execution method',
      error: error.message
    });
  }
});

// Delete execution method for a control
router.delete('/controls/:controlId/methode-execution', async (req, res) => {
  try {
    const { controlId } = req.params;

    console.log('[Control Methode Execution] Deleting execution method for control:', controlId);

    if (!ControlMethodeExecution) {
      return res.status(500).json({
        success: false,
        message: 'ControlMethodeExecution model not found'
      });
    }

    const deletedRowsCount = await ControlMethodeExecution.destroy({
      where: { controlID: controlId }
    });

    if (deletedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Execution method not found for this control'
      });
    }

    res.json({
      success: true,
      message: 'Execution method deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting control execution method:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting control execution method',
      error: error.message
    });
  }
});

module.exports = router;
