const { BusinessProcess } = require('../../models');

// Get all business processes
const getAllBusinessProcesses = async (req, res) => {
  try {
    const businessProcesses = await BusinessProcess.findAll();
    res.json({
      success: true,
      data: businessProcesses
    });
  } catch (error) {
    console.error('Error fetching business processes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch business processes'
    });
  }
};

// Create new business process
const createBusinessProcess = async (req, res) => {
  try {
    const {
      businessProcessID,
      name,
      code,
      comment,
      parentBusinessProcessID
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name'].filter(field => !req.body[field])
      });
    }

    const businessProcess = await BusinessProcess.create({
      businessProcessID: businessProcessID || `BP_${Date.now()}`,
      name,
      code: code || null,
      comment: comment || null,
      parentBusinessProcessID: parentBusinessProcessID || null
    });

    return res.status(201).json({
      success: true,
      message: 'Business process created successfully',
      data: businessProcess
    });
  } catch (error) {
    console.error('Error creating business process:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create business process'
    });
  }
};

// Get business process by ID
const getBusinessProcessById = async (req, res) => {
  try {
    const { id } = req.params;
    const businessProcess = await BusinessProcess.findByPk(id);
    
    if (!businessProcess) {
      return res.status(404).json({
        success: false,
        message: 'Business process not found'
      });
    }
    
    res.json({
      success: true,
      data: businessProcess
    });
  } catch (error) {
    console.error('Error fetching business process:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch business process'
    });
  }
};

// Update business process
const updateBusinessProcess = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      code,
      comment,
      parentBusinessProcessID
    } = req.body;

    const businessProcess = await BusinessProcess.findByPk(id);
    
    if (!businessProcess) {
      return res.status(404).json({
        success: false,
        message: 'Business process not found'
      });
    }

    // Update fields
    await businessProcess.update({
      name: name || businessProcess.name,
      code: code !== undefined ? code : businessProcess.code,
      comment: comment !== undefined ? comment : businessProcess.comment,
      parentBusinessProcessID: parentBusinessProcessID !== undefined ? parentBusinessProcessID : businessProcess.parentBusinessProcessID
    });

    res.json({
      success: true,
      message: 'Business process updated successfully',
      data: businessProcess
    });
  } catch (error) {
    console.error('Error updating business process:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update business process'
    });
  }
};

// Delete business process
const deleteBusinessProcess = async (req, res) => {
  try {
    const { id } = req.params;
    const businessProcess = await BusinessProcess.findByPk(id);
    
    if (!businessProcess) {
      return res.status(404).json({
        success: false,
        message: 'Business process not found'
      });
    }
    
    await businessProcess.destroy();
    
    res.json({
      success: true,
      message: 'Business process deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting business process:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete business process'
    });
  }
};

module.exports = {
  getAllBusinessProcesses,
  createBusinessProcess,
  getBusinessProcessById,
  updateBusinessProcess,
  deleteBusinessProcess
};
