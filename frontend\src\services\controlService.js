import axios from 'axios';

// Determine the API base URL dynamically
const getApiBaseUrl = () => {
  // Use environment variable if available
  if (import.meta.env.VITE_API_URL) {
    return import.meta.env.VITE_API_URL;
  }
  
  // Otherwise, use the current hostname with backend port
  const hostname = window.location.hostname;
  return `http://${hostname}:5001/api`;
};

const API_URL = `${getApiBaseUrl()}/controls`;

// Get all controls
const getAllControls = async () => {
  try {
    const response = await axios.get(API_URL, {
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Get control by ID
const getControlById = async (id) => {
  try {
    console.log('Making API request to:', `${API_URL}/${id}`);
    const response = await axios.get(`${API_URL}/${id}`, {
      withCredentials: true
    });
    console.log('API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('API error:', error.message, error.response?.data);
    throw error;
  }
};

// Create new control
const createControl = async (controlData) => {
  try {
    const response = await axios.post(API_URL, controlData, {
      withCredentials: true,
      headers: { 'Content-Type': 'application/json' }
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Update control - OPTIMIZED VERSION
const updateControl = async (id, controlData) => {
  try {
    // Ensure operation is properly formatted
    if (controlData.operation === 'none') {
      controlData.operation = null;
    }

    const response = await axios.put(`${API_URL}/${id}`, controlData, {
      withCredentials: true,
      headers: { 'Content-Type': 'application/json' }
    });

    return response.data;
  } catch (error) {
    // Only log errors, not successful operations
    console.error('Error updating control:', error.response?.data?.message || error.message);
    throw error;
  }
};

// Delete control
const deleteControl = async (id) => {
  try {
    const response = await axios.delete(`${API_URL}/${id}`, {
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Delete multiple controls
const deleteMultipleControls = async (ids) => {
  try {
    const response = await axios.delete(API_URL, {
      withCredentials: true,
      data: { ids }
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

const controlService = {
  getAllControls,
  getControlById,
  createControl,
  updateControl,
  deleteControl,
  deleteMultipleControls
};

export default controlService;
