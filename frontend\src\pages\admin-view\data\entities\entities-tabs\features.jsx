import { useState, useEffect } from "react";
import { useNavigate, useOutletContext } from "react-router-dom";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import axios from "axios";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from "react-i18next";

function EntitiesFeatures() {
  const { entity, refreshEntity } = useOutletContext();
  const navigate = useNavigate();
  const API_BASE_URL = getApiBaseUrl();
  const [isLoading, setIsLoading] = useState(false);
  const [entities, setEntities] = useState([]);
  const { t } = useTranslation();

  const [formData, setFormData] = useState({
    name: "",
    code: "",
    type: "",
    internalExternal: "Internal", // Default to Internal
    localCurrency: "",
    comment: "",
    parentEntityID: "none",
  });

  // Initialize form data when entity is loaded
  useEffect(() => {
    if (entity) {
      // Log the entity data for debugging
      console.log("Entity data:", entity);
      console.log("Internal/External value:", entity.internalExternal);

      // Ensure internalExternal is either "Internal" or "External"
      let internalExternalValue = "Internal";
      if (entity.internalExternal === "External") {
        internalExternalValue = "External";
      }

      console.log("Setting internalExternal to:", internalExternalValue);

      setFormData({
        name: entity.name || "",
        code: entity.code || "",
        type: entity.type || "",
        internalExternal: internalExternalValue,
        localCurrency: entity.localCurrency || "",
        comment: entity.comment || "",
        parentEntityID: entity.parentEntityID || "none",
      });
    }
  }, [entity]);

  // Fetch all entities for parent selection
  useEffect(() => {
    const fetchEntities = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/entities`, {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
        });

        if (response.data.success) {
          setEntities(response.data.data);
        }
      } catch (error) {
        console.error("Error fetching entities:", error);
      }
    };

    fetchEntities();
  }, []);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle select changes
  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setIsLoading(true);

      // Prepare data for submission
      const updateData = {
        name: formData.name,
        code: formData.code,
        type: formData.type,
        internalExternal: formData.internalExternal === "External" ? "External" : "Internal",
        localCurrency: formData.localCurrency,
        comment: formData.comment,
        parentEntityID: formData.parentEntityID === "none" ? null : formData.parentEntityID,
      };

      console.log("Form data before submission:", formData);
      console.log("Sanitized data for submission:", updateData);

      // Check if trying to set parent to self
      if (updateData.parentEntityID === entity.entityID) {
        toast.error("An entity cannot be its own parent");
        setIsLoading(false);
        return;
      }

      const response = await axios.put(`${API_BASE_URL}/entities/${entity.entityID}`, updateData, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
      });

      if (response.data.success) {
        toast.success("Entity updated successfully");
        refreshEntity();
        navigate(`/admin/data/entities/${entity.entityID}`);
      } else {
        toast.error(response.data.message || "Failed to update entity");
      }
    } catch (error) {
      console.error("Update error:", error);

      if (error.response?.status === 500) {
        toast.error("Server error. Please check the console for details.");
      } else {
        toast.error(error?.message || "Failed to update entity");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h2 className="text-xl font-semibold mb-6">{t('admin.entities.features.title', 'Edit Entity')}</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Name Field */}
        <div className="space-y-2">
          <Label htmlFor="name">{t('admin.entities.features.form.name', 'Name')}</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
          />
        </div>

        {/* Code Field */}
        <div className="space-y-2">
          <Label htmlFor="code">{t('admin.entities.features.form.code', 'Code')}</Label>
          <Input
            id="code"
            name="code"
            value={formData.code}
            onChange={handleChange}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Type Field */}
        <div className="space-y-2">
          <Label htmlFor="type">{t('admin.entities.features.form.type', 'Type')}</Label>
          <Input
            id="type"
            name="type"
            value={formData.type}
            onChange={handleChange}
          />
        </div>

        {/* Internal/External Field */}
        <div className="space-y-2">
          <Label htmlFor="internalExternal">{t('admin.entities.features.form.internal_external', 'Internal/External')}</Label>
          <div className="relative">
            <select
              id="internalExternal"
              name="internalExternal"
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={formData.internalExternal || "Internal"}
              onChange={handleChange}
            >
              <option value="Internal">{t('admin.entities.features.form.internal', 'Internal')}</option>
              <option value="External">{t('admin.entities.features.form.external', 'External')}</option>
            </select>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Local Currency Field */}
        <div className="space-y-2">
          <Label htmlFor="localCurrency">{t('admin.entities.features.form.local_currency', 'Local Currency')}</Label>
          <Input
            id="localCurrency"
            name="localCurrency"
            value={formData.localCurrency}
            onChange={handleChange}
          />
        </div>

        {/* Parent Entity Field */}
        <div className="space-y-2">
          <Label htmlFor="parentEntityID">{t('admin.entities.features.form.parent_entity', 'Parent Entity')}</Label>
          <div className="relative">
            <select
              id="parentEntityID"
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={formData.parentEntityID || "none"}
              onChange={(e) => handleSelectChange("parentEntityID", e.target.value)}
            >
              <option value="none">{t('admin.entities.features.form.none', 'None')}</option>
              {entities && entities
                .filter(e => e.entityID !== entity.entityID)
                .map(e => (
                  <option key={e.entityID} value={e.entityID}>
                    {e.name}
                  </option>
                ))}
            </select>
          </div>
        </div>
      </div>

      {/* Comment Field */}
      <div className="space-y-2">
        <Label htmlFor="comment">{t('admin.entities.features.form.comment', 'Comment')}</Label>
        <Textarea
          id="comment"
          name="comment"
          value={formData.comment}
          onChange={handleChange}
          rows={4}
        />
      </div>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => navigate(`/admin/data/entities/${entity.entityID}`)}
        >
          {t('common.buttons.cancel', 'Cancel')}
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('admin.entities.features.buttons.saving', 'Saving...')}
            </>
          ) : (
            t('admin.entities.features.buttons.save', 'Save Changes')
          )}
        </Button>
      </div>
    </form>
  );
}

export default EntitiesFeatures;
