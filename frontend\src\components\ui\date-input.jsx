import { forwardRef, useState } from "react";
import { Input } from "./input";
import { Calendar } from "lucide-react";
import { cn } from "@/lib/utils";

const DateInput = forwardRef(({ value, onChange, className, ...props }, ref) => {
  const [showPicker, setShowPicker] = useState(false);

  const formatDateForDisplay = (dateString) => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "";
      
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      
      return `${day}/${month}/${year}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return "";
    }
  };

  const parseDateFromDisplay = (displayValue) => {
    if (!displayValue) return "";
    
    // Handle dd/mm/yyyy format
    const parts = displayValue.split('/');
    if (parts.length === 3) {
      const [day, month, year] = parts.map(Number);
      const date = new Date(year, month - 1, day);
      if (!isNaN(date.getTime())) {
        return date.toISOString().split('T')[0];
      }
    }
    
    return "";
  };

  const handleChange = (e) => {
    const inputValue = e.target.value;
    
    // Allow empty value
    if (!inputValue) {
      onChange({ target: { name: props.name, value: "" } });
      return;
    }

    // Handle direct ISO date input (from date picker)
    if (inputValue.match(/^\d{4}-\d{2}-\d{2}$/)) {
      onChange({ target: { name: props.name, value: inputValue } });
      return;
    }

    // Handle manual dd/mm/yyyy input
    const isoDate = parseDateFromDisplay(inputValue);
    if (isoDate) {
      onChange({ target: { name: props.name, value: isoDate } });
    }
  };

  const handlePickerChange = (e) => {
    const date = e.target.value;
    if (date) {
      onChange({ target: { name: props.name, value: date } });
    }
  };

  return (
    <div className="relative">
      <Input
        {...props}
        ref={ref}
        type="text"
        value={formatDateForDisplay(value)}
        onChange={handleChange}
        placeholder="JJ/MM/AAAA"
        pattern="\d{2}/\d{2}/\d{4}"
        maxLength={10}
        className={cn("pr-10", className)}
      />
      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
        <Calendar className="h-4 w-4 text-gray-500" />
      </div>
      <input
        type="date"
        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        value={value || ""}
        onChange={handlePickerChange}
        onFocus={() => setShowPicker(true)}
        onBlur={() => setShowPicker(false)}
      />
    </div>
  );
});

DateInput.displayName = "DateInput";

export { DateInput }; 