import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import * as notificationService from '../services/notification-service';

/**
 * Custom hook to manage notifications, including:
 * - Fetching notifications from the API
 * - Storing notifications in localStorage with user-specific keys
 * - Tracking read/unread notifications
 * - Providing methods to mark notifications as read
 */
export const useNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  
  // Get user-specific storage key
  const getStorageKey = () => {
    if (!user || !user.id) return null;
    return `vitalis-notifications-${user.id}`;
  };

  // Fetch notifications from the API
  const fetchNotifications = async () => {
    if (!isAuthenticated || !user) {
      console.log('[useNotifications] Not authenticated or no user, skipping fetchNotifications');
      return;
    }
    
    console.log('[useNotifications] Fetching notifications for user:', user.id);
    setIsLoading(true);
    setError(null);
    
    try {
      console.log('[useNotifications] Making API call to /notifications');
      const response = await notificationService.getUserNotifications();
      console.log('[useNotifications] API response:', response);
      
      if (response.success) {
        // Format the API response to match our local format
        const formattedNotifications = response.data.map(notification => ({
          id: notification.entity_id,
          notificationId: notification.id.toString(),
          type: notification.type,
          entityName: notification.entity_name,
          message: notification.message,
          assignedBy: notification.assigned_by,
          read: notification.is_read,
          receivedAt: notification.created_at
        }));
        
        console.log('[useNotifications] Formatted notifications:', formattedNotifications);
        setNotifications(formattedNotifications);
        setUnreadCount(formattedNotifications.filter(n => !n.read).length);
        
        // Store in localStorage as backup
        const storageKey = getStorageKey();
        if (storageKey) {
          localStorage.setItem(storageKey, JSON.stringify(formattedNotifications));
        }
      } else {
        console.error('[useNotifications] API returned success:false', response);
      }
    } catch (error) {
      console.error('[useNotifications] Error fetching notifications:', error);
      console.error('[useNotifications] Error details:', error.response?.data || error.message);
      
      // Fall back to localStorage if API fails
      try {
        const storageKey = getStorageKey();
        if (storageKey) {
          const savedNotifications = localStorage.getItem(storageKey);
          if (savedNotifications) {
            console.log('[useNotifications] Falling back to localStorage notifications');
            const parsedNotifications = JSON.parse(savedNotifications);
            setNotifications(parsedNotifications);
            setUnreadCount(parsedNotifications.filter(n => !n.read).length);
          }
        }
      } catch (storageError) {
        console.error('[useNotifications] Error loading notifications from localStorage:', storageError);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch notifications when component mounts and when user changes
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchNotifications();
    }
  }, [isAuthenticated, user?.id]);

  // Add a new notification
  const addNotification = (newNotification) => {
    console.log('[useNotifications] Adding new notification:', newNotification);
    
    if (!newNotification) {
      console.error('[useNotifications] Received null/undefined notification');
      return null;
    }
    
    // Generate a client-side ID if not provided from server
    // The backend provides id as the notification ID, not entity_id
    const notificationId = newNotification.id ? String(newNotification.id) : `temp_${Date.now()}`;

    const formattedNotification = { 
      id: newNotification.entityId || newNotification.entity_id || newNotification.id, // Entity ID 
      notificationId: notificationId, // Notification database ID
      type: newNotification.type || 'general',
      entityName: newNotification.entityName || newNotification.entity_name || 'Unknown',
      message: newNotification.message || `New notification`,
      assignedBy: newNotification.assignedBy || newNotification.assigned_by,
      read: false, 
      receivedAt: newNotification.createdAt || newNotification.created_at || new Date().toISOString()
    };
    
    console.log('[useNotifications] Formatted notification for UI:', formattedNotification);
    
    setNotifications(prevNotifications => {
      // Check if this notification already exists by notification ID
      const existingIndex = prevNotifications.findIndex(n => n.notificationId === formattedNotification.notificationId);
      
      // Make a copy of the notifications array
      const updatedNotifications = [...prevNotifications];
      
      if (existingIndex >= 0) {
        console.log(`[useNotifications] Notification with ID ${formattedNotification.notificationId} already exists, updating`);
        // Update existing notification
        updatedNotifications[existingIndex] = {
          ...updatedNotifications[existingIndex],
          ...formattedNotification,
          // Preserve read status, don't mark a previously read notification as unread
          read: updatedNotifications[existingIndex].read
        };
      } else {
        console.log(`[useNotifications] Adding new notification with ID ${formattedNotification.notificationId}`);
        // Add new notification at the beginning
        updatedNotifications.unshift(formattedNotification);
        
        // Update unread count
        setUnreadCount(prev => prev + 1);
      }
      
      // Keep only the most recent 50 notifications
      const trimmedNotifications = updatedNotifications.slice(0, 50);
      
      // Update localStorage
      const storageKey = getStorageKey();
      if (storageKey) {
        localStorage.setItem(storageKey, JSON.stringify(trimmedNotifications));
        console.log(`[useNotifications] Saved ${trimmedNotifications.length} notifications to localStorage`);
      }
      
      return trimmedNotifications;
    });
    
    return formattedNotification;
  };

  // Mark a notification as read by its ID
  const markAsRead = async (notificationId) => {
    console.log(`[useNotifications] Marking notification as read: ${notificationId}`);
    
    setNotifications(prevNotifications => 
      prevNotifications.map(n => 
        n.notificationId === notificationId ? { ...n, read: true } : n
      )
    );
    
    // Update the state optimistically
    setUnreadCount(prev => Math.max(0, prev - 1));
    
    // Update localStorage
    const storageKey = getStorageKey();
    if (storageKey) {
      localStorage.setItem(storageKey, JSON.stringify(
        notifications.map(n => n.notificationId === notificationId ? { ...n, read: true } : n)
      ));
    }
    
    // Call the API to update the server
    try {
      await notificationService.markNotificationAsRead(notificationId);
      console.log(`[useNotifications] Successfully marked notification ${notificationId} as read on server`);
    } catch (error) {
      console.error('[useNotifications] Error marking notification as read on server:', error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    // Update state optimistically
    setNotifications(prevNotifications => 
      prevNotifications.map(n => ({ ...n, read: true }))
    );
    setUnreadCount(0);
    
    // Update localStorage
    const storageKey = getStorageKey();
    if (storageKey) {
      localStorage.setItem(storageKey, JSON.stringify(
        notifications.map(n => ({ ...n, read: true }))
      ));
    }
    
    // Call the API to update the server
    try {
      await notificationService.markAllNotificationsAsRead();
      console.log('[useNotifications] Successfully marked all notifications as read on server');
    } catch (error) {
      console.error('[useNotifications] Error marking all notifications as read on server:', error);
    }
  };

  // Delete a notification by its ID
  const deleteNotification = async (notificationId) => {
    // Update state optimistically
    setNotifications(prevNotifications => {
      const notification = prevNotifications.find(n => n.notificationId === notificationId);
      const isUnread = notification && !notification.read;
      
      const filtered = prevNotifications.filter(n => n.notificationId !== notificationId);
      
      // Update localStorage
      const storageKey = getStorageKey();
      if (storageKey) {
        localStorage.setItem(storageKey, JSON.stringify(filtered));
      }
      
      // Update unread count if needed
      if (isUnread) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
      
      return filtered;
    });
    
    // Call the API to update the server
    try {
      await notificationService.deleteNotification(notificationId);
      console.log(`[useNotifications] Successfully deleted notification ${notificationId} on server`);
    } catch (error) {
      console.error('[useNotifications] Error deleting notification on server:', error);
    }
  };

  return {
    notifications,
    unreadCount,
    isLoading,
    error,
    addNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    fetchNotifications,
    setNotifications
  };
};

export default useNotifications; 