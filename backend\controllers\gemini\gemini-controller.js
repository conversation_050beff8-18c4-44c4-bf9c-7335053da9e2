const { sequelize } = require('../../models/index');
const AuditMissionRapport = require('../../models/Audit/rapport/auditmissionrapport')(sequelize);
const axios = require('axios');

const escapeText = (str) => {
  if (typeof str !== 'string') return str || 'N/A';
  return str.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
};

exports.generateGeminiConversation = async (req, res) => {
  const { missionId } = req.params;
  res.set({
    'Access-Control-Allow-Origin': 'http://localhost:5173',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, x-user-id',
    'Access-Control-Allow-Credentials': 'true'
  });
  try {
    // Step 1: Extract text directly from reportData
    const reportData = await AuditMissionRapport.getMissionReport(missionId);
    if (!reportData || reportData.length === 0) {
      console.error('[Gemini] No data found for missionId:', missionId);
      return res.status(404).json({ message: 'No data found for the specified mission ID' });
    }
    const autresParticipants = [
      ...new Set(
        reportData
          .flatMap(row => [row.activity_responsable, row.constat_responsable])
          .filter(id => id !== null && id !== undefined)
      ),
    ].join(', ');
    const auditedElements = [
      reportData[0].risk_names,
      reportData[0].entity_names,
      reportData[0].control_names,
      reportData[0].incident_names,
      reportData[0].organizational_process_names,
    ].filter(name => name).join(', ');
    const textParts = [
      `Chef de mission: ${escapeText(reportData[0].chefmission)}`,
      `Directeur d'audit: ${escapeText(reportData[0].directeuraudit)}`,
      `Mission d'audit: ${escapeText(reportData[0].mission_name)}`,
      `Catégorie: ${escapeText(reportData[0].categorie)}`,
      `Évaluation: ${escapeText(reportData[0].evaluation)}`,
      `Objectif: ${escapeText(reportData[0].objectif)}`,
      `Points forts: ${escapeText(reportData[0].pointfort)}`,
      `Points faibles: ${escapeText(reportData[0].pointfaible)}`,
      `Participants: ${escapeText(autresParticipants)}`,
      `Éléments audités: ${escapeText(auditedElements)}`
    ];
    // Add constats and recommandations
    const findings = reportData.filter(row => row.constat_name);
    findings.forEach(row => {
      textParts.push(`Constat: ${escapeText(row.constat_name)}`);
      textParts.push(`Impact: ${escapeText(row.constat_impact)}`);
      textParts.push(`Recommandation: ${escapeText(row.recommendation_name)}`);
      textParts.push(`Détails: ${escapeText(row.recommendation_details)}`);
      textParts.push(`Propriétaire: ${escapeText(row.recommendation_responsable)}`);
      textParts.push(`Date de fin: ${escapeText(row.datefin)}`);
    });
    const pdfText = textParts.join('\n');
    // Step 2: Call Gemini API
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
    if (!GEMINI_API_KEY) {
      return res.status(500).json({ message: 'GEMINI_API_KEY not set in backend .env' });
    }
    const prompt = `Voici le contenu d'un rapport d'audit :\n\n"""${pdfText.substring(0, 4000)}"""\n\nSimule une conversation en français entre deux personnes, Alice et Bob, qui discutent de ce rapport.\nLa conversation doit être naturelle, informative, et alterner entre Alice et Bob.\nPrésente la conversation sous forme de liste JSON structurée : [{speaker: 'Alice', text: '...'}, {speaker: 'Bob', text: '...'}, ...].\nNe fais pas de résumé, fais une vraie discussion.\n`;
    let geminiRes;
    try {
      geminiRes = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
        {
          contents: [
            {
              parts: [
                { text: prompt }
              ]
            }
          ]
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-goog-api-key': GEMINI_API_KEY
          }
        }
      );
    } catch (err) {
      console.error('[Gemini] Gemini API error:', err.response ? err.response.data : err);
      return res.status(500).json({ message: 'Failed to call Gemini API', error: err.message });
    }
    // Parse Gemini response
    let conversation = [];
    if (geminiRes.data && geminiRes.data.candidates && geminiRes.data.candidates[0].content && geminiRes.data.candidates[0].content.parts) {
      const raw = geminiRes.data.candidates[0].content.parts[0].text;
      try {
        conversation = JSON.parse(raw);
      } catch (e) {
        // Try to extract JSON from text
        const match = raw.match(/\[.*\]/s);
        if (match) {
          conversation = JSON.parse(match[0]);
        } else {
          console.error('[Gemini] Failed to parse Gemini conversation output:', raw);
          return res.status(500).json({ message: 'Failed to parse Gemini conversation output', raw });
        }
      }
    }
    res.json({ conversation });
  } catch (err) {
    console.error('[Gemini] Unexpected error:', err);
    res.status(500).json({ message: 'Failed to generate Gemini conversation', error: err.message });
  }
}; 