import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import actionPlanService from '../../services/actionPlanService';
import { toast } from 'sonner';

// Initial state
const initialState = {
  actionPlans: [],
  actionPlan: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: ''
};

// Get all action plans
export const getAllActionPlans = createAsyncThunk(
  'actionPlans/getAll',
  async (_, thunkAPI) => {
    try {
      return await actionPlanService.getAllActionPlans();
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch action plans';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get action plan by ID
export const getActionPlanById = createAsyncThunk(
  'actionPlans/getById',
  async (id, thunkAPI) => {
    try {
      return await actionPlanService.getActionPlanById(id);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch action plan';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create new action plan
export const createActionPlan = createAsyncThunk(
  'actionPlans/create',
  async (actionPlanData, thunkAPI) => {
    try {
      return await actionPlanService.createActionPlan(actionPlanData);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to create action plan';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update action plan
export const updateActionPlan = createAsyncThunk(
  'actionPlans/update',
  async ({ id, actionPlanData }, thunkAPI) => {
    try {
      return await actionPlanService.updateActionPlan(id, actionPlanData);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to update action plan';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete action plan
export const deleteActionPlan = createAsyncThunk(
  'actionPlans/delete',
  async (id, thunkAPI) => {
    try {
      await actionPlanService.deleteActionPlan(id);
      return id;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete action plan';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete multiple action plans
export const deleteMultipleActionPlans = createAsyncThunk(
  'actionPlans/deleteMultiple',
  async (ids, thunkAPI) => {
    try {
      await actionPlanService.deleteMultipleActionPlans(ids);
      return ids;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete action plans';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Action plan slice
const actionPlanSlice = createSlice({
  name: 'actionPlan',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all action plans
      .addCase(getAllActionPlans.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllActionPlans.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.actionPlans = action.payload.data;
      })
      .addCase(getAllActionPlans.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Get action plan by ID
      .addCase(getActionPlanById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getActionPlanById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.actionPlan = action.payload.data;
      })
      .addCase(getActionPlanById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Create action plan
      .addCase(createActionPlan.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createActionPlan.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.actionPlans.push(action.payload.data);
        toast.success('Action plan created successfully');
      })
      .addCase(createActionPlan.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Update action plan
      .addCase(updateActionPlan.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateActionPlan.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;

        // Update the actionPlans array
        state.actionPlans = state.actionPlans.map(actionPlan =>
          actionPlan.actionPlanID === action.payload.data.actionPlanID ? action.payload.data : actionPlan
        );

        // Also update the current actionPlan if it matches the updated one
        if (state.actionPlan && state.actionPlan.actionPlanID === action.payload.data.actionPlanID) {
          state.actionPlan = action.payload.data;
        }

        // Don't show toast here as we'll handle it in the component
      })
      .addCase(updateActionPlan.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Delete action plan
      .addCase(deleteActionPlan.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteActionPlan.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.actionPlans = state.actionPlans.filter(actionPlan => actionPlan.actionPlanID !== action.payload);
        toast.success('Action plan deleted successfully');
      })
      .addCase(deleteActionPlan.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Delete multiple action plans
      .addCase(deleteMultipleActionPlans.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteMultipleActionPlans.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.actionPlans = state.actionPlans.filter(actionPlan => !action.payload.includes(actionPlan.actionPlanID));
        toast.success(`${action.payload.length} action plan(s) deleted successfully`);
      })
      .addCase(deleteMultipleActionPlans.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      });
  }
});

export const { reset } = actionPlanSlice.actions;
export default actionPlanSlice.reducer;
