import { useState } from "react";
import { useOutletContext } from "react-router-dom";
import { Clock, User, Edit, CheckCircle2, AlertCircle } from "lucide-react";

function ActionActivity() {
  const { action } = useOutletContext();
  
  // Mock activity data - would be replaced with real data from API
  const [activities] = useState([
    {
      id: 1,
      type: "status_change",
      oldValue: "Not Started",
      newValue: "In Progress",
      timestamp: "2024-07-01T10:30:00Z",
      user: "<PERSON>"
    },
    {
      id: 2,
      type: "edit",
      field: "description",
      timestamp: "2024-07-02T14:15:00Z",
      user: "<PERSON>"
    },
    {
      id: 3,
      type: "assignee_change",
      oldValue: "<PERSON>",
      newValue: "<PERSON>",
      timestamp: "2024-07-03T09:45:00Z",
      user: "Admin User"
    }
  ]);
  
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  // Get activity icon
  const getActivityIcon = (type) => {
    switch (type) {
      case "status_change":
        return <CheckCircle2 className="h-5 w-5 text-blue-500" />;
      case "edit":
        return <Edit className="h-5 w-5 text-yellow-500" />;
      case "assignee_change":
        return <User className="h-5 w-5 text-green-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };
  
  // Get activity message
  const getActivityMessage = (activity) => {
    switch (activity.type) {
      case "status_change":
        return `Status changed from "${activity.oldValue}" to "${activity.newValue}"`;
      case "edit":
        return `Updated ${activity.field}`;
      case "assignee_change":
        return `Assignee changed from "${activity.oldValue}" to "${activity.newValue}"`;
      default:
        return "Unknown activity";
    }
  };
  
  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">Activity History</h2>
      
      {activities.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <p className="text-gray-500">No activity recorded for this action.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start p-4 border rounded-lg hover:bg-gray-50">
              <div className="mr-4 mt-1">
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1">
                <div className="font-medium">{getActivityMessage(activity)}</div>
                <div className="text-sm text-gray-500 flex items-center mt-1">
                  <Clock className="h-4 w-4 mr-1" />
                  {formatDate(activity.timestamp)}
                  <span className="mx-2">•</span>
                  <User className="h-4 w-4 mr-1" />
                  {activity.user}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default ActionActivity;
