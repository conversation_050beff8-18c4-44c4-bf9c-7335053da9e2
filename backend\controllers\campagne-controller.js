const { <PERSON><PERSON><PERSON>, <PERSON>r, <PERSON>r<PERSON><PERSON><PERSON><PERSON>, CampagneResponse } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const { logLinkingActivity } = require('./data/control-activity-controller');

// Get all campagnes (filtered by user access)
const getAllCampagnes = async (req, res) => {
  try {
    const { userId } = req.user;
    const { page = 1, limit = 10, search = '', statut = '', sortBy = 'createdAt', sortOrder = 'DESC' } = req.query;

    const offset = (page - 1) * limit;
    const whereClause = {};

    // Add search filter
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { code: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Add status filter
    if (statut && statut !== 'all') {
      whereClause.statut = statut;
    }

    // Build include for user filtering
    const include = [
      {
        model: User,
        as: 'assignedUsers',
        attributes: ['id', 'username', 'email'],
        through: {
          attributes: ['assignedAt', 'assignedBy']
        }
      }
    ];

    // Filter campagnes by user access (only show campagnes user is assigned to)
    const userCampagneIds = await UserCampagne.findAll({
      where: { userID: userId },
      attributes: ['campagneID']
    });

    if (userCampagneIds.length > 0) {
      whereClause.campagneID = {
        [Op.in]: userCampagneIds.map(uc => uc.campagneID)
      };
    } else {
      // User has no campagne assignments, return empty result
      return res.json({
        success: true,
        data: {
          campagnes: [],
          pagination: {
            currentPage: parseInt(page),
            totalPages: 0,
            totalItems: 0,
            itemsPerPage: parseInt(limit)
          }
        }
      });
    }

    const { count, rows: campagnes } = await Campagne.findAndCountAll({
      where: whereClause,
      include,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy, sortOrder.toUpperCase()]],
      distinct: true
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        campagnes,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching campagnes:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des campagnes',
      error: error.message
    });
  }
};

// Get single campagne by ID
const getCampagneById = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.user;

    // Check if user has access to this campagne
    const userAccess = await UserCampagne.findOne({
      where: {
        userID: userId,
        campagneID: id
      }
    });

    if (!userAccess) {
      return res.status(403).json({
        success: false,
        message: 'Accès refusé à cette campagne'
      });
    }

    const campagne = await Campagne.findByPk(id, {
      include: [
        {
          model: User,
          as: 'assignedUsers',
          attributes: ['id', 'username', 'email'],
          through: {
            attributes: ['assignedAt', 'assignedBy']
          }
        }
      ]
    });

    if (!campagne) {
      return res.status(404).json({
        success: false,
        message: 'Campagne non trouvée'
      });
    }

    res.json({
      success: true,
      data: campagne
    });
  } catch (error) {
    console.error('Error fetching campagne:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la campagne',
      error: error.message
    });
  }
};

// Create new campagne
const createCampagne = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { name, code, description, statut = 'planifié' } = req.body;
    const { userId } = req.user;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Le nom de la campagne est requis'
      });
    }

    // Check if code already exists (if provided)
    if (code) {
      const existingCampagne = await Campagne.findOne({
        where: { code }
      });

      if (existingCampagne) {
        return res.status(400).json({
          success: false,
          message: 'Ce code de campagne existe déjà'
        });
      }
    }

    // Create campagne with formatted ID
    const campagne = await Campagne.create({
      campagneID: `COM_${Date.now()}`,
      name,
      code,
      description,
      statut
    }, { transaction });

    // Automatically assign the creator to the campagne
    await UserCampagne.create({
      userID: userId,
      campagneID: campagne.campagneID,
      assignedBy: userId,
      assignedAt: new Date()
    }, { transaction });

    await transaction.commit();

    // Fetch the created campagne with associations
    const createdCampagne = await Campagne.findByPk(campagne.campagneID, {
      include: [
        {
          model: User,
          as: 'assignedUsers',
          attributes: ['id', 'username', 'email'],
          through: {
            attributes: ['assignedAt', 'assignedBy']
          }
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Campagne créée avec succès',
      data: createdCampagne
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating campagne:', error);
    
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Erreur de validation',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la campagne',
      error: error.message
    });
  }
};

// Update campagne
const updateCampagne = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, description, statut } = req.body;
    const { userId } = req.user;

    // Check if user has access to this campagne
    const userAccess = await UserCampagne.findOne({
      where: {
        userID: userId,
        campagneID: id
      }
    });

    if (!userAccess) {
      return res.status(403).json({
        success: false,
        message: 'Accès refusé à cette campagne'
      });
    }

    const campagne = await Campagne.findByPk(id);

    if (!campagne) {
      return res.status(404).json({
        success: false,
        message: 'Campagne non trouvée'
      });
    }

    // Check if code already exists (if provided and different from current)
    if (code && code !== campagne.code) {
      const existingCampagne = await Campagne.findOne({
        where: { 
          code,
          campagneID: { [Op.ne]: id }
        }
      });

      if (existingCampagne) {
        return res.status(400).json({
          success: false,
          message: 'Ce code de campagne existe déjà'
        });
      }
    }

    // Update campagne
    await campagne.update({
      name: name || campagne.name,
      code: code !== undefined ? code : campagne.code,
      description: description !== undefined ? description : campagne.description,
      statut: statut || campagne.statut
    });

    // Fetch updated campagne with associations
    const updatedCampagne = await Campagne.findByPk(id, {
      include: [
        {
          model: User,
          as: 'assignedUsers',
          attributes: ['id', 'username', 'email'],
          through: {
            attributes: ['assignedAt', 'assignedBy']
          }
        }
      ]
    });

    res.json({
      success: true,
      message: 'Campagne mise à jour avec succès',
      data: updatedCampagne
    });
  } catch (error) {
    console.error('Error updating campagne:', error);
    
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Erreur de validation',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de la campagne',
      error: error.message
    });
  }
};

// Delete campagne
const deleteCampagne = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { userId } = req.user;

    // Check if user has access to this campagne
    const userAccess = await UserCampagne.findOne({
      where: {
        userID: userId,
        campagneID: id
      }
    });

    if (!userAccess) {
      return res.status(403).json({
        success: false,
        message: 'Accès refusé à cette campagne'
      });
    }

    const campagne = await Campagne.findByPk(id);

    if (!campagne) {
      return res.status(404).json({
        success: false,
        message: 'Campagne non trouvée'
      });
    }

    // Delete all related data first (in order of dependencies)

    // 1. Delete all campagne responses
    const deletedResponsesCount = await CampagneResponse.destroy({
      where: { campagneID: id },
      transaction
    });
    console.log(`Deleted ${deletedResponsesCount} campagne responses for campagne ${id}`);

    // 2. Delete all user assignments
    const deletedAssignmentsCount = await UserCampagne.destroy({
      where: { campagneID: id },
      transaction
    });
    console.log(`Deleted ${deletedAssignmentsCount} user assignments for campagne ${id}`);

    // 3. Delete all related attachments (if AllAttachment model is available)
    try {
      const { AllAttachment } = require('../models');
      const deletedAttachmentsCount = await AllAttachment.destroy({
        where: { campagneID: id },
        transaction
      });
      console.log(`Deleted ${deletedAttachmentsCount} attachments for campagne ${id}`);
    } catch (error) {
      console.log('AllAttachment model not available or no attachments to delete');
    }

    // 4. Finally, delete the campagne itself
    await campagne.destroy({ transaction });

    await transaction.commit();

    res.json({
      success: true,
      message: 'Campagne supprimée avec succès'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting campagne:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de la campagne',
      error: error.message
    });
  }
};

// Assign users to campagne
const assignUsersToCampagne = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { userIds } = req.body;
    const { userId: assignedBy } = req.user;

    if (!userIds || !Array.isArray(userIds)) {
      return res.status(400).json({
        success: false,
        message: 'Liste des utilisateurs requise'
      });
    }

    // Check if campagne exists
    const campagne = await Campagne.findByPk(id);
    if (!campagne) {
      return res.status(404).json({
        success: false,
        message: 'Campagne non trouvée'
      });
    }

    // Check if assigner has access to this campagne
    const assignerAccess = await UserCampagne.findOne({
      where: {
        userID: assignedBy,
        campagneID: id
      }
    });

    if (!assignerAccess) {
      return res.status(403).json({
        success: false,
        message: 'Accès refusé à cette campagne'
      });
    }

    // Verify all users exist
    const users = await User.findAll({
      where: { id: { [Op.in]: userIds } },
      attributes: ['id', 'username', 'email']
    });

    if (users.length !== userIds.length) {
      return res.status(400).json({
        success: false,
        message: 'Un ou plusieurs utilisateurs n\'existent pas'
      });
    }

    // Get existing assignments
    const existingAssignments = await UserCampagne.findAll({
      where: {
        campagneID: id,
        userID: { [Op.in]: userIds }
      }
    });

    const existingUserIds = existingAssignments.map(assignment => assignment.userID);
    const newUserIds = userIds.filter(userId => !existingUserIds.includes(userId));

    // Create new assignments
    const newAssignments = newUserIds.map(userId => ({
      userID: userId,
      campagneID: id,
      assignedBy,
      assignedAt: new Date()
    }));

    if (newAssignments.length > 0) {
      await UserCampagne.bulkCreate(newAssignments, { transaction });
    }

    await transaction.commit();

    // Fetch updated campagne with users
    const updatedCampagne = await Campagne.findByPk(id, {
      include: [
        {
          model: User,
          as: 'assignedUsers',
          attributes: ['id', 'username', 'email'],
          through: {
            attributes: ['assignedAt', 'assignedBy']
          }
        }
      ]
    });

    res.json({
      success: true,
      message: `${newAssignments.length} utilisateur(s) assigné(s) avec succès`,
      data: {
        campagne: updatedCampagne,
        newAssignments: newAssignments.length,
        existingAssignments: existingUserIds.length
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error assigning users to campagne:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'assignation des utilisateurs',
      error: error.message
    });
  }
};

// Get users assigned to campagne
const getCampagneUsers = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.user;

    // Check if user has access to this campagne
    const userAccess = await UserCampagne.findOne({
      where: {
        userID: userId,
        campagneID: id
      }
    });

    if (!userAccess) {
      return res.status(403).json({
        success: false,
        message: 'Accès refusé à cette campagne'
      });
    }

    const campagne = await Campagne.findByPk(id, {
      include: [
        {
          model: User,
          as: 'assignedUsers',
          attributes: ['id', 'username', 'email'],
          through: {
            attributes: ['assignedAt', 'assignedBy']
          }
        }
      ]
    });

    if (!campagne) {
      return res.status(404).json({
        success: false,
        message: 'Campagne non trouvée'
      });
    }

    res.json({
      success: true,
      data: {
        campagne: {
          campagneID: campagne.campagneID,
          name: campagne.name,
          code: campagne.code
        },
        users: campagne.assignedUsers
      }
    });
  } catch (error) {
    console.error('Error fetching campagne users:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des utilisateurs',
      error: error.message
    });
  }
};

// Remove user from campagne
const removeUserFromCampagne = async (req, res) => {
  try {
    const { id, userId: userIdToRemove } = req.params;
    const { userId: currentUserId } = req.user;

    // Check if current user has access to this campagne
    const userAccess = await UserCampagne.findOne({
      where: {
        userID: currentUserId,
        campagneID: id
      }
    });

    if (!userAccess) {
      return res.status(403).json({
        success: false,
        message: 'Accès refusé à cette campagne'
      });
    }

    // Find and remove the assignment
    const assignment = await UserCampagne.findOne({
      where: {
        userID: userIdToRemove,
        campagneID: id
      }
    });

    if (!assignment) {
      return res.status(404).json({
        success: false,
        message: 'Assignation non trouvée'
      });
    }

    await assignment.destroy();

    res.json({
      success: true,
      message: 'Utilisateur retiré de la campagne avec succès'
    });
  } catch (error) {
    console.error('Error removing user from campagne:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de l\'assignation',
      error: error.message
    });
  }
};

// Create campagne and assign users in one operation (for "Lancer la Campagne")
const createCampagneWithUsers = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { campagneData, userIds } = req.body;
    const { userId } = req.user;

    // Validate required fields
    if (!campagneData || !campagneData.name) {
      return res.status(400).json({
        success: false,
        message: 'Les données de la campagne sont requises'
      });
    }

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Au moins un utilisateur doit être assigné'
      });
    }

    // Check if code already exists (if provided)
    if (campagneData.code) {
      const existingCampagne = await Campagne.findOne({
        where: { code: campagneData.code }
      });

      if (existingCampagne) {
        return res.status(400).json({
          success: false,
          message: 'Ce code de campagne existe déjà'
        });
      }
    }

    // Create campagne with formatted ID
    const campagne = await Campagne.create({
      campagneID: `COM_${Date.now()}`,
      name: campagneData.name,
      code: campagneData.code,
      description: campagneData.description,
      statut: campagneData.statut || 'planifié'
    }, { transaction });

    // Verify all users exist
    const users = await User.findAll({
      where: { id: { [Op.in]: userIds } },
      attributes: ['id', 'username', 'email']
    });

    if (users.length !== userIds.length) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Un ou plusieurs utilisateurs n\'existent pas'
      });
    }

    // Create user assignments (including the creator)
    const allUserIds = [...new Set([userId, ...userIds])]; // Add creator and remove duplicates
    const assignments = allUserIds.map(assignedUserId => ({
      userID: assignedUserId,
      campagneID: campagne.campagneID,
      assignedBy: userId,
      assignedAt: new Date()
    }));

    await UserCampagne.bulkCreate(assignments, { transaction });

    await transaction.commit();

    // Fetch the created campagne with associations
    const createdCampagne = await Campagne.findByPk(campagne.campagneID, {
      include: [
        {
          model: User,
          as: 'assignedUsers',
          attributes: ['id', 'username', 'email'],
          through: {
            attributes: ['assignedAt', 'assignedBy']
          }
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: `Campagne créée avec succès et ${userIds.length} utilisateur(s) assigné(s)`,
      data: {
        campagne: createdCampagne,
        assignedUsers: userIds.length,
        totalUsers: allUserIds.length
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating campagne with users:', error);

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Erreur de validation',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la campagne',
      error: error.message
    });
  }
};

// Find or create campagne for a control (for "Lancer la Campagne")
const findOrCreateCampagneForControl = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { controlId, campagneData, userIds } = req.body;
    const { userId } = req.user;

    console.log('🔍 [DEBUG] findOrCreateCampagneForControl called with:', {
      controlId,
      controlIdType: typeof controlId,
      campagneDataName: campagneData?.name,
      campagneDataDescription: campagneData?.description,
      userIds,
      userId
    });

    // Validate required fields
    if (!controlId) {
      return res.status(400).json({
        success: false,
        message: 'ID du contrôle requis'
      });
    }

    if (!campagneData || !campagneData.name) {
      return res.status(400).json({
        success: false,
        message: 'Les données de la campagne sont requises'
      });
    }

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Au moins un utilisateur doit être assigné'
      });
    }

    // Debug: Show existing campagnes for this control
    const existingCampagnesForControl = await Campagne.findAll({
      where: { controlId },
      attributes: ['campagneID', 'name', 'controlId']
    });
    console.log('🔍 [DEBUG] Existing campagnes for this control:', existingCampagnesForControl.map(c => ({
      id: c.campagneID,
      name: c.name,
      controlId: c.controlId
    })));

    // Look for existing campagne for this control using controlId field
    console.log('🔍 [DEBUG] Searching for existing campagne with controlId:', controlId);

    const existingCampagne = await Campagne.findOne({
      where: {
        controlId: controlId
      },
      include: [
        {
          model: User,
          as: 'assignedUsers',
          attributes: ['id', 'username', 'email'],
          through: {
            attributes: ['assignedAt', 'assignedBy']
          }
        }
      ]
    });

    console.log('🔍 [DEBUG] Existing campagne search result:', existingCampagne ? {
      id: existingCampagne.campagneID,
      name: existingCampagne.name,
      description: existingCampagne.description,
      assignedUsersCount: existingCampagne.assignedUsers?.length
    } : 'NOT FOUND');



    let campagne;
    let isNewCampagne = false;

    if (existingCampagne) {
      console.log('✅ [DEBUG] Found existing campagne, updating it:', {
        id: existingCampagne.campagneID,
        currentName: existingCampagne.name,
        keepingName: true,
        currentDescription: existingCampagne.description,
        newDescription: campagneData.description
      });

      // Update existing campagne (keep original name and code)
      await existingCampagne.update({
        // name: keep original name
        // code: keep original code
        description: campagneData.description,
        controlId: controlId, // Ensure controlId is maintained
        statut: campagneData.statut || existingCampagne.statut
      }, { transaction });

      campagne = existingCampagne;
      console.log('✅ [DEBUG] Campagne updated successfully');
    } else {
      console.log('🆕 [DEBUG] No existing campagne found, creating new one:', {
        name: campagneData.name,
        code: campagneData.code,
        description: campagneData.description,
        statut: campagneData.statut || 'planifié'
      });

      // Create new campagne with formatted ID
      campagne = await Campagne.create({
        campagneID: `COM_${Date.now()}`,
        name: campagneData.name,
        code: campagneData.code,
        description: campagneData.description,
        controlId: controlId, // Link campagne to control
        statut: campagneData.statut || 'planifié'
      }, { transaction });

      isNewCampagne = true;
      console.log('🆕 [DEBUG] New campagne created with ID:', campagne.campagneID);
    }

    // Verify all users exist
    const users = await User.findAll({
      where: { id: { [Op.in]: userIds } },
      attributes: ['id', 'username', 'email']
    });

    if (users.length !== userIds.length) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Un ou plusieurs utilisateurs n\'existent pas'
      });
    }

    // Get current assignments
    const currentAssignments = await UserCampagne.findAll({
      where: { campagneID: campagne.campagneID }
    });

    const currentUserIds = currentAssignments.map(assignment => assignment.userID);
    const allUserIds = [...new Set([userId, ...userIds])]; // Add creator and remove duplicates

    console.log('👥 [DEBUG] User assignment analysis:', {
      campagneID: campagne.campagneID,
      currentUserIds,
      requestedUserIds: userIds,
      creatorUserId: userId,
      allUserIds
    });

    // Find users to add and remove
    const usersToAdd = allUserIds.filter(id => !currentUserIds.includes(id));
    const usersToRemove = currentUserIds.filter(id => !allUserIds.includes(id));

    console.log('👥 [DEBUG] User assignment changes:', {
      usersToAdd,
      usersToRemove,
      usersToAddCount: usersToAdd.length,
      usersToRemoveCount: usersToRemove.length
    });

    // Remove users no longer assigned
    if (usersToRemove.length > 0) {
      await UserCampagne.destroy({
        where: {
          campagneID: campagne.campagneID,
          userID: { [Op.in]: usersToRemove }
        },
        transaction
      });
    }

    // Add new users
    if (usersToAdd.length > 0) {
      const newAssignments = usersToAdd.map(assignedUserId => ({
        userID: assignedUserId,
        campagneID: campagne.campagneID,
        assignedBy: userId,
        assignedAt: new Date()
      }));

      await UserCampagne.bulkCreate(newAssignments, { transaction });
    }

    // Log campaign activity for the control
    if (controlId) {
      try {
        // Get username for activity logging
        let username = 'Unknown User';
        try {
          const user = await User.findByPk(userId);
          if (user) {
            username = user.username || user.email || user.name;
          }
        } catch (err) {
          console.error('Error getting user for activity logging:', err);
        }

        // Log campaign launch activity
        await logLinkingActivity(
          controlId,
          username,
          'campagne',
          `${campagne.name} (${campagne.campagneID})`,
          isNewCampagne ? 'link' : 'update'
        );
      } catch (activityError) {
        console.error('Error logging campaign activity:', activityError);
        // Don't fail the main operation if activity logging fails
      }
    }

    await transaction.commit();

    // Fetch the updated campagne with associations
    const updatedCampagne = await Campagne.findByPk(campagne.campagneID, {
      include: [
        {
          model: User,
          as: 'assignedUsers',
          attributes: ['id', 'username', 'email'],
          through: {
            attributes: ['assignedAt', 'assignedBy']
          }
        }
      ]
    });

    res.status(isNewCampagne ? 201 : 200).json({
      success: true,
      message: isNewCampagne
        ? `Campagne créée avec succès et ${userIds.length} utilisateur(s) assigné(s)`
        : `Campagne mise à jour avec succès et ${userIds.length} utilisateur(s) assigné(s)`,
      data: {
        campagne: updatedCampagne,
        assignedUsers: userIds.length,
        totalUsers: allUserIds.length,
        isNewCampagne,
        usersAdded: usersToAdd.length,
        usersRemoved: usersToRemove.length
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error finding or creating campagne for control:', error);

    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        success: false,
        message: 'Erreur de validation',
        errors: error.errors.map(err => ({
          field: err.path,
          message: err.message
        }))
      });
    }

    res.status(500).json({
      success: false,
      message: 'Erreur lors de la gestion de la campagne',
      error: error.message
    });
  }
};

module.exports = {
  getAllCampagnes,
  getCampagneById,
  createCampagne,
  updateCampagne,
  deleteCampagne,
  assignUsersToCampagne,
  getCampagneUsers,
  removeUserFromCampagne,
  createCampagneWithUsers,
  findOrCreateCampagneForControl
};
