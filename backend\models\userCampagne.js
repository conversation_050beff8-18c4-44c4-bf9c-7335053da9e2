module.exports = (sequelize, DataTypes) => {
const UserCampagne = sequelize.define('UserCampagne', {
  userID: {
    type: DataTypes.INTEGER,
    allowNull: false,
    primaryKey: true,
    references: {
      model: 'Users',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE'
  },
  campagneID: {
    type: DataTypes.STRING,
    allowNull: false,
    primaryKey: true,
    references: {
      model: 'Campagnes',
      key: 'campagneID'
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE'
  },
  assignedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  assignedBy: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'SET NULL'
  }
}, {
  tableName: 'UserCampagne',
  timestamps: false,
  indexes: [
    {
      fields: ['userID']
    },
    {
      fields: ['campagneID']
    },
    {
      fields: ['assignedAt']
    },
    {
      fields: ['assignedBy']
    }
  ]
});

// Define associations
UserCampagne.associate = (models) => {
  // Belongs to User (assigned user)
  UserCampagne.belongsTo(models.User, {
    foreignKey: 'userID',
    as: 'assignedUser'
  });

  // Belongs to User (user who made the assignment)
  UserCampagne.belongsTo(models.User, {
    foreignKey: 'assignedBy',
    as: 'assignedByUser'
  });

  // Belongs to Campagne
  UserCampagne.belongsTo(models.Campagne, {
    foreignKey: 'campagneID',
    as: 'campagne'
  });
};

return UserCampagne;
};
