module.exports = (sequelize, DataTypes) => {
  const Control = sequelize.define('Control', {
    controlID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    controlKey: {
      type: DataTypes.INTEGER, // 0 or 1 in Excel, treated as a numeric flag
      allowNull: true,
    },
    controlExecutionMethod: {
      type: DataTypes.ENUM(
        'Observation', 'Exhaustive', 'Exception Control', 'By Sample', 'Standard Control'
      ),
      allowNull: true,
    },
    objective: {
      type: DataTypes.TEXT, // Longer text possible
      allowNull: true,
    },
    executionProcedure: {
      type: DataTypes.TEXT, // e.g., "Vérifier les étapes de contrôles ci-dessous"
      allowNull: true,
    },
    operationalCost: {
      type: DataTypes.FLOAT, // e.g., 15000, 23000
      allowNull: true,
    },
    organizationalLevel: {
      type: DataTypes.ENUM(
        'Global', 'Local'
      ),
      allowNull: true,
    },
    sampleType: {
      type: DataTypes.ENUM(
        'Command', 'Bill', 'Contract'
      ),
      allowNull: true,
    },
    testingFrequency: {
      type: DataTypes.ENUM(
        'Quarterly', 'Bi-Yearly', 'Yearly'
      ),
      allowNull: true,
    },
    testingMethod: {
      type: DataTypes.ENUM(
        'Observation', 'Inquiry', 'Inspection', 'Re-Performance'
      ),
      allowNull: true,
    },
    testingPopulationSize: {
      type: DataTypes.INTEGER, // e.g., 10, 50
      allowNull: true,
    },
    testingProcedure: {
      type: DataTypes.TEXT, // e.g., "Review if all the approved request..."
      allowNull: true,
    },
    implementingActionPlan: {
      type: DataTypes.TEXT, // e.g., "* Améliorer le contrôle des paiements"
      allowNull: true,
    },
    designQuality: {
      type: DataTypes.STRING, // "Satisfaisant" or "Insatisfaisant"
      allowNull: true,
    },
    effectivenessLevel: {
      type: DataTypes.STRING, // "Satisfaisant" or "Insatisfaisant"
      allowNull: true,
    },
    businessProcess: {
      type: DataTypes.STRING, // Foreign key to BusinessProcess
      allowNull: true,
      references: {
        model: 'BusinessProcess',
        key: 'businessProcessID',
      },
    },
    organizationalProcess: {
      type: DataTypes.STRING, // Foreign key to OrganizationalProcess
      allowNull: true,
      references: {
        model: 'OrganizationalProcess',
        key: 'organizationalProcessID',
      },
    },
    operation: {
      type: DataTypes.STRING, // Foreign key to Operation
      allowNull: true,
      references: {
        model: 'Operation', // Match the actual database table name
        key: 'operationID',
      },
    },
    application: {
      type: DataTypes.STRING, // Foreign key to Application
      allowNull: true,
      references: {
        model: 'Application',
        key: 'applicationID',
      },
    },
    entity: {
      type: DataTypes.STRING, // Foreign key to Entity
      allowNull: true,
      references: {
        model: 'Entity',
        key: 'entityID',
      },
    },
    controlType: {
      type: DataTypes.STRING, // Foreign key to ControlType
      allowNull: true,
      references: {
        model: 'ControlType',
        key: 'controlTypeID',
      },
    },
    risk: {
      type: DataTypes.STRING, // Foreign key to Risk
      allowNull: true,
      references: {
        model: 'Risk',
        key: 'riskID',
      },
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  }, {
    tableName: 'Control',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['controlID'],
      },
    ],
  });

  Control.associate = (models) => {
    // Control has many ControlQuestions
    if (models.ControlQuestion) {
      Control.hasMany(models.ControlQuestion, {
        foreignKey: 'controlID',
        as: 'questions'
      });
    }

    // Control has many Users through ControlQuestionAssignment
    if (models.User && models.ControlQuestionAssignment) {
      Control.belongsToMany(models.User, {
        through: models.ControlQuestionAssignment,
        foreignKey: 'controlId',
        otherKey: 'userId',
        as: 'assignedUsers'
      });

      // Direct association with ControlQuestionAssignment
      Control.hasMany(models.ControlQuestionAssignment, {
        foreignKey: 'controlId',
        as: 'userAssignments'
      });
    }

    // Control activity logs
    if (models.ControlActivityLog) {
      Control.hasMany(models.ControlActivityLog, {
        foreignKey: 'controlID',
        as: 'activityLogs'
      });
    }

    // Many-to-many relationship with ActionPlan
    if (models.ActionPlan) {
      Control.belongsToMany(models.ActionPlan, {
        through: {
          model: 'ActionPlanControl',
          timestamps: true
        },
        foreignKey: 'controlID',
        otherKey: 'actionPlanID',
        as: 'actionPlans'
      });
    }
  };

  return Control;
};