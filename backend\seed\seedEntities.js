const db = require('../models');

// Helper function to get random item from array
const getRandomItem = (array) => {
  if (!array || array.length === 0) return null;
  return array[Math.floor(Math.random() * array.length)];
};

// Sample data for entities
const countries = [
  { code: 'US', name: 'United States', currency: 'USD' },
  { code: 'UK', name: 'United Kingdom', currency: 'GBP' },
  { code: 'FR', name: 'France', currency: 'EUR' },
  { code: 'DE', name: 'Germany', currency: 'EUR' },
  { code: 'JP', name: 'Japan', currency: 'JPY' },
  { code: 'CA', name: 'Canada', currency: 'CAD' },
  { code: 'AU', name: 'Australia', currency: 'AUD' },
  { code: 'CH', name: 'Switzerland', currency: 'CHF' },
  { code: 'SG', name: 'Singapore', currency: 'SGD' },
  { code: 'BR', name: 'Brazil', currency: 'BRL' }
];

const cities = {
  'US': ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Miami', 'San Francisco', 'Boston', 'Seattle'],
  'UK': ['London', 'Manchester', 'Birmingham', 'Edinburgh', 'Glasgow', 'Liverpool', 'Bristol', 'Leeds'],
  'FR': ['Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice', 'Bordeaux', 'Strasbourg', 'Lille'],
  'DE': ['Berlin', 'Munich', 'Hamburg', 'Frankfurt', 'Cologne', 'Stuttgart', 'Düsseldorf', 'Leipzig'],
  'JP': ['Tokyo', 'Osaka', 'Kyoto', 'Yokohama', 'Nagoya', 'Sapporo', 'Fukuoka', 'Kobe'],
  'CA': ['Toronto', 'Montreal', 'Vancouver', 'Calgary', 'Ottawa', 'Edmonton', 'Quebec City', 'Winnipeg'],
  'AU': ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide', 'Gold Coast', 'Canberra', 'Hobart'],
  'CH': ['Zurich', 'Geneva', 'Basel', 'Bern', 'Lausanne', 'Lucerne', 'St. Gallen', 'Lugano'],
  'SG': ['Central', 'East', 'North', 'Northeast', 'West', 'Jurong', 'Woodlands', 'Tampines'],
  'BR': ['São Paulo', 'Rio de Janeiro', 'Brasília', 'Salvador', 'Fortaleza', 'Belo Horizonte', 'Manaus', 'Curitiba']
};

const entityTypes = ['HQ', 'Branch', 'Subsidiary', 'Division', 'Department', 'Team', 'Project'];

const internalExternalOptions = ['internal', 'external', 'mixed'];

async function seedEntities(numEntities = 30) {
  try {
    console.log(`Starting to seed ${numEntities} entities...`);

    // Clear existing entities
    await db.Entity.destroy({ where: {} });
    console.log('Cleared existing entities');

    // First, create headquarters for each country (no parent)
    const headquarters = [];

    for (const country of countries) {
      const entityID = `${country.code}001`;
      const hq = {
        entityID,
        name: `${country.name} Headquarters`,
        type: 'HQ',
        localCurrency: country.currency,
        code: `${country.code}HQ`,
        comment: `Main headquarters in ${country.name}`,
        internalExternal: 'internal',
        parentEntityID: null
      };

      headquarters.push(hq);
    }

    // Create the headquarters first
    await db.Entity.bulkCreate(headquarters);
    console.log(`Created ${headquarters.length} headquarters entities`);

    // Now create branches and other entities with parents
    const remainingEntities = [];
    const createdHQs = await db.Entity.findAll({ where: { type: 'HQ' } });

    // Calculate how many additional entities to create
    const additionalEntitiesCount = Math.max(0, numEntities - headquarters.length);

    for (let i = 0; i < additionalEntitiesCount; i++) {
      // Select a random parent from the headquarters
      const parentEntity = getRandomItem(createdHQs);
      const country = countries.find(c => c.code === parentEntity.entityID.substring(0, 2));

      if (!country) continue;

      // Get cities for this country
      const countryCities = cities[country.code] || ['Unknown City'];
      const city = getRandomItem(countryCities);

      // Create a unique entity ID
      const entityCount = await db.Entity.count({ where: { entityID: { [db.Sequelize.Op.like]: `${country.code}%` } } });
      const entityID = `${country.code}${String(entityCount + 1).padStart(3, '0')}`;

      // Select a random entity type (not HQ)
      const entityType = getRandomItem(entityTypes.filter(type => type !== 'HQ'));

      const entity = {
        entityID,
        name: `${city} ${entityType}`,
        type: entityType,
        localCurrency: country.currency,
        code: `${city.substring(0, 3).toUpperCase()}${entityCount + 1}`,
        comment: `${entityType} in ${city}, ${country.name}`,
        internalExternal: getRandomItem(internalExternalOptions),
        parentEntityID: parentEntity.entityID
      };

      remainingEntities.push(entity);
    }

    // Create the remaining entities
    if (remainingEntities.length > 0) {
      await db.Entity.bulkCreate(remainingEntities);
      console.log(`Created ${remainingEntities.length} additional entities with parents`);
    }

    // Log some examples of the created entities
    const allEntities = await db.Entity.findAll({ include: [{ model: db.Entity, as: 'parentEntity' }] });

    console.log('\nExample entities created:');
    for (let i = 0; i < Math.min(5, allEntities.length); i++) {
      const entity = allEntities[i];
      console.log(`- ${entity.name} (${entity.entityID})`);
      console.log(`  Type: ${entity.type}, Currency: ${entity.localCurrency}`);
      console.log(`  Parent: ${entity.parentEntity ? entity.parentEntity.name : 'None'}`);
      console.log('');
    }

    console.log(`Successfully seeded ${allEntities.length} entities`);
  } catch (error) {
    console.error('Error seeding entities:', error);
  }
}

// Run the seeding function
seedEntities();

module.exports = seedEntities;