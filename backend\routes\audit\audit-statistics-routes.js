const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAuditStatistics,
  getQuickCounts
} = require('../../controllers/audit/audit-statistics-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get comprehensive audit statistics for dashboard
router.get('/', authorizeRoles(['audit_director', 'auditor']), getAuditStatistics);

// Get quick counts for specific entities
router.get('/counts', authorizeRoles(['audit_director', 'auditor']), getQuickCounts);

module.exports = router;
