# 📋 Audit Table Template

## 🎯 **Programme de Travail Table Template**

This is the exact table structure from `perimetre-programme.jsx` that can be copied for consistent table implementations across the audit module.

### **Complete Table Structure:**

```jsx
<Card className="overflow-hidden">
  <CardContent className="p-0">
    <div className="overflow-x-auto">
      <Table className="min-w-full">
        <TableHeader>
          <TableRow className="bg-gray-50 hover:bg-gray-100/50">
            <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
            <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</TableHead>
            <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de début</TableHead>
            <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de fin</TableHead>
            <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activités</TableHead>
            <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="divide-y divide-gray-200">
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={6} className="px-4 py-10 text-center">
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F62D51]"></div>
                </div>
              </TableCell>
            </TableRow>
          ) : filteredItems.length > 0 ? (
            filteredItems.map(item => (
              <TableRow
                key={item.id}
                className="hover:bg-gray-50/50 cursor-pointer"
                onClick={() => navigateToItem(item.id)}
              >
                <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                  <div className="flex items-center">
                    <img src={iconSrc} alt="Icon" className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>{item.nom}</span>
                    <ExternalLink className="h-3.5 w-3.5 ml-2 text-gray-400" />
                  </div>
                </TableCell>
                <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{getStatusBadge(item.statut)}</TableCell>
                <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">{formatDate(item.dateDebut)}</TableCell>
                <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">{formatDate(item.dateFin)}</TableCell>
                <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">{item.activitesCount}</TableCell>
                <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                  <div className="flex justify-end gap-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-8 w-8 p-0"
                      onClick={e => { e.stopPropagation(); handleEdit(item); }}
                    >
                      <Edit className="h-4 w-4 text-blue-600" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-8 w-8 p-0"
                      onClick={e => { e.stopPropagation(); handleDelete(item.id); }}
                    >
                      <Trash2 className="h-4 w-4 text-red-600" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={6} className="px-4 py-10 text-center text-sm text-gray-500">
                Aucun élément trouvé
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  </CardContent>
</Card>
```

### **Key Styling Classes:**

#### **Table Container:**
- `Card className="overflow-hidden"`
- `CardContent className="p-0"`
- `div className="overflow-x-auto"`
- `Table className="min-w-full"`

#### **Header Row:**
- `TableRow className="bg-gray-50 hover:bg-gray-100/50"`
- `TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"`

#### **Body Rows:**
- `TableBody className="divide-y divide-gray-200"`
- `TableRow className="hover:bg-gray-50/50 cursor-pointer"`
- `TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap"` (for name column)
- `TableCell className="px-4 py-3 text-sm whitespace-nowrap"` (for status column)
- `TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap"` (for date columns)

#### **Icon Implementation:**
```jsx
<div className="flex items-center">
  <img src={iconSrc} alt="Description" className="h-4 w-4 mr-2 flex-shrink-0" />
  <span>{item.name}</span>
  <ExternalLink className="h-3.5 w-3.5 ml-2 text-gray-400" />
</div>
```

#### **Action Buttons:**
```jsx
<div className="flex justify-end gap-2">
  <Button
    size="sm"
    variant="ghost"
    className="h-8 w-8 p-0"
    onClick={e => { e.stopPropagation(); handleEdit(item); }}
  >
    <Edit className="h-4 w-4 text-blue-600" />
  </Button>
  <Button
    size="sm"
    variant="ghost"
    className="h-8 w-8 p-0"
    onClick={e => { e.stopPropagation(); handleDelete(item.id); }}
  >
    <Trash2 className="h-4 w-4 text-red-600" />
  </Button>
</div>
```

#### **Loading State:**
```jsx
<TableRow>
  <TableCell colSpan={6} className="px-4 py-10 text-center">
    <div className="flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F62D51]"></div>
    </div>
  </TableCell>
</TableRow>
```

#### **Empty State:**
```jsx
<TableRow>
  <TableCell colSpan={6} className="px-4 py-10 text-center text-sm text-gray-500">
    Aucun élément trouvé
  </TableCell>
</TableRow>
```

### **Status Badge Function:**
```jsx
const getStatusBadge = (status) => {
  switch (status) {
    case 'Planifié':
      return <Badge className="bg-yellow-100 text-yellow-800">Planifié</Badge>;
    case 'En cours':
      return <Badge className="bg-blue-100 text-blue-800">En cours</Badge>;
    case 'Terminé':
      return <Badge className="bg-green-100 text-green-800">Terminé</Badge>;
    case 'Suspendu':
      return <Badge className="bg-orange-100 text-orange-800">Suspendu</Badge>;
    case 'Annulé':
      return <Badge className="bg-red-100 text-red-800">Annulé</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};
```

### **Date Formatting Function:**
```jsx
const formatDate = (dateString) => {
  if (!dateString) return "Non définie";
  try {
    return new Date(dateString).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  } catch {
    return dateString;
  }
};
```

### **Search Implementation:**
```jsx
<div className="mb-4 flex justify-between items-center">
  <div className="relative w-64">
    <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
    <Input
      placeholder="Rechercher..."
      className="pl-8"
      value={searchTerm}
      onChange={e => setSearchTerm(e.target.value)}
    />
  </div>
  <Button
    className="bg-[#F62D51] hover:bg-[#F62D51]/90"
    onClick={() => setIsDialogOpen(true)}
  >
    <Plus className="h-4 w-4 mr-2" />
    Ajouter
  </Button>
</div>
```

## 🎨 **Consistent Styling Guidelines**

1. **Colors**: Use `#F62D51` for primary actions and loading spinners
2. **Spacing**: `px-4 py-3` for table cells, `gap-2` for button groups
3. **Icons**: `h-4 w-4` for list icons, `h-3.5 w-3.5` for external link icons
4. **Hover States**: `hover:bg-gray-50/50` for table rows
5. **Loading**: Consistent spinner with `border-[#F62D51]`
6. **Typography**: `text-sm` for table content, `font-medium` for names

This template ensures consistent styling across all audit module tables! 🚀
