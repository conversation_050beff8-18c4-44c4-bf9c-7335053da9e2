import { useOutletContext } from "react-router-dom";
import { GitBranch } from "lucide-react";
import { useTranslation } from "react-i18next";

function ActionPlanWorkflow() {
  const { actionPlan } = useOutletContext();
  const { t } = useTranslation();

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">{t('admin.action_plans.tabs.workflow', 'Workflow')}</h2>

      <div className="flex flex-col items-center justify-center py-12 text-center">
        <GitBranch className="h-16 w-16 text-gray-300 mb-4" />
        <h3 className="text-lg font-medium text-gray-700 mb-2">{t('admin.action_plans.workflow.coming_soon', 'Workflow Management Coming Soon')}</h3>
        <p className="text-gray-500 max-w-md">
          {t('admin.action_plans.workflow.description', 'This section will allow you to manage workflows and processes related to this action plan.')}
        </p>
      </div>
    </div>
  );
}

export default ActionPlanWorkflow;
