import { useOutletContext } from "react-router-dom";
import { Tag, Calendar, Hash, FileText, Network, GitBranch } from "lucide-react";
import { useEffect, useState } from "react";

function OrganizationalProcessesOverview() {
  const { process, organizationalProcesses, businessProcesses } = useOutletContext();
  const [parentOrgProcess, setParentOrgProcess] = useState(null);
  const [parentBusinessProcess, setParentBusinessProcess] = useState(null);

  // Find parent organizational process
  useEffect(() => {
    if (process?.parentOrganizationalProcess && organizationalProcesses?.length) {
      const parent = organizationalProcesses.find(
        p => p.organizationalProcessID === process.parentOrganizationalProcess
      );
      setParentOrgProcess(parent);
    } else {
      setParentOrgProcess(null);
    }
  }, [process, organizationalProcesses]);

  // Find parent business process
  useEffect(() => {
    if (process?.parentBusinessProcess && businessProcesses?.length) {
      const parent = businessProcesses.find(
        p => p.businessProcessID === process.parentBusinessProcess
      );
      setParentBusinessProcess(parent);
    } else {
      setParentBusinessProcess(null);
    }
  }, [process, businessProcesses]);

  // Helper function to format dates
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-GB", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6">
        <div>
          <h3 className="text-lg font-semibold text-[#242A33] mb-4">Organizational Process Details</h3>
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Tag className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Name</p>
                <p className="font-medium">{process.name || "N/A"}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Hash className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Code</p>
                <p className="font-medium">{process.code || "N/A"}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Network className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Parent Organizational Process</p>
                <p className="font-medium">{parentOrgProcess ? parentOrgProcess.name : "None"}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <GitBranch className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Parent Business Process</p>
                <p className="font-medium">{parentBusinessProcess ? parentBusinessProcess.name : "None"}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <FileText className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Comment</p>
                <p className="font-medium">{process.comment || "N/A"}</p>
              </div>
            </div>

            {process.createdAt && (
              <div className="flex items-start gap-4">
                <div className="bg-gray-50 p-2 rounded-lg">
                  <Calendar className="h-5 w-5 text-[#555F6D]" />
                </div>
                <div>
                  <p className="text-sm text-[#555F6D]">Created At</p>
                  <p className="font-medium">{formatDate(process.createdAt)}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default OrganizationalProcessesOverview;
