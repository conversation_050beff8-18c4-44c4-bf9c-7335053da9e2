<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translation Test</title>
</head>
<body>
    <h1>Translation Test</h1>
    <p>This is a simple test to check if the translation files are valid JSON.</p>
    
    <h2>French Translation Test</h2>
    <div id="french-test"></div>
    
    <h2>English Translation Test</h2>
    <div id="english-test"></div>

    <script>
        // Test French translations
        fetch('./src/locales/fr/translation.json')
            .then(response => response.json())
            .then(data => {
                document.getElementById('french-test').innerHTML = `
                    <p>✅ French JSON is valid</p>
                    <p>Loading text: ${data.admin?.controls?.features?.loading?.reference_data || 'NOT FOUND'}</p>
                    <p>Common none: ${data.common?.none || 'NOT FOUND'}</p>
                `;
            })
            .catch(error => {
                document.getElementById('french-test').innerHTML = `
                    <p>❌ French JSON error: ${error.message}</p>
                `;
            });

        // Test English translations
        fetch('./src/locales/en/translation.json')
            .then(response => response.json())
            .then(data => {
                document.getElementById('english-test').innerHTML = `
                    <p>✅ English JSON is valid</p>
                    <p>Loading text: ${data.admin?.controls?.features?.loading?.reference_data || 'NOT FOUND'}</p>
                    <p>Common none: ${data.common?.none || 'NOT FOUND'}</p>
                `;
            })
            .catch(error => {
                document.getElementById('english-test').innerHTML = `
                    <p>❌ English JSON error: ${error.message}</p>
                `;
            });
    </script>
</body>
</html>
