import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

const API_BASE_URL = getApiBaseUrl();

/**
 * Get audit scope items for a mission
 * @param {string} missionId - The mission ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Array, message?: string}>}
 */
export const getAuditScopeByMissionId = async (missionId, signal) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/audit-scopes/mission/${missionId}`, {
      signal,
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Create a new audit scope item
 * @param {Object} scopeData - The scope data including missionId and references
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Object, message?: string}>}
 */
export const createAuditScope = async (scopeData, signal) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/audit-scopes`, scopeData, {
      signal,
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Update audit scope relationships
 * @param {string} missionId - The mission ID
 * @param {Object} relationships - Object containing arrays of IDs for each type
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Object, message?: string}>}
 */
export const updateAuditScopeRelationships = async (missionId, relationships, signal) => {
  try {
    // First, get or create the audit scope for this mission
    let scopeId;
    try {
      const scopeResponse = await axios.get(`${API_BASE_URL}/audit-scopes/mission/${missionId}`, {
        signal,
        withCredentials: true
      });

      if (scopeResponse.data.success && scopeResponse.data.data && scopeResponse.data.data.length > 0) {
        scopeId = scopeResponse.data.data[0].id;
      } else {
        // Create new scope if it doesn't exist
        const createResponse = await axios.post(`${API_BASE_URL}/audit-scopes`, {
          auditMissionID: missionId,
          name: `Scope for Mission ${missionId}`
        }, {
          signal,
          withCredentials: true
        });

        if (!createResponse.data.success || !createResponse.data.data || !createResponse.data.data.id) {
          throw new Error(createResponse.data.message || 'Failed to create audit scope');
        }
        scopeId = createResponse.data.data.id;
      }
    } catch (error) {
      console.error('Error getting/creating audit scope:', error);
      throw new Error('Failed to get or create audit scope: ' + (error.response?.data?.message || error.message));
    }

    if (!scopeId) {
      throw new Error('No valid scope ID obtained');
    }

    // Update all relationships in a single request
    const updateResponse = await axios.put(`${API_BASE_URL}/audit-scopes/${scopeId}`, {
      riskIDs: relationships.risks || [],
      controlIDs: relationships.controls || [],
      entityIDs: relationships.entities || [],
      organizationalProcessIDs: relationships.organizationalProcesses || [],
      businessProcessIDs: relationships.businessProcesses || []
    }, {
      signal,
      withCredentials: true
    });

    if (!updateResponse.data.success) {
      throw new Error(updateResponse.data.message || 'Failed to update audit scope relationships');
    }

    return {
      success: true,
      data: {
        id: scopeId,
        ...relationships
      }
    };
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    console.error('Error in updateAuditScopeRelationships:', error);
    throw error;
  }
};

/**
 * Delete an audit scope item
 * @param {string} scopeId - The scope item ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, message?: string}>}
 */
export const deleteAuditScope = async (scopeId, signal) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/audit-scopes/${scopeId}`, {
      signal,
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
}; 