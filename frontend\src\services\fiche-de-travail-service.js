import axios from 'axios';
import { getApiEndpointUrl, getAuthHeaders } from '@/utils/api-config';

const AXIOS_TIMEOUT = 30000; // 30 seconds

/**
 * Get all fiches de travail
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Array, message?: string}>}
 */
export const getAllFichesDeTravail = async (signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl('audit/fiches-de-travail'),
      {
        headers: getAuthHeaders(),
        signal,
        timeout: AXIOS_TIMEOUT
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Get fiches de travail by activity ID
 * @param {string} activityId - The activity ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Array, message?: string}>}
 */
export const getFichesDeTravailByActivityId = async (activityId, signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl(`audit/fiches-de-travail/activity/${activityId}`),
      {
        headers: getAuthHeaders(),
        signal,
        timeout: AXIOS_TIMEOUT
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Get fiche de travail by ID
 * @param {string} id - The fiche ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Object, message?: string}>}
 */
export const getFicheDeTravailById = async (id, signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl(`audit/fiches-de-travail/${id}`),
      {
        headers: getAuthHeaders(),
        signal,
        timeout: AXIOS_TIMEOUT
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Create a new fiche de travail
 * @param {Object} ficheData - The fiche data
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Object, message?: string}>}
 */
export const createFicheDeTravail = async (ficheData, signal) => {
  try {
    const response = await axios.post(
      getApiEndpointUrl('audit/fiches-de-travail'),
      ficheData,
      {
        headers: getAuthHeaders(),
        signal,
        timeout: AXIOS_TIMEOUT
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Update a fiche de travail
 * @param {string} id - The fiche ID
 * @param {Object} ficheData - The updated fiche data
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Object, message?: string}>}
 */
export const updateFicheDeTravail = async (id, ficheData, signal) => {
  try {
    const response = await axios.put(
      getApiEndpointUrl(`audit/fiches-de-travail/${id}`),
      ficheData,
      {
        headers: getAuthHeaders(),
        signal,
        timeout: AXIOS_TIMEOUT
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Delete a fiche de travail
 * @param {string} id - The fiche ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, message?: string}>}
 */
export const deleteFicheDeTravail = async (id, signal) => {
  try {
    const response = await axios.delete(
      getApiEndpointUrl(`audit/fiches-de-travail/${id}`),
      {
        headers: getAuthHeaders(),
        signal,
        timeout: AXIOS_TIMEOUT
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Get questions by fiche de travail ID
 * @param {string} ficheId - The fiche de travail ID
 * @returns {Promise<{success: boolean, data?: Array, message?: string}>}
 */
export const getQuestionsByFicheId = async (ficheId) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl(`audit/fiches-de-travail/questions/fiche/${ficheId}`),
      {
        headers: getAuthHeaders(),
        timeout: AXIOS_TIMEOUT
      }
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Delete a question by ID
 * @param {string} id - The question ID
 * @returns {Promise<{success: boolean, message?: string}>}
 */
export const deleteQuestion = async (id) => {
  try {
    const response = await axios.delete(
      getApiEndpointUrl(`audit/fiches-de-travail/questions/${id}`),
      {
        headers: getAuthHeaders(),
        timeout: AXIOS_TIMEOUT
      }
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Reorder questions for a fiche de travail
 * @param {string} ficheDeTravailID - The fiche de travail ID
 * @param {string[]} orderedIds - Array of question IDs in new order
 * @returns {Promise<{success: boolean, message?: string}>}
 */
export const reorderQuestions = async (ficheDeTravailID, orderedIds) => {
  try {
    const response = await axios.put(
      getApiEndpointUrl('audit/fiches-de-travail/questions/reorder'),
      { ficheDeTravailID, orderedIds },
      { headers: getAuthHeaders(), timeout: AXIOS_TIMEOUT }
    );
    return response.data;
  } catch (error) {
    throw error;
  }
}; 