const express = require('express');
const router = express.Router();
const { verifyToken } = require('../../middleware/auth');
const incidentFinancialController = require('../../controllers/financial/incident-financial-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all financial data for an incident
router.get('/incident/:incidentId', incidentFinancialController.getIncidentFinancialData);

// Update all financial data for an incident
router.put('/incident/:incidentId', incidentFinancialController.updateIncidentFinancialData);

module.exports = router;
