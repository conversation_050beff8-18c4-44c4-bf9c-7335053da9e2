import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Calendar, User, Target } from "lucide-react";
import missionIcon from "@/assets/mission.png";

/**
 * Mission Details Modal Component
 * Displays comprehensive mission information in a modal dialog
 */
function MissionDetailsModal({ 
  isOpen, 
  onClose, 
  mission, 
  users = [] 
}) {
  // Helper function to get mission status badge
  const getStatusBadge = (etat) => {
    const statusConfig = {
      'Planned': { label: 'Planifiée', variant: 'secondary', color: 'bg-gray-100 text-gray-800' },
      'In Progress': { label: 'En cours', variant: 'default', color: 'bg-blue-100 text-blue-800' },
      'Completed': { label: 'Terminée', variant: 'default', color: 'bg-green-100 text-green-800' },
      'On Hold': { label: 'En attente', variant: 'destructive', color: 'bg-yellow-100 text-yellow-800' },
      'Cancelled': { label: 'Annulée', variant: 'destructive', color: 'bg-red-100 text-red-800' }
    };
    
    const config = statusConfig[etat] || statusConfig['Planned'];
    return (
      <Badge variant={config.variant} className={config.color}>
        {config.label}
      </Badge>
    );
  };

  // Helper function to get user name by ID
  const getUserName = (userId) => {
    const user = users.find(u => u.id === userId);
    return user ? `${user.username}` : 'Non assigné';
  };

  if (!mission) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center text-xl">
            <img src={missionIcon} alt="Mission" className="h-6 w-6 mr-2" />
            {mission.name}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {/* Status and Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500">Statut</label>
              <div>{getStatusBadge(mission.etat)}</div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500">Catégorie</label>
              <p className="text-sm">{mission.categorie || 'Non spécifiée'}</p>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500">Code</label>
              <p className="text-sm font-mono">{mission.code || 'Non défini'}</p>
            </div>
          </div>

          {/* Dates and Progress */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500 flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                Date de début
              </label>
              <p className="text-sm">
                {mission.datedebut ? new Date(mission.datedebut).toLocaleDateString('fr-FR') : 'Non définie'}
              </p>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500 flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                Date de fin
              </label>
              <p className="text-sm">
                {mission.datefin ? new Date(mission.datefin).toLocaleDateString('fr-FR') : 'Non définie'}
              </p>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500">Avancement</label>
              <p className="text-sm">{mission.avancement || '0%'}</p>
            </div>
          </div>

          {/* Team and Responsibilities */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500 flex items-center">
                <User className="h-4 w-4 mr-1" />
                Chef de mission
              </label>
              <p className="text-sm">{getUserName(mission.chefmission)}</p>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500">Principal audité</label>
              <p className="text-sm">{mission.principalAudite || 'Non assigné'}</p>
            </div>
          </div>

          {/* Objective */}
          {mission.objectif && (
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500 flex items-center">
                <Target className="h-4 w-4 mr-1" />
                Objectif
              </label>
              <div className="p-3 bg-blue-50 border border-blue-100 rounded-lg">
                <p className="text-sm text-gray-800">{mission.objectif}</p>
              </div>
            </div>
          )}

          {/* Evaluation */}
          {mission.evaluation && (
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500">Évaluation</label>
              <div className="p-3 bg-amber-50 border border-amber-100 rounded-lg">
                <p className="text-sm text-gray-800">{mission.evaluation}</p>
              </div>
            </div>
          )}

          {/* Points forts et faibles */}
          {(mission.pointfort || mission.pointfaible) && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {mission.pointfort && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-500">Points forts</label>
                  <div className="p-3 bg-green-50 border border-green-100 rounded-lg">
                    <p className="text-sm text-gray-800">{mission.pointfort}</p>
                  </div>
                </div>
              )}
              {mission.pointfaible && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-500">Points faibles</label>
                  <div className="p-3 bg-red-50 border border-red-100 rounded-lg">
                    <p className="text-sm text-gray-800">{mission.pointfaible}</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Additional Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500">Planifiée initialement</label>
              <p className="text-sm">{mission.planifieInitialement ? 'Oui' : 'Non'}</p>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-500">Date de création</label>
              <p className="text-sm">
                {mission.createdAt ? new Date(mission.createdAt).toLocaleDateString('fr-FR') : 'Non disponible'}
              </p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default MissionDetailsModal;
