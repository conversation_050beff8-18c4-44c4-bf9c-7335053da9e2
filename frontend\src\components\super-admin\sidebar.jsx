import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import {
  BadgeCheck,
  LayoutDashboard,
  Zap,
  AlertTriangle,
  ChevronDown,
  Plus,
  List,
  Users,
  Building2,
  Shield,
  LineChart,
  FileType,
  GitBranch,
  FolderTree,
  ChevronLeft,
  ChevronRight,
  ChevronsUpDown,
  Laptop,
  Settings,
  UserCog,
  UserPlus,
  Key,
  ClipboardList,
  FileText,
  Sparkles,
  ClipboardCheck,
  Home,
  Activity,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import logo2 from "../../assets/whitelogovitalis.png";
import bpsIcon from '@/assets/BPS.png';
import orgIcon from '@/assets/org.png';
import operationIcon from '@/assets/operation.png';
import entityIcon from '@/assets/entity.png';

const superAdminMenuItems = [
  {
    id: "home",
    label: "Home",
    path: "/super-admin/welcome",
    icon: <Home className="h-5 w-5" />,
  },
  {
    id: "dashboard",
    label: "Dashboard",
    path: "/super-admin/dashboard",
    icon: <LayoutDashboard className="h-5 w-5" />,
  },
  {
    id: "logs",
    label: "Activity Logs",
    path: "/super-admin/logs",
    icon: <ClipboardList className="h-5 w-5" />,
  },
  {
    id: "users",
    label: "User Management",
    icon: <Users className="h-5 w-5" />,
    submenu: [
      {
        id: "users-list",
        label: "Users List",
        path: "/super-admin/users",
        icon: <UserCog className="h-4 w-4" />,
      },
      {
        id: "add-user",
        label: "Add User",
        path: "/super-admin/users/add",
        icon: <UserPlus className="h-4 w-4" />,
      },
      {
        id: "roles",
        label: "Role Management",
        path: "/super-admin/roles",
        icon: <Key className="h-4 w-4" />,
      },
    ],
  },
  {
    id: "incidents",
    label: "Incidents",
    icon: <Zap className="h-5 w-5" />,
    submenu: [
      {
        id: "incidents-list",
        label: "Incidents List",
        path: "/super-admin/incident",
        icon: <List className="h-4 w-4" />,
      },
      {
        id: "create-incident",
        label: "Create Incident",
        path: "/super-admin/incident/add",
        icon: <Plus className="h-4 w-4" />,
      },
      {
        id: "ai-incident",
        label: "AI Assistant",
        path: "/super-admin/incident/ai",
        icon: <Sparkles className="h-4 w-4 text-purple-500 animate-pulse" />,
        className: "relative group",
        labelClassName: "bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-blue-500 font-medium",
      },
      {
        id: "incident-types",
        label: "Incident Types",
        path: "/super-admin/incident-types",
        icon: <FileType className="h-4 w-4" />,
      },
    ],
  },
  {
    id: "risks",
    label: "Risks",
    icon: <AlertTriangle className="h-5 w-5" />,
    submenu: [
      {
        id: "risks-list",
        label: "Risks List",
        path: "/super-admin/risks",
        icon: <List className="h-4 w-4" />,
      },
      {
        id: "risk-types",
        label: "Risk Types",
        path: "/super-admin/risk-types",
        icon: <FileType className="h-4 w-4" />,
      },
    ],
  },
  {
    id: "reports",
    label: "Reports",
    path: "/super-admin/reports",
    icon: <FileText className="h-5 w-5" />,
  },
  // Rapports section removed
  {
    id: "processes",
    label: "Processes",
    icon: <img src={bpsIcon} className="sidebar-icon" alt="processes" />,
    submenu: [
      {
        id: "tree-view",
        label: "Tree View",
        path: "/super-admin/processes/tree-view",
        icon: <FolderTree className="h-4 w-4" />,
      },
      {
        id: "business-processes",
        label: "Business Processes",
        path: "/super-admin/processes/business-processes",
        icon: <img src={bpsIcon} className="sidebar-icon-sm" alt="business processes" />,
      },
      {
        id: "organizational-processes",
        label: "Org. Processes",
        path: "/super-admin/processes/organizational-processes",
        icon: <img src={orgIcon} className="sidebar-icon-sm" alt="organizational processes" />,
      },
      {
        id: "operations",
        label: "Operations",
        path: "/super-admin/processes/operations",
        icon: <img src={operationIcon} className="sidebar-icon-sm" alt="operations" />,
      },
    ],
  },
  {
    id: "data",
    label: "Data",
    icon: <Users className="h-5 w-5" />,
    submenu: [
      {
        id: "entities",
        label: "Entities",
        path: "/super-admin/data/entities",
        icon: <img src={entityIcon} className="sidebar-icon-sm" alt="entities" />,
      },
      {
        id: "controls",
        label: "Controls",
        path: "/super-admin/data/controls",
        icon: <Shield className="h-4 w-4" />,
      },
      {
        id: "campagnes",
        label: "Campagnes",
        path: "/super-admin/campagnes",
        icon: <Activity className="h-4 w-4" />,
      },
      {
        id: "control-types",
        label: "Control Types",
        path: "/super-admin/data/control-types",
        icon: <Shield className="h-4 w-4" />,
      },
      {
        id: "business-lines",
        label: "Business Lines",
        path: "/super-admin/data/business-lines",
        icon: <LineChart className="h-4 w-4" />,
      },
      {
        id: "applications",
        label: "Applications",
        path: "/super-admin/data/applications",
        icon: <Laptop className="h-4 w-4" />,
      },
      {
        id: "action-plans",
        label: "Action Plans",
        path: "/super-admin/data/action-plans",
        icon: <ClipboardCheck className="h-4 w-4" />,
      },
    ],
  },
  {
    id: "settings",
    label: "Settings",
    path: "/super-admin/settings",
    icon: <Settings className="h-5 w-5" />,
  },
];

function MenuItem({ item, isActive, onClick, isCollapsed }) {
  const [isOpen, setIsOpen] = React.useState(false);
  const hasSubmenu = item.submenu && item.submenu.length > 0;
  const location = useLocation();

  const isSubmenuActive = hasSubmenu && item.submenu.some(subItem =>
    location.pathname.startsWith(subItem.path)
  );

  if (hasSubmenu) {
    // When sidebar is collapsed, render a popover with submenu items
    if (isCollapsed) {
      return (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                "w-full text-white hover:bg-white hover:text-[#272D36]",
                isSubmenuActive && "bg-red-500 text-white hover:bg-red-500 hover:text-white",
                "justify-center px-2"
              )}
              title={item.label}
            >
              {item.icon}
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="bg-[#242A33] border-[#3A424E] p-2 w-48"
            side="right"
            align="start"
          >
            <div className="flex flex-col space-y-1">
              <div className="text-white font-medium px-2 py-1 border-b border-[#3A424E] mb-1">
                {item.label}
              </div>
              {item.submenu.map((subItem) => (
                <Button
                  key={subItem.id}
                  variant="ghost"
                  className={cn(
                    "w-full justify-start text-white hover:bg-white hover:text-[#272D36]",
                    isActive(subItem.path) && "bg-red-500 text-white hover:bg-red-500 hover:text-white"
                  )}
                  onClick={() => onClick(subItem.path)}
                >
                  {subItem.icon && <span className="mr-2">{subItem.icon}</span>}
                  {subItem.label}
                </Button>
              ))}
            </div>
          </PopoverContent>
        </Popover>
      );
    }

    // When sidebar is expanded, use the Collapsible component
    return (
      <Collapsible
        open={isOpen || isSubmenuActive}
        onOpenChange={setIsOpen}
      >
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-between text-white hover:bg-white hover:text-[#272D36]",
              isSubmenuActive && "text-white font-medium"
            )}
          >
            <div className="flex items-center gap-2">
              {item.icon}
              <span>{item.label}</span>
            </div>
            <ChevronDown
              className={cn("h-4 w-4 transition-transform", {
                "transform rotate-180": isOpen || isSubmenuActive,
              })}
            />
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="transition-all duration-200 ease-in-out">
          <div className="pt-1 pb-2">
            {item.submenu.map((subItem) => (
              <Button
                key={subItem.id}
                variant="ghost"
                className={cn(
                  "w-full pl-9 justify-start text-white hover:bg-white hover:text-[#272D36]",
                  isActive(subItem.path) && "bg-red-500 text-white hover:bg-red-500 hover:text-white"
                )}
                onClick={() => onClick(subItem.path)}
              >
                {subItem.icon && <span className="mr-2">{subItem.icon}</span>}
                {subItem.label}
              </Button>
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    );
  }

  return (
    <Button
      variant="ghost"
      className={cn(
        "w-full text-white hover:bg-white hover:text-[#272D36]",
        isActive(item.path) && "bg-red-500 text-white hover:bg-red-500 hover:text-white",
        isCollapsed ? "justify-center px-2" : "justify-start"
      )}
      onClick={() => onClick(item.path)}
      title={isCollapsed ? item.label : undefined}
    >
      {item.icon}
      {!isCollapsed && <span className="ml-2">{item.label}</span>}
    </Button>
  );
}

function SuperAdminSideBar({ open, setOpen, onCollapseChange }) {
  const navigate = useNavigate();
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Load collapsed state from localStorage on component mount
  useEffect(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState !== null) {
      setIsCollapsed(savedState === 'true');
    }
  }, []);

  // Save collapsed state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', isCollapsed);
    // Notify parent component about the collapse state change
    if (onCollapseChange) {
      onCollapseChange(isCollapsed);
    }
  }, [isCollapsed, onCollapseChange]);

  const toggleCollapse = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);

    // Update localStorage directly for immediate effect
    localStorage.setItem('sidebarCollapsed', newState);

    // Dispatch custom event to notify other components
    window.dispatchEvent(new Event('sidebarStateChanged'));

    // Notify parent component directly if callback exists
    if (onCollapseChange) {
      onCollapseChange(newState);
    }
  };

  const isActive = (path) => {
    // Exact match for dashboard
    if (path === "/super-admin/dashboard") {
      return location.pathname === path;
    }

    // Special case for incident list - only active when on list or editing an incident
    if (path === "/super-admin/incident") {
      return location.pathname === path ||
             location.pathname.startsWith("/super-admin/incident/edit/");
    }

    // Special case for create incident - only active when on create page
    if (path === "/super-admin/incident/add") {
      return location.pathname === path;
    }

    // Special case for risks list - only active when on list or editing a risk
    if (path === "/super-admin/risks") {
      return location.pathname === path ||
             location.pathname.startsWith("/super-admin/risks/edit/");
    }

    // Special case for create risk - only active when on create page
    if (path === "/super-admin/risks/add") {
      return location.pathname === path;
    }

    // For other pages, check if the current path starts with the menu path
    return location.pathname.startsWith(path);
  };

  return (
    <>
      {/* Mobile Sidebar */}
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetContent
          side="left"
          className="w-64 bg-[#242A33] text-white border-[#3A424E] p-0 overflow-hidden flex flex-col"
        >
          <SidebarContent
            navigate={navigate}
            isActive={isActive}
            setOpen={setOpen}
            isCollapsed={false} // Always expanded in mobile view
          />
        </SheetContent>
      </Sheet>

      {/* Desktop Sidebar */}
      <aside
        className={cn(
          "hidden lg:flex flex-col border-r border-[#3A424E] bg-[#242A33] h-screen fixed transition-all duration-300 overflow-visible",
          isCollapsed ? "w-16" : "w-64"
        )}
        style={{ zIndex: 30 }}
      >
        <SidebarContent
          navigate={navigate}
          isActive={isActive}
          isCollapsed={isCollapsed}
          toggleCollapse={toggleCollapse}
        />
      </aside>
    </>
  );
}

function SidebarContent({ navigate, isActive, setOpen, isCollapsed, toggleCollapse }) {
  const handleNavigate = (path) => {
    navigate(path);
    setOpen?.(false);
  };

  // Get user data from Redux store
  const user = useSelector((state) => state.auth.user);

  return (
    <div className="flex flex-col h-full relative">
      {/* Logo and Collapse Toggle */}
      <div className="p-3 border-b border-[#3A424E] flex justify-between items-center h-[60px]">
        <div
          onClick={() => handleNavigate("/super-admin/dashboard")}
          className="cursor-pointer flex items-center justify-center w-full"
        >
          <img src={logo2} alt="Logo" className="h-auto w-auto max-h-10" />
        </div>
        {toggleCollapse && (
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleCollapse}
            className={cn(
              "absolute text-white rounded-full shadow-md",
              "w-8 h-8 flex items-center justify-center",
              "bg-[#1E2329] hover:bg-[#2A3038] border border-[#3A424E]",
              "transition-all duration-200 hover:scale-110",
              "right-[-15px]"
            )}
            style={{
              transform: "translateX(75%)",
              zIndex: 999, // Ensure it's above everything
              top: "calc(30px - 1rem)" // Center vertically
            }}
          >
            {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        )}
      </div>

      {/* Menu Items */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className={cn("space-y-1", isCollapsed ? "px-1 py-4" : "px-3 py-4")}>
            {superAdminMenuItems.map((item) => (
              <MenuItem
                key={item.id}
                item={item}
                isActive={isActive}
                onClick={handleNavigate}
                isCollapsed={isCollapsed}
              />
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Profile Section */}
      <div className="border-t border-[#3A424E] h-[72px] p-2 flex items-center justify-center">
        {/* When collapsed, use a simpler button structure */}
        {isCollapsed ? (
          <div
            className="w-10 h-10 flex items-center justify-center cursor-pointer"
            onClick={() => handleNavigate("/super-admin/profile")}
            title="Profile"
          >
            <div className="w-8 h-8 rounded-full bg-gray-800 border border-gray-600 flex items-center justify-center">
              <span className="text-white font-medium text-sm">
                {user?.username?.charAt(0)?.toUpperCase() || 'S'}
              </span>
            </div>
          </div>
        ) : (
          <Button
            variant="ghost"
            className="flex items-center text-white hover:bg-gray-700 hover:text-white p-3 rounded-lg justify-start gap-3 w-full h-[56px]"
            onClick={() => handleNavigate("/super-admin/profile")}
            title="Profile"
          >
            <div className="w-8 h-8 rounded-full bg-gray-800 border border-gray-600 flex items-center justify-center mr-2">
              <span className="text-white font-medium text-sm">
                {user?.username?.charAt(0)?.toUpperCase() || 'S'}
              </span>
            </div>
            <div className="flex flex-col items-start">
              <span className="text-sm font-medium text-white">{user?.username || 'Super Admin'}</span>
              <span className="text-xs text-gray-400">{user?.email || '<EMAIL>'}</span>
            </div>
            <ChevronsUpDown className="h-5 w-5 ml-auto" />
          </Button>
        )}
      </div>
    </div>
  );
}

export default SuperAdminSideBar;
