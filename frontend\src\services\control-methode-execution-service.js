import { getApiBaseUrl } from '@/utils/api-config';

const API_BASE_URL = getApiBaseUrl();

// Get execution method for a specific control
export const getControlMethodeExecution = async (controlId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/controls/${controlId}/methode-execution`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching control execution method:', error);
    throw error;
  }
};

// Create or update execution method for a control
export const createOrUpdateControlMethodeExecution = async (controlId, executionData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/controls/${controlId}/methode-execution`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(executionData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error creating/updating control execution method:', error);
    throw error;
  }
};

// Update execution method for a control
export const updateControlMethodeExecution = async (controlId, executionData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/controls/${controlId}/methode-execution`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(executionData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error updating control execution method:', error);
    throw error;
  }
};

// Delete execution method for a control
export const deleteControlMethodeExecution = async (controlId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/controls/${controlId}/methode-execution`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error deleting control execution method:', error);
    throw error;
  }
};
