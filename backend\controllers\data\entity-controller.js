const { Entity } = require('../../models');

// Get all entities
const getAllEntities = async (req, res) => {
  try {
    const entities = await Entity.findAll();
    res.json({
      success: true,
      data: entities
    });
  } catch (error) {
    console.error('Error fetching entities:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch entities'
    });
  }
};

// Create new entity
const createEntity = async (req, res) => {
  try {
    const {
      entityID,
      name,
      type,
      localCurrency,
      code,
      comment,
      internalExternal,
      parentEntityID
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name'].filter(field => !req.body[field])
      });
    }

    const entity = await Entity.create({
      entityID: entityID || `ENT_${Date.now()}`,
      name,
      type: type || null,
      localCurrency: localCurrency || null,
      code: code || null,
      comment: comment || null,
      internalExternal: internalExternal || null,
      parentEntityID: parentEntityID || null
    });

    return res.status(201).json({
      success: true,
      message: 'Entity created successfully',
      data: entity
    });
  } catch (error) {
    console.error('Error creating entity:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create entity'
    });
  }
};

// Get entity by ID
const getEntityById = async (req, res) => {
  try {
    const { id } = req.params;
    const entity = await Entity.findByPk(id);
    
    if (!entity) {
      return res.status(404).json({
        success: false,
        message: 'Entity not found'
      });
    }
    
    res.json({
      success: true,
      data: entity
    });
  } catch (error) {
    console.error('Error fetching entity:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch entity'
    });
  }
};

// Update entity
const updateEntity = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      type,
      localCurrency,
      code,
      comment,
      internalExternal,
      parentEntityID
    } = req.body;

    const entity = await Entity.findByPk(id);
    
    if (!entity) {
      return res.status(404).json({
        success: false,
        message: 'Entity not found'
      });
    }

    // Update fields
    await entity.update({
      name: name || entity.name,
      type: type !== undefined ? type : entity.type,
      localCurrency: localCurrency !== undefined ? localCurrency : entity.localCurrency,
      code: code !== undefined ? code : entity.code,
      comment: comment !== undefined ? comment : entity.comment,
      internalExternal: internalExternal !== undefined ? internalExternal : entity.internalExternal,
      parentEntityID: parentEntityID !== undefined ? parentEntityID : entity.parentEntityID
    });

    res.json({
      success: true,
      message: 'Entity updated successfully',
      data: entity
    });
  } catch (error) {
    console.error('Error updating entity:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update entity'
    });
  }
};

// Delete entity
const deleteEntity = async (req, res) => {
  try {
    const { id } = req.params;
    const entity = await Entity.findByPk(id);
    
    if (!entity) {
      return res.status(404).json({
        success: false,
        message: 'Entity not found'
      });
    }
    
    await entity.destroy();
    
    res.json({
      success: true,
      message: 'Entity deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting entity:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete entity'
    });
  }
};

module.exports = {
  getAllEntities,
  createEntity,
  getEntityById,
  updateEntity,
  deleteEntity
};
