module.exports = (sequelize, DataTypes) => {
  const ActionPlanAttachment = sequelize.define('ActionPlanAttachment', {
    attachmentID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    filePath: {
      type: DataTypes.STRING,
      allowNull: true
    },
    url: {
      type: DataTypes.STRING,
      allowNull: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    uploadDate: {
      type: DataTypes.DATE,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('business-document', 'external-reference'),
      allowNull: false
    },
    actionPlanID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'action_plan',
        key: 'actionPlanID'
      }
    }
  }, {
    tableName: 'ActionPlanAttachment',
    timestamps: false
  });

  ActionPlanAttachment.associate = (models) => {
    ActionPlanAttachment.belongsTo(models.ActionPlan, {
      foreignKey: 'actionPlanID',
      targetKey: 'actionPlanID',
      as: 'actionPlan'
    });
  };

  return ActionPlanAttachment;
};