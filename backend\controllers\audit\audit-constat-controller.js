const db = require('../../models');
const AuditConstat = db.AuditConstat;
const AuditActivity = db.AuditActivity;
const User = db.User;
const EquipeIntervenante = db.EquipeIntervenante;
const { v4: uuidv4 } = require('uuid');
const { sendEmailDirect } = require('../email-controller');

// Get all audit constats - OPTIMIZED VERSION
const getAllAuditConstats = async (req, res) => {
  try {
    // Add pagination support
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    // Check if this is for statistics only (welcome page)
    const statsOnly = req.query.statsOnly === 'true';

    if (statsOnly) {
      // For statistics, only return counts grouped by impact
      const statsQuery = `
        SELECT
          impact,
          COUNT(*) as count
        FROM "AuditConstats"
        GROUP BY impact
      `;

      const stats = await db.sequelize.query(statsQuery, {
        type: db.sequelize.QueryTypes.SELECT
      });

      return res.status(200).json({
        success: true,
        data: stats,
        isStats: true
      });
    }

    // For full data, use optimized query
    const auditConstats = await AuditConstat.findAndCountAll({
      attributes: [
        'id', 'name', 'description', 'impact', 'auditActivityID',
         'createdAt', 'updatedAt'
      ],
      include: [
        {
          model: AuditActivity,
          as: 'auditActivity',
          attributes: ['id', 'name'],
          required: false
        },
        {
          model: db.EquipeIntervenante,
          as: 'equipeIntervenantes',
          required: false,
          include: [
            { model: db.User, as: 'user', attributes: ['id', 'username', 'email'] }
          ]
        }
      ],
      limit,
      offset,
      order: [['createdAt', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      data: auditConstats.rows,
      pagination: {
        total: auditConstats.count,
        page,
        limit,
        totalPages: Math.ceil(auditConstats.count / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching audit constats:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit constats',
      error: error.message
    });
  }
};

// Get audit constats by activity ID
const getConstatsByActivityId = async (req, res) => {
  try {
    const { activityId } = req.params;
    
    const auditConstats = await AuditConstat.findAll({
      where: { auditActivityID: activityId },
      include: [
        {
          model: db.EquipeIntervenante,
          as: 'equipeIntervenantes',
          required: false,
          include: [
            { model: db.User, as: 'user', attributes: ['id', 'username', 'email'] }
          ]
        }
      ]
    });
    
    return res.status(200).json({
      success: true,
      data: auditConstats
    });
  } catch (error) {
    console.error('Error fetching audit constats by activity ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit constats',
      error: error.message
    });
  }
};

// Create a new audit constat
const createAuditConstat = async (req, res) => {
  try {
    const {
      name,
      type,
      impact,
      description,
      causes,
      auditActivityID
    } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Name is required for audit constat'
      });
    }
    
    if (!auditActivityID) {
      return res.status(400).json({
        success: false,
        message: 'Audit activity ID is required'
      });
    }
    
    // Check if audit activity exists
    const auditActivity = await AuditActivity.findByPk(auditActivityID);
    if (!auditActivity) {
      return res.status(404).json({
        success: false,
        message: 'Audit activity not found'
      });
    }
    
    // Validate type is one of the allowed values
    const allowedTypes = ['Point Fort', 'Point Faible'];
    if (type && !allowedTypes.includes(type)) {
      return res.status(400).json({
        success: false,
        message: `Type must be one of: ${allowedTypes.join(', ')}`
      });
    }
    
    // Create the audit constat
    const auditConstat = await AuditConstat.create({
      id: `AC_${uuidv4().substring(0, 8)}`,
      name,
      type,
      impact,
      description,
      causes: causes || null,
      auditActivityID
    });

    // Add the missing success response
    return res.status(201).json({
      success: true,
      message: 'Audit constat created successfully',
      data: auditConstat
    });
  } catch (error) {
    console.error('Error creating audit constat:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create audit constat',
      error: error.message
    });
  }
};

// Get audit constat by ID
const getAuditConstatById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const auditConstat = await AuditConstat.findByPk(id, {
      include: [
        {
          model: AuditActivity,
          as: 'auditActivity',
          attributes: ['id', 'name']
        },
        {
          model: db.Risk,
          as: 'risks'
        },
        {
          model: db.EquipeIntervenante,
          as: 'equipeIntervenantes',
          required: false,
          include: [
            { model: db.User, as: 'user', attributes: ['id', 'username', 'email'] }
          ]
        }
      ]
    });
    
    if (!auditConstat) {
      return res.status(404).json({
        success: false,
        message: 'Audit constat not found'
      });
    }
    
    return res.status(200).json({
      success: true,
      data: auditConstat
    });
  } catch (error) {
    console.error('Error fetching audit constat:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit constat',
      error: error.message
    });
  }
};

// Update audit constat
const updateAuditConstat = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      type,
      impact,
      description,
      causes,
      auditActivityID
    } = req.body;
    
    const auditConstat = await AuditConstat.findByPk(id);
    
    if (!auditConstat) {
      return res.status(404).json({
        success: false,
        message: 'Audit constat not found'
      });
    }
    
    // If auditActivityID is being changed, check if the new activity exists
    if (auditActivityID && auditActivityID !== auditConstat.auditActivityID) {
      const auditActivity = await AuditActivity.findByPk(auditActivityID);
      if (!auditActivity) {
        return res.status(404).json({
          success: false,
          message: 'New audit activity not found'
        });
      }
    }
    
    await auditConstat.update({
      name: name || auditConstat.name,
      type: type || auditConstat.type,
      impact: impact !== undefined ? impact : auditConstat.impact,
      description: description !== undefined ? description : auditConstat.description,
      causes: causes !== undefined ? causes : auditConstat.causes,
      auditActivityID: auditActivityID || auditConstat.auditActivityID
    });
    
    return res.status(200).json({
      success: true,
      message: 'Audit constat updated successfully',
      data: auditConstat
    });
  } catch (error) {
    console.error('Error updating audit constat:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update audit constat',
      error: error.message
    });
  }
};

// Delete audit constat
const deleteAuditConstat = async (req, res) => {
  try {
    const { id } = req.params;
    
    const auditConstat = await AuditConstat.findByPk(id);
    
    if (!auditConstat) {
      return res.status(404).json({
        success: false,
        message: 'Audit constat not found'
      });
    }
    
    await auditConstat.destroy();
    
    return res.status(200).json({
      success: true,
      message: 'Audit constat deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting audit constat:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete audit constat',
      error: error.message
    });
  }
};

// Add risks to a constat (many-to-many)
const addRisksToConstat = async (req, res) => {
  try {
    const { constatId } = req.params;
    const { riskIDs } = req.body;

    // Allow empty arrays to unlink all risks
    if (!Array.isArray(riskIDs)) {
      return res.status(400).json({ success: false, message: 'riskIDs must be an array' });
    }

    const constat = await db.AuditConstat.findByPk(constatId);
    if (!constat) {
      return res.status(404).json({ success: false, message: 'Constat not found' });
    }

    // Set (replace) the risks for this constat - empty array will unlink all
    await constat.setRisks(riskIDs);
    const updatedConstat = await db.AuditConstat.findByPk(constatId, {
      include: [{ model: db.Risk, as: 'risks' }]
    });

    const message = riskIDs.length === 0 ? 'All risks unlinked from constat successfully' : 'Risks linked to constat successfully';
    return res.status(200).json({ success: true, message, data: updatedConstat.risks });
  } catch (error) {
    console.error('Error linking risks to constat:', error);
    return res.status(500).json({ success: false, message: 'Failed to link risks to constat', error: error.message });
  }
};

module.exports = {
  getAllAuditConstats,
  getConstatsByActivityId,
  createAuditConstat,
  getAuditConstatById,
  updateAuditConstat,
  deleteAuditConstat,
  addRisksToConstat,
};



