const { AuditScope, AuditMission, Risk, Control, Entity, OrganizationalProcess, BusinessProcess, sequelize } = require('../../models');
const { v4: uuidv4 } = require('uuid');

// Get all audit scopes - OPTIMIZED VERSION
const getAllAuditScopes = async (req, res) => {
  try {
    // OPTIMIZED: Remove duplicate includes and use specific attributes
    const auditScopes = await AuditScope.findAll({
      attributes: [
        'id', 'auditMissionID', 'riskID', 'controlID', 'entityID',
        'organizationalProcessID', 'businessProcessID', 'incidentID',
        'createdAt', 'updatedAt'
      ],
      include: [
        {
          model: AuditMission,
          as: 'auditMission',
          attributes: ['id', 'name', 'etat'],
          required: false
        },
        // Only include many-to-many relationships, not single ones to avoid duplicates
        {
          model: Risk,
          as: 'risks',
          attributes: ['riskID', 'name', 'code'],
          through: { attributes: [] }, // Exclude junction table attributes
          required: false
        },
        {
          model: Control,
          as: 'controls',
          attributes: ['controlID', 'name', 'code'],
          through: { attributes: [] },
          required: false
        },
        {
          model: Entity,
          as: 'entities',
          attributes: ['entityID', 'name', 'code'],
          through: { attributes: [] },
          required: false
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      count: auditScopes.length,
      data: auditScopes
    });
  } catch (error) {
    console.error('Error fetching audit scopes:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit scopes',
      error: error.message
    });
  }
};

// Get audit scopes by mission ID
const getAuditScopesByMission = async (req, res) => {
  try {
    const { missionId } = req.params;
    
    const auditScopes = await AuditScope.findAll({
      where: { auditMissionID: missionId },
      include: [
        { model: AuditMission, as: 'auditMission' },
        { model: Risk, as: 'risk' },
        { model: Control, as: 'control' },
        { model: Entity, as: 'entity' },
        { model: OrganizationalProcess, as: 'organizationalProcess' },
        { model: BusinessProcess, as: 'businessProcess' },
        ...(sequelize.models.Incident ? [{ model: sequelize.models.Incident, as: 'incident' }] : []),
        // Include many-to-many relationships
        { model: Risk, as: 'risks' },
        { model: Control, as: 'controls' },
        { model: Entity, as: 'entities' },
        { model: OrganizationalProcess, as: 'processes' },
        { model: BusinessProcess, as: 'businessProcesses' },
        // Check if Incident model exists before including it
        ...(sequelize.models.Incident ? [{ model: sequelize.models.Incident, as: 'incidents' }] : [])
      ]
    });

    // If no scopes exist for this mission, create one
    if (auditScopes.length === 0) {
      const newAuditScope = await AuditScope.create({
        id: `AS_${uuidv4().substring(0, 8)}`,
        name: `Scope for Mission ${missionId}`,
        auditMissionID: missionId
      });

      // Fetch the newly created scope with its relationships
      const createdScope = await AuditScope.findByPk(newAuditScope.id, {
        include: [
          { model: AuditMission, as: 'auditMission' },
          { model: Risk, as: 'risk' },
          { model: Control, as: 'control' },
          { model: Entity, as: 'entity' },
          { model: OrganizationalProcess, as: 'organizationalProcess' },
          { model: BusinessProcess, as: 'businessProcess' },
          { model: Risk, as: 'risks' },
          { model: Control, as: 'controls' },
          { model: Entity, as: 'entities' },
          { model: OrganizationalProcess, as: 'processes' },
          { model: BusinessProcess, as: 'businessProcesses' }
        ]
      });

      return res.status(200).json({
        success: true,
        count: 1,
        data: [createdScope]
      });
    }

    return res.status(200).json({
      success: true,
      count: auditScopes.length,
      data: auditScopes
    });
  } catch (error) {
    console.error('Error fetching audit scopes by mission:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit scopes',
      error: error.message
    });
  }
};

// Get a single audit scope
const getAuditScope = async (req, res) => {
  try {
    const { id } = req.params;
    
    const auditScope = await AuditScope.findByPk(id, {
      include: [
        { model: AuditMission, as: 'auditMission' },
        { model: Risk, as: 'risk' },
        { model: Control, as: 'control' },
        { model: Entity, as: 'entity' },
        { model: OrganizationalProcess, as: 'organizationalProcess' },
        { model: BusinessProcess, as: 'businessProcess' },
        // Check if Incident model exists before including it
        ...(sequelize.models.Incident ? [{ model: sequelize.models.Incident, as: 'incident' }] : []),
        { model: Risk, as: 'risks' },
        { model: Control, as: 'controls' },
        { model: Entity, as: 'entities' },
        { model: OrganizationalProcess, as: 'processes' },
        { model: BusinessProcess, as: 'businessProcesses' },
        // Check if Incident model exists before including it
        ...(sequelize.models.Incident ? [{ model: sequelize.models.Incident, as: 'incidents' }] : [])
      ]
    });
    
    if (!auditScope) {
      return res.status(404).json({
        success: false,
        message: 'Audit scope not found'
      });
    }

    return res.status(200).json({
      success: true,
      data: auditScope
    });
  } catch (error) {
    console.error('Error fetching audit scope:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit scope',
      error: error.message
    });
  }
};

// Create a new audit scope
const createAuditScope = async (req, res) => {
  try {
    const {
      name,
      auditMissionID,
      riskID,
      controlID,
      entityID,
      organizationalProcessID,
      incidentID,
      businessProcessID,
      // New multi-select fields
      riskIDs,
      controlIDs,
      entityIDs,
      organizationalProcessIDs,
      incidentIDs,
      businessProcessIDs
    } = req.body;
    
    // Validate required fields
    if (!auditMissionID) {
      return res.status(400).json({
        success: false,
        message: 'Audit mission ID is required'
      });
    }
    
    // Check if audit mission exists
    const auditMission = await AuditMission.findByPk(auditMissionID);
    if (!auditMission) {
      return res.status(404).json({
        success: false,
        message: 'Audit mission not found'
      });
    }
    
    // Create the audit scope
    const auditScope = await AuditScope.create({
      id: `AS_${uuidv4().substring(0, 8)}`, // Generate a unique ID with prefix
      name,
      auditMissionID,
      riskID,
      controlID,
      entityID,
      organizationalProcessID,
      incidentID,
      businessProcessID
    });

    // Handle many-to-many relationships if provided
    if (riskIDs && Array.isArray(riskIDs) && riskIDs.length > 0) {
      // Get unique risk IDs to avoid duplicates
      const uniqueRiskIDs = [...new Set(riskIDs)];
      const risks = await Risk.findAll({
        where: { riskID: uniqueRiskIDs }
      });
      if (risks.length > 0) {
        await auditScope.setRisks(risks);
      }
    }

    if (controlIDs && Array.isArray(controlIDs) && controlIDs.length > 0) {
      // Get unique control IDs to avoid duplicates
      const uniqueControlIDs = [...new Set(controlIDs)];
      const controls = await Control.findAll({
        where: { controlID: uniqueControlIDs }
      });
      if (controls.length > 0) {
        await auditScope.setControls(controls);
      }
    }

    if (entityIDs && Array.isArray(entityIDs) && entityIDs.length > 0) {
      // Get unique entity IDs to avoid duplicates
      const uniqueEntityIDs = [...new Set(entityIDs)];
      const entities = await Entity.findAll({
        where: { entityID: uniqueEntityIDs }
      });
      if (entities.length > 0) {
        await auditScope.setEntities(entities);
      }
    }

    if (organizationalProcessIDs && Array.isArray(organizationalProcessIDs) && organizationalProcessIDs.length > 0) {
      // Get unique process IDs to avoid duplicates
      const uniqueProcessIDs = [...new Set(organizationalProcessIDs)];
      const processes = await OrganizationalProcess.findAll({
        where: { organizationalProcessID: uniqueProcessIDs }
      });
      if (processes.length > 0) {
        await auditScope.setProcesses(processes);
      }
    }

    // Handle incident IDs if provided and Incident model exists
    if (incidentIDs && Array.isArray(incidentIDs) && incidentIDs.length > 0 && sequelize.models.Incident) {
      // Get unique incident IDs to avoid duplicates
      const uniqueIncidentIDs = [...new Set(incidentIDs)];
      const incidents = await sequelize.models.Incident.findAll({
        where: { incidentID: uniqueIncidentIDs }
      });
      if (incidents.length > 0) {
        await auditScope.setIncidents(incidents);
      }
    }

    // Handle business process IDs if provided
    if (businessProcessIDs && Array.isArray(businessProcessIDs) && businessProcessIDs.length > 0) {
      // Get unique business process IDs to avoid duplicates
      const uniqueBusinessProcessIDs = [...new Set(businessProcessIDs)];
      const businessProcesses = await BusinessProcess.findAll({
        where: { businessProcessID: uniqueBusinessProcessIDs }
      });
      if (businessProcesses.length > 0) {
        await auditScope.setBusinessProcesses(businessProcesses);
      }
    }

    // Fetch the created audit scope with its relationships
    const createdAuditScope = await AuditScope.findByPk(auditScope.id, {
      include: [
        { model: AuditMission, as: 'auditMission' },
        { model: Risk, as: 'risk' },
        { model: Control, as: 'control' },
        { model: Entity, as: 'entity' },
        { model: OrganizationalProcess, as: 'organizationalProcess' },
        { model: BusinessProcess, as: 'businessProcess' },
        // Check if Incident model exists before including it
        ...(sequelize.models.Incident ? [{ model: sequelize.models.Incident, as: 'incident' }] : []),
        { model: Risk, as: 'risks' },
        { model: Control, as: 'controls' },
        { model: Entity, as: 'entities' },
        { model: OrganizationalProcess, as: 'processes' },
        { model: BusinessProcess, as: 'businessProcesses' },
        // Check if Incident model exists before including it
        ...(sequelize.models.Incident ? [{ model: sequelize.models.Incident, as: 'incidents' }] : [])
      ]
    });

    return res.status(201).json({
      success: true,
      message: 'Audit scope created successfully',
      data: createdAuditScope
    });
  } catch (error) {
    console.error('Error creating audit scope:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create audit scope',
      error: error.message
    });
  }
};

// Update an audit scope
const updateAuditScope = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      auditMissionID,
      riskID,
      controlID,
      entityID,
      organizationalProcessID,
      incidentID,
      businessProcessID,
      // Add multi-select fields for update
      riskIDs,
      controlIDs,
      entityIDs,
      organizationalProcessIDs,
      incidentIDs,
      businessProcessIDs
    } = req.body;
    
    const auditScope = await AuditScope.findByPk(id);
    
    if (!auditScope) {
      return res.status(404).json({
        success: false,
        message: 'Audit scope not found'
      });
    }
    
    // If auditMissionID is being changed, check if the new mission exists
    if (auditMissionID && auditMissionID !== auditScope.auditMissionID) {
      const auditMission = await AuditMission.findByPk(auditMissionID);
      if (!auditMission) {
        return res.status(404).json({
          success: false,
          message: 'New audit mission not found'
        });
      }
    }
    
    // Update the audit scope
    await auditScope.update({
      name: name || auditScope.name,
      auditMissionID: auditMissionID || auditScope.auditMissionID,
      riskID: riskID !== undefined ? riskID : auditScope.riskID,
      controlID: controlID !== undefined ? controlID : auditScope.controlID,
      entityID: entityID !== undefined ? entityID : auditScope.entityID,
      organizationalProcessID: organizationalProcessID !== undefined ? organizationalProcessID : auditScope.organizationalProcessID,
      incidentID: incidentID !== undefined ? incidentID : auditScope.incidentID,
      businessProcessID: businessProcessID !== undefined ? businessProcessID : auditScope.businessProcessID
    });

    // Update many-to-many relationships if provided
    if (riskIDs && Array.isArray(riskIDs)) {
      const uniqueRiskIDs = [...new Set(riskIDs)];
      const risks = await Risk.findAll({
        where: { riskID: uniqueRiskIDs }
      });
      await auditScope.setRisks(risks);
    }

    if (controlIDs && Array.isArray(controlIDs)) {
      const uniqueControlIDs = [...new Set(controlIDs)];
      const controls = await Control.findAll({
        where: { controlID: uniqueControlIDs }
      });
      await auditScope.setControls(controls);
    }

    if (entityIDs && Array.isArray(entityIDs)) {
      const uniqueEntityIDs = [...new Set(entityIDs)];
      const entities = await Entity.findAll({
        where: { entityID: uniqueEntityIDs }
      });
      await auditScope.setEntities(entities);
    }

    if (organizationalProcessIDs && Array.isArray(organizationalProcessIDs)) {
      const uniqueProcessIDs = [...new Set(organizationalProcessIDs)];
      const processes = await OrganizationalProcess.findAll({
        where: { organizationalProcessID: uniqueProcessIDs }
      });
      if (processes.length > 0) {
        await auditScope.setProcesses(processes);
      }
    }

    if (incidentIDs && Array.isArray(incidentIDs) && incidentIDs.length > 0) {
      // Check if Incident model exists
      if (sequelize.models.Incident) {
        // Get unique incident IDs to avoid duplicates
        const uniqueIncidentIDs = [...new Set(incidentIDs)];
        const incidents = await sequelize.models.Incident.findAll({
          where: { incidentID: uniqueIncidentIDs }
        });
        if (incidents.length > 0) {
          await auditScope.setIncidents(incidents);
        }
      }
    }

    if (businessProcessIDs && Array.isArray(businessProcessIDs) && businessProcessIDs.length > 0) {
      // Get unique business process IDs to avoid duplicates
      const uniqueBusinessProcessIDs = [...new Set(businessProcessIDs)];
      const businessProcesses = await BusinessProcess.findAll({
        where: { businessProcessID: uniqueBusinessProcessIDs }
      });
      if (businessProcesses.length > 0) {
        await auditScope.setBusinessProcesses(businessProcesses);
      }
    }

    // Fetch the updated audit scope with its relationships
    const updatedAuditScope = await AuditScope.findByPk(id, {
      include: [
        { model: AuditMission, as: 'auditMission' },
        { model: Risk, as: 'risk' },
        { model: Control, as: 'control' },
        { model: Entity, as: 'entity' },
        { model: OrganizationalProcess, as: 'organizationalProcess' },
        { model: BusinessProcess, as: 'businessProcess' },
        // Check if Incident model exists before including it
        ...(sequelize.models.Incident ? [{ model: sequelize.models.Incident, as: 'incident' }] : []),
        { model: Risk, as: 'risks' },
        { model: Control, as: 'controls' },
        { model: Entity, as: 'entities' },
        { model: OrganizationalProcess, as: 'processes' },
        { model: BusinessProcess, as: 'businessProcesses' },
        // Check if Incident model exists before including it
        ...(sequelize.models.Incident ? [{ model: sequelize.models.Incident, as: 'incidents' }] : [])
      ]
    });

    return res.status(200).json({
      success: true,
      message: 'Audit scope updated successfully',
      data: updatedAuditScope
    });
  } catch (error) {
    console.error('Error updating audit scope:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update audit scope',
      error: error.message
    });
  }
};

// Delete an audit scope
const deleteAuditScope = async (req, res) => {
  try {
    const { id } = req.params;
    
    const auditScope = await AuditScope.findByPk(id);
    
    if (!auditScope) {
      return res.status(404).json({
        success: false,
        message: 'Audit scope not found'
      });
    }
    
    await auditScope.destroy();

    return res.status(200).json({
      success: true,
      message: 'Audit scope deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting audit scope:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete audit scope',
      error: error.message
    });
  }
};

module.exports = {
  getAllAuditScopes,
  getAuditScopesByMission,
  getAuditScope,
  createAuditScope,
  updateAuditScope,
  deleteAuditScope
};









