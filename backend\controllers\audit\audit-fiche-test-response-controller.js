const db = require('../../models');
const FicheTestResponse = db.FicheTestResponse;
const FicheDeTravail = db.FicheDeTravail;
const Question = db.Question;
const { v4: uuidv4 } = require('uuid');

// Get all responses for a fiche de travail
const getResponsesByFicheId = async (req, res) => {
  try {
    const { ficheDeTravailID } = req.params;
    
    const responses = await FicheTestResponse.findAll({
      where: { ficheDeTravailID },
      include: [
        {
          model: Question,
          as: 'question',
          attributes: ['id', 'question_text', 'input_type', 'options']
        }
      ],
      order: [
        ['sampleNumber', 'ASC'],
        ['questionID', 'ASC']
      ]
    });

    return res.status(200).json({ 
      success: true, 
      data: responses 
    });
  } catch (error) {
    console.error('Error fetching test responses:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch test responses',
      error: error.message
    });
  }
};

// Get response by ID
const getResponseById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const response = await FicheTestResponse.findByPk(id, {
      include: [
        {
          model: Question,
          as: 'question',
          attributes: ['id', 'question_text', 'input_type', 'options']
        }
      ]
    });

    if (!response) {
      return res.status(404).json({ 
        success: false, 
        message: 'Response not found' 
      });
    }

    return res.status(200).json({ 
      success: true, 
      data: response 
    });
  } catch (error) {
    console.error('Error fetching test response:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch test response',
      error: error.message
    });
  }
};

// Create or update a response (upsert)
const upsertResponse = async (req, res) => {
  try {
    const {
      ficheDeTravailID,
      questionID,
      sampleNumber,
      answer
    } = req.body;

    // Validate required fields
    if (!ficheDeTravailID) {
      return res.status(400).json({ 
        success: false, 
        message: 'Fiche de travail ID is required' 
      });
    }
    if (!questionID) {
      return res.status(400).json({ 
        success: false, 
        message: 'Question ID is required' 
      });
    }
    if (!sampleNumber || sampleNumber < 1 || sampleNumber > 99) {
      return res.status(400).json({ 
        success: false, 
        message: 'Sample number must be between 1 and 99' 
      });
    }

    // Validate that fiche de travail and question exist
    const [ficheDeTravail, question] = await Promise.all([
      FicheDeTravail.findByPk(ficheDeTravailID),
      Question.findByPk(questionID)
    ]);

    if (!ficheDeTravail) {
      return res.status(404).json({ 
        success: false, 
        message: 'Fiche de travail not found' 
      });
    }
    if (!question) {
      return res.status(404).json({ 
        success: false, 
        message: 'Question not found' 
      });
    }

    // Check if response already exists
    const existingResponse = await FicheTestResponse.findOne({
      where: {
        ficheDeTravailID,
        questionID,
        sampleNumber
      }
    });

    let response;
    if (existingResponse) {
      // Update existing response
      await existingResponse.update({ answer });
      response = existingResponse;
    } else {
      // Create new response
      response = await FicheTestResponse.create({
        id: `FTR_${uuidv4().substring(0, 8)}`,
        ficheDeTravailID,
        questionID,
        sampleNumber,
        answer
      });
    }

    // Fetch the response with question details
    const responseWithQuestion = await FicheTestResponse.findByPk(response.id, {
      include: [
        {
          model: Question,
          as: 'question',
          attributes: ['id', 'question_text', 'input_type', 'options']
        }
      ]
    });

    return res.status(200).json({
      success: true,
      message: existingResponse ? 'Response updated successfully' : 'Response created successfully',
      data: responseWithQuestion
    });
  } catch (error) {
    console.error('Error upserting test response:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to save test response',
      error: error.message
    });
  }
};

// Bulk upsert responses
const bulkUpsertResponses = async (req, res) => {
  try {
    const { ficheDeTravailID, responses } = req.body;

    if (!ficheDeTravailID) {
      return res.status(400).json({ 
        success: false, 
        message: 'Fiche de travail ID is required' 
      });
    }
    if (!Array.isArray(responses) || responses.length === 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'Responses array is required' 
      });
    }

    // Validate that fiche de travail exists
    const ficheDeTravail = await FicheDeTravail.findByPk(ficheDeTravailID);
    if (!ficheDeTravail) {
      return res.status(404).json({ 
        success: false, 
        message: 'Fiche de travail not found' 
      });
    }

    const results = [];
    const errors = [];

    for (const responseData of responses) {
      try {
        const { questionID, sampleNumber, answer } = responseData;

        if (!questionID || !sampleNumber || sampleNumber < 1 || sampleNumber > 99) {
          errors.push({
            questionID,
            sampleNumber,
            error: 'Invalid question ID or sample number'
          });
          continue;
        }

        // Check if question exists
        const question = await Question.findByPk(questionID);
        if (!question) {
          errors.push({
            questionID,
            sampleNumber,
            error: 'Question not found'
          });
          continue;
        }

        // Upsert response
        const [response, created] = await FicheTestResponse.upsert({
          id: `FTR_${uuidv4().substring(0, 8)}`,
          ficheDeTravailID,
          questionID,
          sampleNumber,
          answer
        }, {
          returning: true
        });

        results.push({
          questionID,
          sampleNumber,
          created,
          responseId: response.id
        });
      } catch (error) {
        errors.push({
          questionID: responseData.questionID,
          sampleNumber: responseData.sampleNumber,
          error: error.message
        });
      }
    }

    return res.status(200).json({
      success: true,
      message: `Processed ${results.length} responses successfully`,
      data: {
        results,
        errors: errors.length > 0 ? errors : undefined
      }
    });
  } catch (error) {
    console.error('Error bulk upserting test responses:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to bulk save test responses',
      error: error.message
    });
  }
};

// Delete a response
const deleteResponse = async (req, res) => {
  try {
    const { id } = req.params;
    
    const response = await FicheTestResponse.findByPk(id);
    if (!response) {
      return res.status(404).json({ 
        success: false, 
        message: 'Response not found' 
      });
    }

    await response.destroy();

    return res.status(200).json({
      success: true,
      message: 'Response deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting test response:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete test response',
      error: error.message
    });
  }
};

// Delete all responses for a fiche de travail
const deleteResponsesByFicheId = async (req, res) => {
  try {
    const { ficheDeTravailID } = req.params;
    
    const deletedCount = await FicheTestResponse.destroy({
      where: { ficheDeTravailID }
    });

    return res.status(200).json({
      success: true,
      message: `${deletedCount} responses deleted successfully`
    });
  } catch (error) {
    console.error('Error deleting test responses:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete test responses',
      error: error.message
    });
  }
};

module.exports = {
  getResponsesByFicheId,
  getResponseById,
  upsertResponse,
  bulkUpsertResponses,
  deleteResponse,
  deleteResponsesByFicheId
}; 