const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllBusinessLines,
  createBusinessLine,
  getBusinessLineById,
  updateBusinessLine,
  deleteBusinessLine
} = require('../../controllers/data/business-line-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all business lines
router.get('/', getAllBusinessLines);

// Create new business line
router.post('/', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), createBusinessLine);

// Get business line by ID
router.get('/:id', getBusinessLineById);

// Update business line
router.put('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), updateBusinessLine);

// Delete business line
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteBusinessLine);

module.exports = router;
