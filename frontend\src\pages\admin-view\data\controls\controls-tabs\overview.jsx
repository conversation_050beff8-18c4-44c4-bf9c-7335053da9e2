import { useOutletContext } from "react-router-dom";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  FileText,
  Tag,
  Link as LinkIcon,
  Shield,
  FileCheck,
  Briefcase,
  Building,
  Database,
  BarChart,
  Globe,
  CheckCircle,
  Clock,
  Search,
  Wrench,
  ClipboardList,
  DollarSign,
  BookOpen,
  GitBranch,
  Folder,
  Info,
  Package,
  AlertTriangle,
  Paperclip
} from "lucide-react";
import { ControlAttachmentsSection } from "@/components/attachments_grc/ControlAttachmentsSection";

function ControlsOverview() {
  const { control } = useOutletContext();
  const { t } = useTranslation();
  // Get reference data from Redux store
  const controlTypes = useSelector((state) => state.controlType.controlTypes);
  const risks = useSelector((state) => state.risk.risks);
  const actionPlans = useSelector((state) => state.actionPlan.actionPlans);
  const businessProcesses = useSelector((state) => state.businessProcess.businessProcesses);
  const organizationalProcesses = useSelector((state) => state.organizationalProcess.organizationalProcesses);
  const operations = useSelector((state) => state.operation.operations);
  const applications = useSelector((state) => state.application.applications);
  const entities = useSelector((state) => state.entity.entities);

  // Translation mappings for dropdown values
  const testingFrequencyMap = {
    "Quarterly": "Trimestriel",
    "Bi-Yearly": "Semestriel",
    "Yearly": "Annuel"
  };

  const sampleTypeMap = {
    "Command": "Commande",
    "Bill": "Facture",
    "Contract": "Contrat"
  };

  const testingMethodMap = {
    "Observation": "Observation",
    "Inquiry": "Enquête",
    "Inspection": "Inspection",
    "Re-Performance": "Re-exécution"
  };

  const controlExecutionMethodMap = {
    "Observation": "Observation",
    "Exhaustive": "Exhaustif",
    "Exception Control": "Contrôle d'Exception",
    "By Sample": "Par Échantillon",
    "Standard Control": "Contrôle Standard"
  };

  // Helper functions to display formatted values
  const formatValue = (value, defaultText = "None") => {
    return value !== undefined && value !== null && value !== "" ? value : defaultText;
  };

  const formatTranslatedValue = (value, mapping, defaultText = "None") => {
    if (value !== undefined && value !== null && value !== "") {
      return mapping[value] || value;
    }
    return defaultText;
  };

  const getRelatedEntityName = (id, entityType) => {
    if (!id || id === "none") return "None";

    switch(entityType) {
      case 'businessProcess': {
        const businessProcess = businessProcesses?.find(bp => bp.businessProcessID === id);
        return businessProcess ? businessProcess.name : "None";
      }
      case 'organizationalProcess': {
        const orgProcess = organizationalProcesses?.find(op => op.organizationalProcessID === id);
        return orgProcess ? orgProcess.name : "None";
      }
      case 'operation': {
        const operation = operations?.find(op => op.operationID === id);
        return operation ? operation.name : "None";
      }
      case 'application': {
        const application = applications?.find(app => app.applicationID === id);
        return application ? application.name : "None";
      }
      case 'entity': {
        const entity = entities?.find(ent => ent.entityID === id);
        return entity ? entity.name : "None";
      }
      case 'controlType': {
        const controlType = controlTypes?.find(ct => ct.controlTypeID === id);
        return controlType ? controlType.name : "None";
      }
      case 'risk': {
        const risk = risks?.find(r => r.riskID === id);
        return risk ? risk.name : "None";
      }
      case 'actionPlan': {
        const actionPlan = actionPlans?.find(ap => ap.actionPlanID === id);
        return actionPlan ? actionPlan.name : "None";
      }
      default:
        return "None";
    }
  };

  return (
    <div className="space-y-6">
      {/* Basic Information Section */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
          <div className="flex items-center gap-2">
            <Info className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">{t('admin.controls.overview.basic_information', 'Basic Information')}</span>
          </div>
        </div>

        <div className="p-5 bg-white">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-start">
              <Tag className="h-5 w-5 mr-3 text-blue-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.name', 'Name')}</p>
                <p className="text-base">{formatValue(control?.name)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Tag className="h-5 w-5 mr-3 text-indigo-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.code', 'Code')}</p>
                <p className="text-base">{formatValue(control?.code)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <CheckCircle className="h-5 w-5 mr-3 text-green-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">Contrôle Clé</p>
                <p className="text-base">{control?.controlKey === 1 ? 'Oui' : control?.controlKey === 0 ? 'Non' : 'None'}</p>
              </div>
            </div>

            <div className="flex items-start">
              <DollarSign className="h-5 w-5 mr-3 text-amber-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.operational_cost', 'Operational Cost')}</p>
                <p className="text-base">{formatValue(control?.operationalCost)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Globe className="h-5 w-5 mr-3 text-purple-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.organizational_level', 'Organizational Level')}</p>
                <p className="text-base">{formatValue(control?.organizationalLevel)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Wrench className="h-5 w-5 mr-3 text-cyan-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">Méthode d'Exécution du Contrôle</p>
                <p className="text-base">{formatTranslatedValue(control?.controlExecutionMethod, controlExecutionMethodMap)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Testing Information Section */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg">
          <div className="flex items-center gap-2">
            <FileCheck className="h-5 w-5 text-green-600 mr-1" />
            <span className="text-lg font-medium text-green-800">{t('admin.controls.overview.testing_details', 'Testing Details')}</span>
          </div>
        </div>

        <div className="p-5 bg-white">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-start">
              <Package className="h-5 w-5 mr-3 text-emerald-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">Type d'Échantillon</p>
                <p className="text-base">{formatTranslatedValue(control?.sampleType, sampleTypeMap)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Clock className="h-5 w-5 mr-3 text-teal-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">Fréquence des Tests</p>
                <p className="text-base">{formatTranslatedValue(control?.testingFrequency, testingFrequencyMap)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Search className="h-5 w-5 mr-3 text-cyan-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">Méthode de Test</p>
                <p className="text-base">{formatTranslatedValue(control?.testingMethod, testingMethodMap)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <BarChart className="h-5 w-5 mr-3 text-blue-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.testing_population_size', 'Testing Population Size')}</p>
                <p className="text-base">{formatValue(control?.testingPopulationSize)}</p>
              </div>
            </div>

            <div className="flex items-start col-span-2">
              <ClipboardList className="h-5 w-5 mr-3 text-indigo-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.testing_procedure', 'Testing Procedure')}</p>
                <p className="text-base">{formatValue(control?.testingProcedure)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Reference Data Section */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-t-lg">
          <div className="flex items-center gap-2">
            <LinkIcon className="h-5 w-5 text-amber-600 mr-1" />
            <span className="text-lg font-medium text-amber-800">{t('admin.controls.overview.reference_data', 'Reference Data')}</span>
          </div>
        </div>

        <div className="p-5 bg-white">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-start">
              <Shield className="h-5 w-5 mr-3 text-red-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.control_type', 'Control Type')}</p>
                <p className="text-base">{formatValue(getRelatedEntityName(control?.controlType, 'controlType'))}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Briefcase className="h-5 w-5 mr-3 text-blue-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.business_process', 'Business Process')}</p>
                <p className="text-base">{formatValue(getRelatedEntityName(control?.businessProcess, 'businessProcess'))}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Building className="h-5 w-5 mr-3 text-purple-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.organizational_process', 'Organizational Process')}</p>
                <p className="text-base">{formatValue(getRelatedEntityName(control?.organizationalProcess, 'organizationalProcess'))}</p>
              </div>
            </div>

            <div className="flex items-start">
              <GitBranch className="h-5 w-5 mr-3 text-green-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.operation', 'Operation')}</p>
                <p className="text-base">{formatValue(getRelatedEntityName(control?.operation, 'operation'))}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Database className="h-5 w-5 mr-3 text-cyan-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.application', 'Application')}</p>
                <p className="text-base">{formatValue(getRelatedEntityName(control?.application, 'application'))}</p>
              </div>
                </div>

            <div className="flex items-start">
              <Building className="h-5 w-5 mr-3 text-indigo-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.entity', 'Entity')}</p>
                <p className="text-base">{formatValue(getRelatedEntityName(control?.entity, 'entity'))}</p>
              </div>
            </div>

              <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 mr-3 text-orange-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.associated_risk', 'Associated Risk')}</p>
                <p className="text-base">{formatValue(getRelatedEntityName(control?.risk, 'risk'))}</p>
              </div>
                </div>

            <div className="flex items-start">
              <FileCheck className="h-5 w-5 mr-3 text-emerald-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.action_plan', 'Action Plan')}</p>
                <p className="text-base">{formatValue(getRelatedEntityName(control?.implementingActionPlan, 'actionPlan'))}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Descriptions Section */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-t-lg">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-purple-600 mr-1" />
            <span className="text-lg font-medium text-purple-800">{t('admin.controls.overview.descriptions', 'Descriptions')}</span>
          </div>
        </div>

        <div className="p-5 bg-white">
          <div className="grid grid-cols-1 gap-6">
            <div className="flex items-start">
              <BookOpen className="h-5 w-5 mr-3 text-indigo-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.objective', 'Objective')}</p>
                <p className="text-base whitespace-pre-wrap">{formatValue(control?.objective)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <ClipboardList className="h-5 w-5 mr-3 text-blue-600 mt-0.5" />
        <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.execution_procedure', 'Execution Procedure')}</p>
                <p className="text-base whitespace-pre-wrap">{formatValue(control?.executionProcedure)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <FileText className="h-5 w-5 mr-3 text-gray-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.controls.overview.comment', 'Comment')}</p>
                <p className="text-base whitespace-pre-wrap">{formatValue(control?.comment)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Attachments Section */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-4 bg-gradient-to-r from-violet-50 to-indigo-50 rounded-t-lg">
          <div className="flex items-center gap-2">
            <Paperclip className="h-5 w-5 text-violet-600 mr-1" />
            <span className="text-lg font-medium text-violet-800">Pièces jointes</span>
          </div>
        </div>
        <div className="p-5 bg-white">
          <ControlAttachmentsSection control={control} />
        </div>
      </div>
    </div>
  );
}

export default ControlsOverview;
