const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../../middleware/auth');
const {
  getAllRapportSupports,
  getRapportSupportByMissionId,
  getRapportSupportById,
  createRapportSupport,
  updateRapportSupport,
  deleteRapportSupport
} = require('../../../controllers/audit/rapport/audit-mission-rapport-support-controller');

console.log('AuditMissionRapportSupport routes loaded');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all rapport supports
router.get('/', authorizeRoles(['audit_director', 'auditor']), getAllRapportSupports);

// Get rapport support by mission ID
router.get('/mission/:missionId', authorizeRoles(['audit_director', 'auditor']), getRapportSupportByMissionId);

// Get rapport support by ID
router.get('/:id', authorizeRoles(['audit_director', 'auditor']), getRapportSupportById);

// Create a new rapport support
router.post('/', authorizeRoles(['audit_director', 'auditor']), createRapportSupport);

// Update rapport support
router.put('/:id', authorizeRoles(['audit_director', 'auditor']), updateRapportSupport);

// Delete rapport support
router.delete('/:id', authorizeRoles(['audit_director', 'auditor']), deleteRapportSupport);

module.exports = router;
