import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { toast } from "sonner";
import { getAuthHeaders } from "@/utils/auth-headers";
import { getApiBaseUrl } from '../../utils/api-config';

const API_URL = `${getApiBaseUrl()}`;

const initialState = {
  businessLines: [],
  currentBusinessLine: null,
  isLoading: false,
  isError: false,
  error: null,
};

// Async thunks
export const getBusinessLines = createAsyncThunk(
  "businessLine/getBusinessLines",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_URL}/businessLines`, {
        withCredentials: true,
        headers: getAuthHeaders()
      });
      return response.data.data;
    } catch (error) {
      console.error("Error in getBusinessLines:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to fetch business lines");
    }
  }
);

export const getBusinessLineById = createAsyncThunk(
  "businessLine/getBusinessLineById",
  async (id, { rejectWithValue }) => {
    try {
      console.log("Fetching business line with ID:", id);

      const response = await axios.get(`${API_URL}/businessLines/${id}`, {
        withCredentials: true,
        headers: getAuthHeaders()
      });

      console.log("API Response:", response.data);
      return response.data.data;
    } catch (error) {
      console.error("Error in getBusinessLineById:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to fetch business line");
    }
  }
);

export const createBusinessLine = createAsyncThunk(
  "businessLine/createBusinessLine",
  async (businessLineData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_URL}/businessLines`, businessLineData, {
        withCredentials: true,
        headers: getAuthHeaders()
      });
      return response.data.data;
    } catch (error) {
      console.error("Error in createBusinessLine:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to create business line");
    }
  }
);

export const updateBusinessLine = createAsyncThunk(
  "businessLine/updateBusinessLine",
  async ({ id, businessLineData }, { rejectWithValue }) => {
    try {
      const response = await axios.put(`${API_URL}/businessLines/${id}`, businessLineData, {
        withCredentials: true,
        headers: getAuthHeaders()
      });
      return response.data.data;
    } catch (error) {
      console.error("Error in updateBusinessLine:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to update business line");
    }
  }
);

export const deleteBusinessLine = createAsyncThunk(
  "businessLine/deleteBusinessLine",
  async (id, { rejectWithValue }) => {
    try {
      await axios.delete(`${API_URL}/businessLines/${id}`, {
        withCredentials: true,
        headers: getAuthHeaders()
      });
      return id;
    } catch (error) {
      console.error("Error in deleteBusinessLine:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to delete business line");
    }
  }
);

const businessLineSlice = createSlice({
  name: "businessLine",
  initialState,
  reducers: {
    reset: (state) => {
      state.currentBusinessLine = null;
      state.isLoading = false;
      state.isError = false;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get Business Lines
      .addCase(getBusinessLines.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(getBusinessLines.fulfilled, (state, action) => {
        state.isLoading = false;
        state.businessLines = action.payload;
      })
      .addCase(getBusinessLines.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
        toast.error(action.payload);
      })
      // Get Business Line by ID
      .addCase(getBusinessLineById.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(getBusinessLineById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentBusinessLine = action.payload;
      })
      .addCase(getBusinessLineById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
        toast.error(action.payload);
      })
      // Create Business Line
      .addCase(createBusinessLine.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(createBusinessLine.fulfilled, (state, action) => {
        state.isLoading = false;
        state.businessLines.push(action.payload);
        toast.success("Business line created successfully");
      })
      .addCase(createBusinessLine.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
        toast.error(action.payload);
      })
      // Update Business Line
      .addCase(updateBusinessLine.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(updateBusinessLine.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.businessLines.findIndex(
          (line) => line.businessLineID === action.payload.businessLineID
        );
        if (index !== -1) {
          state.businessLines[index] = action.payload;
        }
        state.currentBusinessLine = action.payload;
        toast.success("Business line updated successfully");
      })
      .addCase(updateBusinessLine.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
        toast.error(action.payload);
      })
      // Delete Business Line
      .addCase(deleteBusinessLine.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(deleteBusinessLine.fulfilled, (state, action) => {
        state.isLoading = false;
        state.businessLines = state.businessLines.filter(
          (line) => line.businessLineID !== action.payload
        );
        if (state.currentBusinessLine?.businessLineID === action.payload) {
          state.currentBusinessLine = null;
        }
        toast.success("Business line deleted successfully");
      })
      .addCase(deleteBusinessLine.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
        toast.error(action.payload);
      });
  },
});

export const { reset } = businessLineSlice.actions;
export default businessLineSlice.reducer;
