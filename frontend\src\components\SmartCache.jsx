import { useState, useEffect, useRef, createContext, useContext } from 'react';
import { useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import AdminWelcome from '@/pages/admin-view/welcome-page';
import AdminDashboard from '@/pages/admin-view/dashboard';
import AuditWelcome from '@/pages/audit-view/welcome-page';

// Global cache context
const CacheContext = createContext();

// Cache Provider
export function CacheProvider({ children }) {
  const { isAuthenticated, user } = useSelector((state) => state.auth);
  const [cachedPages, setCachedPages] = useState(new Map());
  const [isInitialized, setIsInitialized] = useState(false);
  const initTimeoutRef = useRef(null);

  // Initialize cache system after login
  useEffect(() => {
    if (isAuthenticated && user) {
      initTimeoutRef.current = setTimeout(() => {
        setIsInitialized(true);
        console.log('🚀 Smart cache system initialized');
      }, 1000);
    } else {
      setCachedPages(new Map());
      setIsInitialized(false);
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current);
      }
    }

    return () => {
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current);
      }
    };
  }, [isAuthenticated, user]);

  const cachePage = (path, component) => {
    setCachedPages(prev => {
      const newCache = new Map(prev);
      newCache.set(path, component);
      console.log(`📦 Cached page: ${path}`);
      return newCache;
    });
  };

  const getCachedPage = (path) => {
    return cachedPages.get(path);
  };

  const isCached = (path) => {
    return cachedPages.has(path);
  };

  return (
    <CacheContext.Provider value={{
      cachePage,
      getCachedPage,
      isCached,
      isInitialized,
      cachedPages
    }}>
      {children}
    </CacheContext.Provider>
  );
}

// Hook to use cache
export function useCache() {
  const context = useContext(CacheContext);
  if (!context) {
    throw new Error('useCache must be used within CacheProvider');
  }
  return context;
}

// Smart Page Component that handles caching
export function SmartPage({ path, component: Component, ...props }) {
  const location = useLocation();
  const { getCachedPage, cachePage, isCached, isInitialized } = useCache();
  const [isLoading, setIsLoading] = useState(false);
  const cacheTimeoutRef = useRef(null);
  const currentPath = location.pathname;

  // Check if this is the current page
  const isCurrentPage = currentPath === path;

  useEffect(() => {
    if (!isCurrentPage || !isInitialized) {
      return;
    }

    // If page is cached, we're done
    if (isCached(path)) {
      console.log(`⚡ Using cached version of ${path}`);
      return;
    }

    // If not cached and this is current page, cache it after data loads
    if (!isLoading) {
      setIsLoading(true);
      cacheTimeoutRef.current = setTimeout(() => {
        const componentToCache = <Component {...props} />;
        cachePage(path, componentToCache);
        setIsLoading(false);
      }, 2500); // Wait for data to load
    }

    return () => {
      if (cacheTimeoutRef.current) {
        clearTimeout(cacheTimeoutRef.current);
      }
    };
  }, [isCurrentPage, path, Component, props, isInitialized, isCached, cachePage, isLoading]);

  // Return cached version if available and this is current page
  if (isCurrentPage && isCached(path)) {
    return getCachedPage(path);
  }

  // Return normal component if current page but not cached yet
  if (isCurrentPage) {
    return <Component {...props} />;
  }

  // Don't render if not current page
  return null;
}

// Pre-configured smart pages
export const SmartAdminWelcome = (props) => (
  <SmartPage path="/admin/welcome" component={AdminWelcome} {...props} />
);

export const SmartAdminDashboard = (props) => (
  <SmartPage path="/admin/dashboard" component={AdminDashboard} {...props} />
);

export const SmartAuditWelcome = (props) => (
  <SmartPage path="/audit/welcome" component={AuditWelcome} {...props} />
);
