import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Loader2, Trash2, ArrowUpDown, AlertOctagon } from 'lucide-react';
import { useSelector } from 'react-redux';
import { hasPermission } from '@/store/auth-slice';
import PageHeader from '@/components/ui/page-header';
import axios from 'axios';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import TablePagination from "../../../components/ui/table-pagination";

function IncidentTypes() {
  const navigate = useNavigate();
  const canCreate = useSelector(state => hasPermission(state, 'create'));
  const canDelete = useSelector(state => hasPermission(state, 'delete'));
  const [incidentTypes, setIncidentTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedIncidentTypes, setSelectedIncidentTypes] = useState([]);
  const [newIncidentType, setNewIncidentType] = useState({
    incidentTypeID: '',
    name: '',
    description: ''
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

  // Fetch Incident Types
  const fetchIncidentTypes = async (signal) => {
    try {
      // Get token from localStorage
      const token = localStorage.getItem('authToken');
      const headers = {
        'Content-Type': 'application/json'
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
        console.log('Using token for incident types fetch');
      }
      
      const response = await axios.get("http://localhost:5001/api/incidentTypes", {
        signal,
        withCredentials: true,
        headers
      });
      
      if (response.data.success) {
        setIncidentTypes(response.data.data);
      }
    } catch (error) {
      if (!error.name === 'AbortError') {
        console.error("Error fetching incident types:", error);
        toast.error("Failed to fetch incident types");
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const controller = new AbortController();
    fetchIncidentTypes(controller.signal);
    return () => controller.abort();
  }, []);

  // Handle Create Incident Type
  const handleSubmit = async (e) => {
    e.preventDefault();

    const incidentTypeToCreate = {
      ...newIncidentType,
      incidentTypeID: newIncidentType.incidentTypeID || `INC_TYPE_${Date.now()}`
    };

    try {
      setSubmitting(true);
      const response = await axios.post(
        "http://localhost:5001/api/incidentTypes",
        incidentTypeToCreate,
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
          timeout: 60000,
        }
      );

      if (response.data.success) {
        toast.success("Incident Type created successfully");
        setNewIncidentType({
          incidentTypeID: '',
          name: '',
          description: ''
        });
        setIsOpen(false);
        const controller = new AbortController();
        await fetchIncidentTypes(controller.signal);
      }
    } catch (error) {
      console.error("Error creating incident type:", error);
      toast.error("Failed to create incident type");
    } finally {
      setSubmitting(false);
    }
  };

  // Handle Select All
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedIncidentTypes(filteredIncidentTypes.map(type => type.incidentTypeID));
    } else {
      setSelectedIncidentTypes([]);
    }
  };

  // Handle Select Individual
  const handleSelectIncidentType = (id) => {
    setSelectedIncidentTypes(prev => {
      if (prev.includes(id)) {
        return prev.filter(item => item !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  // Handle Row Click
  const handleRowClick = (id) => {
    navigate(`/admin/incident-types/${id}`);
  };

  // Handle Delete Selected
  const handleDeleteSelected = async () => {
    if (selectedIncidentTypes.length === 0) {
      toast.error("No incident types selected");
      return;
    }

    if (window.confirm(`Are you sure you want to delete ${selectedIncidentTypes.length} selected incident type(s)?`)) {
      try {
        setLoading(true);
        let failedDeletions = [];

        const attemptDelete = async (id, retries = 2) => {
          for (let attempt = 0; attempt <= retries; attempt++) {
            try {
              await axios.delete(`http://localhost:5001/api/incidentTypes/${id}`, {
                withCredentials: true,
                timeout: 60000,
                headers: {
                  "Content-Type": "application/json",
                },
              });
              return true;
            } catch (error) {
              if (attempt === retries) {
                console.error(`Failed to delete incident type ${id} after ${retries + 1} attempts:`, error);
                return false;
              }
              await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
            }
          }
        };

        const batchSize = 3;
        for (let i = 0; i < selectedIncidentTypes.length; i += batchSize) {
          const batch = selectedIncidentTypes.slice(i, i + batchSize);
          await Promise.all(
            batch.map(async (id) => {
              const success = await attemptDelete(id);
              if (!success) failedDeletions.push(id);
              await new Promise(resolve => setTimeout(resolve, 500));
              return success;
            })
          );

          if (i + batchSize < selectedIncidentTypes.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        await fetchIncidentTypes();
        setSelectedIncidentTypes([]);

        if (failedDeletions.length > 0) {
          toast.error(`Failed to delete ${failedDeletions.length} incident type(s): ${failedDeletions.join(', ')}`);
        } else {
          toast.success("All selected incident types deleted successfully");
        }

      } catch (error) {
        console.error("Error in deletion process:", error);
        toast.error("An error occurred during the deletion process");
      } finally {
        setLoading(false);
      }
    }
  };

  // Filter incident types based on search
  const filteredIncidentTypes = incidentTypes.filter((type) =>
    Object.values(type).some((value) =>
      value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  // Add sorting handler
  const handleSort = (key) => {
    setSortConfig((prevConfig) => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Define columns configuration
  const columns = [
    { key: 'name', label: 'Name', sortable: true },
    { key: 'description', label: 'Description', sortable: true }
  ];

  // Sort the filtered incident types
  const sortedIncidentTypes = [...filteredIncidentTypes].sort((a, b) => {
    if (!sortConfig.key) return 0;

    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];

    if (aValue === null || aValue === undefined) return 1;
    if (bValue === null || bValue === undefined) return -1;

    if (sortConfig.direction === 'asc') {
      return aValue.toString().localeCompare(bValue.toString(), undefined, { numeric: true });
    } else {
      return bValue.toString().localeCompare(aValue.toString(), undefined, { numeric: true });
    }
  });

  // Calculate pagination values
  const totalPages = Math.ceil(sortedIncidentTypes.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentIncidentTypes = sortedIncidentTypes.slice(indexOfFirstItem, indexOfLastItem);

  // Add page change handler
  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  return (
    <div className="p-6">
      <PageHeader
        title="Incident Type Management"
        description="Define and manage different types of incidents for better categorization and reporting."
        section="Incident"
        currentPage="Types"
        icon={AlertOctagon}
        searchPlaceholder="Search incident types..."
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
      />

      {/* Action Buttons */}
      <div className="flex justify-end items-center gap-3 mb-4">
        {selectedIncidentTypes.length > 0 && (
          <Button
            variant="outline"
            className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
              canDelete && !loading
                ? 'bg-[#F62D51]/90 text-white hover:bg-[#F62D51] hover:shadow-md'
                : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
            } flex items-center gap-2 px-6 py-2 font-semibold`}
            onClick={handleDeleteSelected}
            disabled={!canDelete || loading}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
            Delete ({selectedIncidentTypes.length})
              </>
            )}
          </Button>
        )}
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          {canCreate && (
          <DialogTrigger asChild>
            <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Incident Type
            </Button>
          </DialogTrigger>
          )}
          <DialogContent className="max-w-3xl p-8">
            <DialogHeader>
              <DialogTitle>Create New Incident Type</DialogTitle>
              <DialogDescription>
                Fill in the details below to create a new incident type. Fields marked with * are required.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-8 mt-4">
              <div className="flex flex-col">
                <Label htmlFor="name" className="mb-3">
                  Name *
                </Label>
                <Input
                  id="name"
                  value={newIncidentType.name}
                  onChange={(e) =>
                    setNewIncidentType({ ...newIncidentType, name: e.target.value })
                  }
                  placeholder="Enter incident type name"
                  required
                />
              </div>

              <div className="flex flex-col">
                <Label htmlFor="description" className="mb-3">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={newIncidentType.description}
                  onChange={(e) =>
                    setNewIncidentType({ ...newIncidentType, description: e.target.value })
                  }
                  placeholder="Enter description"
                  rows={4}
                />
              </div>

              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-[#F62D51] hover:bg-red-700"
                  disabled={submitting}
                >
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Incident Type'
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="w-full">
        {loading ? (
          <div className="flex justify-center items-center min-h-[200px]">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        ) : (
          <>
            <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                        <Checkbox
                          checked={
                            selectedIncidentTypes.length === currentIncidentTypes.length &&
                            currentIncidentTypes.length > 0
                          }
                          onCheckedChange={handleSelectAll}
                          aria-label="Select all"
                        />
                      </th>
                      {columns.map((column) => (
                        <th
                          key={column.key}
                          className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100"
                          onClick={() => column.sortable && handleSort(column.key)}
                        >
                          <div className="flex items-center gap-1">
                            {column.label}
                            {sortConfig.key === column.key && (
                              <ArrowUpDown className="w-4 h-4" />
                            )}
                          </div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {currentIncidentTypes.map((type, index) => (
                      <tr
                        key={type.incidentTypeID}
                        className={`hover:bg-gray-50 cursor-pointer ${
                          index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                        }`}
                        onClick={() => handleRowClick(type.incidentTypeID)}
                      >
                        <td className="px-6 py-4">
                          <Checkbox
                            checked={selectedIncidentTypes.includes(type.incidentTypeID)}
                            onCheckedChange={() => handleSelectIncidentType(type.incidentTypeID)}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </td>

                        <td className="px-6 py-4 text-sm whitespace-nowrap">
                          <span className="font-bold text-[#242A33]">{type.name}</span>
                        </td>
                        <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                          {type.description || '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <TablePagination
              currentPage={currentPage}
              totalPages={totalPages}
              itemsPerPage={itemsPerPage}
              totalItems={filteredIncidentTypes.length}
              onPageChange={handlePageChange}
              onItemsPerPageChange={(value) => {
                setItemsPerPage(value);
                setCurrentPage(1);
              }}
              startIndex={indexOfFirstItem}
              endIndex={indexOfLastItem}
            />
          </>
        )}
      </div>
    </div>
  );
}

export default IncidentTypes;