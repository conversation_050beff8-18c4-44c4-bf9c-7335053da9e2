import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";
import { getApiBaseUrl } from '../../utils/api-config';

const API_URL = getApiBaseUrl();

const initialState = {
  isInitialized: false,
  businessProcesses: {
    data: [],
    loading: false,
    error: null,
  },
  organizationalProcesses: {
    data: [],
    loading: false,
    error: null,
  },
  operations: {
    data: [],
    loading: false,
    error: null,
  },
  applications: {
    data: [],
    loading: false,
    error: null,
  },
  entities: {
    data: [],
    loading: false,
    error: null,
  },
  riskTypes: {
    data: [],
    loading: false,
    error: null,
  },
  controls: {
    data: [],
    loading: false,
    error: null,
  },
  businessLines: {
    data: [],
    loading: false,
    error: null,
  },
  incidentTypes: {
    data: [],
    loading: false,
    error: null,
  },
  products: {
    data: [],
    loading: false,
    error: null,
  },
};

// Business Processes
export const fetchBusinessProcesses = createAsyncThunk(
  "referenceData/fetchBusinessProcesses",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_URL}/businessProcesses`, {
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: "Failed to fetch business processes" });
    }
  }
);

// Organizational Processes
export const fetchOrganizationalProcesses = createAsyncThunk(
  "referenceData/fetchOrganizationalProcesses",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_URL}/organizationalProcesses`, {
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: "Failed to fetch organizational processes" });
    }
  }
);

// Operations
export const fetchOperations = createAsyncThunk(
  "referenceData/fetchOperations",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_URL}/operations`, {
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: "Failed to fetch operations" });
    }
  }
);

// Applications
export const fetchApplications = createAsyncThunk(
  "referenceData/fetchApplications",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_URL}/applications`, {
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: "Failed to fetch applications" });
    }
  }
);

// Entities
export const fetchEntities = createAsyncThunk(
  "referenceData/fetchEntities",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_URL}/entities`, {
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: "Failed to fetch entities" });
    }
  }
);

// Risk Types
export const fetchRiskTypes = createAsyncThunk(
  "referenceData/fetchRiskTypes",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_URL}/riskTypes`, {
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: "Failed to fetch risk types" });
    }
  }
);

// Controls
export const fetchControls = createAsyncThunk(
  "referenceData/fetchControls",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_URL}/controls`, {
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: "Failed to fetch controls" });
    }
  }
);

// Business Lines
export const fetchBusinessLines = createAsyncThunk(
  "referenceData/fetchBusinessLines",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_URL}/businessLines`, {
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: "Failed to fetch business lines" });
    }
  }
);

// Incident Types
export const fetchIncidentTypes = createAsyncThunk(
  "referenceData/fetchIncidentTypes",
  async (_, { rejectWithValue }) => {
    try {
      // Get token from localStorage
      const token = localStorage.getItem('authToken');
      const headers = {
        'Content-Type': 'application/json'
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await axios.get(`${API_URL}/incidentTypes`, {
        withCredentials: true,
        headers
      });

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: "Failed to fetch incident types" });
    }
  }
);

// Products
export const fetchProducts = createAsyncThunk(
  "referenceData/fetchProducts",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_URL}/products`, {
        withCredentials: true,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: "Failed to fetch products" });
    }
  }
);

// Fetch all reference data at once
export const fetchAllReferenceData = createAsyncThunk(
  "referenceData/fetchAllReferenceData",
  async (_, { dispatch, getState }) => {
    // Check if data is already initialized
    const state = getState();
    if (state.referenceData.isInitialized) {
      return { success: true, cached: true };
    }

    try {
      // Use Promise.all to fetch all data in parallel
      const results = await Promise.all([
        dispatch(fetchBusinessProcesses()),
        dispatch(fetchOrganizationalProcesses()),
        dispatch(fetchOperations()),
        dispatch(fetchApplications()),
        dispatch(fetchEntities()),
        dispatch(fetchRiskTypes()),
        dispatch(fetchControls()),
        dispatch(fetchBusinessLines()),
        dispatch(fetchIncidentTypes()),
        dispatch(fetchProducts())
      ]);

      // Mark data as initialized
      dispatch({ type: 'referenceData/setInitialized', payload: true });

      return { success: true };
    } catch (error) {
      console.error('Error fetching reference data');
      return { success: false, error: 'Failed to fetch reference data' };
    }
  }
);

const referenceDataSlice = createSlice({
  name: "referenceData",
  initialState,
  reducers: {
    setInitialized: (state, action) => {
      state.isInitialized = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Business Processes
      .addCase(fetchBusinessProcesses.pending, (state) => {
        state.businessProcesses.loading = true;
        state.businessProcesses.error = null;
      })
      .addCase(fetchBusinessProcesses.fulfilled, (state, action) => {
        state.businessProcesses.loading = false;
        state.businessProcesses.data = action.payload.data || [];
      })
      .addCase(fetchBusinessProcesses.rejected, (state, action) => {
        state.businessProcesses.loading = false;
        state.businessProcesses.error = action.payload?.message || "Failed to fetch business processes";
      })

      // Organizational Processes
      .addCase(fetchOrganizationalProcesses.pending, (state) => {
        state.organizationalProcesses.loading = true;
        state.organizationalProcesses.error = null;
      })
      .addCase(fetchOrganizationalProcesses.fulfilled, (state, action) => {
        state.organizationalProcesses.loading = false;
        state.organizationalProcesses.data = action.payload.data || [];
      })
      .addCase(fetchOrganizationalProcesses.rejected, (state, action) => {
        state.organizationalProcesses.loading = false;
        state.organizationalProcesses.error = action.payload?.message || "Failed to fetch organizational processes";
      })

      // Operations
      .addCase(fetchOperations.pending, (state) => {
        state.operations.loading = true;
        state.operations.error = null;
      })
      .addCase(fetchOperations.fulfilled, (state, action) => {
        state.operations.loading = false;
        state.operations.data = action.payload.data || [];
      })
      .addCase(fetchOperations.rejected, (state, action) => {
        state.operations.loading = false;
        state.operations.error = action.payload?.message || "Failed to fetch operations";
      })

      // Applications
      .addCase(fetchApplications.pending, (state) => {
        state.applications.loading = true;
        state.applications.error = null;
      })
      .addCase(fetchApplications.fulfilled, (state, action) => {
        state.applications.loading = false;
        state.applications.data = action.payload.data || [];
      })
      .addCase(fetchApplications.rejected, (state, action) => {
        state.applications.loading = false;
        state.applications.error = action.payload?.message || "Failed to fetch applications";
      })

      // Entities
      .addCase(fetchEntities.pending, (state) => {
        state.entities.loading = true;
        state.entities.error = null;
      })
      .addCase(fetchEntities.fulfilled, (state, action) => {
        state.entities.loading = false;
        state.entities.data = action.payload.data || [];
      })
      .addCase(fetchEntities.rejected, (state, action) => {
        state.entities.loading = false;
        state.entities.error = action.payload?.message || "Failed to fetch entities";
      })

      // Risk Types
      .addCase(fetchRiskTypes.pending, (state) => {
        state.riskTypes.loading = true;
        state.riskTypes.error = null;
      })
      .addCase(fetchRiskTypes.fulfilled, (state, action) => {
        state.riskTypes.loading = false;
        state.riskTypes.data = action.payload.data || [];
      })
      .addCase(fetchRiskTypes.rejected, (state, action) => {
        state.riskTypes.loading = false;
        state.riskTypes.error = action.payload?.message || "Failed to fetch risk types";
      })

      // Controls
      .addCase(fetchControls.pending, (state) => {
        state.controls.loading = true;
        state.controls.error = null;
      })
      .addCase(fetchControls.fulfilled, (state, action) => {
        state.controls.loading = false;
        state.controls.data = action.payload.data || [];
      })
      .addCase(fetchControls.rejected, (state, action) => {
        state.controls.loading = false;
        state.controls.error = action.payload?.message || "Failed to fetch controls";
      })

      // Business Lines
      .addCase(fetchBusinessLines.pending, (state) => {
        state.businessLines.loading = true;
        state.businessLines.error = null;
      })
      .addCase(fetchBusinessLines.fulfilled, (state, action) => {
        state.businessLines.loading = false;
        state.businessLines.data = action.payload.data || [];
      })
      .addCase(fetchBusinessLines.rejected, (state, action) => {
        state.businessLines.loading = false;
        state.businessLines.error = action.payload?.message || "Failed to fetch business lines";
      })

      // Incident Types
      .addCase(fetchIncidentTypes.pending, (state) => {
        state.incidentTypes.loading = true;
        state.incidentTypes.error = null;
      })
      .addCase(fetchIncidentTypes.fulfilled, (state, action) => {
        state.incidentTypes.loading = false;
        state.incidentTypes.data = action.payload.data || [];
      })
      .addCase(fetchIncidentTypes.rejected, (state, action) => {
        state.incidentTypes.loading = false;
        state.incidentTypes.error = action.payload?.message || "Failed to fetch incident types";
      })

      // Products
      .addCase(fetchProducts.pending, (state) => {
        state.products.loading = true;
        state.products.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.products.loading = false;
        state.products.data = action.payload.data || [];
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.products.loading = false;
        state.products.error = action.payload?.message || "Failed to fetch products";
      });
  },
});

export const { setInitialized } = referenceDataSlice.actions;
export default referenceDataSlice.reducer;
