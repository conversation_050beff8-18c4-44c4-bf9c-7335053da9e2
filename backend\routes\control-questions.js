const express = require('express');
const router = express.Router();
const db = require('../models');
const ControlQuestion = db.ControlQuestion;
const CampagneResponse = db.CampagneResponse;
const { sequelize } = require('../models');

// Get all questions for a specific control
router.get('/controls/:controlId/questions', async (req, res) => {
  try {
    const { controlId } = req.params;

    console.log('[Control Questions] ControlQuestion model:', ControlQuestion);
    console.log('[Control Questions] Available models:', Object.keys(db));

    if (!ControlQuestion) {
      return res.status(500).json({
        success: false,
        message: 'ControlQuestion model not found'
      });
    }

    const questions = await ControlQuestion.findAll({
      where: { controlID: controlId },
      order: [['order', 'ASC'], ['id', 'ASC']]
    });

    res.json({
      success: true,
      data: questions
    });
  } catch (error) {
    console.error('Error fetching control questions:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching control questions',
      error: error.message
    });
  }
});

// Create a new question for a control
router.post('/controls/:controlId/questions', async (req, res) => {
  try {
    const { controlId } = req.params;
    const { question_text, input_type, options } = req.body;

    // Validate required fields
    if (!question_text || !input_type) {
      return res.status(400).json({
        success: false,
        message: 'Question text and input type are required'
      });
    }

    // Get the next order number for this control
    const maxOrderQuestion = await ControlQuestion.findOne({
      where: { controlID: controlId },
      order: [['order', 'DESC']]
    });
    const nextOrder = maxOrderQuestion ? maxOrderQuestion.order + 1 : 1;

    // Create the new question
    const newQuestion = await ControlQuestion.create({
      controlID: controlId,
      question_text,
      input_type,
      options: options || null,
      order: nextOrder
    });

    res.status(201).json({
      success: true,
      data: newQuestion,
      message: 'Question created successfully'
    });
  } catch (error) {
    console.error('Error creating control question:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating control question',
      error: error.message
    });
  }
});

// Update a question
router.put('/controls/questions/:questionId', async (req, res) => {
  try {
    const { questionId } = req.params;
    const { question_text, input_type, options } = req.body;

    console.log('Updating question:', questionId);
    console.log('Request body:', req.body);
    console.log('Question text:', question_text);
    console.log('Input type:', input_type);
    console.log('Options:', options);

    // Validate required fields
    if (!question_text || !input_type) {
      console.log('Validation failed: missing required fields');
      return res.status(400).json({
        success: false,
        message: 'Question text and input type are required',
        received: { question_text, input_type, options }
      });
    }

    // Validate input_type
    const validInputTypes = ['text', 'number', 'date', 'radio', 'checkbox', 'file', 'url'];
    if (!validInputTypes.includes(input_type)) {
      console.log('Validation failed: invalid input type');
      return res.status(400).json({
        success: false,
        message: `Invalid input type. Must be one of: ${validInputTypes.join(', ')}`,
        received: input_type
      });
    }

    // Validate options for choice-based questions
    if (['radio', 'checkbox'].includes(input_type)) {
      if (!Array.isArray(options) || options.length === 0) {
        console.log('Validation failed: options required for choice-based questions');
        return res.status(400).json({
          success: false,
          message: 'Options array is required for radio and checkbox questions',
          received: options
        });
      }
    }

    const [updatedRowsCount, updatedQuestions] = await ControlQuestion.update(
      {
        question_text,
        input_type,
        options: options || null
      },
      {
        where: { id: questionId },
        returning: true
      }
    );

    if (updatedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Question not found'
      });
    }

    res.json({
      success: true,
      data: updatedQuestions[0],
      message: 'Question updated successfully'
    });
  } catch (error) {
    console.error('Error updating control question:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating control question',
      error: error.message
    });
  }
});

// Delete a question
router.delete('/controls/questions/:questionId', async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { questionId } = req.params;
    const questionIdInt = parseInt(questionId, 10);

    // Check if the question exists
    const question = await ControlQuestion.findByPk(questionIdInt);
    if (!question) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Question not found'
      });
    }

    // First, delete all related campagne responses
    const deletedResponsesCount = await CampagneResponse.destroy({
      where: { questionID: questionIdInt },
      transaction
    });

    console.log(`Deleted ${deletedResponsesCount} campagne responses for question ${questionIdInt}`);

    // Then delete the question itself
    const deletedRowsCount = await ControlQuestion.destroy({
      where: { id: questionIdInt },
      transaction
    });

    await transaction.commit();

    res.json({
      success: true,
      message: 'Question deleted successfully',
      data: {
        deletedQuestion: deletedRowsCount > 0,
        deletedResponses: deletedResponsesCount
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting control question:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting control question',
      error: error.message
    });
  }
});

// Reorder questions for a control
router.put('/controls/:controlId/questions/reorder', async (req, res) => {
  try {
    const { controlId } = req.params;
    const { questionIds } = req.body;

    if (!Array.isArray(questionIds)) {
      return res.status(400).json({
        success: false,
        message: 'questionIds must be an array'
      });
    }

    // Update the order for each question without transaction for now
    try {
      for (let i = 0; i < questionIds.length; i++) {
        await ControlQuestion.update(
          { order: i + 1 },
          {
            where: {
              id: questionIds[i],
              controlID: controlId
            }
          }
        );
      }

      res.json({
        success: true,
        message: 'Questions reordered successfully'
      });
    } catch (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error reordering control questions:', error);
    res.status(500).json({
      success: false,
      message: 'Error reordering control questions',
      error: error.message
    });
  }
});

module.exports = router;
