import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft, Sparkles, Loader2, AlertTriangle, Zap, Brain, Cpu, Moon, Sun } from 'lucide-react';
import './ai-loading.css'; // Import custom animations
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from 'react-hot-toast';
import { useTranslation } from "react-i18next";

function IncidentAI() {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // State for the prompt and response
  const [prompt, setPrompt] = useState('');
  const [response, setResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [typingEffect, setTypingEffect] = useState('');
  const [typingIndex, setTypingIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(false);

  // State for page loading animation
  const [pageLoading, setPageLoading] = useState(true);

  // Initialize theme from localStorage or default to light mode
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const savedTheme = localStorage.getItem('aiPageTheme');
    return savedTheme ? JSON.parse(savedTheme) : false;
  });

  // Ensure loading animation stays visible for at least 1 second
  useEffect(() => {
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // State for the incident form (similar to add-incident.jsx)
  const [incidentData, setIncidentData] = useState({
    name: '',
    declaredBy: '',
    declarationDate: '',
    declarantEntity: '',
    detectionDate: '',
    occurrenceDate: '',
    nearMiss: false,
    nature: '',
    impact: '',
    priority: '',
    currency: '',
    grossLoss: 0,
    recoveries: 0,
    provisions: 0,
    riskID: '',
    controlID: '',
    entityID: '',
    businessLineID: '',
    incidentTypeID: '',
    businessProcessID: '',
    organizationalProcessID: '',
    productID: '',
    applicationID: '',
    description: ''
  });

  // Typing effect for AI response
  useEffect(() => {
    if (isTyping && response) {
      const timer = setTimeout(() => {
        if (typingIndex < response.length) {
          // Type multiple characters at once for faster effect
          // Vary the number of characters to add for a more natural feel
          const isNewLine = response.charAt(typingIndex) === '\n';
          const isPunctuation = ['.', ',', ':', ';', '!', '?'].includes(response.charAt(typingIndex));

          // Add more chars at once for regular text, fewer for special characters
          const baseChars = isNewLine ? 1 : (isPunctuation ? 2 : 8);
          // Add slight randomness to the typing speed
          const charsToAdd = Math.min(baseChars + Math.floor(Math.random() * 3), response.length - typingIndex);

          const nextChars = response.substring(typingIndex, typingIndex + charsToAdd);
          setTypingEffect(prev => prev + nextChars);
          setTypingIndex(typingIndex + charsToAdd);
        } else {
          setIsTyping(false);
        }
      }, 3); // Even faster typing speed

      return () => clearTimeout(timer);
    }
  }, [isTyping, typingIndex, response]);

  // Handle prompt submission
  const handlePromptSubmit = async () => {
    if (!prompt.trim()) {
      toast.error(t('admin.incidents.ai.prompt_required', 'Please enter a prompt'));
      return;
    }

    setIsLoading(true);
    setTypingEffect('');
    setTypingIndex(0);

    try {
      // This is a mock response for the frontend implementation
      // In a real implementation, this would be an API call to your AI service
      setTimeout(() => {
        const mockResponse = `Based on your description, I've identified the following incident details:

Name: System Outage in Payment Processing
Description: The payment processing system experienced a complete outage for 2 hours, affecting all customer transactions.
Declared By: John Smith
Impact: High
Priority: High
Nature: Technical Failure
Detection Date: ${new Date().toISOString().split('T')[0]}
Occurrence Date: ${new Date().toISOString().split('T')[0]}

This appears to be related to the database server failure that was reported last week.`;

        setResponse(mockResponse);
        setIsTyping(true);

        // Auto-fill the form with extracted information
        setIncidentData({
          ...incidentData,
          name: 'System Outage in Payment Processing',
          description: 'The payment processing system experienced a complete outage for 2 hours, affecting all customer transactions.',
          declaredBy: 'John Smith',
          impact: 'High',
          priority: 'High',
          nature: 'Technical Failure',
          detectionDate: new Date().toISOString().split('T')[0],
          occurrenceDate: new Date().toISOString().split('T')[0]
        });

        setIsLoading(false);
      }, 2000);
    } catch (error) {
      console.error('Error processing AI prompt:', error);
      toast.error(t('admin.incidents.ai.failed_to_process', 'Failed to process your prompt. Please try again.'));
      setIsLoading(false);
    }
  };

  // Handle input changes for the incident form
  const handleInputChange = (field, value) => {
    setIncidentData((prev) => ({
      ...prev,
      [field]: field === 'nearMiss'
        ? Boolean(value)
        : ['grossLoss', 'recoveries', 'provisions'].includes(field)
          ? value === '' ? 0 : Number(value)
          : value
    }));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    // In a real implementation, this would submit the form data to your API
    toast.success(t('admin.incidents.created_successfully', 'Incident created successfully!'));
    navigate('/admin/incident');
  };

  // Toggle between dark and light mode
  const toggleThemeMode = () => {
    const newThemeValue = !isDarkMode;
    setIsDarkMode(newThemeValue);
    // Save to localStorage
    localStorage.setItem('aiPageTheme', JSON.stringify(newThemeValue));
  };

  return (
    <div className={`p-0 m-0 max-w-full min-h-screen ${isDarkMode
      ? 'bg-gradient-to-b from-slate-900 to-slate-800 text-gray-100'
      : 'bg-gradient-to-b from-gray-100 to-white text-gray-800'}`}>

      <div className="p-6 max-w-[1200px] mx-auto relative">
        {/* Custom Loading Animation - Only covers the content area */}
        {pageLoading && (
          <div className="absolute inset-0 z-50 flex items-center justify-center">
            <div className={`absolute inset-0 ${isDarkMode ? 'bg-slate-900/90' : 'bg-white/90'}`}></div>
            <div className="relative z-10 flex flex-col items-center">
              <Sparkles
                className={`h-20 w-20 ${isDarkMode ? 'text-purple-400' : 'text-purple-600'} ai-pulse`}
              />
              <div className={`mt-4 text-lg font-medium ${isDarkMode ? 'text-purple-300' : 'text-purple-700'} ai-fade`}>
                {t('admin.incidents.ai.initializing', 'Initializing AI Assistant...')}
              </div>
            </div>
          </div>
        )}

        {/* Custom styled header for AI page */}
        <div className="mb-6">
          <div className={`${isDarkMode
          ? 'bg-slate-800/50 border-purple-500/20'
          : 'bg-white/80 border-purple-400/30'}
          backdrop-blur-sm rounded-xl shadow-lg overflow-hidden border relative`}>
          {/* Top accent bar */}
          <div className="h-1.5 bg-gradient-to-r from-purple-600 to-blue-600"></div>

          {/* Theme toggle button */}
          <div className="absolute top-4 right-4 z-20">
            <Button
              onClick={toggleThemeMode}
              className={`w-12 h-8 rounded-full relative ${isDarkMode
                ? 'bg-slate-700 hover:bg-slate-600'
                : 'bg-gray-200 hover:bg-gray-300'}
                transition-all duration-300 p-0 overflow-hidden`}
            >
              <div className={`absolute inset-0 flex items-center transition-all duration-300 ${isDarkMode ? 'justify-start pl-1' : 'justify-end pr-1'}`}>
                <div className={`w-6 h-6 rounded-full flex items-center justify-center ${isDarkMode
                  ? 'bg-slate-900 text-yellow-300'
                  : 'bg-white text-slate-800'}
                  shadow-md transition-all duration-300`}>
                  {isDarkMode ? <Moon className="h-3.5 w-3.5" /> : <Sun className="h-3.5 w-3.5" />}
                </div>
              </div>
            </Button>
          </div>

          {/* Decorative elements */}
          <div className={`absolute -top-10 -right-10 w-40 h-40 ${isDarkMode ? 'bg-purple-500/10' : 'bg-purple-500/5'} rounded-full blur-2xl`}></div>
          <div className={`absolute -bottom-8 -left-8 w-32 h-32 ${isDarkMode ? 'bg-blue-500/10' : 'bg-blue-500/5'} rounded-full blur-xl`}></div>

          <div className="py-4 px-6 relative z-10">
            {/* Title section with geometric accent */}
            <div className="flex items-start gap-6">
              {/* Left geometric accent */}
              <div className="hidden md:block relative min-w-[5rem] h-16">
                <div className="absolute top-0 left-0 w-10 h-10 bg-purple-600/30 rounded-lg transform rotate-45 translate-x-3"></div>
                <div className="absolute bottom-0 right-0 w-6 h-6 bg-blue-600/30 rounded-lg transform rotate-45 -translate-y-1"></div>
              </div>

              {/* Title content */}
              <div className="flex-1">
                <div className={`flex items-center gap-2 text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  <Sparkles className={`h-3 w-3 ${isDarkMode ? 'text-purple-400' : 'text-purple-500'}`} />
                  <span>{t('admin.incidents.ai.title_section', 'Incident')}</span>
                  <span>/</span>
                  <span className={`${isDarkMode ? 'text-purple-400' : 'text-purple-600'} font-medium`}>{t('admin.incidents.ai.title_section_ai', 'AI Assistant')}</span>
                </div>

                <h1 className={`${isDarkMode ? 'text-white' : 'text-gray-800'} text-xl font-bold leading-tight mt-1`}>
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-blue-500">AI-Powered Incident Creation</span>
                  <span className="ml-2 inline-block w-2 h-2 bg-purple-500 rounded-full align-middle"></span>
                </h1>

                <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} mt-1 max-w-2xl whitespace-nowrap overflow-hidden text-ellipsis`}>
                  {t('admin.incidents.ai.use_ai', 'Use artificial intelligence to help create and analyze incidents based on your descriptions.')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center mb-6">
        <Button
          variant="outline"
          className={`flex items-center gap-2 ${isDarkMode
            ? 'border-purple-500/50 bg-slate-800/50 text-purple-400 hover:bg-purple-900/20 hover:border-purple-500 hover:text-purple-300 shadow-purple-900/10'
            : 'border-purple-400/50 bg-white/80 text-purple-600 hover:bg-purple-50 hover:border-purple-400 hover:text-purple-700 shadow-purple-200/30'}
            backdrop-blur-sm transition-all duration-300 shadow-lg`}
          onClick={() => navigate('/admin/incident')}
        >
          <ChevronLeft className="h-4 w-4" />
          {t('admin.incidents.back_to_incidents', 'Back to Incidents')}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left column: Prompt and Response */}
        <div className="space-y-8">
          {/* Prompt Area */}
          <div className={`${isDarkMode
            ? 'bg-slate-800/50 border-purple-500/20'
            : 'bg-white/80 border-purple-400/30'}
            backdrop-blur-sm rounded-xl p-6 shadow-lg border relative overflow-hidden`}>
            {/* Decorative elements */}
            <div className={`absolute -top-10 -right-10 w-40 h-40 ${isDarkMode ? 'bg-purple-500/10' : 'bg-purple-500/5'} rounded-full blur-2xl`}></div>
            <div className={`absolute -bottom-8 -left-8 w-32 h-32 ${isDarkMode ? 'bg-blue-500/10' : 'bg-blue-500/5'} rounded-full blur-xl`}></div>

            <h2 className={`text-xl font-semibold mb-4 flex items-center gap-2 ${isDarkMode ? 'text-white' : 'text-gray-800'} relative z-10`}>
              <Brain className={`h-5 w-5 ${isDarkMode ? 'text-purple-400' : 'text-purple-500'}`} />
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-blue-500">{t('admin.incidents.ai.describe_incident', 'Describe the Incident')}</span>
            </h2>
            <div className="space-y-4 relative z-10">
              <Textarea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder={t('admin.incidents.ai.describe_incident_placeholder', 'Describe the incident in detail. For example: \'There was a system outage in the payment processing system yesterday at 3 PM that lasted for 2 hours...\'')}
                className={`min-h-[150px] w-full ${isDarkMode
                  ? 'bg-slate-900/50 border-purple-500/30 focus:border-purple-400 text-gray-100 placeholder:text-gray-500'
                  : 'bg-gray-50/80 border-purple-300/30 focus:border-purple-500 text-gray-800 placeholder:text-gray-400'}
                  resize-none`}
              />
              <Button
                onClick={handlePromptSubmit}
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white flex items-center justify-center gap-2 shadow-lg shadow-purple-700/20 transition-all duration-300 hover:shadow-purple-700/40 border-0"
                disabled={isLoading || !prompt.trim()}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span className="animate-pulse">{t('admin.incidents.ai.processing', 'Processing...')}</span>
                  </>
                ) : (
                  <>
                    <Zap className="h-5 w-5" />
                    {t('admin.incidents.ai.generate_incident_details', 'Generate Incident Details')}
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Response Area */}
          <div className={`${isDarkMode
            ? 'bg-slate-800/50 border-blue-500/20'
            : 'bg-white/80 border-blue-400/30'}
            backdrop-blur-sm rounded-xl p-6 shadow-lg border relative overflow-hidden`}>
            {/* Decorative elements */}
            <div className={`absolute -top-10 -left-10 w-40 h-40 ${isDarkMode ? 'bg-blue-500/10' : 'bg-blue-500/5'} rounded-full blur-2xl`}></div>
            <div className={`absolute -bottom-8 -right-8 w-32 h-32 ${isDarkMode ? 'bg-purple-500/10' : 'bg-purple-500/5'} rounded-full blur-xl`}></div>

            <h2 className={`text-xl font-semibold mb-4 flex items-center gap-2 ${isDarkMode ? 'text-white' : 'text-gray-800'} relative z-10`}>
              <Cpu className={`h-5 w-5 ${isDarkMode ? 'text-blue-400' : 'text-blue-500'}`} />
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-purple-500">{t('admin.incidents.ai.ai_analysis', 'AI Analysis')}</span>
            </h2>
            <div className={`rounded-lg p-5 min-h-[220px] relative z-10 ${response
              ? (isDarkMode ? 'bg-slate-900/50 border border-blue-500/30' : 'bg-gray-50/80 border border-blue-400/30')
              : (isDarkMode ? 'bg-slate-900/30 border border-slate-700/50' : 'bg-gray-50/50 border border-gray-200/50')}`}>
              {response ? (
                <pre className={`whitespace-pre-wrap font-mono text-sm ${isDarkMode ? 'text-blue-100' : 'text-blue-900'} leading-relaxed`}>{isTyping ? typingEffect : response}</pre>
              ) : (
                <div className={`${isDarkMode ? 'text-slate-500' : 'text-gray-400'} italic text-center mt-16 flex flex-col items-center`}>
                  <Cpu className={`h-10 w-10 mb-3 ${isDarkMode ? 'text-slate-600' : 'text-gray-300'} opacity-50`} />
                  {t('admin.incidents.ai.ai_response_will_appear', 'AI response will appear here after you submit your description.')}
                </div>
              )}
              {isTyping && <div className="absolute bottom-3 right-3"><div className={`w-2 h-5 ${isDarkMode ? 'bg-blue-400' : 'bg-blue-500'} animate-pulse rounded-full`}></div></div>}
            </div>
          </div>
        </div>

        {/* Right column: Incident Form */}
        <div className={`${isDarkMode
          ? 'bg-slate-800/50 border-red-500/20'
          : 'bg-white/80 border-red-400/30'}
          backdrop-blur-sm rounded-xl p-6 shadow-lg border relative overflow-hidden`}>
          {/* Decorative elements */}
          <div className={`absolute -top-10 -right-10 w-40 h-40 ${isDarkMode ? 'bg-red-500/10' : 'bg-red-500/5'} rounded-full blur-2xl`}></div>
          <div className={`absolute -bottom-8 -left-8 w-32 h-32 ${isDarkMode ? 'bg-orange-500/10' : 'bg-orange-500/5'} rounded-full blur-xl`}></div>

          <h2 className={`text-xl font-semibold mb-4 flex items-center gap-2 ${isDarkMode ? 'text-white' : 'text-gray-800'} relative z-10`}>
            <AlertTriangle className={`h-5 w-5 ${isDarkMode ? 'text-red-400' : 'text-red-500'}`} />
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-red-500 to-orange-500">{t('admin.incidents.ai.create_incident', 'Create Incident')}</span>
          </h2>
          <form onSubmit={handleSubmit} className="space-y-6 relative z-10">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              {/* Name */}
              <div className="space-y-2 md:col-span-2">
                <label className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{t('admin.incidents.ai.name', 'Name *')}</label>
                <Input
                  value={incidentData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={t('admin.incidents.ai.name_placeholder', 'Incident name')}
                  required
                  className={`w-full ${isDarkMode
                    ? 'bg-slate-900/50 border-slate-700 focus:border-red-400 text-gray-100'
                    : 'bg-gray-50/80 border-gray-300 focus:border-red-500 text-gray-800'}`}
                />
              </div>

              {/* Declared By */}
              <div className="space-y-2">
                <label className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{t('admin.incidents.ai.declared_by', 'Declared By *')}</label>
                <Input
                  value={incidentData.declaredBy}
                  onChange={(e) => handleInputChange('declaredBy', e.target.value)}
                  placeholder={t('admin.incidents.ai.declared_by_placeholder', 'Declarant name')}
                  required
                  className={`w-full ${isDarkMode
                    ? 'bg-slate-900/50 border-slate-700 focus:border-red-400 text-gray-100'
                    : 'bg-gray-50/80 border-gray-300 focus:border-red-500 text-gray-800'}`}
                />
              </div>

              {/* Declaration Date */}
              <div className="space-y-2">
                <label className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{t('admin.incidents.ai.declaration_date', 'Declaration Date *')}</label>
                <Input
                  type="date"
                  value={incidentData.declarationDate}
                  onChange={(e) => handleInputChange('declarationDate', e.target.value)}
                  className={`w-full ${isDarkMode
                    ? 'bg-slate-900/50 border-slate-700 focus:border-red-400 text-gray-100'
                    : 'bg-gray-50/80 border-gray-300 focus:border-red-500 text-gray-800'}`}
                  required
                />
              </div>

              {/* Detection Date */}
              <div className="space-y-2">
                <label className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{t('admin.incidents.ai.detection_date', 'Detection Date')}</label>
                <Input
                  type="date"
                  value={incidentData.detectionDate}
                  onChange={(e) => handleInputChange('detectionDate', e.target.value)}
                  className={`w-full ${isDarkMode
                    ? 'bg-slate-900/50 border-slate-700 focus:border-red-400 text-gray-100'
                    : 'bg-gray-50/80 border-gray-300 focus:border-red-500 text-gray-800'}`}
                />
              </div>

              {/* Occurrence Date */}
              <div className="space-y-2">
                <label className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{t('admin.incidents.ai.occurrence_date', 'Occurrence Date')}</label>
                <Input
                  type="date"
                  value={incidentData.occurrenceDate}
                  onChange={(e) => handleInputChange('occurrenceDate', e.target.value)}
                  className={`w-full ${isDarkMode
                    ? 'bg-slate-900/50 border-slate-700 focus:border-red-400 text-gray-100'
                    : 'bg-gray-50/80 border-gray-300 focus:border-red-500 text-gray-800'}`}
                />
              </div>

              {/* Impact */}
              <div className="space-y-2">
                <label className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{t('admin.incidents.ai.impact', 'Impact')}</label>
                <Select value={incidentData.impact} onValueChange={(value) => handleInputChange('impact', value)}>
                  <SelectTrigger className={`${isDarkMode
                    ? 'bg-slate-900/50 border-slate-700 focus:border-red-400 text-gray-100'
                    : 'bg-gray-50/80 border-gray-300 focus:border-red-500 text-gray-800'}`}>
                    <SelectValue placeholder={t('admin.incidents.ai.select_impact_level', 'Select impact level')} />
                  </SelectTrigger>
                  <SelectContent className={`${isDarkMode ? 'bg-slate-800 border-slate-700 text-gray-100' : 'bg-white border-gray-200 text-gray-800'}`}>
                    <SelectItem value="Very Low">{t('admin.incidents.ai.very_low', 'Very Low')}</SelectItem>
                    <SelectItem value="Low">{t('admin.incidents.ai.low', 'Low')}</SelectItem>
                    <SelectItem value="Medium">{t('admin.incidents.ai.medium', 'Medium')}</SelectItem>
                    <SelectItem value="High">{t('admin.incidents.ai.high', 'High')}</SelectItem>
                    <SelectItem value="Very High">{t('admin.incidents.ai.very_high', 'Very High')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Priority */}
              <div className="space-y-2">
                <label className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{t('admin.incidents.ai.priority', 'Priority')}</label>
                <Select value={incidentData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                  <SelectTrigger className={`${isDarkMode
                    ? 'bg-slate-900/50 border-slate-700 focus:border-red-400 text-gray-100'
                    : 'bg-gray-50/80 border-gray-300 focus:border-red-500 text-gray-800'}`}>
                    <SelectValue placeholder={t('admin.incidents.ai.select_priority_level', 'Select priority level')} />
                  </SelectTrigger>
                  <SelectContent className={`${isDarkMode ? 'bg-slate-800 border-slate-700 text-gray-100' : 'bg-white border-gray-200 text-gray-800'}`}>
                    <SelectItem value="Low">{t('admin.incidents.ai.low', 'Low')}</SelectItem>
                    <SelectItem value="Medium">{t('admin.incidents.ai.medium', 'Medium')}</SelectItem>
                    <SelectItem value="High">{t('admin.incidents.ai.high', 'High')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Nature */}
              <div className="space-y-2">
                <label className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{t('admin.incidents.ai.nature', 'Nature')}</label>
                <Input
                  value={incidentData.nature}
                  onChange={(e) => handleInputChange('nature', e.target.value)}
                  placeholder={t('admin.incidents.ai.nature_placeholder', 'Incident nature')}
                  className={`w-full ${isDarkMode
                    ? 'bg-slate-900/50 border-slate-700 focus:border-red-400 text-gray-100'
                    : 'bg-gray-50/80 border-gray-300 focus:border-red-500 text-gray-800'}`}
                />
              </div>

              {/* Description */}
              <div className="space-y-2 md:col-span-2">
                <label className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>{t('admin.incidents.ai.description', 'Description')}</label>
                <Textarea
                  value={incidentData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder={t('admin.incidents.ai.description_placeholder', 'Incident description')}
                  className={`min-h-[100px] w-full ${isDarkMode
                    ? 'bg-slate-900/50 border-slate-700 focus:border-red-400 text-gray-100'
                    : 'bg-gray-50/80 border-gray-300 focus:border-red-500 text-gray-800'} resize-none`}
                />
              </div>
            </div>

            <div className="flex justify-end gap-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/admin/incident')}
                className={`px-6 ${isDarkMode
                  ? 'border-slate-600/50 bg-slate-800/50 text-slate-300 hover:bg-slate-700/50 hover:border-slate-500 hover:text-slate-200 shadow-slate-900/10'
                  : 'border-gray-300 bg-white/80 text-gray-600 hover:bg-gray-100 hover:border-gray-400 hover:text-gray-800 shadow-gray-200/30'}
                  backdrop-blur-sm shadow-lg`}
              >
                {t('admin.incidents.ai.cancel', 'Cancel')}
              </Button>
              <Button
                type="submit"
                className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 px-6 shadow-lg shadow-red-700/20 transition-all duration-300 hover:shadow-red-700/40 border-0"
              >
                {t('admin.incidents.ai.create_incident', 'Create Incident')}
              </Button>
            </div>
          </form>
        </div>
      </div>
      </div>
    </div>
  );
}

export default IncidentAI;
