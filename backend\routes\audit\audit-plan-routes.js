const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllAuditPlans,
  createAuditPlan,
  getAuditPlanById,
  updateAuditPlan,
  deleteAuditPlan
} = require('../../controllers/audit/audit-plan-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all audit plans - both Audit Director and Auditor can access
router.get('/', authorizeRoles(['audit_director', 'auditor']),getAllAuditPlans);

// Create new audit plan - only Audit Director can access
router.post('/', authorizeRoles(['audit_director']), createAuditPlan);

// Get audit plan by ID - both Audit Director and Auditor can access
router.get('/:id', authorizeR<PERSON>s(['audit_director', 'auditor']),getAuditPlanById);

// Update audit plan - only Audit Director can access
router.put('/:id', authorizeRoles(['audit_director']), updateAuditPlan);

// Delete audit plan - only Audit Director can access
router.delete('/:id', authorizeRoles(['audit_director']), deleteAuditPlan);

module.exports = router;

