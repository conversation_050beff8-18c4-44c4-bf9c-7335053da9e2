import React, { useState, useEffect, useCallback } from 'react';
import { ChevronDown, ChevronUp, Clock, Settings, FileText, Activity, ArrowLeft, ArrowRight, Loader, Trash2, Link, Database } from 'lucide-react';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import { getApiBaseUrl } from "@/utils/api-config";

// API Base URL
const API_BASE_URL = getApiBaseUrl();

export default function ControlFilActivite() {
  const [isOpen, setIsOpen] = useState(true);
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);
  const { id: controlId } = useParams();

  const fetchActivities = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.get(
        `${API_BASE_URL}/controls/${controlId}/activity`,
        { withCredentials: true }
      );

      if (response.data && response.data.success) {
        setActivities(response.data.data || []);
        setCurrentPage(1); // Reset to first page when new data is fetched
      } else {
        setError("Échec du chargement des activités");
      }
    } catch (err) {
      console.error("Error fetching control activities:", err);
      setError(err.response?.data?.message || "Échec du chargement des activités");
    } finally {
      setLoading(false);
    }
  }, [controlId]);

  // Fetch activities on component mount or when controlId changes
  useEffect(() => {
    if (controlId) {
      fetchActivities();
    }
  }, [controlId, fetchActivities]);

  // Get current items for pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = activities.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(activities.length / itemsPerPage);

  // Change page
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Function to get the appropriate icon for an activity type
  const getActivityIcon = (type) => {
    switch(type) {
      case 'creation':
        return <FileText className="h-5 w-5 text-green-500" />;
      case 'update':
        return <Settings className="h-5 w-5 text-blue-500" />;
      case 'deletion':
        return <Trash2 className="h-5 w-5 text-red-500" />;
      case 'link':
        return <Link className="h-5 w-5 text-purple-500" />;
      case 'unlink':
        return <Database className="h-5 w-5 text-orange-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  // Function to format date nicely in French
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Function to get French field names
  const getFieldDisplayName = (fieldName) => {
    const fieldNames = {
      'name': 'Nom',
      'code': 'Code',
      'objective': 'Objectif',
      'executionProcedure': 'Procédure d\'Exécution',
      'controlKey': 'Contrôle Clé',
      'controlExecutionMethod': 'Méthode d\'Exécution du Contrôle',
      'organizationalLevel': 'Niveau Organisationnel',
      'sampleType': 'Type d\'Échantillon',
      'testingFrequency': 'Fréquence des Tests',
      'testingMethod': 'Méthode de Test',
      'testingProcedure': 'Procédure de Test',
      'controlType': 'Type de Contrôle',
      'businessProcess': 'Processus Métier',
      'organizationalProcess': 'Processus Organisationnel',
      'operation': 'Opération',
      'application': 'Application',
      'entity': 'Entité',
      'risk': 'Risque Associé',
      'actionPlan': 'Plan d\'Action',
      'campagne': 'Campagne',
      'comment': 'Commentaire'
    };
    return fieldNames[fieldName] || fieldName;
  };

  // Render activity item
  const renderActivityItem = (activity) => {
    return (
      <div key={activity.id} className="mb-4 border-b pb-4 last:border-b-0">
        <div className="flex items-start">
          <div className="mr-3 mt-1">
            {getActivityIcon(activity.type)}
          </div>
          <div className="flex-1">
            <div className="text-sm text-gray-500 mb-1">{formatDate(activity.timestamp)}</div>
            <div className="font-medium mb-1">
              {activity.user} {getActivityText(activity)}
            </div>
            {activity.type === 'update' && activity.field && (
              <div className="text-sm bg-gray-50 p-2 rounded mt-1">
                <span className="font-medium">{getFieldDisplayName(activity.field)}:</span>{" "}
                {activity.oldValue !== undefined && (
                  <span className="line-through mr-2 text-red-600">
                    {activity.oldValue || 'Vide'}
                  </span>
                )}
                {activity.newValue !== undefined && (
                  <span className="text-green-600 font-medium">
                    {activity.newValue || 'Vide'}
                  </span>
                )}
              </div>
            )}
            {(activity.type === 'link' || activity.type === 'unlink') && activity.field && (
              <div className="text-sm bg-blue-50 p-2 rounded mt-1">
                <span className="font-medium">{getFieldDisplayName(activity.field)}</span>
                {activity.linkedValue && (
                  <span className="ml-2 text-blue-600">
                    → {activity.linkedValue}
                  </span>
                )}
              </div>
            )}
            {activity.details && (
              <div className="text-sm text-gray-600 mt-1">{activity.details}</div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Get text based on activity type in French
  const getActivityText = (activity) => {
    switch(activity.type) {
      case 'creation':
        return 'a créé ce contrôle';
      case 'update':
        return 'a modifié le contrôle';
      case 'deletion':
        return 'a supprimé le contrôle';
      case 'link':
        return 'a lié une donnée de référence';
      case 'unlink':
        return 'a délié une donnée de référence';
      default:
        return 'a effectué une action';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <Activity className="h-6 w-6 mr-3 text-[#F62D51]" />
          Fil d'activité
        </h2>
      </div>

      <div className="border rounded-lg">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gray-50 rounded-t-lg"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center gap-2">
            {isOpen ? (
              <ChevronUp className="h-5 w-5" />
            ) : (
              <ChevronDown className="h-5 w-5" />
            )}
            <span className="text-lg font-medium">Fil d'Activité</span>
          </div>
        </button>

        {isOpen && (
          <div className="p-4">
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader className="h-6 w-6 animate-spin mr-2" />
                <span>Chargement des activités...</span>
              </div>
            ) : error ? (
              <div className="text-red-500 py-4">{error}</div>
            ) : activities.length === 0 ? (
              <div className="text-gray-500 py-4">Aucune activité enregistrée pour le moment.</div>
            ) : (
              <>
                <div className="space-y-2">
                  {currentItems.map(renderActivityItem)}
                </div>

                {/* Pagination controls */}
                {totalPages > 1 && (
                  <div className="flex justify-between items-center mt-6 pt-4 border-t">
                    <button
                      onClick={goToPreviousPage}
                      disabled={currentPage === 1}
                      className={`flex items-center ${
                        currentPage === 1
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-blue-500 hover:text-blue-700'
                      }`}
                    >
                      <ArrowLeft className="h-4 w-4 mr-1" />
                      Précédent
                    </button>
                    <span className="text-sm text-gray-600">
                      Page {currentPage} sur {totalPages}
                    </span>
                    <button
                      onClick={goToNextPage}
                      disabled={currentPage === totalPages}
                      className={`flex items-center ${
                        currentPage === totalPages
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-blue-500 hover:text-blue-700'
                      }`}
                    >
                      Suivant
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
