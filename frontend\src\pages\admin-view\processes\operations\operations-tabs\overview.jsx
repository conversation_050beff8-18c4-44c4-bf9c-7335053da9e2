import { useOutletContext } from "react-router-dom";
import { Tag, Calendar, Hash, FileText, Workflow } from "lucide-react";
import { useEffect, useState } from "react";
import axios from "axios";

function OperationsOverview() {
  const { operation, organizationalProcesses } = useOutletContext();
  const [parentOrgProcess, setParentOrgProcess] = useState(null);

  // Find parent organizational process
  useEffect(() => {
    if (operation?.parentOrganizationalProcess && organizationalProcesses?.length) {
      const parent = organizationalProcesses.find(
        process => process.organizationalProcessID === operation.parentOrganizationalProcess
      );
      setParentOrgProcess(parent);
    } else {
      setParentOrgProcess(null);
    }
  }, [operation, organizationalProcesses]);

  // Helper function to format dates
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-GB", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Helper function to render values with N/A styled in grey
  const renderValue = (value) => {
    if (value === "N/A" || value === null || value === undefined) {
      return <span className="text-gray-400">N/A</span>;
    }
    return value;
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6">
        <div>
          <h3 className="text-lg font-semibold text-[#242A33] mb-4">Operation Details</h3>
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Tag className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Name</p>
                <p className="font-medium">{renderValue(operation.name || "N/A")}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Hash className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Code</p>
                <p className="font-medium">{renderValue(operation.code || "N/A")}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Workflow className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Parent Organizational Process</p>
                <p className="font-medium">{renderValue(parentOrgProcess ? parentOrgProcess.name : "None")}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <FileText className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Comment</p>
                <p className="font-medium">{renderValue(operation.comment || "N/A")}</p>
              </div>
            </div>

            {operation.createdAt && (
              <div className="flex items-start gap-4">
                <div className="bg-gray-50 p-2 rounded-lg">
                  <Calendar className="h-5 w-5 text-[#555F6D]" />
                </div>
                <div>
                  <p className="text-sm text-[#555F6D]">Created At</p>
                  <p className="font-medium">{renderValue(formatDate(operation.createdAt))}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default OperationsOverview;
