import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Title,
  <PERSON><PERSON><PERSON>ooter,
} from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
import { Search } from "lucide-react";
import { useState } from "react";

export function ApplicationSelectionModal({ open, onClose, onSelect, applications = [] }) {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredApplications = applications.filter((application) =>
    Object.values(application).some((value) =>
      value && value.toString().toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-[90vw] md:max-w-[700px] max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Sélectionner une Application</DialogTitle>
          <p className="text-sm text-gray-500">
            <PERSON><PERSON><PERSON><PERSON>nez une application à associer à ce contrôle.
          </p>
          <div className="relative mt-4">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Rechercher des applications..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:border-[#F62D51]"
            />
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto mt-4 pr-2">
          <div className="space-y-2">
            {filteredApplications.length > 0 ? (
              filteredApplications.map((application) => (
                <div
                  key={application.applicationID || application.id}
                  className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    onSelect(application);
                    onClose();
                  }}
                >
                  <div className="flex justify-between items-center">
                    <div className="flex-1">
                      <h3 className="font-medium">{application.name}</h3>
                      {application.description && (
                        <p className="text-sm text-gray-500">{application.description}</p>
                      )}
                    </div>
                    {application.code && (
                      <div className="text-xs text-gray-400">
                        Code: {application.code}
                      </div>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="p-4 border rounded-lg text-center text-gray-500">
                Aucune application trouvée correspondant à vos critères de recherche.
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="mt-4">
          <Button type="button" variant="outline" onClick={onClose}>
            Annuler
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
