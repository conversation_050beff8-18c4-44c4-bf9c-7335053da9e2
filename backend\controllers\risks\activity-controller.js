const db = require('../../models');
const { Op } = require('sequelize');

/**
 * Get all activities for a specific risk
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getRiskActivities = async (req, res) => {
  try {
    const { id: riskId } = req.params;

    // Validate risk exists
    const risk = await db.Risk.findByPk(riskId);
    if (!risk) {
      return res.status(404).json({
        success: false,
        message: 'Risk not found'
      });
    }

    // Get workflow events from Events table
    const workflowEvents = await db.Event.findAll({
      where: { risk_id: riskId },
      attributes: ['id', 'timestamp', 'user', 'step', 'transition', 'message'],
      order: [['timestamp', 'DESC']]
    });

    // Get activity logs from ActivityLog table
    const activityLogs = await db.ActivityLog.findAll({
      where: { risk_id: riskId },
      attributes: ['id', 'timestamp', 'user', 'type', 'field', 'old_value', 'new_value', 'details'],
      order: [['timestamp', 'DESC']]
    });

    // Format workflow events as activity items
    const workflowActivities = workflowEvents.map(event => ({
      id: `event-${event.id}`,
      type: 'transition',
      timestamp: event.timestamp,
      user: event.user,
      details: event.transition === 'Create' 
        ? 'Risk created' 
        : `${event.transition} to ${event.step}`
    }));

    // Format activity logs as activity items
    const logActivities = activityLogs.map(log => ({
      id: `log-${log.id}`,
      type: log.type,
      timestamp: log.timestamp,
      user: log.user,
      field: log.field,
      oldValue: log.old_value,
      newValue: log.new_value,
      details: log.details
    }));

    // Combine and sort all activities by timestamp (newest first)
    const allActivities = [...workflowActivities, ...logActivities]
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    return res.status(200).json({
      success: true,
      data: allActivities
    });
  } catch (error) {
    console.error('Error in getRiskActivities:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve risk activities',
      error: error.message
    });
  }
};

/**
 * Log a creation activity
 * @param {Object} risk - The created risk object
 * @param {String} username - Username who created the risk
 */
exports.logCreationActivity = async (risk, username) => {
  try {
    await db.ActivityLog.create({
      risk_id: risk.riskID,
      user: username,
      type: 'creation',
      details: 'Risk created'
    });
    console.log(`Creation activity logged for risk ${risk.riskID}`);
    return true;
  } catch (error) {
    console.error('Error logging creation activity:', error);
    return false;
  }
};

/**
 * Log update activities by comparing old and new risk values
 * @param {Object} oldRisk - Risk object before update
 * @param {Object} newData - New data being applied to the risk
 * @param {String} username - Username who updated the risk
 */
exports.logUpdateActivities = async (oldRisk, newData, username) => {
  try {
    const changes = [];
    
    // Define which fields to track and their display names
    const trackableFields = {
      name: 'Title',
      code: 'Code',
      impact: 'Impact',
      probability: 'Probability',
      DMR: 'DMR',
      appetite: 'Appetite',
      comment: 'Comment',
      mitigatingActionPlan: 'Mitigating action plan',
      acceptance: 'Acceptance strategy',
      avoidance: 'Avoidance strategy',
      insurance: 'Insurance strategy',
      reduction: 'Reduction strategy',
      major: 'Major risk flag',
      createdAt: 'Creation date',
      updatedAt: 'Last update date',
      dueDate: 'Due date',
      completionDate: 'Completion date'
    };

    // Check each trackable field for changes
    for (const [field, displayName] of Object.entries(trackableFields)) {
      if (newData[field] !== undefined) {
        // Format boolean values for better readability
        let oldValue = oldRisk[field];
        let newValue = newData[field];
        
        if (typeof oldValue === 'boolean') {
          oldValue = oldValue ? 'Yes' : 'No';
        }
        
        if (typeof newValue === 'boolean') {
          newValue = newValue ? 'Yes' : 'No';
        }
        
        // Format dates for better readability and consistent comparison
        if ((field.includes('Date') || field === 'createdAt' || field === 'updatedAt') && oldValue) {
          oldValue = new Date(oldValue).toISOString().split('T')[0];
        }
        
        if ((field.includes('Date') || field === 'createdAt' || field === 'updatedAt') && newValue) {
          newValue = new Date(newValue).toISOString().split('T')[0];
        }
        
        // Only log if there's an actual change after normalization
        if (String(oldValue) !== String(newValue)) {
          // Create activity log entry
          await db.ActivityLog.create({
            risk_id: oldRisk.riskID,
            user: username,
            type: 'update',
            field: field,
            old_value: String(oldValue || ''),
            new_value: String(newValue || ''),
            details: `${displayName} changed from '${oldValue || ''}' to '${newValue || ''}'`
          });
          
          changes.push(field);
        }
      }
    }
    
    // Special handling for reference fields that use IDs
    const referenceFields = {
      entityID: 'Entity',
      riskTypeID: 'Risk Type',
      businessProcessID: 'Business Process',
      organizationalProcessID: 'Organizational Process',
      operationID: 'Operation',
      applicationID: 'Application',
      controlID: 'Control'
    };
    
    for (const [field, displayName] of Object.entries(referenceFields)) {
      if (newData[field] !== undefined && oldRisk[field] !== newData[field]) {
        await db.ActivityLog.create({
          risk_id: oldRisk.riskID,
          user: username,
          type: 'update',
          field: field,
          old_value: oldRisk[field] || '',
          new_value: newData[field] || '',
          details: `${displayName} reference updated`
        });
        
        changes.push(field);
      }
    }
    
    console.log(`Logged ${changes.length} changes for risk ${oldRisk.riskID}`);
    return changes.length > 0;
  } catch (error) {
    console.error('Error logging update activities:', error);
    return false;
  }
};

/**
 * Log a general activity for a risk
 * @param {Object} activityData - Activity data including userId, action, entityType, entityId, details
 * @param {Object} options - Additional options like transaction
 */
exports.logActivity = async (activityData, options = {}) => {
  try {
    const { userId, action, entityType, entityId, details } = activityData;
    
    // Find username if userId is provided
    let username = 'System';
    if (userId) {
      const user = await db.User.findByPk(userId, { transaction: options.transaction });
      if (user) {
        username = user.username || user.email || `User ID: ${userId}`;
      }
    }
    
    // Map custom actions to existing enum values
    let type = action.toLowerCase();
    if (type === 'assign_contributor' || type === 'remove_contributor') {
      type = 'update'; // Map to an existing enum value
    }
    
    // Create activity log entry for risks
    await db.ActivityLog.create({
      risk_id: entityId,
      user: username,
      type: type,
      details: details || `${action} performed`,
      timestamp: new Date()
    }, { transaction: options.transaction });
    
    return true;
  } catch (error) {
    console.error('Error logging activity:', error);
    return false;
  }
}; 