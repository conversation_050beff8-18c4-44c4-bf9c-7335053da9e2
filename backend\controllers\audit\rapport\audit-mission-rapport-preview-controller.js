const { sequelize } = require('../../../models/index');
const AuditMissionRapport = require('../../../models/Audit/rapport/auditmissionrapport')(sequelize);
const PDFDocument = require('pdfkit');
const pdfParse = require('pdf-parse');
const streamBuffers = require('stream-buffers');
const axios = require('axios');

const escapeText = (str) => {
  if (typeof str !== 'string') return str || 'N/A';
  return str.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
};

exports.previewMissionReportPdf = async (req, res) => {
  const { missionId } = req.params;

  // Set CORS headers immediately
  res.set({
    'Access-Control-Allow-Origin': 'http://localhost:5173',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, x-user-id',
    'Access-Control-Allow-Credentials': 'true'
  });

  try {
    const reportData = await AuditMissionRapport.getMissionReport(missionId);
    if (!reportData || reportData.length === 0) {
      return res.status(404).json({ message: 'No data found for the specified mission ID' });
    }

    const autresParticipants = [
      ...new Set(
        reportData
          .flatMap(row => [row.activity_responsable, row.constat_responsable])
          .filter(id => id !== null && id !== undefined)
      ),
    ].join(', ');

    const auditedElements = [
      reportData[0].risk_names,
      reportData[0].entity_names,
      reportData[0].control_names,
      reportData[0].incident_names,
      reportData[0].organizational_process_names,
    ]
      .filter(name => name)
      .join(', ');

    let missionName = reportData[0].mission_name || missionId;
    missionName = missionName.replace(/[^a-zA-Z0-9_-]/g, '_');

    // Set PDF headers for inline preview
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `inline; filename=mission_audit_report_${missionName}.pdf`);

    const doc = new PDFDocument({ margin: 50 });
    doc.pipe(res);

    // Title
    doc.fontSize(16).font('Helvetica-Bold').text('Rapport de Mission d\'Audit', { align: 'center' });
    doc.moveDown(2);

    // Section 1: Diffusion
    doc.fontSize(14).text('1. Diffusion', { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Chef de mission: ${escapeText(reportData[0].chefmission)}`);
    doc.text(`Directeur d'audit: ${escapeText(reportData[0].directeuraudit)}`);
    doc.text(`Autre(s) Participant(s): ${escapeText(autresParticipants)}`);
    doc.moveDown();

    // Section 2: Résumé
    doc.fontSize(14).font('Helvetica-Bold').text('2. Résumé', { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Mission d'audit: ${escapeText(reportData[0].mission_name)}`);
    doc.text(`Catégorie: ${escapeText(reportData[0].categorie)}`);
    doc.text(`Évaluation: ${escapeText(reportData[0].evaluation)}`);
    doc.moveDown();

    doc.fontSize(12).font('Helvetica-Bold').text('2.1 Objectif de la mission d\'Audit');
    doc.fontSize(12).font('Helvetica').text(escapeText(reportData[0].objectif), { indent: 20 });
    doc.moveDown();

    doc.fontSize(12).font('Helvetica-Bold').text('2.2 Points forts');
    doc.fontSize(12).font('Helvetica').text(`La mission a permis de constater que : ${escapeText(reportData[0].pointfort)}`, { indent: 20 });
    doc.moveDown();

    doc.fontSize(12).font('Helvetica-Bold').text('2.3 Points faibles');
    doc.fontSize(12).font('Helvetica').text(`Une faiblesse majeure a été relevée : ${escapeText(reportData[0].pointfaible)}`, { indent: 20 });
    doc.moveDown();

    // Section 3: Contexte
    doc.fontSize(14).font('Helvetica-Bold').text('3. Contexte', { underline: true });
    doc.moveDown(0.5);

    doc.fontSize(12).font('Helvetica-Bold').text('3.1 Managers opérationnels audités');
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Élément audité: ${escapeText(auditedElements)}`);
    doc.text(`Propriétaire: ${escapeText(reportData[0].recommendation_responsable)}`);
    doc.moveDown();

    doc.fontSize(12).font('Helvetica-Bold').text('3.2 Coûts de la mission d\'Audit');
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Charge de travail estimée (Heures): ${escapeText(String(reportData[0].chargedetravailestimee))}`);
    doc.text(`Charge de travail effective (Heures): ${escapeText(String(reportData[0].chargedetravaileffective))}`);
    doc.text(`Total des dépenses: ${escapeText(String(reportData[0].depenses))}`);
    doc.moveDown();

    doc.fontSize(12).font('Helvetica-Bold').text('3.3 Ressources de la mission d\'Audit');
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Chef de mission: ${escapeText(reportData[0].chefmission)}`);
    doc.text(`Principal audité: ${escapeText(reportData[0].principalAudite)}`);
    doc.text(`Audité(s): ${escapeText(auditedElements)}`);
    doc.text(`Autre(s) Participant(s): ${escapeText(autresParticipants)}`);
    doc.moveDown();

    // Section 4: Avis
    doc.fontSize(14).font('Helvetica-Bold').text('4. Avis concernant la mission d\'Audit', { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Les objectifs de la mission d'Audit sont : ${escapeText(reportData[0].objectif)}`);
    doc.text('Les sections suivantes présentent les détails du résultat de la mission d\'Audit.');
    doc.moveDown();

    doc.fontSize(12).font('Helvetica-Bold').text('4.1 Points forts');
    doc.fontSize(12).font('Helvetica').text(`La mission a permis de constater que : ${escapeText(reportData[0].pointfort)}`, { indent: 20 });
    doc.moveDown();

    doc.fontSize(12).font('Helvetica-Bold').text('4.2 Points faibles');
    doc.fontSize(12).font('Helvetica').text(`Une faiblesse majeure a été relevée : ${escapeText(reportData[0].pointfaible)}`, { indent: 20 });
    doc.moveDown();

    // Section 5: Constats
    doc.fontSize(14).font('Helvetica-Bold').text('5. Constats', { underline: true });
    doc.moveDown(0.5);

    doc.fontSize(12).font('Helvetica-Bold').text('5.1 Constats urgents');
    doc.moveDown(0.5);
    const urgentFindings = reportData.filter(row => row.constat_impact === 'très fort');
    if (urgentFindings.length === 0) {
      doc.fontSize(12).font('Helvetica').text('Aucun constat urgent trouvé.');
    } else {
      urgentFindings.forEach(row => {
        doc.fontSize(12).font('Helvetica').text(`Constat: ${escapeText(row.constat_name)}`);
        doc.text(`Impact: ${escapeText(row.constat_impact)}`);
        doc.moveDown(0.5);
      });
    }
    doc.moveDown();

    doc.fontSize(12).font('Helvetica-Bold').text('5.2 Détails des constats et recommandations');
    doc.moveDown(0.5);
    const findings = reportData.filter(row => row.constat_name);
    findings.forEach(row => {
      doc.fontSize(12).font('Helvetica');
      doc.text(`Constat: ${escapeText(row.constat_name)}`);
      doc.text(`Impact: ${escapeText(row.constat_impact)}`);
      doc.text(`Recommandation: ${escapeText(row.recommendation_name)}`);
      doc.text(`Détails: ${escapeText(row.recommendation_details)}`);
      doc.text(`Propriétaire: ${escapeText(row.recommendation_responsable)}`);
      doc.text(`Date de fin: ${escapeText(row.datefin)}`);
      doc.moveDown(0.5);
    });

    // Finalize the PDF document
    doc.end();
  } catch (pdfError) {
    console.error('[AuditMissionRapportPreview] PDF generation error:', pdfError);
    res.status(500).json({ message: 'Failed to generate PDF for preview', error: pdfError.message });
  }
};

exports.extractMissionReportPdfText = async (req, res) => {
  const { missionId } = req.params;
  res.set({
    'Access-Control-Allow-Origin': 'http://localhost:5173',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, x-user-id',
    'Access-Control-Allow-Credentials': 'true'
  });
  try {
    const reportData = await AuditMissionRapport.getMissionReport(missionId);
    if (!reportData || reportData.length === 0) {
      return res.status(404).json({ message: 'No data found for the specified mission ID' });
    }
    const autresParticipants = [
      ...new Set(
        reportData
          .flatMap(row => [row.activity_responsable, row.constat_responsable])
          .filter(id => id !== null && id !== undefined)
      ),
    ].join(', ');
    const auditedElements = [
      reportData[0].risk_names,
      reportData[0].entity_names,
      reportData[0].control_names,
      reportData[0].incident_names,
      reportData[0].organizational_process_names,
    ]
      .filter(name => name)
      .join(', ');
    let missionName = reportData[0].mission_name || missionId;
    missionName = missionName.replace(/[^a-zA-Z0-9_-]/g, '_');
    // Generate PDF in memory
    const doc = new PDFDocument({ margin: 50 });
    const writableBuffer = new streamBuffers.WritableStreamBuffer();
    doc.pipe(writableBuffer);
    // (Copy the same PDF content logic as previewMissionReportPdf)
    doc.fontSize(16).font('Helvetica-Bold').text('Rapport de Mission d\'Audit', { align: 'center' });
    doc.moveDown(2);
    doc.fontSize(14).text('1. Diffusion', { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Chef de mission: ${escapeText(reportData[0].chefmission)}`);
    doc.text(`Directeur d'audit: ${escapeText(reportData[0].directeuraudit)}`);
    doc.text(`Autre(s) Participant(s): ${escapeText(autresParticipants)}`);
    doc.moveDown();
    doc.fontSize(14).font('Helvetica-Bold').text('2. Résumé', { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Mission d'audit: ${escapeText(reportData[0].mission_name)}`);
    doc.text(`Catégorie: ${escapeText(reportData[0].categorie)}`);
    doc.text(`Évaluation: ${escapeText(reportData[0].evaluation)}`);
    doc.moveDown();
    doc.fontSize(12).font('Helvetica-Bold').text('2.1 Objectif de la mission d\'Audit');
    doc.fontSize(12).font('Helvetica').text(escapeText(reportData[0].objectif), { indent: 20 });
    doc.moveDown();
    doc.fontSize(12).font('Helvetica-Bold').text('2.2 Points forts');
    doc.fontSize(12).font('Helvetica').text(`La mission a permis de constater que : ${escapeText(reportData[0].pointfort)}`, { indent: 20 });
    doc.moveDown();
    doc.fontSize(12).font('Helvetica-Bold').text('2.3 Points faibles');
    doc.fontSize(12).font('Helvetica').text(`Une faiblesse majeure a été relevée : ${escapeText(reportData[0].pointfaible)}`, { indent: 20 });
    doc.moveDown();
    doc.fontSize(14).font('Helvetica-Bold').text('3. Contexte', { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica-Bold').text('3.1 Managers opérationnels audités');
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Élément audité: ${escapeText(auditedElements)}`);
    doc.text(`Propriétaire: ${escapeText(reportData[0].recommendation_responsable)}`);
    doc.moveDown();
    doc.fontSize(12).font('Helvetica-Bold').text('3.2 Coûts de la mission d\'Audit');
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Charge de travail estimée (Heures): ${escapeText(String(reportData[0].chargedetravailestimee))}`);
    doc.text(`Charge de travail effective (Heures): ${escapeText(String(reportData[0].chargedetravaileffective))}`);
    doc.text(`Total des dépenses: ${escapeText(String(reportData[0].depenses))}`);
    doc.moveDown();
    doc.fontSize(12).font('Helvetica-Bold').text('3.3 Ressources de la mission d\'Audit');
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Chef de mission: ${escapeText(reportData[0].chefmission)}`);
    doc.text(`Principal audité: ${escapeText(reportData[0].principalAudite)}`);
    doc.text(`Audité(s): ${escapeText(auditedElements)}`);
    doc.text(`Autre(s) Participant(s): ${escapeText(autresParticipants)}`);
    doc.moveDown();
    doc.fontSize(14).font('Helvetica-Bold').text('4. Avis concernant la mission d\'Audit', { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Les objectifs de la mission d'Audit sont : ${escapeText(reportData[0].objectif)}`);
    doc.text('Les sections suivantes présentent les détails du résultat de la mission d\'Audit.');
    doc.moveDown();
    doc.fontSize(12).font('Helvetica-Bold').text('4.1 Points forts');
    doc.fontSize(12).font('Helvetica').text(`La mission a permis de constater que : ${escapeText(reportData[0].pointfort)}`, { indent: 20 });
    doc.moveDown();
    doc.fontSize(12).font('Helvetica-Bold').text('4.2 Points faibles');
    doc.fontSize(12).font('Helvetica').text(`Une faiblesse majeure a été relevée : ${escapeText(reportData[0].pointfaible)}`, { indent: 20 });
    doc.moveDown();
    doc.fontSize(14).font('Helvetica-Bold').text('5. Constats', { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica-Bold').text('5.1 Constats urgents');
    doc.moveDown(0.5);
    const urgentFindings = reportData.filter(row => row.constat_impact === 'très fort');
    if (urgentFindings.length === 0) {
      doc.fontSize(12).font('Helvetica').text('Aucun constat urgent trouvé.');
    } else {
      urgentFindings.forEach(row => {
        doc.fontSize(12).font('Helvetica').text(`Constat: ${escapeText(row.constat_name)}`);
        doc.text(`Impact: ${escapeText(row.constat_impact)}`);
        doc.moveDown(0.5);
      });
    }
    doc.moveDown();
    doc.fontSize(12).font('Helvetica-Bold').text('5.2 Détails des constats et recommandations');
    doc.moveDown(0.5);
    const findings = reportData.filter(row => row.constat_name);
    findings.forEach(row => {
      doc.fontSize(12).font('Helvetica');
      doc.text(`Constat: ${escapeText(row.constat_name)}`);
      doc.text(`Impact: ${escapeText(row.constat_impact)}`);
      doc.text(`Recommandation: ${escapeText(row.recommendation_name)}`);
      doc.text(`Détails: ${escapeText(row.recommendation_details)}`);
      doc.text(`Propriétaire: ${escapeText(row.recommendation_responsable)}`);
      doc.text(`Date de fin: ${escapeText(row.datefin)}`);
      doc.moveDown(0.5);
    });
    doc.end();
    writableBuffer.on('finish', async () => {
      try {
        const pdfBuffer = writableBuffer.getContents();
        const data = await pdfParse(pdfBuffer);
        res.json({ text: data.text });
      } catch (err) {
        res.status(500).json({ message: 'Failed to extract text from PDF', error: err.message });
      }
    });
  } catch (err) {
    res.status(500).json({ message: 'Failed to generate or extract PDF text', error: err.message });
  }
};

exports.generateGeminiConversation = async (req, res) => {
  const { missionId } = req.params;
  res.set({
    'Access-Control-Allow-Origin': 'http://localhost:5173',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, x-user-id',
    'Access-Control-Allow-Credentials': 'true'
  });
  try {
    // Step 1: Extract PDF text (reuse logic)
    const reportData = await AuditMissionRapport.getMissionReport(missionId);
    if (!reportData || reportData.length === 0) {
      return res.status(404).json({ message: 'No data found for the specified mission ID' });
    }
    // Generate PDF in memory
    const doc = new PDFDocument({ margin: 50 });
    const writableBuffer = new streamBuffers.WritableStreamBuffer();
    doc.pipe(writableBuffer);
    // (Copy the same PDF content logic as previewMissionReportPdfText)
    doc.fontSize(16).font('Helvetica-Bold').text('Rapport de Mission d\'Audit', { align: 'center' });
    doc.moveDown(2);
    doc.fontSize(14).text('1. Diffusion', { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Chef de mission: ${escapeText(reportData[0].chefmission)}`);
    doc.text(`Directeur d'audit: ${escapeText(reportData[0].directeuraudit)}`);
    doc.text(`Autre(s) Participant(s): ...`); // Shorten for prompt
    doc.moveDown();
    doc.fontSize(14).font('Helvetica-Bold').text('2. Résumé', { underline: true });
    doc.moveDown(0.5);
    doc.fontSize(12).font('Helvetica');
    doc.text(`Mission d'audit: ${escapeText(reportData[0].mission_name)}`);
    doc.text(`Catégorie: ${escapeText(reportData[0].categorie)}`);
    doc.text(`Évaluation: ${escapeText(reportData[0].evaluation)}`);
    doc.moveDown();
    doc.fontSize(12).font('Helvetica-Bold').text('2.1 Objectif de la mission d\'Audit');
    doc.fontSize(12).font('Helvetica').text(escapeText(reportData[0].objectif), { indent: 20 });
    doc.moveDown();
    doc.fontSize(12).font('Helvetica-Bold').text('2.2 Points forts');
    doc.fontSize(12).font('Helvetica').text(`La mission a permis de constater que : ...`); // Shorten for prompt
    doc.moveDown();
    doc.fontSize(12).font('Helvetica-Bold').text('2.3 Points faibles');
    doc.fontSize(12).font('Helvetica').text(`Une faiblesse majeure a été relevée : ...`); // Shorten for prompt
    doc.moveDown();
    doc.end();
    // Wait for PDF buffer
    await new Promise(resolve => writableBuffer.on('finish', resolve));
    const pdfBuffer = writableBuffer.getContents();
    const data = await pdfParse(pdfBuffer);
    const pdfText = data.text;
    // Step 2: Call Gemini API
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
    if (!GEMINI_API_KEY) {
      return res.status(500).json({ message: 'GEMINI_API_KEY not set in backend .env' });
    }
    const prompt = `Voici le contenu d'un rapport d'audit :\n\n"""${pdfText.substring(0, 4000)}"""\n\nSimule une conversation en français entre deux personnes, Alice et Bob, qui discutent de ce rapport.\nLa conversation doit être naturelle, informative, et alterner entre Alice et Bob.\nPrésente la conversation sous forme de liste JSON structurée : [{speaker: 'Alice', text: '...'}, {speaker: 'Bob', text: '...'}, ...].\nNe fais pas de résumé, fais une vraie discussion.\n`;
    const geminiRes = await axios.post(
      'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
      {
        contents: [{ role: 'user', parts: [{ text: prompt }] }]
      },
      {
        params: { key: GEMINI_API_KEY },
        headers: { 'Content-Type': 'application/json' }
      }
    );
    // Parse Gemini response
    let conversation = [];
    if (geminiRes.data && geminiRes.data.candidates && geminiRes.data.candidates[0].content && geminiRes.data.candidates[0].content.parts) {
      const raw = geminiRes.data.candidates[0].content.parts[0].text;
      try {
        conversation = JSON.parse(raw);
      } catch (e) {
        // Try to extract JSON from text
        const match = raw.match(/\[.*\]/s);
        if (match) {
          conversation = JSON.parse(match[0]);
        } else {
          return res.status(500).json({ message: 'Failed to parse Gemini conversation output', raw });
        }
      }
    }
    res.json({ conversation });
  } catch (err) {
    res.status(500).json({ message: 'Failed to generate Gemini conversation', error: err.message });
  }
}; 