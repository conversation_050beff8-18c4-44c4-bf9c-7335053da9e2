const db = require('../models');

async function syncCampagnes() {
  try {
    console.log('🚀 Starting Campagnes table sync...');
    
    // Test database connection
    await db.sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Check if models are loaded
    console.log('Available models:', Object.keys(db));
    
    if (db.Campagne) {
      console.log('📋 Syncing Campagne table...');
      await db.Campagne.sync({ alter: true });
      console.log('✅ Campagne table synced successfully');
    } else {
      console.log('❌ Campagne model not found');
    }
    
    if (db.UserCampagne) {
      console.log('📋 Syncing UserCampagne table...');
      await db.UserCampagne.sync({ alter: true });
      console.log('✅ UserCampagne table synced successfully');
    } else {
      console.log('❌ UserCampagne model not found');
    }
    
    console.log('✅ All campagne tables synced successfully!');
    
  } catch (error) {
    console.error('❌ Error syncing campagne tables:', error);
  } finally {
    // Close the database connection
    await db.sequelize.close();
    console.log('Database connection closed');
  }
}

// Run the sync
syncCampagnes();
