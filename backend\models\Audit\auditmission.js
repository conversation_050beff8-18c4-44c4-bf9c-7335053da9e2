'use strict';

module.exports = (sequelize, DataTypes) => {
  const AuditMission = sequelize.define('AuditMission', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    categorie: {
      type: DataTypes.STRING,
      allowNull: true
    },
    code: {
      type: DataTypes.STRING,
      allowNull: true
    },
    etat: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'Planned'
    },
    principalAudite: {
      type: DataTypes.STRING,
      allowNull: true
    },
    objectif: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    avancement: {
      type: DataTypes.STRING,
      allowNull: true
    },
    transition: {
      type: DataTypes.STRING,
      allowNull: true
    },
    planifieInitialement: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    evaluation: {
      type: DataTypes.ENUM('Bon niveau', 'Peut être améliorée', 'Amélioration nécessaire', 'A risque'),
      allowNull: true
    },
    datedebut: {
      type: DataTypes.DATE,
      allowNull: true
    },
    datefin: {
      type: DataTypes.DATE,
      allowNull: true
    },
    pointfort: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    pointfaible: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    auditplanID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'AuditPlans',
        key: 'id'
      }
    }
  }, {
    tableName: 'AuditMissions',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['id']
      },
      {
        fields: ['auditplanID'] // For foreign key lookups
      },
      {
        fields: ['etat'] // For filtering by state
      },
      {
        fields: ['evaluation'] // For filtering by evaluation
      },
      {
        fields: ['datedebut', 'datefin'] // For date range queries
      },
      {
        fields: ['categorie'] // For filtering by category
      },
      {
        fields: ['auditplanID', 'etat'] // Composite index for plan-based queries with status
      }
    ]
  });

  AuditMission.associate = function(models) {
    // AuditMission belongs to AuditPlan
    AuditMission.belongsTo(models.AuditPlan, {
      foreignKey: 'auditplanID',
      as: 'auditPlan'
    });
    // Add this:
  AuditMission.hasMany(models.EquipeIntervenante, {
    foreignKey: 'auditMissionId',
    as: 'equipeIntervenantes'
  });

  // AuditMission has one AuditMissionRapportSupport
  AuditMission.hasOne(models.AuditMissionRapportSupport, {
    foreignKey: 'auditMissionId',
    as: 'rapportSupport'
  });
  };

  

  return AuditMission;
};