const { Sequelize } = require('sequelize');
require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });

// Create Sequelize instance
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: console.log
  }
);

async function runMigration() {
  try {
    console.log('🚀 Starting Control Question Assignment migration...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Create the table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS control_question_assignments (
        id SERIAL PRIMARY KEY,
        "userId" INTEGER NOT NULL,
        "controlId" VARCHAR(255) NOT NULL,
        "assignedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "assignedBy" INTEGER,
        "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        
        -- Foreign key constraints
        CONSTRAINT fk_control_assignments_user_id 
          FOREIGN KEY ("userId") REFERENCES "Users"(id) 
          ON UPDATE CASCADE ON DELETE CASCADE,
          
        CONSTRAINT fk_control_assignments_control_id 
          FOREIGN KEY ("controlId") REFERENCES "Control"("controlID") 
          ON UPDATE CASCADE ON DELETE CASCADE,
          
        CONSTRAINT fk_control_assignments_assigned_by 
          FOREIGN KEY ("assignedBy") REFERENCES "Users"(id) 
          ON UPDATE CASCADE ON DELETE SET NULL,
          
        -- Unique constraint to prevent duplicate assignments
        CONSTRAINT unique_user_control_assignment 
          UNIQUE ("userId", "controlId")
      );
    `);
    
    console.log('✅ control_question_assignments table created successfully.');

    // Create indexes
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_control_assignments_user_id 
      ON control_question_assignments ("userId");
    `);
    
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_control_assignments_control_id 
      ON control_question_assignments ("controlId");
    `);
    
    await sequelize.query(`
      CREATE INDEX IF NOT EXISTS idx_control_assignments_assigned_by 
      ON control_question_assignments ("assignedBy");
    `);
    
    console.log('✅ Indexes created successfully.');
    
    // Verify table structure
    const [results] = await sequelize.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'control_question_assignments'
      ORDER BY ordinal_position;
    `);
    
    console.log('📋 Table structure:');
    console.table(results);
    
    console.log('🎉 Control Question Assignment migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Run the migration
if (require.main === module) {
  runMigration()
    .then(() => {
      console.log('✅ Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = runMigration;
