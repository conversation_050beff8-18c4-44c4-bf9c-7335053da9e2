// Migration: Add dateLimite column to AuditRecommendations
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('AuditRecommendations', 'dateLimite', {
      type: Sequelize.DATE,
      allowNull: true
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('AuditRecommendations', 'dateLimite');
  }
}; 