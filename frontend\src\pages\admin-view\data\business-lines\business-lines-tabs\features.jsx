import { useState, useEffect } from "react";
import { useNavigate, useOutletContext } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Loader2 } from "lucide-react";
import { updateBusinessLine, getBusinessLineById } from "@/store/slices/businessLineSlice";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

function BusinessLinesFeatures() {
  const { businessLine } = useOutletContext();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const { isLoading } = useSelector((state) => state.businessLine);

  const [formData, setFormData] = useState({
    name: "",
    description: ""
  });

  // Set form data when business line is loaded
  useEffect(() => {
    if (businessLine) {
      setFormData({
        name: businessLine.name || "",
        description: businessLine.description || ""
      });
    }
  }, [businessLine]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const updateData = {
        businessLineID: businessLine.businessLineID,
        name: formData.name,
        description: formData.description
      };

      await dispatch(updateBusinessLine({
        id: businessLine.businessLineID,
        businessLineData: updateData
      })).unwrap();

      // Refresh the current business line data
      dispatch(getBusinessLineById(businessLine.businessLineID));
    } catch (error) {
      toast.error(error.message || t('admin.business_lines.error.update_failed', 'Failed to update business line'));
    }
  };

  const handleGoBack = () => {
    navigate("/admin/data/business-lines");
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h2 className="text-xl font-semibold mb-6">{t('admin.business_lines.features.title', 'Edit Business Line')}</h2>
      <div className="grid grid-cols-1 gap-6">
        <div className="space-y-2">
          <Label htmlFor="name">{t('admin.business_lines.form.name', 'Name')} *</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder={t('admin.business_lines.form.name_placeholder', 'Enter business line name')}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="description">{t('admin.business_lines.form.description', 'Description')}</Label>
          <Textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder={t('admin.business_lines.form.description_placeholder', 'Enter description')}
            className="min-h-[100px]"
          />
        </div>
      </div>

      <div className="flex justify-end gap-4 mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={handleGoBack}
        >
          {t('common.buttons.cancel', 'Cancel')}
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-[#F62D51] hover:bg-red-700"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('common.saving', 'Saving...')}
            </>
          ) : (
            t('common.buttons.save', 'Save Changes')
          )}
        </Button>
      </div>
    </form>
  );
}

export default BusinessLinesFeatures;