import React, { useState, useEffect } from 'react';
import { Bar } from 'react-chartjs-2';
import axios from 'axios';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const LossesByIncidentType = () => {
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await axios.get('http://localhost:5001/api/lossesByIncidentType', {
          withCredentials: true,
          headers: { 'x-user-id': '1' },
        });
        const data = response.data.data;

        const labels = data.map(item => item.incident_type_name); // Use incident_type_name for labels
        const grossLosses = data.map(item => item.gross_loss);
        const netLosses = data.map(item => item.net_loss);

        setChartData({
          labels,
          datasets: [
            {
              label: 'Perte brute',
              data: grossLosses,
              backgroundColor: '#FF0000',
              barThickness: 20,
            },
            {
              label: 'Perte nette',
              data: netLosses,
              backgroundColor: '#0000FF',
              barThickness: 20,
            },
          ],
        });
      } catch (err) {
        console.error('Error fetching losses by incident type:', err);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 20,
          boxHeight: 10,
          padding: 10,
          font: {
            size: 12,
          },
        },
      },
      title: {
        display: false,
      },
      tooltip: {
        enabled: true,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 12,
          },
        },
        title: {
          display: false,
        },
      },
      y: {
        grid: {
          color: '#E0E0E0',
        },
        ticks: {
          color: '#A0A0A0',
          font: {
            size: 12,
          },
        },
        title: {
          display: false,
        },
        beginAtZero: true,
      },
    },
    categoryPercentage: 0.6,
    barPercentage: 0.8,
  };

  if (loading) return <div className="p-6 text-center">Chargement...</div>;
  if (error) return <div className="p-6 text-center text-red-500">{error}</div>;

  return (
    <div className="p-6 bg-white rounded-lg shadow border border-gray-200">
      <Bar data={chartData} options={options} />
    </div>
  );
};

export default LossesByIncidentType;