const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllApplications,
  createApplication,
  getApplicationById,
  updateApplication,
  deleteApplication
} = require('../../controllers/data/application-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all applications
router.get('/', getAllApplications);

// Create new application
router.post('/', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), createApplication);

// Get application by ID
router.get('/:id', getApplicationById);

// Update application
router.put('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), updateApplication);

// Delete application
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteApplication);

module.exports = router;
