'use strict';

module.exports = (sequelize, DataTypes) => {
  const AuditScope = sequelize.define('AuditScope', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    auditMissionID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'AuditMissions',
        key: 'id'
      }
    },
    // We'll keep these fields for backward compatibility
    // but will also create junction tables for many-to-many relationships
    riskID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Risk',
        key: 'riskID'
      }
    },
    controlID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Control',
        key: 'controlID'
      }
    },
    entityID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Entity',
        key: 'entityID'
      }
    },
    organizationalProcessID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'OrganizationalProcess',
        key: 'organizationalProcessID'
      }
    },
    incidentID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Incident',
        key: 'incidentID'
      }
    },
    businessProcessID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'BusinessProcess',
        key: 'businessProcessID'
      }
    }
  }, {
    tableName: 'AuditScopes',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['id']
      },
      {
        fields: ['auditMissionID'] // For foreign key lookups
      },
      {
        fields: ['riskID'] // For risk lookups
      },
      {
        fields: ['controlID'] // For control lookups
      },
      {
        fields: ['entityID'] // For entity lookups
      },
      {
        fields: ['incidentID'] // For incident lookups
      },
      {
        fields: ['businessProcessID'] // For business process lookups
      }
    ]
  });

  AuditScope.associate = function(models) {
    // AuditScope belongs to AuditMission
    AuditScope.belongsTo(models.AuditMission, {
      foreignKey: 'auditMissionID',
      as: 'auditMission'
    });

    // Single relationships (backward compatibility)
    AuditScope.belongsTo(models.Risk, {
      foreignKey: 'riskID',
      as: 'risk'
    });

    AuditScope.belongsTo(models.Control, {
      foreignKey: 'controlID',
      as: 'control'
    });

    AuditScope.belongsTo(models.Entity, {
      foreignKey: 'entityID',
      as: 'entity'
    });

    AuditScope.belongsTo(models.OrganizationalProcess, {
      foreignKey: 'organizationalProcessID',
      as: 'organizationalProcess'
    });

    // Add single relationship for Incident (backward compatibility)
    if (models.Incident) {
      AuditScope.belongsTo(models.Incident, {
        foreignKey: 'incidentID',
        as: 'incident'
      });
    }

    // Add single relationship for BusinessProcess (backward compatibility)
    AuditScope.belongsTo(models.BusinessProcess, {
      foreignKey: 'businessProcessID',
      as: 'businessProcess'
    });

    // Many-to-many relationships
    AuditScope.belongsToMany(models.Risk, {
      through: 'AuditScopeRisks',
      foreignKey: 'auditScopeID',
      otherKey: 'riskID',
      as: 'risks'
    });

    AuditScope.belongsToMany(models.Control, {
      through: 'AuditScopeControls',
      foreignKey: 'auditScopeID',
      otherKey: 'controlID',
      as: 'controls'
    });

    AuditScope.belongsToMany(models.Entity, {
      through: 'AuditScopeEntities',
      foreignKey: 'auditScopeID',
      otherKey: 'entityID',
      as: 'entities'
    });

    // Make sure the model name matches exactly with what's in the database
    AuditScope.belongsToMany(models.OrganizationalProcess, {
      through: 'AuditScopeProcesses',
      foreignKey: 'auditScopeID',
      otherKey: 'organizationalProcessID',
      as: 'processes'
    });

    // For incidents, assuming you have an Incident model
    if (models.Incident) {
      AuditScope.belongsToMany(models.Incident, {
        through: 'AuditScopeIncidents',
        foreignKey: 'auditScopeID',
        otherKey: 'incidentID',
        as: 'incidents'
      });
    }

    // Add many-to-many relationship for BusinessProcess
    AuditScope.belongsToMany(models.BusinessProcess, {
      through: 'AuditScopeBusinessProcesses',
      foreignKey: 'auditScopeID',
      otherKey: 'businessProcessID',
      as: 'businessProcesses'
    });
  };

  return AuditScope;
};



