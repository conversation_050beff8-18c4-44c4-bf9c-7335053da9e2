import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Textarea } from "../ui/textarea";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export function NewRiskModal({ open, onClose, onSubmit }) {
  const { t } = useTranslation();
  const [newRisk, setNewRisk] = useState({
    name: "",
    impact: "3",
    DMR: "3",
    probability: "3",
    comment: ""
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    e.stopPropagation();

    // Validate the form
    if (!newRisk.name) {
      alert('Risk name is required');
      return;
    }

    // Call the onSubmit callback
    onSubmit(newRisk);

    // Reset the form
    setNewRisk({
      name: "",
      impact: "3",
      DMR: "3",
      probability: "3",
      comment: ""
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{t('admin.incidents.new_risk.title')}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('admin.incidents.new_risk.name')}</label>
            <Input
              value={newRisk.name}
              onChange={(e) => setNewRisk({ ...newRisk, name: e.target.value })}
              required
            />
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('admin.incidents.new_risk.impact')}</label>
              <Select
                value={newRisk.impact}
                onValueChange={(value) => setNewRisk({ ...newRisk, impact: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5].map((level) => (
                    <SelectItem key={level} value={level.toString()}>
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full ${level === 1 ? 'bg-green-500' : level === 2 ? 'bg-blue-500' : level === 3 ? 'bg-yellow-500' : level === 4 ? 'bg-orange-500' : 'bg-red-500'} mr-2`}></div>
                        {level} - {level === 1 ? 'Very Low' : level === 2 ? 'Low' : level === 3 ? 'Medium' : level === 4 ? 'High' : 'Very High'}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">{t('admin.incidents.new_risk.dmr')}</label>
              <Select
                value={newRisk.DMR}
                onValueChange={(value) => setNewRisk({ ...newRisk, DMR: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5].map((level) => (
                    <SelectItem key={level} value={level.toString()}>
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full ${level === 1 ? 'bg-red-500' : level === 2 ? 'bg-orange-500' : level === 3 ? 'bg-yellow-500' : level === 4 ? 'bg-blue-500' : 'bg-green-500'} mr-2`}></div>
                        {level} - {level === 1 ? 'Very Low' : level === 2 ? 'Low' : level === 3 ? 'Medium' : level === 4 ? 'High' : 'Very High'}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">{t('admin.incidents.new_risk.probability')}</label>
              <Select
                value={newRisk.probability}
                onValueChange={(value) => setNewRisk({ ...newRisk, probability: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[1, 2, 3, 4, 5].map((level) => (
                    <SelectItem key={level} value={level.toString()}>
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full ${level === 1 ? 'bg-green-500' : level === 2 ? 'bg-blue-500' : level === 3 ? 'bg-yellow-500' : level === 4 ? 'bg-orange-500' : 'bg-red-500'} mr-2`}></div>
                        {level} - {level === 1 ? 'Very Low' : level === 2 ? 'Low' : level === 3 ? 'Medium' : level === 4 ? 'High' : 'Very High'}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">{t('admin.incidents.new_risk.comment')}</label>
            <Textarea
              value={newRisk.comment}
              onChange={(e) => setNewRisk({ ...newRisk, comment: e.target.value })}
              placeholder={t('admin.incidents.new_risk.comment_placeholder')}
              className="min-h-[100px]"
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              {t('admin.incidents.new_risk.cancel')}
            </Button>
            <Button type="submit" className="bg-[#F62D51] hover:bg-red-700">
              {t('admin.incidents.new_risk.add_risk')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
