import React, { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search, Edit, ExternalLink, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useNavigate, useParams, useOutletContext } from "react-router-dom";
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';
import TablePagination from "@/components/ui/table-pagination";

// Import activité icon
import activiteIcon from '@/assets/activite.png';

function useSafeOutletContext() {
  try {
    return useOutletContext();
  } catch {
    return undefined;
  }
}

function ActivitesTab() {
  const navigate = useNavigate();
  const { planId, missionAuditId } = useParams();
  const parentContext = useSafeOutletContext();
  const missionAudit = parentContext?.missionAudit;

  // Debug: log params
  console.log('[ActivitesTab] planId:', planId, 'missionAuditId:', missionAuditId);

  const [activities, setActivities] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Fetch activities for the current mission
  useEffect(() => {
    const fetchActivities = async () => {
      if (!missionAuditId) {
        console.warn('[ActivitesTab] No missionAuditId provided');
        setIsLoading(false);
        return;
      }
      try {
        setIsLoading(true);
        const response = await axios.get(`${getApiBaseUrl()}/audit-activities/mission/${missionAuditId}`, {
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' }
        });
        console.log('[ActivitesTab] API response:', response.data);
        if (response.data.success) {
          setActivities(response.data.data || []);
          if (!response.data.data || response.data.data.length === 0) {
            console.warn('[ActivitesTab] No activities found for mission', missionAuditId);
          }
        } else {
          console.error('[ActivitesTab] Failed to fetch activities:', response.data.message);
          setActivities([]);
        }
      } catch (error) {
        console.error('[ActivitesTab] Error fetching activities:', error);
        toast.error('Erreur lors du chargement des activités');
        setActivities([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchActivities();
  }, [missionAuditId]);

  // Filter activities based on search term
  const filteredActivities = activities.filter(activity =>
    activity.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    activity.nom?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination
  const totalPages = Math.ceil(filteredActivities.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, filteredActivities.length);
  const currentActivities = filteredActivities.slice(startIndex, endIndex);

  // Navigate to activity detail page
  const navigateToActivity = (activityId) => {
    let finalPlanId = planId;
    if (!finalPlanId && missionAudit) {
      finalPlanId = missionAudit.planId;
    }
    if (finalPlanId && missionAuditId) {
      navigate(`/audit/plans-daudit/${finalPlanId}/missions-audits/${missionAuditId}/activites/${activityId}`);
    } else {
      toast.error("Impossible d'ouvrir l'activité : planId manquant");
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "Non définie";
    try {
      return new Date(dateString).toLocaleDateString("fr-FR", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch {
      return dateString;
    }
  };

  // Get status badge for activity
  const getStatusBadge = (status) => {
    switch (status) {
      case 'Terminé':
      case 'Completed':
        return <Badge className="bg-green-100 text-green-800">Terminé</Badge>;
      case 'En cours':
      case 'In Progress':
        return <Badge className="bg-blue-100 text-blue-800">En cours</Badge>;
      case 'Planifié':
      case 'Planned':
        return <Badge className="bg-yellow-100 text-yellow-800">Planifié</Badge>;
      case 'Suspendu':
      case 'Suspended':
        return <Badge className="bg-orange-100 text-orange-800">Suspendu</Badge>;
      case 'Annulé':
      case 'Cancelled':
        return <Badge className="bg-red-100 text-red-800">Annulé</Badge>;
      default:
        return <Badge variant="outline">{status || 'Non défini'}</Badge>;
    }
  };

  // Remove the early return - let the table handle empty state

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <img src={activiteIcon} alt="Activité" className="h-6 w-6 mr-2 flex-shrink-0" />
        <h2 className="text-xl font-semibold text-gray-900">Activités</h2>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Rechercher par nom d'activité..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Activities Table */}
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table className="min-w-full">
              <TableHeader>
                <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de début</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de fin</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Constats</TableHead>
                  <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="divide-y divide-gray-200">
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="px-4 py-10 text-center">
                      <div className="flex items-center justify-center">
                        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
                      </div>
                    </TableCell>
                  </TableRow>
                ) : currentActivities.length > 0 ? (
                  currentActivities.map(activity => (
                    <TableRow
                      key={activity.id}
                      className="hover:bg-gray-50/50 cursor-pointer"
                      onClick={() => navigateToActivity(activity.id)}
                    >
                      <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                        <div className="flex items-center">
                          <img src={activiteIcon} alt="Activité" className="h-4 w-4 mr-2 flex-shrink-0" />
                          <span>{activity.name || activity.nom}</span>
                          <ExternalLink className="h-3.5 w-3.5 ml-2 text-gray-400" />
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                        {getStatusBadge(activity.status || activity.statut)}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">
                        {formatDate(activity.startDate || activity.dateDebut)}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">
                        {formatDate(activity.endDate || activity.dateFin)}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm text-gray-500 whitespace-nowrap">
                        {activity.constatsCount || 0}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                        <div className="flex justify-end gap-2">
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={e => {
                              e.stopPropagation();
                              navigateToActivity(activity.id);
                            }}
                          >
                            <Edit className="h-4 w-4 text-blue-600" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="px-4 py-10 text-center text-sm text-gray-500">
                      {searchTerm ? 'Aucune activité trouvée pour cette recherche' : 'Aucune activité dans cette mission'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <TablePagination
        totalPages={totalPages}
        currentPage={currentPage}
        onPageChange={setCurrentPage}
        hasItems={filteredActivities.length > 0}
        totalItems={filteredActivities.length}
        itemsPerPage={itemsPerPage}
        startIndex={startIndex}
        endIndex={endIndex}
      />
    </div>
  );

}

export default ActivitesTab; 