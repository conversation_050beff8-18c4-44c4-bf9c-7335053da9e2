import React, { useState, useEffect } from 'react';
import { X, User, Check, Loader2 } from 'lucide-react';
import userService from '../../services/userService';
import { getControlAssignedUsers, assignUsersToControl } from '../../services/control-assignment-service';
import { findOrCreateCampagneForControl } from '../../services/campagne-service';
import { toast } from 'react-hot-toast';

const UserAssignmentModal = ({ isOpen, onClose, controlId, controlName, onAssignmentComplete }) => {
  const [users, setUsers] = useState([]);
  const [assignedUsers, setAssignedUsers] = useState([]);
  const [selectedUserIds, setSelectedUserIds] = useState([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);

  // Load users and current assignments when modal opens
  useEffect(() => {
    if (isOpen && controlId) {
      loadData();
    }
  }, [isOpen, controlId]);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Load all users
      const usersData = await userService.fetchUsers({ force: false, silent: false });
      setUsers(usersData || []);

      // Load currently assigned users
      const assignedResponse = await getControlAssignedUsers(controlId);
      if (assignedResponse.success) {
        const assigned = assignedResponse.data || [];
        setAssignedUsers(assigned);
        setSelectedUserIds(assigned.map(user => user.id));
      }
    } catch (error) {
      console.error('Error loading data:', error);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const handleUserToggle = (userId) => {
    setSelectedUserIds(prev => {
      if (prev.includes(userId)) {
        return prev.filter(id => id !== userId);
      } else {
        return [...prev, userId];
      }
    });
  };

  const handleSave = async () => {
    setSaving(true);
    setError(null);

    try {
      // First assign users to control (existing functionality)
      const result = await assignUsersToControl(controlId, selectedUserIds);

      // If users are selected, create a campagne and assign them
      if (selectedUserIds.length > 0) {
        try {
          // Generate a random campagne name (only used for new campagnes)
          const randomNumber = Math.floor(Math.random() * 10000);
          const newCampagneName = `Campagne ${randomNumber}`;

          // Create campagne data (name will be ignored for existing campagnes)
          const campagneData = {
            name: newCampagneName,
            code: `CAMP-${randomNumber}`,
            description: `Campagne automatiquement créée pour le contrôle ${controlName || controlId}`,
            statut: 'planifié'
          };

          console.log('🔍 [FRONTEND DEBUG] Calling findOrCreateCampagneForControl with:', {
            controlId,
            controlIdType: typeof controlId,
            controlName,
            campagneData,
            selectedUserIds,
            selectedUserIdsLength: selectedUserIds.length
          });

          // Find or create campagne for this control and assign users
          const campagneResult = await findOrCreateCampagneForControl(controlId, campagneData, selectedUserIds);

          console.log('🔍 [FRONTEND DEBUG] Campagne result:', {
            success: campagneResult.success,
            isNewCampagne: campagneResult.data?.isNewCampagne,
            campagneId: campagneResult.data?.campagne?.campagneID,
            campagneName: campagneResult.data?.campagne?.name,
            message: campagneResult.message
          });

          if (campagneResult.success) {
            const action = campagneResult.data.isNewCampagne ? 'créée' : 'mise à jour';
            const actualCampagneName = campagneResult.data.campagne?.name || newCampagneName;
            console.log(`Campagne "${actualCampagneName}" ${action} avec succès et ${selectedUserIds.length} utilisateur(s) assigné(s)`);
            toast.success(`Campagne "${actualCampagneName}" ${action} avec succès! ${selectedUserIds.length} utilisateur(s) assigné(s).`);
          } else {
            throw new Error(campagneResult.message || 'Erreur lors de la gestion de la campagne');
          }
        } catch (campagneError) {
          console.error('Error creating campagne:', campagneError);
          toast.error(`Erreur lors de la création de la campagne: ${campagneError.message}`);
          // Don't throw here - we still want to show success for user assignment
        }
      }

      // Show success message based on the action
      if (selectedUserIds.length === 0) {
        console.log('All user assignments removed successfully');
      } else {
        console.log(`Successfully assigned ${selectedUserIds.length} users to control and created campagne`);
      }

      // Call the callback to notify parent component
      if (onAssignmentComplete) {
        onAssignmentComplete();
      }

      onClose();
    } catch (error) {
      console.error('Error saving assignments:', error);
      setError('Erreur lors de la sauvegarde des affectations');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    // Reset to original assignments
    setSelectedUserIds(assignedUsers.map(user => user.id));
    setError(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: 12,
        width: '90%',
        maxWidth: 600,
        maxHeight: '85vh',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      }}>
        {/* Header */}
        <div style={{
          padding: '20px 24px',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div>
            <h2 style={{
              fontSize: '20px',
              fontWeight: '600',
              color: '#1f2937',
              margin: 0,
              marginBottom: 4
            }}>
              Attribuer des utilisateurs
            </h2>
            <p style={{
              fontSize: '14px',
              color: '#6b7280',
              margin: 0
            }}>
              {controlName || `Contrôle ${controlId}`}
            </p>
          </div>
          <button
            onClick={handleCancel}
            style={{
              padding: 8,
              border: 'none',
              background: 'none',
              cursor: 'pointer',
              borderRadius: 6,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <X size={20} color="#6b7280" />
          </button>
        </div>

        {/* Content */}
        <div style={{
          padding: '24px',
          flex: 1,
          overflowY: 'auto',
          minHeight: 0
        }}>
          {loading ? (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '40px',
              flexDirection: 'column',
              gap: 12
            }}>
              <Loader2 size={24} className="animate-spin" color="#3b82f6" />
              <span style={{ color: '#6b7280', fontSize: '14px' }}>
                Chargement des utilisateurs...
              </span>
            </div>
          ) : error ? (
            <div style={{
              padding: '16px',
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: 8,
              color: '#dc2626',
              fontSize: '14px'
            }}>
              {error}
            </div>
          ) : (
            <div>
              <div style={{
                marginBottom: 16,
                fontSize: '14px',
                color: '#6b7280'
              }}>
                Sélectionnez les utilisateurs à affecter à ce contrôle ({selectedUserIds.length} sélectionné{selectedUserIds.length !== 1 ? 's' : ''})
                {selectedUserIds.length === 0 && (
                  <div style={{
                    marginTop: 8,
                    padding: '8px 12px',
                    backgroundColor: '#fef3c7',
                    border: '1px solid #f59e0b',
                    borderRadius: 6,
                    fontSize: '13px',
                    color: '#92400e'
                  }}>
                    ⚠️ Aucun utilisateur sélectionné - cela supprimera toutes les affectations existantes
                  </div>
                )}
              </div>

              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: 8
              }}>
                {users.map(user => {
                  const isSelected = selectedUserIds.includes(user.id);
                  const wasOriginallyAssigned = assignedUsers.some(assigned => assigned.id === user.id);
                  
                  return (
                    <div
                      key={user.id}
                      onClick={() => handleUserToggle(user.id)}
                      style={{
                        padding: '12px 16px',
                        border: `2px solid ${isSelected ? '#3b82f6' : '#e5e7eb'}`,
                        borderRadius: 8,
                        cursor: 'pointer',
                        backgroundColor: isSelected ? '#eff6ff' : '#ffffff',
                        transition: 'all 0.2s ease',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 12
                      }}
                    >
                      <div style={{
                        width: 20,
                        height: 20,
                        borderRadius: 4,
                        border: `2px solid ${isSelected ? '#3b82f6' : '#d1d5db'}`,
                        backgroundColor: isSelected ? '#3b82f6' : 'transparent',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexShrink: 0
                      }}>
                        {isSelected && <Check size={12} color="white" />}
                      </div>
                      
                      <div style={{
                        width: 32,
                        height: 32,
                        borderRadius: '50%',
                        backgroundColor: '#f3f4f6',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexShrink: 0
                      }}>
                        <User size={16} color="#6b7280" />
                      </div>
                      
                      <div style={{ flex: 1 }}>
                        <div style={{
                          fontSize: '14px',
                          fontWeight: '500',
                          color: '#1f2937'
                        }}>
                          {user.username}
                        </div>
                        <div style={{
                          fontSize: '12px',
                          color: '#6b7280'
                        }}>
                          {user.email}
                        </div>
                      </div>
                      
                      {wasOriginallyAssigned && (
                        <div style={{
                          fontSize: '12px',
                          color: '#059669',
                          backgroundColor: '#d1fae5',
                          padding: '2px 8px',
                          borderRadius: 12,
                          fontWeight: '500'
                        }}>
                          Déjà affecté
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div style={{
          padding: '16px 24px',
          borderTop: '1px solid #e5e7eb',
          display: 'flex',
          justifyContent: 'flex-end',
          gap: 12,
          flexShrink: 0
        }}>
          <button
            onClick={handleCancel}
            disabled={saving}
            style={{
              padding: '8px 16px',
              border: '1px solid #d1d5db',
              backgroundColor: 'white',
              color: '#374151',
              borderRadius: 6,
              fontSize: '14px',
              fontWeight: '500',
              cursor: saving ? 'not-allowed' : 'pointer',
              opacity: saving ? 0.6 : 1
            }}
          >
            Annuler
          </button>
          <button
            onClick={handleSave}
            disabled={saving || loading}
            style={{
              padding: '8px 16px',
              border: 'none',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: 6,
              fontSize: '14px',
              fontWeight: '500',
              cursor: (saving || loading) ? 'not-allowed' : 'pointer',
              opacity: (saving || loading) ? 0.6 : 1,
              display: 'flex',
              alignItems: 'center',
              gap: 8
            }}
          >
            {saving && <Loader2 size={16} className="animate-spin" />}
            {saving
              ? 'Sauvegarde...'
              : 'Lancer la Campagne'
            }
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserAssignmentModal;
