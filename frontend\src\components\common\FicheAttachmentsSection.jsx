import { useState, useEffect, useCallback, useRef } from "react";
import axios from "axios";
import { Upload, File, Trash2, FileText, ExternalLink, Download, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { getApiBaseUrl } from '@/utils/api-config';

export function FicheAttachmentsSection({ fiche }) {
  const [businessDocuments, setBusinessDocuments] = useState([]);
  const [externalReferences, setExternalReferences] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [, setActiveTab] = useState("business-documents");
  const [isDraggingBusiness, setIsDraggingBusiness] = useState(false);
  const [isDraggingExternal, setIsDraggingExternal] = useState(false);
  const API_BASE_URL = getApiBaseUrl();
  const businessDocInputRef = useRef(null);
  const externalRefInputRef = useRef(null);

  // Fetch attachments from the server
  const fetchAttachments = useCallback(async () => {
    if (!fiche?.id && !fiche?.ficheID) return;
    const ficheID = fiche.ficheID || fiche.id;
    try {
      setIsLoading(true);
      const timestamp = new Date().getTime();
      // Fetch business documents
      const businessDocsResponse = await axios.get(`${API_BASE_URL}/fiche-uploads?ficheID=${ficheID}&type=business-document&_t=${timestamp}`, {
        withCredentials: true,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      // Fetch external references
      const externalRefsResponse = await axios.get(`${API_BASE_URL}/fiche-uploads?ficheID=${ficheID}&type=external-reference&_t=${timestamp}`, {
        withCredentials: true,
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      if (businessDocsResponse.data.success) {
        setBusinessDocuments(businessDocsResponse.data.data);
      }
      if (externalRefsResponse.data.success) {
        setExternalReferences(externalRefsResponse.data.data);
      }
    } catch (error) {
      console.error('Error fetching attachments:', error);
      toast.error('Erreur lors du chargement des pièces jointes', {
        description: 'Veuillez actualiser la page',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  }, [fiche?.id, fiche?.ficheID]);

  useEffect(() => {
    fetchAttachments();
    if (fiche?.id || fiche?.ficheID) {
      const pollingInterval = setInterval(fetchAttachments, 30000);
      return () => clearInterval(pollingInterval);
    }
  }, [fiche?.id, fiche?.ficheID, fetchAttachments]);

  // Handle file upload for business documents
  const handleBusinessDocumentUpload = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;
    const allowedExtensions = [
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.txt', '.csv', '.rtf', '.odt', '.ods', '.odp',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg',
      '.zip', '.rar', '.7z', '.tar', '.gz'
    ];
    const MAX_FILE_SIZE = 50 * 1024 * 1024;
    const oversizedFiles = files.filter(file => file.size > MAX_FILE_SIZE);
    if (oversizedFiles.length > 0) {
      const fileNames = oversizedFiles.map(file => file.name).join(', ');
      toast.error('Fichiers trop volumineux', {
        description: `Les fichiers suivants dépassent la limite de 50 Mo : ${fileNames}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }
    const invalidFiles = files.filter(file => {
      const extension = '.' + file.name.split('.').pop().toLowerCase();
      return !allowedExtensions.includes(extension);
    });
    if (invalidFiles.length > 0) {
      const fileNames = invalidFiles.map(file => file.name).join(', ');
      toast.error('Type de fichier non supporté', {
        description: `Les fichiers suivants ont des formats non supportés : ${fileNames}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }
    try {
      setIsLoading(true);
      setUploadProgress(0);
      const ficheID = fiche.ficheID || fiche.id;
      const formData = new FormData();
      formData.append('type', 'business-document');
      formData.append('ficheID', ficheID);
      files.forEach(file => {
        formData.append('files', file);
      });
      const response = await axios.post(`${API_BASE_URL}/fiche-uploads`, formData, {
        withCredentials: true,
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });
      if (response.data.success) {
        toast.success('Téléchargement réussi', {
          description: `${files.length} document${files.length !== 1 ? 's' : ''} téléchargé(s) avec succès`,
          duration: 3000,
        });
        fetchAttachments();
      }
    } catch (error) {
      console.error('Erreur lors du téléchargement des documents métier:', error);
      toast.error('Échec du téléchargement', {
        description: error.response?.data?.message || 'Erreur lors du téléchargement',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
      setUploadProgress(0);
      e.target.value = "";
    }
  };

  // Handle file upload for external references
  const handleExternalReferenceUpload = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;
    const allowedExtensions = [
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
      '.txt', '.csv', '.rtf', '.odt', '.ods', '.odp',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg',
      '.zip', '.rar', '.7z', '.tar', '.gz'
    ];
    const MAX_FILE_SIZE = 50 * 1024 * 1024;
    const oversizedFiles = files.filter(file => file.size > MAX_FILE_SIZE);
    if (oversizedFiles.length > 0) {
      const fileNames = oversizedFiles.map(file => file.name).join(', ');
      toast.error('Fichiers trop volumineux', {
        description: `Les fichiers suivants dépassent la limite de 50 Mo : ${fileNames}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }
    const invalidFiles = files.filter(file => {
      const extension = '.' + file.name.split('.').pop().toLowerCase();
      return !allowedExtensions.includes(extension);
    });
    if (invalidFiles.length > 0) {
      const fileNames = invalidFiles.map(file => file.name).join(', ');
      toast.error('Type de fichier non supporté', {
        description: `Les fichiers suivants ont des formats non supportés : ${fileNames}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }
    try {
      setIsLoading(true);
      setUploadProgress(0);
      const ficheID = fiche.ficheID || fiche.id;
      const formData = new FormData();
      formData.append('type', 'external-reference');
      formData.append('ficheID', ficheID);
      files.forEach(file => {
        formData.append('files', file);
      });
      const response = await axios.post(`${API_BASE_URL}/fiche-uploads`, formData, {
        withCredentials: true,
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });
      if (response.data.success) {
        toast.success('Téléchargement réussi', {
          description: `${files.length} référence${files.length !== 1 ? 's' : ''} téléchargée(s) avec succès`,
          duration: 3000,
        });
        fetchAttachments();
      }
    } catch (error) {
      console.error('Erreur lors du téléchargement des références externes:', error);
      toast.error('Échec du téléchargement', {
        description: error.response?.data?.message || 'Erreur lors du téléchargement',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
      setUploadProgress(0);
      e.target.value = "";
    }
  };

  // Download an attachment
  const downloadAttachment = async (id, fileName) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/fiche-uploads/download/${id}`, {
        withCredentials: true,
        responseType: 'blob'
      });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error('Erreur lors du téléchargement de la pièce jointe:', error);
      toast.error('Échec du téléchargement', {
        description: 'Impossible de télécharger le fichier. Veuillez réessayer plus tard.',
        duration: 5000,
      });
    }
  };

  // Delete a business document
  const deleteBusinessDocument = async (id) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) return;
    try {
      const response = await axios.delete(`${API_BASE_URL}/fiche-uploads/${id}`, {
        withCredentials: true
      });
      if (response.data.success) {
        toast.success('Document supprimé', {
          description: 'Le document a été supprimé avec succès',
          duration: 3000,
        });
        fetchAttachments();
      }
    } catch (error) {
      console.error('Erreur lors de la suppression du document:', error);
      toast.error('Échec de la suppression', {
        description: 'Impossible de supprimer le document. Veuillez réessayer plus tard.',
        duration: 5000,
      });
    }
  };

  // Delete an external reference
  const deleteExternalReference = async (id) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette référence ?')) return;
    try {
      const response = await axios.delete(`${API_BASE_URL}/fiche-uploads/${id}`, {
        withCredentials: true
      });
      if (response.data.success) {
        toast.success('Référence supprimée', {
          description: 'La référence a été supprimée avec succès',
          duration: 3000,
        });
        fetchAttachments();
      }
    } catch (error) {
      console.error('Erreur lors de la suppression de la référence:', error);
      toast.error('Échec de la suppression', {
        description: 'Impossible de supprimer la référence. Veuillez réessayer plus tard.',
        duration: 5000,
      });
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Trigger file input click
  const triggerBusinessDocUpload = () => {
    businessDocInputRef.current?.click();
  };

  const triggerExternalRefUpload = () => {
    externalRefInputRef.current?.click();
  };

  // Drag and drop handlers for business documents
  const handleBusinessDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleBusinessDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(true);
  };

  const handleBusinessDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(false);
  };

  const handleBusinessDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(false);
    if (isLoading) return;
    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;
    if (businessDocInputRef.current) {
      const dataTransfer = new DataTransfer();
      files.forEach(file => dataTransfer.items.add(file));
      const fileList = dataTransfer.files;
      businessDocInputRef.current.files = fileList;
      const event = new Event('change', { bubbles: true });
      businessDocInputRef.current.dispatchEvent(event);
    }
  };

  // Drag and drop handlers for external references
  const handleExternalDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleExternalDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingExternal(true);
  };

  const handleExternalDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingExternal(false);
  };

  const handleExternalDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingExternal(false);
    if (isLoading) return;
    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;
    if (externalRefInputRef.current) {
      const dataTransfer = new DataTransfer();
      files.forEach(file => dataTransfer.items.add(file));
      const fileList = dataTransfer.files;
      externalRefInputRef.current.files = fileList;
      const event = new Event('change', { bubbles: true });
      externalRefInputRef.current.dispatchEvent(event);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <Tabs defaultValue="business-documents" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="business-documents" className="text-center">
            <FileText className="h-4 w-4 mr-2" />
            Documents Métier
          </TabsTrigger>
          <TabsTrigger value="external-references" className="text-center">
            <ExternalLink className="h-4 w-4 mr-2" />
            Références Externes
          </TabsTrigger>
        </TabsList>
        {/* Business Documents Tab */}
        <TabsContent value="business-documents" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-md font-medium">Documents Métier</h3>
            <div>
              <input
                type="file"
                id="business-document-upload"
                multiple
                className="hidden"
                onChange={handleBusinessDocumentUpload}
                disabled={isLoading}
                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.rtf,.odt,.ods,.odp,.jpg,.jpeg,.png,.gif,.bmp,.svg,.zip,.rar,.7z,.tar,.gz"
                ref={businessDocInputRef}
              />
              <label htmlFor="business-document-upload">
                <Button
                  type="button"
                  variant="outline"
                  className="flex items-center gap-2 cursor-pointer"
                  disabled={isLoading}
                  asChild
                >
                  <span>
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Téléchargement... {uploadProgress > 0 ? `${uploadProgress}%` : ''}
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4" />
                        Télécharger un document
                      </>
                    )}
                  </span>
                </Button>
              </label>
            </div>
          </div>
          {/* Business Documents List */}
          {businessDocuments.length === 0 ? (
            <div
              className={`text-center py-8 border-2 border-dashed rounded-lg transition-colors ${
                isDraggingBusiness ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
              }`}
              onDragOver={handleBusinessDragOver}
              onDragEnter={handleBusinessDragEnter}
              onDragLeave={handleBusinessDragLeave}
              onDrop={handleBusinessDrop}
            >
              <File className="h-12 w-12 mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">{isDraggingBusiness ? 'Déposez les fichiers ici' : 'Aucun document métier téléchargé'}</p>
              <p className="text-sm text-gray-400">Glissez-déposez les fichiers ici ou cliquez sur le bouton de téléchargement</p>
              <p className="text-xs text-gray-400 mt-2">Taille maximale : 50 Mo. Types de fichiers autorisés : PDF, documents Office, images, archives.</p>
            </div>
          ) : (
            <div
              className={`border rounded-lg overflow-hidden transition-colors ${isDraggingBusiness ? 'border-blue-500 border-2' : ''}`}
              onDragOver={handleBusinessDragOver}
              onDragEnter={handleBusinessDragEnter}
              onDragLeave={handleBusinessDragLeave}
              onDrop={handleBusinessDrop}
            >
              {isDraggingBusiness && (
                <div className="absolute inset-0 bg-blue-50 bg-opacity-70 flex items-center justify-center z-10 pointer-events-none">
                  <div className="text-center">
                    <Upload className="h-12 w-12 mx-auto text-blue-500 mb-2" />
                    <p className="text-blue-600 font-medium">Déposez les fichiers pour télécharger</p>
                  </div>
                </div>
              )}
              <table className="w-full relative">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Nom</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Taille</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Date</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {businessDocuments.map((doc) => (
                    <tr key={doc.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm text-gray-900 flex items-center">
                        <File className="h-4 w-4 mr-2 text-gray-500" />
                        <span className="truncate max-w-[200px]">{doc.name}</span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        {formatFileSize(doc.size)}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        {formatDate(doc.uploadDate)}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500 text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => downloadAttachment(doc.id, doc.name)}
                            title="Télécharger"
                          >
                            <Download className="h-4 w-4 text-gray-500" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteBusinessDocument(doc.id)}
                            title="Supprimer"
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>
        {/* External References Tab */}
        <TabsContent value="external-references" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-md font-medium">Références Externes</h3>
            <div>
              <input
                type="file"
                id="external-reference-upload"
                multiple
                className="hidden"
                onChange={handleExternalReferenceUpload}
                disabled={isLoading}
                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.rtf,.odt,.ods,.odp,.jpg,.jpeg,.png,.gif,.bmp,.svg,.zip,.rar,.7z,.tar,.gz"
                ref={externalRefInputRef}
              />
              <label htmlFor="external-reference-upload">
                <Button
                  type="button"
                  variant="outline"
                  className="flex items-center gap-2 cursor-pointer"
                  disabled={isLoading}
                  asChild
                >
                  <span>
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Téléchargement... {uploadProgress > 0 ? `${uploadProgress}%` : ''}
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4" />
                        Télécharger une référence
                      </>
                    )}
                  </span>
                </Button>
              </label>
            </div>
          </div>
          {/* External References List */}
          {externalReferences.length === 0 ? (
            <div
              className={`text-center py-8 border-2 border-dashed rounded-lg transition-colors ${
                isDraggingExternal ? 'border-green-500 bg-green-50' : 'border-gray-300'
              }`}
              onDragOver={handleExternalDragOver}
              onDragEnter={handleExternalDragEnter}
              onDragLeave={handleExternalDragLeave}
              onDrop={handleExternalDrop}
            >
              <ExternalLink className="h-12 w-12 mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">{isDraggingExternal ? 'Déposez les fichiers ici' : 'Aucune référence externe téléchargée'}</p>
              <p className="text-sm text-gray-400">Glissez-déposez les fichiers ici ou cliquez sur le bouton de téléchargement</p>
              <p className="text-xs text-gray-400 mt-2">Taille maximale : 50 Mo. Types de fichiers autorisés : PDF, documents Office, images, archives.</p>
            </div>
          ) : (
            <div
              className={`border rounded-lg overflow-hidden transition-colors ${isDraggingExternal ? 'border-green-500 border-2' : ''}`}
              onDragOver={handleExternalDragOver}
              onDragEnter={handleExternalDragEnter}
              onDragLeave={handleExternalDragLeave}
              onDrop={handleExternalDrop}
            >
              {isDraggingExternal && (
                <div className="absolute inset-0 bg-green-50 bg-opacity-70 flex items-center justify-center z-10 pointer-events-none">
                  <div className="text-center">
                    <Upload className="h-12 w-12 mx-auto text-green-500 mb-2" />
                    <p className="text-green-600 font-medium">Déposez les fichiers pour télécharger</p>
                  </div>
                </div>
              )}
              <table className="w-full relative">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Nom</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Taille</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Date</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {externalReferences.map((ref) => (
                    <tr key={ref.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm text-gray-900 flex items-center">
                        <File className="h-4 w-4 mr-2 text-gray-500" />
                        <span className="truncate max-w-[200px]">{ref.name}</span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        {formatFileSize(ref.size)}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        {formatDate(ref.uploadDate)}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500 text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => downloadAttachment(ref.id, ref.name)}
                            title="Télécharger"
                          >
                            <Download className="h-4 w-4 text-gray-500" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteExternalReference(ref.id)}
                            title="Supprimer"
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default FicheAttachmentsSection; 