const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllOperations,
  createOperation,
  getOperationById,
  updateOperation,
  deleteOperation
} = require('../../controllers/processes/operation-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all operations
router.get('/', getAllOperations);

// Create new operation
router.post('/', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), createOperation);

// Get operation by ID
router.get('/:id', getOperationById);

// Update operation
router.put('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), updateOperation);

// Delete operation
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteOperation);

module.exports = router;
