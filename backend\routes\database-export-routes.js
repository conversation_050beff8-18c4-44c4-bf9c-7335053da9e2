const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../middleware/auth');
const {
  getTablesList,
  exportTableToExcel,
  exportMultipleTablesToExcel,
  exportEntireDatabase
} = require('../controllers/exports/database-export-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);



// Get list of all tables
router.get('/tables',  getTablesList);

// Export a single table
router.get('/table/:tableName',  exportTableToExcel);

// Export multiple tables
router.post('/tables',  exportMultipleTablesToExcel);

// Export entire database
router.get('/full',  exportEntireDatabase);

module.exports = router;