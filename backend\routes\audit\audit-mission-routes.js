const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllAuditMissions,
  getAuditMissionsByPlanId,
  createAuditMission,
  getAuditMissionById,
  updateAuditMission,
  deleteAuditMission,
  updateMissionWorkflow,
  getMissionWorkflowHistory
} = require('../../controllers/audit/audit-mission-controller');
console.log('AuditMission routes loaded');
// Apply authentication middleware to all routes
router.use(verifyToken);
// Equipe Intervenante routes (team assignment for missions)
router.use('/equipe-intervenante', require('./equipe-intervenante-routes'));
// Get all audit missions - both Audit Director and Auditor can access
router.get('/', authorizeRoles(['audit_director', 'auditor']),getAllAuditMissions);

// Get audit missions by plan ID - both Audit Director and Auditor can access
router.get('/plan/:planId', authorizeRoles(['audit_director', 'auditor']),getAuditMissionsByPlanId);

// Create new audit mission - both Audit Director and Auditor can access
router.post('/', authorizeRoles(['audit_director', 'auditor']), createAuditMission);

// Get audit mission by ID - both Audit Director and Auditor can access
router.get('/:id', authorizeRoles(['audit_director', 'auditor']),getAuditMissionById);

// Update audit mission - both Audit Director and Auditor can access
router.put('/:id', authorizeRoles(['audit_director', 'auditor']), updateAuditMission);

// Delete audit mission - only Audit Director can access
router.delete('/:id', authorizeRoles(['audit_director']), deleteAuditMission);

// Workflow management routes (no permission checks as requested)
// Update mission workflow - both Audit Director and Auditor can access
router.put('/:id/workflow', authorizeRoles(['audit_director', 'auditor']), updateMissionWorkflow);

// Get mission workflow history - both Audit Director and Auditor can access
router.get('/:id/workflow/history', authorizeRoles(['audit_director', 'auditor']), getMissionWorkflowHistory);



module.exports = router;
