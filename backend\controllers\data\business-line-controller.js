const { BusinessLine } = require('../../models');

// Get all business lines
const getAllBusinessLines = async (req, res) => {
  try {
    const businessLines = await BusinessLine.findAll();
    res.json({
      success: true,
      data: businessLines
    });
  } catch (error) {
    console.error('Error fetching business lines:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch business lines'
    });
  }
};

// Create new business line
const createBusinessLine = async (req, res) => {
  try {
    const {
      businessLineID,
      name,
      description
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name'].filter(field => !req.body[field])
      });
    }

    const businessLine = await BusinessLine.create({
      businessLineID: businessLineID || `BL_${Date.now()}`,
      name,
      description: description || null
    });

    return res.status(201).json({
      success: true,
      message: 'Business line created successfully',
      data: businessLine
    });
  } catch (error) {
    console.error('Error creating business line:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create business line'
    });
  }
};

// Get business line by ID
const getBusinessLineById = async (req, res) => {
  try {
    const { id } = req.params;
    const businessLine = await BusinessLine.findOne({
      where: { businessLineID: id }
    });
    
    if (!businessLine) {
      return res.status(404).json({
        success: false,
        message: 'Business line not found'
      });
    }
    
    res.json({
      success: true,
      data: businessLine
    });
  } catch (error) {
    console.error('Error fetching business line:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch business line'
    });
  }
};

// Update business line
const updateBusinessLine = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description
    } = req.body;

    const businessLine = await BusinessLine.findByPk(id);
    
    if (!businessLine) {
      return res.status(404).json({
        success: false,
        message: 'Business line not found'
      });
    }

    // Update fields
    await businessLine.update({
      name: name || businessLine.name,
      description: description !== undefined ? description : businessLine.description
    });

    res.json({
      success: true,
      message: 'Business line updated successfully',
      data: businessLine
    });
  } catch (error) {
    console.error('Error updating business line:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update business line'
    });
  }
};

// Delete business line
const deleteBusinessLine = async (req, res) => {
  try {
    const { id } = req.params;
    const businessLine = await BusinessLine.findByPk(id);
    
    if (!businessLine) {
      return res.status(404).json({
        success: false,
        message: 'Business line not found'
      });
    }
    
    await businessLine.destroy();
    
    res.json({
      success: true,
      message: 'Business line deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting business line:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete business line'
    });
  }
};

module.exports = {
  getAllBusinessLines,
  createBusinessLine,
  getBusinessLineById,
  updateBusinessLine,
  deleteBusinessLine
};
