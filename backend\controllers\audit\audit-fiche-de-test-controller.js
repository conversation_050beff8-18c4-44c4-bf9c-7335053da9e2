const { v4: uuidv4 } = require('uuid');
const db = require('../../models');
const FicheDeTest = db.FicheDeTest;
const FicheDeTravail = db.FicheDeTravail;
const Question = db.Question;
const User = db.User;

// Get all fiches de test
const getAllFicheDeTest = async (req, res) => {
  try {
    const fiches = await FicheDeTest.findAll({
      include: [
        { model: FicheDeTravail, as: 'ficheDeTravail', attributes: ['id', 'name'] }
      ]
    });
    return res.status(200).json({ success: true, data: fiches });
  } catch (error) {
    console.error('Error fetching fiches de test:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch fiches de test',
      error: error.message
    });
  }
};

// Get fiche de test by ID
const getFicheDeTestById = async (req, res) => {
  try {
    const { id } = req.params;
    const fiche = await FicheDeTest.findByPk(id, {
      include: [
        { model: FicheDeTravail, as: 'ficheDeTravail', attributes: ['id', 'name'] }
      ]
    });
    
    if (!fiche) {
      return res.status(404).json({ success: false, message: 'Fiche de test not found' });
    }
    
    return res.status(200).json({ success: true, data: fiche });
  } catch (error) {
    console.error('Error fetching fiche de test:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch fiche de test',
      error: error.message
    });
  }
};

// Get fiches de test by fiche de travail ID
const getFicheDeTestByFicheDeTravailId = async (req, res) => {
  try {
    const { ficheDeTravailId } = req.params;
    
    // Use Promise.all for parallel validation and data fetching
    const [ficheDeTravail, fiches] = await Promise.all([
      FicheDeTravail.findByPk(ficheDeTravailId),
      FicheDeTest.findAll({
        where: { ficheDeTravailID: ficheDeTravailId },
        include: [
          { model: FicheDeTravail, as: 'ficheDeTravail', attributes: ['id', 'name'] }
        ]
      })
    ]);
    
    if (!ficheDeTravail) {
      return res.status(404).json({ success: false, message: 'Fiche de travail not found' });
    }
    
    return res.status(200).json({ success: true, data: fiches });
  } catch (error) {
    console.error('Error fetching fiches de test by fiche de travail ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch fiches de test',
      error: error.message
    });
  }
};

// Create a new fiche de test
const createFicheDeTest = async (req, res) => {
  try {
    const {
      titre,
      questionnaire,
      elementTrouve,
      commentaire,
      preuve,
      dateSignature,
      ficheDeTravailID,
      questionIds,
      answers
    } = req.body;

    // Validate required fields
    if (!titre) {
      return res.status(400).json({ success: false, message: 'Titre is required' });
    }
    if (!ficheDeTravailID) {
      return res.status(400).json({ success: false, message: 'Fiche de travail ID is required' });
    }

    // Use Promise.all for parallel validation
    const [ficheDeTravail] = await Promise.all([
      FicheDeTravail.findByPk(ficheDeTravailID)
    ]);

    if (!ficheDeTravail) {
      return res.status(404).json({ success: false, message: 'Fiche de travail not found' });
    }

    // Create the fiche de test
    const ficheDeTest = await FicheDeTest.create({
      id: `FDT_${uuidv4().substring(0, 8)}`,
      titre,
      questionnaire,
      elementTrouve,
      commentaire,
      preuve,
      dateSignature,
      ficheDeTravailID,
      answers
    });

    // Associate questions if provided
    if (questionIds && Array.isArray(questionIds) && questionIds.length > 0) {
      // Verify all questions exist
      const questions = await Question.findAll({
        where: { id: questionIds }
      });
      
      if (questions.length !== questionIds.length) {
        return res.status(400).json({ 
          success: false, 
          message: 'One or more questions not found' 
        });
      }
      
      // Associate questions with the fiche de test
      await ficheDeTest.setQuestions(questions);
      
      // Update questionnaire field with question IDs
      await ficheDeTest.update({ 
        questionnaire: JSON.stringify(questionIds) 
      });
    }

    // Return the created fiche de test with associations
    const createdFicheWithAssociations = await FicheDeTest.findByPk(ficheDeTest.id, {
      include: [
        { model: FicheDeTravail, as: 'ficheDeTravail', attributes: ['id', 'name'] }
      ]
    });

    return res.status(201).json({
      success: true,
      message: 'Fiche de test created successfully',
      data: createdFicheWithAssociations
    });
  } catch (error) {
    console.error('Error creating fiche de test:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create fiche de test',
      error: error.message
    });
  }
};

// Update fiche de test
const updateFicheDeTest = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      titre,
      questionnaire,
      elementTrouve,
      commentaire,
      preuve,
      dateSignature,
      ficheDeTravailID,
      questionIds,
      answers
    } = req.body;

    // Find the fiche de test
    const ficheDeTest = await FicheDeTest.findByPk(id);
    if (!ficheDeTest) {
      return res.status(404).json({ success: false, message: 'Fiche de test not found' });
    }

    // Validate fiche de travail if provided
    if (ficheDeTravailID && ficheDeTravailID !== ficheDeTest.ficheDeTravailID) {
      const ficheDeTravail = await FicheDeTravail.findByPk(ficheDeTravailID);
      if (!ficheDeTravail) {
        return res.status(404).json({ success: false, message: 'Fiche de travail not found' });
      }
    }

    // Update the fiche de test
    await ficheDeTest.update({
      titre: titre || ficheDeTest.titre,
      questionnaire: questionnaire !== undefined ? questionnaire : ficheDeTest.questionnaire,
      elementTrouve: elementTrouve !== undefined ? elementTrouve : ficheDeTest.elementTrouve,
      commentaire: commentaire !== undefined ? commentaire : ficheDeTest.commentaire,
      preuve: preuve !== undefined ? preuve : ficheDeTest.preuve,
      dateSignature: dateSignature !== undefined ? dateSignature : ficheDeTest.dateSignature,
      ficheDeTravailID: ficheDeTravailID || ficheDeTest.ficheDeTravailID,
      answers: answers !== undefined ? answers : ficheDeTest.answers
    });

    // Update question associations if provided
    if (questionIds && Array.isArray(questionIds)) {
      // Verify all questions exist
      const questions = await Question.findAll({
        where: { id: questionIds }
      });
      
      if (questions.length !== questionIds.length) {
        return res.status(400).json({ 
          success: false, 
          message: 'One or more questions not found' 
        });
      }
      
      // Update question associations
      await ficheDeTest.setQuestions(questions);
      
      // Update questionnaire field with question IDs
      await ficheDeTest.update({ 
        questionnaire: JSON.stringify(questionIds) 
      });
    }

    // Return the updated fiche de test with associations
    const updatedFicheWithAssociations = await FicheDeTest.findByPk(id, {
      include: [
        { model: FicheDeTravail, as: 'ficheDeTravail', attributes: ['id', 'name'] }
      ]
    });

    return res.status(200).json({
      success: true,
      message: 'Fiche de test updated successfully',
      data: updatedFicheWithAssociations
    });
  } catch (error) {
    console.error('Error updating fiche de test:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update fiche de test',
      error: error.message
    });
  }
};

// Delete fiche de test
const deleteFicheDeTest = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the fiche de test
    const ficheDeTest = await FicheDeTest.findByPk(id);
    if (!ficheDeTest) {
      return res.status(404).json({ success: false, message: 'Fiche de test not found' });
    }
    
    // Remove associations with questions
    await ficheDeTest.setQuestions([]);
    
    // Delete the fiche de test
    await ficheDeTest.destroy();
    
    return res.status(200).json({
      success: true,
      message: 'Fiche de test deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting fiche de test:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete fiche de test',
      error: error.message
    });
  }
};

module.exports = {
  getAllFicheDeTest,
  getFicheDeTestById,
  getFicheDeTestByFicheDeTravailId,
  createFicheDeTest,
  updateFicheDeTest,
  deleteFicheDeTest
};