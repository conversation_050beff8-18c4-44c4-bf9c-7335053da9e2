module.exports = (sequelize, DataTypes) => {
  const Action = sequelize.define('Action', {
    actionID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    priority: {
      type: DataTypes.STRING(20),
      allowNull: true,
      // Low, Medium, High, Critical
    },
    status: {
      type: DataTypes.STRING(20),
      allowNull: true,
      defaultValue: 'Not Started',
      // Not Started, In Progress, Completed, Cancelled
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    assigneeId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  }, {
    tableName: 'action',
    timestamps: false
  });

  // Define associations
  Action.associate = (models) => {
    // Action belongs to many ActionPlans
    Action.belongsToMany(models.ActionPlan, {
      through: 'ActionActionPlan',
      foreignKey: 'actionID',
      otherKey: 'actionPlanID',
      as: 'actionPlans'
    });

    // Action belongs to a User (assignee)
    Action.belongsTo(models.User, {
      foreignKey: 'assigneeId',
      as: 'assignee'
    });
  };

  return Action;
};
