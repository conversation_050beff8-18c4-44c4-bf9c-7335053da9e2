const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const { getRecentActivities, getUserActivities } = require('../../controllers/activities/activity-controller');

// Get recent activities (admin and super_admin only)
router.get('/', verifyToken, authorizeRoles(['admin', 'super_admin']), getRecentActivities);

// Get activities for a specific user
router.get('/user/:userId', verifyToken, async (req, res, next) => {
  // Load user roles if not already loaded
  if (!req.user.roles) {
    await require('../../middleware/auth').loadUserRoles(req, res, () => {});
  }

  // Get user role codes
  const userRoleCodes = req.user.roles ? req.user.roles.map(r => r.code) : [];
  const isAdmin = userRoleCodes.some(code => ['grc_admin', 'grc_manager', 'risk_manager'].includes(code));

  // Allow users to see their own activities, and admins to see anyone's
  if (!isAdmin && req.user.userId !== parseInt(req.params.userId)) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: Cannot access other users\' activities'
    });
  }
  next();
}, getUserActivities);

module.exports = router;
