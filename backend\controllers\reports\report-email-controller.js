const nodemailer = require('nodemailer');
const { User } = require('../../models');
const puppeteer = require('puppeteer');
const config = require('../../config/config');
const { sendEmailWithAttachment } = require('../email-controller');

/**
 * Get all users for recipient selection
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEmailRecipients = async (req, res) => {
  try {
    // Get all users with their id, name, and email
    const users = await User.findAll({
      attributes: ['id', 'username', 'email'],
      order: [['username', 'ASC']]
    });
    
    return res.status(200).json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Error fetching email recipients:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch email recipients'
    });
  }
};

/**
 * Send a report via email
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.sendReportEmail = async (req, res) => {
  // Forward the request to the working email controller's sendEmailWithAttachment
  return sendEmailWithAttachment(req, res);
};

