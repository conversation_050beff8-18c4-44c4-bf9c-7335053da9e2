const { BusinessProcess } = require('../models');

const businessProcesses = [
  // Core Business Processes
  {
    businessProcessID: 'BPS_CORE_001',
    name: 'Product Development',
    code: 'PD001',
    comment: 'End-to-end process of developing new products',
    parentBusinessProcessID: null
  },
  {
    businessProcessID: 'BPS_CORE_002',
    name: 'Sales and Marketing',
    code: 'SM001',
    comment: 'Customer acquisition and retention processes',
    parentBusinessProcessID: null
  },
  {
    businessProcessID: 'BPS_CORE_003',
    name: 'Customer Service',
    code: 'CS001',
    comment: 'Customer support and service delivery',
    parentBusinessProcessID: null
  },

  // Product Development Sub-processes
  {
    businessProcessID: 'BPS_PD_001',
    name: 'Research and Design',
    code: 'RD001',
    comment: 'Initial research and product design phase',
    parentBusinessProcessID: 'BPS_CORE_001'
  },
  {
    businessProcessID: 'BPS_PD_002',
    name: 'Prototyping',
    code: 'PT001',
    comment: 'Creating and testing product prototypes',
    parentBusinessProcessID: 'BPS_CORE_001'
  },
  {
    businessProcessID: 'BPS_PD_003',
    name: 'Quality Assurance',
    code: 'QA001',
    comment: 'Product testing and quality control',
    parentBusinessProcessID: 'BPS_CORE_001'
  },

  // Sales and Marketing Sub-processes
  {
    businessProcessID: 'BPS_SM_001',
    name: 'Lead Generation',
    code: 'LG001',
    comment: 'Identifying and qualifying potential customers',
    parentBusinessProcessID: 'BPS_CORE_002'
  },
  {
    businessProcessID: 'BPS_SM_002',
    name: 'Campaign Management',
    code: 'CM001',
    comment: 'Planning and executing marketing campaigns',
    parentBusinessProcessID: 'BPS_CORE_002'
  },
  {
    businessProcessID: 'BPS_SM_003',
    name: 'Sales Operations',
    code: 'SO001',
    comment: 'Managing sales pipeline and closing deals',
    parentBusinessProcessID: 'BPS_CORE_002'
  },

  // Customer Service Sub-processes
  {
    businessProcessID: 'BPS_CS_001',
    name: 'Ticket Management',
    code: 'TM001',
    comment: 'Processing and resolving customer support tickets',
    parentBusinessProcessID: 'BPS_CORE_003'
  },
  {
    businessProcessID: 'BPS_CS_002',
    name: 'Customer Feedback',
    code: 'CF001',
    comment: 'Collecting and analyzing customer feedback',
    parentBusinessProcessID: 'BPS_CORE_003'
  },
  {
    businessProcessID: 'BPS_CS_003',
    name: 'Service Quality Monitoring',
    code: 'SQ001',
    comment: 'Monitoring and improving service quality',
    parentBusinessProcessID: 'BPS_CORE_003'
  },

  // Support Business Processes
  {
    businessProcessID: 'BPS_SUP_001',
    name: 'Human Resources',
    code: 'HR001',
    comment: 'Employee management and development',
    parentBusinessProcessID: null
  },
  {
    businessProcessID: 'BPS_SUP_002',
    name: 'Finance Management',
    code: 'FM001',
    comment: 'Financial planning and accounting',
    parentBusinessProcessID: null
  },
  {
    businessProcessID: 'BPS_SUP_003',
    name: 'IT Operations',
    code: 'IT001',
    comment: 'Technology infrastructure and support',
    parentBusinessProcessID: null
  },

  // HR Sub-processes
  {
    businessProcessID: 'BPS_HR_001',
    name: 'Recruitment',
    code: 'RC001',
    comment: 'Hiring and onboarding new employees',
    parentBusinessProcessID: 'BPS_SUP_001'
  },
  {
    businessProcessID: 'BPS_HR_002',
    name: 'Training',
    code: 'TR001',
    comment: 'Employee training and development programs',
    parentBusinessProcessID: 'BPS_SUP_001'
  },

  // Finance Sub-processes
  {
    businessProcessID: 'BPS_FM_001',
    name: 'Budgeting',
    code: 'BG001',
    comment: 'Budget planning and allocation',
    parentBusinessProcessID: 'BPS_SUP_002'
  },
  {
    businessProcessID: 'BPS_FM_002',
    name: 'Accounting',
    code: 'AC001',
    comment: 'Financial record keeping and reporting',
    parentBusinessProcessID: 'BPS_SUP_002'
  },

  // IT Sub-processes
  {
    businessProcessID: 'BPS_IT_001',
    name: 'Infrastructure Management',
    code: 'IM001',
    comment: 'Managing IT infrastructure and systems',
    parentBusinessProcessID: 'BPS_SUP_003'
  },
  {
    businessProcessID: 'BPS_IT_002',
    name: 'Security Operations',
    code: 'SO001',
    comment: 'Managing IT security and compliance',
    parentBusinessProcessID: 'BPS_SUP_003'
  }
];

async function seedBusinessProcesses() {
  try {
    // Clear existing records
    await BusinessProcess.destroy({ where: {} });
    
    // Insert new records
    await BusinessProcess.bulkCreate(businessProcesses);
    
    console.log('Successfully seeded business processes');
  } catch (error) {
    console.error('Error seeding business processes:', error);
  }
}

// Run the seeding function
seedBusinessProcesses();

module.exports = seedBusinessProcesses;