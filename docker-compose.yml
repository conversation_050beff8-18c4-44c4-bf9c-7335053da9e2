services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5001:5001"
    environment:
      - NODE_ENV=${NODE_ENV}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_HOST=db
      - DB_PORT=${DB_PORT}
      - JWT_SECRET=${JWT_SECRET}
      - PORT=${PORT}
    depends_on:
      - db
    networks:
      - vitalis-network
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "5173:5173"
    environment:
      - VITE_API_URL=${VITE_API_URL}
    networks:
      - vitalis-network
  db:
    image: postgres:17.4
    environment:
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=${DB_NAME}
    ports:
      - "5432:5432"
    volumes:
      - db-data:/var/lib/postgresql/data
    networks:
      - vitalis-network
networks:
  vitalis-network:
    driver: bridge
volumes:
  db-data: