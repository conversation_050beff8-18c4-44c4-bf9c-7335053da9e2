import { useEffect, useState, useRef, useMemo, useCallback } from "react";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Tag, Calendar, FileText, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { DateInput } from "@/components/ui/date-input";
import { toast } from "sonner";
import { updateAuditPlanById } from '@/store/slices/auditPlanSlice';
import { debounce } from "lodash";

function CaracteristiquesTab({ auditPlan }) {
  const { id } = useParams();
  const dispatch = useDispatch();
  const abortControllerRef = useRef(null);
  const lastPlanIdRef = useRef(); // <-- Add this line
  
  const error = useSelector((state) => state.auditPlans?.error);

  const [isAutoSaving, setIsAutoSaving] = useState(false);

  const [formData, setFormData] = useState({
    name: "",
    datedebut: "",
    datefin: "",
    description: "",
    nom: '',
    statut: '',
    type: '',
    priorite: '',
    responsable: '',
    entite: '',
    site: '',
    departement: '',
    service: '',
    processus: '',
    activite: '',
    domaine: '',
    sousdomaine: '',
    risque: '',
    impact: '',
    frequence: '',
    niveau: '',
    commentaire: ''
  });

  // Only update form data when the plan ID changes
  useEffect(() => {
    if (auditPlan && auditPlan.id !== lastPlanIdRef.current) {
      setFormData({
        name: auditPlan.name || "",
        datedebut: auditPlan.datedebut || "",
        datefin: auditPlan.datefin || "",
        description: auditPlan.description || "",
        nom: auditPlan.nom || '',
        statut: auditPlan.statut || '',
        type: auditPlan.type || '',
        priorite: auditPlan.priorite || '',
        responsable: auditPlan.responsable || '',
        entite: auditPlan.entite || '',
        site: auditPlan.site || '',
        departement: auditPlan.departement || '',
        service: auditPlan.service || '',
        processus: auditPlan.processus || '',
        activite: auditPlan.activite || '',
        domaine: auditPlan.domaine || '',
        sousdomaine: auditPlan.sousdomaine || '',
        risque: auditPlan.risque || '',
        impact: auditPlan.impact || '',
        frequence: auditPlan.frequence || '',
        niveau: auditPlan.niveau || '',
        commentaire: auditPlan.commentaire || ''
      });
      lastPlanIdRef.current = auditPlan.id;
      console.log('[CaracteristiquesTab] Form reset for planId:', auditPlan.id);
    }
  }, [auditPlan]);

  // Handle error state
  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  // Auto-save function using useCallback to prevent recreation
  const autoSave = useCallback(
    async (currentFormData) => {
      if (!id || !currentFormData.name.trim()) {
        console.warn("Audit Plan ID or name is missing for auto-save.");
        return;
      }

      if (currentFormData.datefin && new Date(currentFormData.datefin) <= new Date(currentFormData.datedebut)) {
        toast.error("La date de fin doit être après la date de début");
        return;
      }

      setIsAutoSaving(true);
      // Cancel any existing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new AbortController
      abortControllerRef.current = new AbortController();

      try {
        await dispatch(updateAuditPlanById({
          id,
          data: {
            ...currentFormData,
            name: currentFormData.name.trim(),
            description: currentFormData.description.trim()
          },
          signal: abortControllerRef.current.signal
        })).unwrap();

        toast.success("Plan d'audit sauvegardé");
      } catch (saveError) {
        if (saveError.name !== 'CanceledError') {
          toast.error(saveError.message || "Erreur lors de la sauvegarde du plan d'audit");
        }
      } finally {
        setIsAutoSaving(false);
      }
    },
    [id, dispatch]
  );

  // Create debounced save function
  const debouncedSave = useMemo(
    () => debounce(autoSave, 1500), // 1.5 second debounce delay
    [autoSave]
  );

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => {
      const newFormData = { ...prev, [name]: value };
      // Only trigger save if the form has been initialized and has required data
      if (id && newFormData.name.trim()) {
        debouncedSave(newFormData);
      }
      return newFormData;
    });
  };

  // Cleanup on unmount (kept separate from debounced save effect)
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  if (!auditPlan) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
          <p className="text-gray-500">Chargement des caractéristiques du plan d'audit...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Fixed header */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 shadow-sm">
        <div className="px-4 py-3 flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-800">Caractéristiques du Plan d'Audit</h3>
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <span className="text-sm text-gray-600">
              Plan d'audit: <span className="font-medium">{auditPlan.name}</span>
            </span>
          </div>
        </div>
      </div>

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-6 max-w-4xl mx-auto">
          {/* Name field */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Tag className="h-4 w-4 text-purple-500" />
              Nom <span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Entrez le nom du plan d'audit"
              className="w-full"
              required
            />
          </div>

          {/* Date fields */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="datedebut" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Calendar className="h-4 w-4 text-blue-500" />
                Date début <span className="text-red-500">*</span>
              </Label>
              <DateInput
                id="datedebut"
                name="datedebut"
                value={formData.datedebut}
                onChange={handleInputChange}
                className="w-full"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="datefin" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                <Calendar className="h-4 w-4 text-red-500" />
                Date fin
              </Label>
              <DateInput
                id="datefin"
                name="datefin"
                value={formData.datefin}
                onChange={handleInputChange}
                className="w-full"
              />
            </div>
          </div>

          {/* Description field */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <FileText className="h-4 w-4 text-indigo-500" />
              Description
            </Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Entrez la description du plan d'audit"
              className="w-full"
              rows={4}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default CaracteristiquesTab;
