module.exports = (sequelize, DataTypes) => {
  const Operation = sequelize.define('Operation', {
    operationID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: true,
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    parentOrganizationalProcess: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'OrganizationalProcess',
        key: 'organizationalProcessID',
      },
    },
    // Keep these fields for backward compatibility
    entityID: {
      type: DataTypes.STRING,
      allowNull: true, // Changed to true to make it optional
      references: {
        model: 'Entity',
        key: 'entityID',
      },
    },
  }, {
    tableName: 'Operation', // Match the actual database table name
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['operationID'],
      },
    ],
  });

  // Associations are defined in models/index.js

  return Operation;
};
