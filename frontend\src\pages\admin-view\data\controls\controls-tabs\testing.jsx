import React from "react";
import { TestTube } from "lucide-react";

function ControlTesting() {

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <TestTube className="h-6 w-6 mr-3 text-[#F62D51]" />
          Testing
        </h2>
      </div>

      <div className="border rounded-lg shadow-sm">
        <div className="p-6 bg-white">
          <p className="text-gray-500 text-center py-8">
            Testing tab - Coming soon
          </p>
        </div>
      </div>
    </div>
  );
}

export default ControlTesting;
