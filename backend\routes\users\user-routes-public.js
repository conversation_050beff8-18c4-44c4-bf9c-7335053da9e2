const express = require('express');
const router = express.Router();
const { verifyToken } = require('../../middleware/auth');
const { getAllUsersPublic } = require('../../controllers/users/user-controller-public');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all users (public version with limited fields)
router.get('/', getAllUsersPublic);

module.exports = router;
