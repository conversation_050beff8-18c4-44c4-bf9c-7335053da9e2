const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllIncidentTypes,
  createIncidentType,
  getIncidentTypeById,
  updateIncidentType,
  deleteIncidentType
} = require('../../controllers/incidents/incident-type-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all incident types
router.get('/', getAllIncidentTypes);

// Create new incident type
router.post('/', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), createIncidentType);

// Get incident type by ID
router.get('/:id', getIncidentTypeById);

// Update incident type
router.put('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), updateIncidentType);

// Delete incident type
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteIncidentType);

module.exports = router;
