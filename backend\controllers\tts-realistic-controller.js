const axios = require('axios');

exports.generateRealisticTTS = async (req, res) => {
  try {
    const { text } = req.body;
    if (!text) {
      return res.status(400).json({ error: 'Text is required' });
    }
    const options = {
      method: 'POST',
      url: 'https://du_tts_api.p.rapidapi.com/tts/tts',
      headers: {
        'x-rapidapi-key': process.env.RAPIDAPI_KEY,
        'x-rapidapi-host': 'du_tts_api.p.rapidapi.com',
        'Content-Type': 'application/json'
      },
      data: { text }
    };
    const response = await axios.request(options);
    // The API may return a URL or audio data. We'll check for both.
    if (response.data?.url) {
      // If the API returns a URL to the audio file
      return res.json({ audioUrl: response.data.url });
    } else if (response.data?.audioContent) {
      // If the API returns base64 audio data
      return res.json({ audioContent: response.data.audioContent });
    } else {
      return res.status(500).json({ error: 'No audio returned from du_tts_api' });
    }
  } catch (error) {
    console.error('[du_tts_api] Error:', error.response?.data || error.message);
    res.status(500).json({ error: 'Failed to generate TTS audio' });
  }
}; 