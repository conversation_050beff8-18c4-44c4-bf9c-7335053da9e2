const db = require('../models');

async function fixControlActivityLogs() {
  try {
    console.log('🔧 Fixing Control Activity Logs...\n');

    // Connect to database
    await db.sequelize.authenticate();
    console.log('✅ Database connected successfully\n');

    // Step 1: Check and fix ENUM type
    console.log('📋 Step 1: Checking ENUM type...');
    try {
      const enumExists = await db.sequelize.query(`
        SELECT EXISTS (
          SELECT 1 FROM pg_type WHERE typname = 'enum_ControlActivityLogs_type'
        );
      `, { type: db.sequelize.QueryTypes.SELECT });

      if (!enumExists[0].exists) {
        console.log('❌ ENUM type does not exist. Creating it...');
        
        // Create the ENUM type
        await db.sequelize.query(`
          CREATE TYPE "enum_ControlActivityLogs_type" AS ENUM ('creation', 'update', 'deletion', 'link', 'unlink');
        `);
        
        // Update the column to use the ENUM type
        await db.sequelize.query(`
          ALTER TABLE "ControlActivityLogs" 
          ALTER COLUMN "type" TYPE "enum_ControlActivityLogs_type" 
          USING "type"::text::"enum_ControlActivityLogs_type";
        `);
        
        console.log('✅ ENUM type created and applied to table');
      } else {
        console.log('✅ ENUM type exists');
        
        // Check current values
        const enumValues = await db.sequelize.query(`
          SELECT unnest(enum_range(NULL::enum_ControlActivityLogs_type)) AS enum_value;
        `, { type: db.sequelize.QueryTypes.SELECT });
        
        const currentValues = enumValues.map(row => row.enum_value);
        console.log('📋 Current ENUM values:', currentValues);
        
        // Add missing values
        if (!currentValues.includes('link')) {
          await db.sequelize.query(`
            ALTER TYPE "enum_ControlActivityLogs_type" ADD VALUE 'link';
          `);
          console.log('✅ Added "link" value to ENUM');
        }
        
        if (!currentValues.includes('unlink')) {
          await db.sequelize.query(`
            ALTER TYPE "enum_ControlActivityLogs_type" ADD VALUE 'unlink';
          `);
          console.log('✅ Added "unlink" value to ENUM');
        }
      }
    } catch (enumError) {
      console.error('❌ Error with ENUM type:', enumError.message);
    }

    // Step 2: Test activity log creation
    console.log('\n📋 Step 2: Testing activity log creation...');
    
    // Find a test control
    const testControl = await db.Control.findOne({
      attributes: ['controlID', 'name'],
      limit: 1
    });
    
    if (!testControl) {
      console.log('❌ No controls found to test with');
      return;
    }
    
    console.log(`📋 Using test control: ${testControl.controlID} - ${testControl.name}`);
    
    // Test creation activity
    try {
      const creationLog = await db.ControlActivityLog.create({
        control_id: testControl.controlID,
        user: 'Fix Script Test User',
        type: 'creation',
        details: `Test creation activity log`
      }, {
        returning: ['id', 'control_id', 'timestamp', 'user', 'type', 'details']
      });
      
      console.log('✅ Creation activity log test: SUCCESS', creationLog.id);
      
      // Clean up
      await creationLog.destroy();
      console.log('✅ Test creation log cleaned up');
      
    } catch (createError) {
      console.error('❌ Creation activity log test: FAILED', createError.message);
    }
    
    // Test update activity
    try {
      const updateLog = await db.ControlActivityLog.create({
        control_id: testControl.controlID,
        user: 'Fix Script Test User',
        type: 'update',
        field: 'name',
        old_value: 'Old Name',
        new_value: 'New Name',
        details: `Test update activity log`
      }, {
        returning: ['id', 'control_id', 'timestamp', 'user', 'type', 'field', 'old_value', 'new_value', 'details']
      });
      
      console.log('✅ Update activity log test: SUCCESS', updateLog.id);
      
      // Clean up
      await updateLog.destroy();
      console.log('✅ Test update log cleaned up');
      
    } catch (updateError) {
      console.error('❌ Update activity log test: FAILED', updateError.message);
    }
    
    // Test link activity
    try {
      const linkLog = await db.ControlActivityLog.create({
        control_id: testControl.controlID,
        user: 'Fix Script Test User',
        type: 'link',
        field: 'operation',
        new_value: 'OP_TEST_123',
        details: `Test link activity log`
      }, {
        returning: ['id', 'control_id', 'timestamp', 'user', 'type', 'field', 'new_value', 'details']
      });
      
      console.log('✅ Link activity log test: SUCCESS', linkLog.id);
      
      // Clean up
      await linkLog.destroy();
      console.log('✅ Test link log cleaned up');
      
    } catch (linkError) {
      console.error('❌ Link activity log test: FAILED', linkError.message);
    }

    // Step 3: Test activity logging functions
    console.log('\n📋 Step 3: Testing activity logging functions...');
    
    try {
      const { logCreationActivity, logUpdateActivities } = require('../controllers/data/control-activity-controller');
      
      // Test creation function
      const creationResult = await logCreationActivity(testControl, 'Fix Script Test User');
      console.log(`✅ logCreationActivity function: ${creationResult ? 'SUCCESS' : 'FAILED'}`);
      
      // Test update function
      const updateResult = await logUpdateActivities(
        testControl, 
        { name: 'Updated Name Test' }, 
        'Fix Script Test User'
      );
      console.log(`✅ logUpdateActivities function: ${updateResult.length > 0 ? 'SUCCESS' : 'FAILED'}`);
      console.log(`   Changes logged: ${updateResult.join(', ')}`);
      
      // Clean up any logs created by the functions
      await db.ControlActivityLog.destroy({
        where: {
          control_id: testControl.controlID,
          user: 'Fix Script Test User'
        }
      });
      console.log('✅ Function test logs cleaned up');
      
    } catch (functionError) {
      console.error('❌ Error testing functions:', functionError.message);
    }

    // Step 4: Final verification
    console.log('\n📋 Step 4: Final verification...');
    
    const finalCount = await db.ControlActivityLog.count();
    console.log(`📊 Total activity logs in database: ${finalCount}`);
    
    // Check ENUM values one more time
    const finalEnumValues = await db.sequelize.query(`
      SELECT unnest(enum_range(NULL::enum_ControlActivityLogs_type)) AS enum_value;
    `, { type: db.sequelize.QueryTypes.SELECT });
    
    console.log('✅ Final ENUM values:');
    finalEnumValues.forEach(row => {
      console.log(`   - ${row.enum_value}`);
    });

    console.log('\n🎯 Control Activity Logs fix completed!');
    console.log('✅ You can now restart your backend server and test activity logging.');

  } catch (error) {
    console.error('❌ Error during fix:', error);
  } finally {
    await db.sequelize.close();
  }
}

// Run the fix
fixControlActivityLogs();
