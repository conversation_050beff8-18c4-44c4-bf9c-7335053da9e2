const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Database configuration
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: console.log,
  }
);

async function runMigration() {
  try {
    console.log('Starting Control ENUM values update migration...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Import and run the migration
    const migration = require('../migrations/20241227000002-update-control-enum-values.js');
    
    console.log('Running migration UP...');
    await migration.up(sequelize.getQueryInterface(), Sequelize);
    
    console.log('✅ Migration completed successfully!');
    console.log('Control ENUM values have been updated.');
    
    // Verify the changes
    console.log('\nVerifying migration...');
    const [results] = await sequelize.query(`
      SELECT column_name, data_type, udt_name 
      FROM information_schema.columns 
      WHERE table_name = 'Control' 
      AND column_name IN ('controlExecutionMethod', 'organizationalLevel', 'sampleType', 'testingFrequency', 'testingMethod')
      ORDER BY column_name;
    `);
    
    console.log('Updated column types:');
    results.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type} (${row.udt_name})`);
    });
    
    // Show sample data
    console.log('\nSample data after migration:');
    const [sampleData] = await sequelize.query(`
      SELECT "controlID", "controlExecutionMethod", "organizationalLevel", "sampleType", "testingFrequency", "testingMethod"
      FROM "Control" 
      LIMIT 5;
    `);
    
    console.table(sampleData);
    
    // Show available ENUM values
    console.log('\nAvailable ENUM values:');
    
    const enumQueries = [
      { field: 'controlExecutionMethod', query: `SELECT unnest(enum_range(NULL::"enum_Control_controlExecutionMethod_temp")) as value` },
      { field: 'organizationalLevel', query: `SELECT unnest(enum_range(NULL::"enum_Control_organizationalLevel_new")) as value` },
      { field: 'sampleType', query: `SELECT unnest(enum_range(NULL::"enum_Control_sampleType_new")) as value` },
      { field: 'testingFrequency', query: `SELECT unnest(enum_range(NULL::"enum_Control_testingFrequency_new")) as value` },
      { field: 'testingMethod', query: `SELECT unnest(enum_range(NULL::"enum_Control_testingMethod_new")) as value` }
    ];
    
    for (const enumQuery of enumQueries) {
      try {
        const [enumValues] = await sequelize.query(enumQuery.query);
        console.log(`${enumQuery.field}:`, enumValues.map(row => row.value).join(', '));
      } catch (error) {
        console.log(`${enumQuery.field}: Could not retrieve ENUM values`);
      }
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run the migration
runMigration();
