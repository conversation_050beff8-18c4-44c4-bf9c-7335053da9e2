const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ControlQuestionAssignment = sequelize.define('ControlQuestionAssignment', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    controlId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Controls',
        key: 'controlID'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    assignedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    assignedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    }
  }, {
    tableName: 'control_question_assignments',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['userId', 'controlId']
      },
      {
        fields: ['userId']
      },
      {
        fields: ['controlId']
      }
    ]
  });

  ControlQuestionAssignment.associate = (models) => {
    // Association with User (assigned user)
    ControlQuestionAssignment.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });

    // Association with User (who assigned)
    ControlQuestionAssignment.belongsTo(models.User, {
      foreignKey: 'assignedBy',
      as: 'assignedByUser'
    });

    // Association with Control
    ControlQuestionAssignment.belongsTo(models.Control, {
      foreignKey: 'controlId',
      as: 'control'
    });
  };

  return ControlQuestionAssignment;
};
