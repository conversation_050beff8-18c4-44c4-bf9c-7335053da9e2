import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { toast } from "sonner";
import { getAuthHeaders } from "@/utils/auth-headers";
import { getApiBaseUrl } from '../utils/api-config';

// MODIFY: Update with your specific API endpoint
const API_URL = `${getApiBaseUrl()}/items`;

// Get all items
export const getItems = createAsyncThunk(
  "item/getItems",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(API_URL, {
        withCredentials: true,
        headers: getAuthHeaders()
      });
      return response.data.data;
    } catch (error) {
      console.error("Error in getItems:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to fetch items");
    }
  }
);

// Get item by ID
export const getItemById = createAsyncThunk(
  "item/getItemById",
  async (id, { rejectWithValue }) => {
    try {
      console.log("Fetching item with ID:", id);
      
      const response = await axios.get(`${API_URL}/${id}`, {
        withCredentials: true,
        headers: getAuthHeaders()
      });
      
      console.log("API Response:", response.data);
      return response.data.data;
    } catch (error) {
      console.error("Error in getItemById:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to fetch item");
    }
  }
);

// Create new item
export const createItem = createAsyncThunk(
  "item/createItem",
  async (itemData, { rejectWithValue }) => {
    try {
      const response = await axios.post(API_URL, itemData, {
        withCredentials: true,
        headers: getAuthHeaders()
      });
      return response.data.data;
    } catch (error) {
      console.error("Error in createItem:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to create item");
    }
  }
);

// Update item
export const updateItem = createAsyncThunk(
  "item/updateItem",
  async ({ id, itemData }, { rejectWithValue }) => {
    try {
      const response = await axios.put(`${API_URL}/${id}`, itemData, {
        withCredentials: true,
        headers: getAuthHeaders()
      });
      return response.data.data;
    } catch (error) {
      console.error("Error in updateItem:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to update item");
    }
  }
);

// Delete item
export const deleteItem = createAsyncThunk(
  "item/deleteItem",
  async (id, { rejectWithValue }) => {
    try {
      await axios.delete(`${API_URL}/${id}`, {
        withCredentials: true,
        headers: getAuthHeaders()
      });
      return id;
    } catch (error) {
      console.error("Error in deleteItem:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to delete item");
    }
  }
);

// Initial state
const initialState = {
  items: [],
  currentItem: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  error: null,
};

// Create slice
const itemSlice = createSlice({
  name: "item",
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.error = null;
    },
    clearCurrentItem: (state) => {
      state.currentItem = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all items
      .addCase(getItems.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getItems.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.items = action.payload;
      })
      .addCase(getItems.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      
      // Get item by ID
      .addCase(getItemById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getItemById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentItem = action.payload;
      })
      .addCase(getItemById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      
      // Create item
      .addCase(createItem.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createItem.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.items.push(action.payload);
      })
      .addCase(createItem.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      
      // Update item
      .addCase(updateItem.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateItem.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentItem = action.payload;
        state.items = state.items.map((item) =>
          item.id === action.payload.id ? action.payload : item
        );
      })
      .addCase(updateItem.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      
      // Delete item
      .addCase(deleteItem.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteItem.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.items = state.items.filter((item) => item.id !== action.payload);
      })
      .addCase(deleteItem.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      });
  },
});

export const { reset, clearCurrentItem } = itemSlice.actions;
export default itemSlice.reducer;
