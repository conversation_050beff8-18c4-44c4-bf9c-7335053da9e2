import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { getApiEndpointUrl, getAuthHeaders } from '@/utils/api-config';
import { toast } from 'sonner';
import { format } from 'date-fns';

const EmailReportModal = ({ isOpen, onClose, defaultSubject = '', defaultMessage = '', defaultAttachment = null, reportTitle }) => {
  const [recipients, setRecipients] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [selectedRecipients, setSelectedRecipients] = useState([]);
  const [customEmails, setCustomEmails] = useState('');
  const [subject, setSubject] = useState(defaultSubject);
  const [message, setMessage] = useState(defaultMessage);
  const [attachmentFile, setAttachmentFile] = useState(defaultAttachment);
  const [attachmentBase64, setAttachmentBase64] = useState(null);
  const [attachmentName, setAttachmentName] = useState('');
  const [attachmentType, setAttachmentType] = useState('');
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    if (isOpen) {
      fetchRecipients();
      setSubject(defaultSubject);
      setMessage(defaultMessage);
      setAttachmentFile(defaultAttachment);
      if (defaultAttachment && typeof defaultAttachment === 'object' && defaultAttachment.base64 && defaultAttachment.filename && defaultAttachment.contentType) {
        setAttachmentBase64(defaultAttachment.base64);
        setAttachmentName(defaultAttachment.filename);
        setAttachmentType(defaultAttachment.contentType);
      } else {
        setAttachmentBase64(null);
        setAttachmentName('');
        setAttachmentType('');
      }
      setSelectedRecipients([]);
      setCustomEmails('');
    }
  }, [isOpen, defaultSubject, defaultMessage, defaultAttachment]);

  const fetchRecipients = async () => {
    try {
      setLoading(true);
      const url = getApiEndpointUrl('reports/email/recipients');
      const response = await axios.get(url, { headers: getAuthHeaders(), withCredentials: true });
      if (response.data.success) {
        setRecipients(response.data.data || []);
      } else {
        toast.error('Failed to load recipients');
      }
    } catch (error) {
      console.error('Error fetching recipients:', error);
      toast.error('Failed to load recipients');
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    setAttachmentFile(file);
    setAttachmentName(file.name);
    setAttachmentType(file.type);
    // Convert to base64
    const reader = new FileReader();
    reader.onload = () => {
      const base64 = reader.result.split(',')[1];
      setAttachmentBase64(base64);
    };
    reader.readAsDataURL(file);
  };

  const handleSendEmail = async () => {
    // Validate recipients
    const allEmails = [
      ...selectedRecipients.map(id => {
        const user = recipients.find(u => u.id === parseInt(id));
        return user?.email;
      }).filter(Boolean),
      ...customEmails.split(',').map(e => e.trim()).filter(e => e)
    ];
    if (allEmails.length === 0) {
      toast.error('Please select or enter at least one recipient email');
      return;
    }
    // Use default subject/message if blank
    let finalSubject = subject && subject.trim() ? subject : (typeof reportTitle === 'string' && reportTitle ? `Rapport: ${reportTitle}` : 'Rapport PDF');
    let finalMessage = message && message.trim() ? message : 'Veuillez trouver ci-joint le rapport PDF généré.';
    if (!attachmentBase64 || !attachmentName || !attachmentType) {
      toast.error('Aucun PDF généré à envoyer.');
      return;
    }
    try {
      setSending(true);
      const payload = {
        to: allEmails,
        subject: finalSubject,
        text: finalMessage,
        attachment: {
          filename: attachmentName,
          content: attachmentBase64,
          contentType: attachmentType
        }
      };
      const url = getApiEndpointUrl('email/send-with-attachment');
      const response = await axios.post(url, payload, {
        withCredentials: true,
        headers: getAuthHeaders()
      });
      if (response.data.success) {
        toast.success('Email sent successfully');
        onClose();
      } else {
        toast.error(response.data.message || 'Failed to send email');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      toast.error(error.response?.data?.message || 'Failed to send email');
    } finally {
      setSending(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-bold mb-4">Envoyer un email avec pièce jointe</h2>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Sélectionner les destinataires</label>
          <input
            type="text"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            placeholder="Rechercher un utilisateur..."
            className="w-full mb-2 p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <div className="max-h-40 overflow-y-auto border rounded p-2 bg-gray-50">
            {recipients
              .filter(user => {
                const term = searchTerm.toLowerCase();
                return (
                  (user.name && user.name.toLowerCase().includes(term)) ||
                  (user.username && user.username.toLowerCase().includes(term)) ||
                  (user.email && user.email.toLowerCase().includes(term))
                );
              })
              .map(user => (
                <label key={user.id} className="flex items-center space-x-2 py-1 cursor-pointer">
                  <input
                    type="checkbox"
                    value={user.id}
                    checked={selectedRecipients.includes(user.id.toString())}
                    onChange={e => {
                      const id = e.target.value;
                      setSelectedRecipients(prev =>
                        e.target.checked
                          ? [...prev, id]
                          : prev.filter(val => val !== id)
                      );
                    }}
                    className="accent-blue-600"
                    disabled={loading}
                  />
                  <span>{user.name || user.username || user.email}</span>
                </label>
              ))}
          </div>
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Ajouter des emails personnalisés (séparés par des virgules)</label>
          <input
            type="text"
            value={customEmails}
            onChange={e => setCustomEmails(e.target.value)}
            placeholder="ex: <EMAIL>, <EMAIL>"
            className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Sujet</label>
          <input
            type="text"
            value={subject}
            onChange={e => setSubject(e.target.value)}
            className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Message</label>
          <textarea
            value={message}
            onChange={e => setMessage(e.target.value)}
            className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={3}
          />
        </div>
        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 transition-colors"
            disabled={sending}
          >
            Annuler
          </button>
          <button
            onClick={handleSendEmail}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            disabled={sending}
          >
            {sending ? 'Envoi...' : 'Envoyer'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EmailReportModal;



