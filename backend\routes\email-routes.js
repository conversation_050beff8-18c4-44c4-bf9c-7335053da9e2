const express = require('express');
const router = express.Router();
const emailController = require('../controllers/email-controller');
const authMiddleware = require('../middleware/auth');

// Send a normal email
router.post('/send', authMiddleware.verifyToken, emailController.sendEmail);

// Send an email with attachment (JSON body, no multer)
router.post('/send-with-attachment', authMiddleware.verifyToken, emailController.sendEmailWithAttachment);

module.exports = router; 