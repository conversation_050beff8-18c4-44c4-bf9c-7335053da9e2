import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";
import { getApiBaseUrl } from '../../utils/api-config';

// Try to import getApiEndpointUrl, but fallback to a local implementation if not available
let getApiEndpointUrl;
try {
  const apiConfig = require('../../utils/api-config');
  getApiEndpointUrl = apiConfig.getApiEndpointUrl;
} catch (e) {
  // Fallback implementation
  getApiEndpointUrl = (endpoint) => {
    const baseUrl = getApiBaseUrl();
    return `${baseUrl}/${endpoint.startsWith('/') ? endpoint.substring(1) : endpoint}`;
  };
}

const API_URL = `${getApiBaseUrl()}/auth`;

// Define the permission matrix for each role
const PERMISSION_MATRIX = {
  // Full access
  'grc_admin': ['create', 'read', 'update', 'delete', 'user_management', 'workflow_advance', 'workflow_validate', 'workflow_reject', 'workflow_close'],

  // Full CRUD access plus workflow capabilities
  'grc_manager': ['create', 'read', 'update', 'delete', 'workflow_advance', 'workflow_validate', 'workflow_reject', 'workflow_close'],
  'risk_manager': ['create', 'read', 'update', 'delete', 'workflow_advance', 'workflow_validate', 'workflow_reject', 'workflow_close'],
  'incident_manager': ['create', 'read', 'update', 'delete', 'workflow_advance', 'workflow_validate', 'workflow_reject', 'workflow_close'],

  // Limited workflow capabilities
  'grc_contributor': ['create', 'read', 'update', 'workflow_advance_limited', 'workflow_reject_own'],

  // Other roles with Read-only access by default
  'internal_controller': ['read'],
  'compliance_manager': ['read'],
  'audit_director': ['read'],
  'auditor': ['read'],
};

// Helper function to check if a user has a specific permission
export const hasPermission = (state, permission) => {
  if (!state.auth.isAuthenticated || !state.auth.user || !state.auth.user.roles) {
    return false;
  }

  const userRoles = state.auth.user.roles;

  // Check if any of user's roles grant the permission
  return userRoles.some(role => {
    // Handle both code and name for role matching
    const roleCode = role.code ? role.code.toLowerCase() : '';
    const roleName = role.name ? role.name.toLowerCase().replace(/\s+/g, '_') : '';

    // Check if the role code exists in the matrix
    const permissionsFromCode = PERMISSION_MATRIX[roleCode] || [];

    // If the role name is similar to a key in the matrix, also check that
    const permissionsFromName = Object.keys(PERMISSION_MATRIX).some(key =>
      roleName.includes(key) || key.includes(roleName)
    ) ?
      Object.keys(PERMISSION_MATRIX).filter(key =>
        roleName.includes(key) || key.includes(roleName)
      ).flatMap(key => PERMISSION_MATRIX[key])
      : [];

    // Combine permissions from both code and name matching
    const allPermissions = [...new Set([...permissionsFromCode, ...permissionsFromName])];

    return allPermissions.includes(permission);
  });
};

// Helper function for workflow permissions
export const hasWorkflowPermission = (state, workflowAction, currentStep, isCreator = false) => {
  // Admin/Manager roles can do any action
  if (hasPermission(state, `workflow_${workflowAction}`)) {
    return true;
  }

  // For contributors, special limited permissions
  if (workflowAction === 'advance' && hasPermission(state, 'workflow_advance_limited')) {
    // Contributors can only advance up to but not beyond the 'To Approve' step
    if (currentStep === 'To Approve') {
      return false;
    }
    return true;
  }

  // Contributors can reject their own incidents/risks
  if (workflowAction === 'reject' && hasPermission(state, 'workflow_reject_own') && isCreator) {
    return true;
  }

  return false;
};

// Helper function to check if a user has any of the specified permissions
export const hasAnyPermission = (state, permissions) => {
  return permissions.some(permission => hasPermission(state, permission));
};

// Helper function to check if a user has all of the specified permissions
export const hasAllPermissions = (state, permissions) => {
  return permissions.every(permission => hasPermission(state, permission));
};

const initialState = {
  user: null,
  isAuthenticated: null, // Changed from false to null
  isLoading: true,
  token: localStorage.getItem('authToken') // Initialize from localStorage
};

export const registerUser = createAsyncThunk(
  "auth/register",
  async (formData, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${API_URL}/register`,
        formData,
        { withCredentials: true }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

export const loginUser = createAsyncThunk(
  "auth/login",
  async (formData, { rejectWithValue }) => {
    try {
      // Make login request without logging sensitive information
      const response = await axios.post(
        `${API_URL}/login`,
        formData,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      // Store token in localStorage if it exists in the response
      if (response.data?.token) {
        localStorage.setItem('authToken', response.data.token);
      }

      return response.data;
    } catch (error) {
      console.error("[AuthSlice loginUser] Login failed");
      return rejectWithValue(error.response?.data || { message: 'Login failed' });
    }
  }
);

export const logoutUser = createAsyncThunk(
  "/auth/logout",
  async () => {
    const response = await axios.post(
      getApiEndpointUrl('auth/logout'),
      {},
      {
        withCredentials: true,
      }
    );

    // Clear token from localStorage on logout
    localStorage.removeItem('authToken');

    return response.data;
  }
);

export const checkAuth = createAsyncThunk(
  "auth/checkAuth",
  async (_, { rejectWithValue }) => {
    try {
      // Get token from localStorage
      const token = localStorage.getItem('authToken');
      const headers = {};

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await axios.get(
        `${API_URL}/check-auth`,
        {
          withCredentials: true,
          headers
        }
      );

      // Update token in localStorage if a new one is provided
      if (response.data?.token) {
        localStorage.setItem('authToken', response.data.token);
      }

      return response.data;
    } catch (error) {
      console.error("[AuthSlice checkAuth] Authentication check failed");
      return rejectWithValue(error.response?.data || { message: 'Authentication failed' });
    }
  }
);

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    // Add a reducer to handle logout
    logout: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.isLoading = false;
      state.token = null;
      // Clear token from localStorage
      localStorage.removeItem('authToken');
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(registerUser.fulfilled, (state) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
      })
      .addCase(registerUser.rejected, (state) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
      })
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        // Store token in state
        state.token = action.payload.token;
        // Store token in localStorage (redundant but safe)
        if (action.payload.token) {
          localStorage.setItem('authToken', action.payload.token);
        }
      })
      .addCase(loginUser.rejected, (state) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
      })
      .addCase(checkAuth.pending, (state) => {
        state.isLoading = true;
        state.isAuthenticated = null;
      })
      .addCase(checkAuth.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload && action.payload.user) {
          state.isAuthenticated = true;
          state.user = action.payload.user;
        } else {
          state.isAuthenticated = false; // Explicitly set to false if user data is not as expected
          state.user = null;
        }
      })
      .addCase(checkAuth.rejected, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
      });
  },
});

export const { setUser, logout } = authSlice.actions;
export default authSlice.reducer;
