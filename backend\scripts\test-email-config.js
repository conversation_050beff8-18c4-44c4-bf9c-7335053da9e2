require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });
const { sendEmailDirect } = require('../controllers/email-controller');

async function testEmailConfiguration() {
  try {
    console.log('🧪 Testing Email Configuration...');
    
    // Check environment variables
    console.log('\n📋 Email Configuration:');
    console.log(`   SMTP_HOST: ${process.env.SMTP_HOST || 'NOT SET'}`);
    console.log(`   SMTP_PORT: ${process.env.SMTP_PORT || 'NOT SET'}`);
    console.log(`   SMTP_USER: ${process.env.SMTP_USER || 'NOT SET'}`);
    console.log(`   SMTP_PASS: ${process.env.SMTP_PASS ? '***SET***' : 'NOT SET'}`);
    
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASS) {
      console.error('❌ Email configuration incomplete!');
      console.log('\n📝 Required environment variables:');
      console.log('   SMTP_HOST=smtp.gmail.com');
      console.log('   SMTP_PORT=465');
      console.log('   SMTP_USER=<EMAIL>');
      console.log('   SMTP_PASS=your-app-password');
      return;
    }
    
    console.log('\n✅ Email configuration looks complete');
    
    // Test email sending
    console.log('\n📧 Testing email sending...');
    const testEmail = {
      to: process.env.SMTP_USER, // Send to self for testing
      subject: 'Test Email - Control Assignment System',
      text: 'This is a test email from the control assignment system. If you receive this, email configuration is working correctly!',
      html: '<p>This is a <b>test email</b> from the control assignment system.</p><p>If you receive this, email configuration is working correctly! ✅</p>'
    };
    
    console.log(`   Sending test email to: ${testEmail.to}`);
    
    const result = await sendEmailDirect(testEmail);
    
    console.log('✅ Email sent successfully!');
    console.log(`   Message ID: ${result.messageId}`);
    console.log(`   Response: ${result.response}`);
    
    console.log('\n🎉 Email configuration test completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   • SMTP configuration: ✅ Complete');
    console.log('   • Connection test: ✅ Successful');
    console.log('   • Email sending: ✅ Working');
    console.log('\n✉️ Check your inbox for the test email!');
    
  } catch (error) {
    console.error('\n❌ Email test failed:', error.message);
    
    if (error.message.includes('ESOCKET') || error.message.includes('connection failed')) {
      console.log('\n🔧 Troubleshooting tips:');
      console.log('   • Check your internet connection');
      console.log('   • Verify SMTP_HOST is correct (smtp.gmail.com for Gmail)');
      console.log('   • Verify SMTP_PORT is correct (465 for Gmail SSL)');
      console.log('   • Check if your firewall/antivirus is blocking the connection');
    } else if (error.message.includes('authentication failed')) {
      console.log('\n🔧 Authentication troubleshooting:');
      console.log('   • Verify SMTP_USER is your full Gmail address');
      console.log('   • Verify SMTP_PASS is your Gmail App Password (not regular password)');
      console.log('   • Make sure 2-factor authentication is enabled on Gmail');
      console.log('   • Generate a new App Password if needed');
    }
    
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testEmailConfiguration()
    .then(() => {
      console.log('\n✅ Email test script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Email test script failed');
      process.exit(1);
    });
}

module.exports = testEmailConfiguration;
