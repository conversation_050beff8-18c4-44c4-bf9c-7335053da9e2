const { Operation } = require('../../models');

// Get all operations
const getAllOperations = async (req, res) => {
  try {
    const operations = await Operation.findAll();
    res.json({
      success: true,
      data: operations
    });
  } catch (error) {
    console.error('Error fetching operations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch operations',
      error: error.message
    });
  }
};

// Create new operation
const createOperation = async (req, res) => {
  try {
    const {
      operationID,
      name,
      code,
      comment,
      parentOrganizationalProcess
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name'].filter(field => !req.body[field])
      });
    }

    const operation = await Operation.create({
      operationID: operationID || `OP_${Date.now()}`,
      name,
      code: code || null,
      comment: comment || null,
      parentOrganizationalProcess: parentOrganizationalProcess || null
    });

    return res.status(201).json({
      success: true,
      message: 'Operation created successfully',
      data: operation
    });
  } catch (error) {
    console.error('Error creating operation:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create operation'
    });
  }
};

// Get operation by ID
const getOperationById = async (req, res) => {
  try {
    const { id } = req.params;
    const operation = await Operation.findByPk(id);

    if (!operation) {
      return res.status(404).json({
        success: false,
        message: 'Operation not found'
      });
    }

    res.json({
      success: true,
      data: operation
    });
  } catch (error) {
    console.error('Error fetching operation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch operation'
    });
  }
};

// Update operation
const updateOperation = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      code,
      comment,
      parentOrganizationalProcess
    } = req.body;

    const operation = await Operation.findByPk(id);

    if (!operation) {
      return res.status(404).json({
        success: false,
        message: 'Operation not found'
      });
    }

    // Update fields
    await operation.update({
      name: name || operation.name,
      code: code !== undefined ? code : operation.code,
      comment: comment !== undefined ? comment : operation.comment,
      parentOrganizationalProcess: parentOrganizationalProcess !== undefined ? parentOrganizationalProcess : operation.parentOrganizationalProcess
    });

    res.json({
      success: true,
      message: 'Operation updated successfully',
      data: operation
    });
  } catch (error) {
    console.error('Error updating operation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update operation'
    });
  }
};

// Delete operation
const deleteOperation = async (req, res) => {
  try {
    const { id } = req.params;
    const operation = await Operation.findByPk(id);

    if (!operation) {
      return res.status(404).json({
        success: false,
        message: 'Operation not found'
      });
    }

    await operation.destroy();

    res.json({
      success: true,
      message: 'Operation deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting operation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete operation'
    });
  }
};

module.exports = {
  getAllOperations,
  createOperation,
  getOperationById,
  updateOperation,
  deleteOperation
};
