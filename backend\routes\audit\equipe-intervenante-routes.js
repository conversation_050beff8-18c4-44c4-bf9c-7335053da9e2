const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  createEquipeIntervenante,
  getEquipeIntervenantes,
  updateEquipeIntervenante,
  deleteEquipeIntervenante
} = require('../../controllers/audit/equipe-intervenante-controller');
console.log('EquipeIntervenante routes loaded');
// Apply authentication middleware to all routes
router.use(verifyToken);

// List all or filter
router.get('/', authorizeRoles(['audit_director', 'auditor']), getEquipeIntervenantes);
// Create
router.post('/', authorizeRoles(['audit_director', 'auditor']), createEquipeIntervenante);
// Update
router.put('/:id', authorizeRoles(['audit_director', 'auditor']), updateEquipeIntervenante);
// Delete
router.delete('/:id', authorizeRoles(['audit_director']), deleteEquipeIntervenante);

module.exports = router; 