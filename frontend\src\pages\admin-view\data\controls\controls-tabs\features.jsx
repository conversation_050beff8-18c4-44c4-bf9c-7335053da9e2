import React, { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { useOutletContext, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Loader2, Database, Gauge, FileText, Activity, Layers, Link } from "lucide-react";
import { updateControl } from "@/store/slices/controlSlice";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Combobox } from "@/components/ui/combobox";
import { toast } from "sonner";
import isEqual from 'lodash/isEqual';
import { Checkbox } from '@/components/ui/checkbox';
import { getAllControlTypes } from "@/store/slices/controlTypeSlice";
import { getAllRisks } from "@/store/slices/riskSlice";
import { getAllBusinessProcesses } from "@/store/slices/businessProcessSlice";
import { getAllOrganizationalProcesses } from "@/store/slices/organizationalProcessSlice";
import { getAllOperations } from "@/store/slices/operationSlice";
import { getAllApplications } from "@/store/slices/applicationSlice";
import { getAllEntities } from "@/store/slices/entitySlice";
import { getAllActionPlans } from "@/store/slices/actionPlanSlice";

// Import selection modals
import { ControlTypeSelectionModal } from "@/components/controls/ControlTypeSelectionModal";
import { BusinessProcessSelectionModal } from "@/components/controls/BusinessProcessSelectionModal";
import { OrganizationalProcessSelectionModal } from "@/components/controls/OrganizationalProcessSelectionModal";
import { OperationSelectionModal } from "@/components/controls/OperationSelectionModal";
import { ApplicationSelectionModal } from "@/components/controls/ApplicationSelectionModal";
import { EntitySelectionModal } from "@/components/controls/EntitySelectionModal";
import { RiskSelectionModal } from "@/components/controls/RiskSelectionModal";

// Debounce utility
function useDebouncedCallback(callback, delay) {
  const timeoutRef = React.useRef();
  return (...args) => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  };
}

const InputField = React.memo(({ label, name, value, onChange, disabled = false, icon, t }) => {
  return (
    <div className="mb-4">
      <Label className="flex items-center gap-2 mb-3">
        {icon && <span className="text-gray-500">{icon}</span>}
        {label}
      </Label>
      <Input
        id={name}
        name={name}
        type="text"
        value={value || ""}
        onChange={onChange}
        disabled={disabled}
        placeholder={disabled ? t('common.none', 'None') : ""}
      />
    </div>
  );
});

function ControlFeatures() {
  const { control, onNavigateAway } = useOutletContext();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();

  // Remove translation debug logs for performance

  // Get state from Redux store
  const controlTypes = useSelector((state) => state.controlType?.controlTypes || []);
  const risks = useSelector((state) => state.risk?.risks || []);
  const businessProcesses = useSelector((state) => state.businessProcess?.businessProcesses || []);
  const organizationalProcesses = useSelector((state) => state.organizationalProcess?.organizationalProcesses || []);
  const operations = useSelector((state) => state.operation?.operations || []);
  const applications = useSelector((state) => state.application?.applications || []);
  const entities = useSelector((state) => state.entity?.entities || []);
  const actionPlans = useSelector((state) => state.actionPlan?.actionPlans || []);

  // Loading states
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);

  // Form data state
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    controlKey: "",
    controlExecutionMethod: "",
    objective: "",
    executionProcedure: "",
    operationalCost: "",
    organizationalLevel: "Global",
    sampleType: "Command",
    testingFrequency: "Quarterly",
    testingMethod: "Observation",
    testingPopulationSize: "",
    testingProcedure: "",
    implementingActionPlan: "none",
    businessProcessID: "none",
    organizationalProcessID: "none",
    operationID: "none",
    applicationID: "none",
    entityID: "none",
    controlTypeID: "none",
    riskID: "none",
    comment: "",
  });

  const lastSavedDataRef = useRef(formData);

  // Track if there are unsaved changes
  const hasUnsavedChanges = useMemo(() => {
    return !isEqual(formData, lastSavedDataRef.current);
  }, [formData]);

  const isFirstLoad = useRef(true);
  const lastControlDataRef = useRef(null);
  const isManualUpdate = useRef(false); // Flag to prevent auto-save after manual updates

  // Modal states for selection dialogs
  const [isControlTypeModalOpen, setIsControlTypeModalOpen] = useState(false);
  const [isBusinessProcessModalOpen, setIsBusinessProcessModalOpen] = useState(false);
  const [isOrganizationalProcessModalOpen, setIsOrganizationalProcessModalOpen] = useState(false);
  const [isOperationModalOpen, setIsOperationModalOpen] = useState(false);
  const [isApplicationModalOpen, setIsApplicationModalOpen] = useState(false);
  const [isEntityModalOpen, setIsEntityModalOpen] = useState(false);
  const [isRiskModalOpen, setIsRiskModalOpen] = useState(false);

  // Loading states for linking operations
  const [isLinkingControlType, setIsLinkingControlType] = useState(false);
  const [isLinkingBusinessProcess, setIsLinkingBusinessProcess] = useState(false);
  const [isLinkingOrganizationalProcess, setIsLinkingOrganizationalProcess] = useState(false);
  const [isLinkingOperation, setIsLinkingOperation] = useState(false);
  const [isLinkingApplication, setIsLinkingApplication] = useState(false);
  const [isLinkingEntity, setIsLinkingEntity] = useState(false);
  const [isLinkingRisk, setIsLinkingRisk] = useState(false);

  // Options for select fields with French labels
  const controlExecutionMethodOptions = [
    { id: "Observation", name: "Observation" },
    { id: "Exhaustive", name: "Exhaustif" },
    { id: "Exception Control", name: "Contrôle d'Exception" },
    { id: "By Sample", name: "Par Échantillon" },
    { id: "Standard Control", name: "Contrôle Standard" }
  ];

  const organizationalLevelOptions = [
    { id: "Global", name: "Global" },
    { id: "Local", name: "Local" }
  ];

  const sampleTypeOptions = [
    { id: "Command", name: "Commande" },
    { id: "Bill", name: "Facture" },
    { id: "Contract", name: "Contrat" }
  ];

  const testingFrequencyOptions = [
    { id: "Quarterly", name: "Trimestriel" },
    { id: "Bi-Yearly", name: "Semestriel" },
    { id: "Yearly", name: "Annuel" }
  ];

  const testingMethodOptions = [
    { id: "Observation", name: "Observation" },
    { id: "Inquiry", name: "Enquête" },
    { id: "Inspection", name: "Inspection" },
    { id: "Re-Performance", name: "Re-exécution" }
  ];

  // Helper function to get French display name for dropdown values
  const getDisplayName = (options, value) => {
    const option = options.find(opt => opt.id === value);
    return option ? option.name : value;
  };

  // Load all reference data when component mounts
  useEffect(() => {
    let isMounted = true;

    const loadAllReferenceData = async () => {
      if (!isMounted) return;
      setIsLoadingData(true);

      try {
        // Load reference data in parallel
        const fetchPromises = [];

        if (controlTypes.length === 0) fetchPromises.push(dispatch(getAllControlTypes()));
        if (risks.length === 0) fetchPromises.push(dispatch(getAllRisks()));
        if (businessProcesses.length === 0) fetchPromises.push(dispatch(getAllBusinessProcesses()));
        if (organizationalProcesses.length === 0) fetchPromises.push(dispatch(getAllOrganizationalProcesses()));
        if (operations.length === 0) fetchPromises.push(dispatch(getAllOperations()));
        if (applications.length === 0) fetchPromises.push(dispatch(getAllApplications()));
        if (entities.length === 0) fetchPromises.push(dispatch(getAllEntities()));
        if (actionPlans.length === 0) fetchPromises.push(dispatch(getAllActionPlans()));

        // Wait for all data to load
        if (fetchPromises.length > 0) {
          await Promise.all(fetchPromises);
        }

        if (isMounted) {
          setDataLoaded(true);
        }
      } catch (error) {
        console.error('Error loading reference data:', error);
        if (isMounted) {
          setDataLoaded(true); // Allow form to work with partial data
        }
      } finally {
        if (isMounted) {
          setIsLoadingData(false);
        }
      }
    };

    loadAllReferenceData();

    return () => {
      isMounted = false;
    };
  }, [dispatch]); // Simplified dependency - only run once on mount

  // Optimized: Set form data when control data is available and reference data is loaded
  useEffect(() => {
    if (control && dataLoaded) {
      // Only compare relevant form fields, not the entire control object
      const relevantControlData = {
        controlID: control.controlID,
        name: control.name,
        code: control.code,
        controlKey: control.controlKey,
        controlExecutionMethod: control.controlExecutionMethod,
        organizationalLevel: control.organizationalLevel,
        sampleType: control.sampleType,
        testingFrequency: control.testingFrequency,
        testingMethod: control.testingMethod,
        objective: control.objective,
        executionProcedure: control.executionProcedure,
        operationalCost: control.operationalCost,
        testingPopulationSize: control.testingPopulationSize,
        testingProcedure: control.testingProcedure,
        businessProcess: control.businessProcess,
        organizationalProcess: control.organizationalProcess,
        operation: control.operation,
        application: control.application,
        entity: control.entity,
        controlType: control.controlType,
        risk: control.risk,
        comment: control.comment
      };

      const controlDataString = JSON.stringify(relevantControlData);

      // Always populate form data on first load or when switching to different control
      const isDifferentControl = lastControlDataRef.current &&
        lastControlDataRef.current !== controlDataString &&
        JSON.parse(lastControlDataRef.current).controlID !== control.controlID;

      if (isFirstLoad.current || isDifferentControl || !lastControlDataRef.current) {
        // Form data initialization

        const newFormData = {
          name: control.name || "",
          code: control.code || "",
          controlKey: String(control.controlKey ?? "0"),
          controlExecutionMethod: control.controlExecutionMethod || "",
          objective: control.objective || "",
          executionProcedure: control.executionProcedure || "",
          operationalCost: control.operationalCost !== null ? control.operationalCost.toString() : "",
          // FIXED: Preserve actual values from database, use empty string only for display
          organizationalLevel: control.organizationalLevel || "",
          sampleType: control.sampleType || "",
          testingFrequency: control.testingFrequency || "",
          testingMethod: control.testingMethod || "",
          testingPopulationSize: control.testingPopulationSize !== null ? control.testingPopulationSize.toString() : "",
          testingProcedure: control.testingProcedure || "",
          implementingActionPlan: control.implementingActionPlan ? String(control.implementingActionPlan) : "none",
          // FIXED: All foreign keys are strings in the database, ensure proper string conversion
          businessProcessID: control.businessProcess ? String(control.businessProcess) : "none",
          organizationalProcessID: control.organizationalProcess ? String(control.organizationalProcess) : "none",
          operationID: control.operation ? String(control.operation) : "none",
          applicationID: control.application ? String(control.application) : "none",
          entityID: control.entity ? String(control.entity) : "none",
          controlTypeID: control.controlType ? String(control.controlType) : "none",
          riskID: control.risk ? String(control.risk) : "none",
          comment: control.comment || "",
        };

        setFormData(newFormData);
        lastSavedDataRef.current = { ...newFormData };

        // Handle missing operations (add temporary items for display)
        if (control.operation && control.operationName && operations.length > 0) {
          const matchingOperation = operations.find(
            (op) => op.operationID.toString() === control.operation.toString()
          );

          if (!matchingOperation) {
            // Create a temporary operation for display purposes without mutating the array
            const tempOperations = [...operations];
            tempOperations.push({
                operationID: control.operation,
                name: `${control.operationName} (current value)`,
                isTemporary: true
            });

            // Update operations in Redux store
            dispatch({
              type: "operation/getAllOperations/fulfilled",
              payload: { success: true, data: tempOperations }
            });
          }
        }

        isFirstLoad.current = false;
      }

      // Update the reference to track control data changes
      lastControlDataRef.current = controlDataString;
    }
  }, [control?.controlID, dataLoaded]); // Optimized dependencies

  // Removed fallback effect for performance optimization

  // OPTIMIZED: Process options for select fields with better memoization
  const selectOptions = useMemo(() => {
    // Early return if data is not loaded yet
    if (!dataLoaded) {
      return {
        businessProcesses: [{ id: "none", name: t('common.none', 'None') }],
        organizationalProcesses: [{ id: "none", name: t('common.none', 'None') }],
        operations: [{ id: "none", name: t('common.none', 'None') }],
        applications: [{ id: "none", name: t('common.none', 'None') }],
        entities: [{ id: "none", name: t('common.none', 'None') }],
        controlTypes: [{ id: "none", name: t('common.none', 'None') }],
        risks: [{ id: "none", name: t('common.none', 'None') }]
      };
    }

    return {
      businessProcesses: [
        { id: "none", name: t('common.none', 'None') },
        ...businessProcesses.map(process => ({
          id: process.businessProcessID,
          name: process.name,
          key: `bp-${process.businessProcessID}`
        }))
      ],
      organizationalProcesses: [
        { id: "none", name: t('common.none', 'None') },
        ...organizationalProcesses.map(process => ({
          id: process.organizationalProcessID,
          name: process.name,
          key: `op-${process.organizationalProcessID}`
        }))
      ],
      operations: [
        { id: "none", name: t('common.none', 'None') },
        ...operations.map(op => ({
          id: op.operationID,
          name: op.name,
          isTemporary: op.isTemporary,
          disabled: op.isTemporary,
          className: op.isTemporary ? "text-yellow-500 font-semibold" : "",
          key: `operation-${op.operationID}`
        }))
      ],
      applications: [
        { id: "none", name: t('common.none', 'None') },
        ...applications.map(app => ({
          id: app.applicationID,
          name: app.name,
          key: `app-${app.applicationID}`
        }))
      ],
      entities: [
        { id: "none", name: t('common.none', 'None') },
        ...entities.map(entity => ({
          id: entity.entityID,
          name: entity.name,
          key: `entity-${entity.entityID}`
        }))
      ],
      controlTypes: [
        { id: "none", name: t('common.none', 'None') },
        ...controlTypes.map(type => ({
          id: type.controlTypeID,
          name: type.name,
          key: `ct-${type.controlTypeID}`
        }))
      ],
      risks: [
        { id: "none", name: t('common.none', 'None') },
        ...risks.map(risk => ({
          id: risk.riskID,
          name: risk.name,
          key: `risk-${risk.riskID}`
        }))
      ]
    };
  }, [dataLoaded, businessProcesses, organizationalProcesses, operations, applications, entities, controlTypes, risks, t]);

  // Event handlers with useCallback to prevent unnecessary re-renders
  const handleInputChange = useCallback((fieldName, value) => {
    // Optimized: Simple input change handler
    const controlData = {
      [fieldName]: value
    };

    dispatch(updateControl({ id: control.controlID, controlData }))
      .unwrap()
      .catch((error) => {
        console.error(`Error updating ${fieldName}:`, error);
      });
  }, [dispatch, control?.controlID]);

  // Keep the old handler for regular input fields
  const handleRegularInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  // Simple handler for form fields that still use the old pattern
  const handleSelectChange = useCallback((name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  // Modal selection handlers
  const handleControlTypeSelection = useCallback(async (selectedType) => {
    setIsControlTypeModalOpen(false);
    if (selectedType) {
      const typeId = selectedType.controlTypeID || selectedType.id;
      if (typeId) {
        try {
          setIsLinkingControlType(true);
          const controlData = { controlType: typeId };
          await dispatch(updateControl({ id: control.controlID, controlData })).unwrap();
          isManualUpdate.current = true; // Prevent auto-save trigger
          setFormData(prev => ({ ...prev, controlTypeID: typeId.toString() }));
          toast.success("Type de contrôle lié avec succès");
        } catch (error) {
          console.error('Error linking control type:', error);
          toast.error("Erreur lors de la liaison du type de contrôle");
        } finally {
          setIsLinkingControlType(false);
        }
      }
    }
  }, [dispatch, control?.controlID]);

  const handleBusinessProcessSelection = useCallback(async (selectedProcess) => {
    setIsBusinessProcessModalOpen(false);
    if (selectedProcess) {
      const processId = selectedProcess.businessProcessID || selectedProcess.id;
      if (processId) {
        try {
          setIsLinkingBusinessProcess(true);
          const controlData = { businessProcess: processId };
          await dispatch(updateControl({ id: control.controlID, controlData })).unwrap();
          isManualUpdate.current = true; // Prevent auto-save trigger
          setFormData(prev => ({ ...prev, businessProcessID: processId.toString() }));
          toast.success("Processus métier lié avec succès");
        } catch (error) {
          console.error('Error linking business process:', error);
          toast.error("Erreur lors de la liaison du processus métier");
        } finally {
          setIsLinkingBusinessProcess(false);
        }
      }
    }
  }, [dispatch, control?.controlID]);

  const handleOrganizationalProcessSelection = useCallback(async (selectedProcess) => {
    setIsOrganizationalProcessModalOpen(false);
    if (selectedProcess) {
      const processId = selectedProcess.organizationalProcessID || selectedProcess.id;
      if (processId) {
        try {
          setIsLinkingOrganizationalProcess(true);
          const controlData = { organizationalProcess: processId };
          await dispatch(updateControl({ id: control.controlID, controlData })).unwrap();
          isManualUpdate.current = true; // Prevent auto-save trigger
          setFormData(prev => ({ ...prev, organizationalProcessID: processId.toString() }));
          toast.success("Processus organisationnel lié avec succès");
        } catch (error) {
          console.error('Error linking organizational process:', error);
          toast.error("Erreur lors de la liaison du processus organisationnel");
        } finally {
          setIsLinkingOrganizationalProcess(false);
        }
      }
    }
  }, [dispatch, control?.controlID]);

  const handleOperationSelection = useCallback(async (selectedOperation) => {
    setIsOperationModalOpen(false);
    if (selectedOperation) {
      const operationId = selectedOperation.operationID || selectedOperation.id;
      if (operationId) {
        try {
          setIsLinkingOperation(true);
          const controlData = { operation: operationId };
          await dispatch(updateControl({ id: control.controlID, controlData })).unwrap();
          isManualUpdate.current = true; // Prevent auto-save trigger
          setFormData(prev => ({ ...prev, operationID: operationId.toString() }));
          toast.success("Opération liée avec succès");
        } catch (error) {
          console.error('Error linking operation:', error);
          toast.error("Erreur lors de la liaison de l'opération");
        } finally {
          setIsLinkingOperation(false);
        }
      }
    }
  }, [dispatch, control?.controlID]);

  const handleApplicationSelection = useCallback(async (selectedApplication) => {
    setIsApplicationModalOpen(false);
    if (selectedApplication) {
      const applicationId = selectedApplication.applicationID || selectedApplication.id;
      if (applicationId) {
        try {
          setIsLinkingApplication(true);
          const controlData = { application: applicationId };
          await dispatch(updateControl({ id: control.controlID, controlData })).unwrap();
          isManualUpdate.current = true; // Prevent auto-save trigger
          setFormData(prev => ({ ...prev, applicationID: applicationId.toString() }));
          toast.success("Application liée avec succès");
        } catch (error) {
          console.error('Error linking application:', error);
          toast.error("Erreur lors de la liaison de l'application");
        } finally {
          setIsLinkingApplication(false);
        }
      }
    }
  }, [dispatch, control?.controlID]);

  const handleEntitySelection = useCallback(async (selectedEntity) => {
    setIsEntityModalOpen(false);
    if (selectedEntity) {
      const entityId = selectedEntity.entityID || selectedEntity.id;
      if (entityId) {
        try {
          setIsLinkingEntity(true);
          const controlData = { entity: entityId };
          await dispatch(updateControl({ id: control.controlID, controlData })).unwrap();
          isManualUpdate.current = true; // Prevent auto-save trigger
          setFormData(prev => ({ ...prev, entityID: entityId.toString() }));
          toast.success("Entité liée avec succès");
        } catch (error) {
          console.error('Error linking entity:', error);
          toast.error("Erreur lors de la liaison de l'entité");
        } finally {
          setIsLinkingEntity(false);
        }
      }
    }
  }, [dispatch, control?.controlID]);

  const handleRiskSelection = useCallback(async (selectedRisk) => {
    setIsRiskModalOpen(false);
    if (selectedRisk) {
      try {
        setIsLinkingRisk(true);
        const controlData = { risk: selectedRisk.riskID || selectedRisk.id };
        await dispatch(updateControl({ id: control.controlID, controlData })).unwrap();
        isManualUpdate.current = true; // Prevent auto-save trigger
        setFormData(prev => ({ ...prev, riskID: (selectedRisk.riskID || selectedRisk.id).toString() }));
        toast.success("Risque lié avec succès");
      } catch (error) {
        console.error('Error linking risk:', error);
        toast.error("Erreur lors de la liaison du risque");
      } finally {
        setIsLinkingRisk(false);
      }
    }
  }, [dispatch, control?.controlID]);

  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();

    if (!formData.name) {
      toast.error(t('admin.controls.edit.features.errors.name_required', 'Name is required'));
      return;
    }

    setIsSubmitting(true);

    // FIXED: Prepare control data with null handling for ENUM fields
    const controlData = {
      name: formData.name,
      code: formData.code,
      controlKey: formData.controlKey ? parseInt(formData.controlKey, 10) : null,

      // FIXED: ENUM fields - send null if empty, otherwise send the value
      controlExecutionMethod: formData.controlExecutionMethod && formData.controlExecutionMethod !== "none" && formData.controlExecutionMethod !== ""
        ? formData.controlExecutionMethod : null,
      organizationalLevel: formData.organizationalLevel && formData.organizationalLevel !== "none" && formData.organizationalLevel !== ""
        ? formData.organizationalLevel : null,
      sampleType: formData.sampleType && formData.sampleType !== "none" && formData.sampleType !== ""
        ? formData.sampleType : null,
      testingFrequency: formData.testingFrequency && formData.testingFrequency !== "none" && formData.testingFrequency !== ""
        ? formData.testingFrequency : null,
      testingMethod: formData.testingMethod && formData.testingMethod !== "none" && formData.testingMethod !== ""
        ? formData.testingMethod : null,

      objective: formData.objective || null,
      executionProcedure: formData.executionProcedure || null,
      operationalCost: formData.operationalCost ? parseFloat(formData.operationalCost) : null,
      testingPopulationSize: formData.testingPopulationSize
        ? parseInt(formData.testingPopulationSize, 10)
        : null,
      testingProcedure: formData.testingProcedure || null,
      implementingActionPlan:
        formData.implementingActionPlan === "none" ? null : formData.implementingActionPlan || null,
      businessProcess: formData.businessProcessID === "none" ? null : formData.businessProcessID || null,
      organizationalProcess:
        formData.organizationalProcessID === "none" ? null : formData.organizationalProcessID || null,
      operation: formData.operationID === "none" ? null : formData.operationID,
      application: formData.applicationID === "none" ? null : formData.applicationID || null,
      entity: formData.entityID === "none" ? null : formData.entityID || null,
      controlType: formData.controlTypeID === "none" ? null : formData.controlTypeID || null,
      risk: formData.riskID === "none" ? null : formData.riskID || null,
      comment: formData.comment || null,
    };

    try {
      await dispatch(
        updateControl({
          id: control ? control.controlID : `CTL_${Date.now()}`,
          controlData,
        })
      ).unwrap();
      lastSavedDataRef.current = formData;
      toast.success(control ? t('admin.controls.edit.features.messages.updated_successfully', 'Control updated successfully') : t('admin.controls.edit.features.messages.created_successfully', 'Control created successfully'));
      // Wait a moment before navigating to ensure the state is updated
      setTimeout(() => {
        if (control) {
          navigate(`/admin/controls/edit/${control.controlID}/overview`);
        } else {
          navigate("/admin/controls");
        }
      }, 200);
    } catch (error) {
      console.error('Error in control update:', error);

      // Handle foreign key constraint errors
      if (error.message?.includes('foreign key constraint') ||
          error.message?.includes('does not exist')) {

        const errorMessage = error.response?.data?.message || error.message;
        const fieldMatch = errorMessage.match(/The ([a-zA-Z]+) with ID ([\w_]+) does not exist/i);

        if (fieldMatch && fieldMatch.length >= 3) {
          const fieldName = fieldMatch[1];
          const fieldValue = fieldMatch[2];

          toast.error(t('admin.controls.edit.features.errors.foreign_key_not_found', 'The {{fieldName}} with ID {{fieldValue}} does not exist in the database.', { fieldName, fieldValue }));

          // Special handling for operation field
          if (fieldName === 'operation') {
            setFormData(prev => ({ ...prev, operationID: 'none' }));

            // Try to resubmit with operation set to null
            try {
              await dispatch(
                updateControl({
                  id: control ? control.controlID : `CTL_${Date.now()}`,
                  controlData: { ...controlData, operation: null },
                })
              ).unwrap();

              toast.success(t('admin.controls.edit.features.messages.updated_successfully', 'Control updated successfully'));

              setTimeout(() => {
                if (control) {
                  navigate(`/admin/controls/edit/${control.controlID}/overview`);
                } else {
                  navigate("/admin/controls");
                }
              }, 200);
            } catch (retryError) {
              console.error('Error in retry:', retryError);
              toast.error(t('admin.controls.edit.features.errors.save_failed_retry', 'Failed to save control even after retry'));
            }
          }
        } else {
          toast.error(t('admin.controls.edit.features.errors.foreign_key_constraint', 'Foreign key constraint error: One of the selected references does not exist.'));
        }
      } else {
        toast.error(error.message || t('admin.controls.edit.features.errors.save_failed', 'Failed to save control'));
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, control, navigate, dispatch, t]);

  // Optimized auto-save with improved performance
  const debouncedAutoSave = useDebouncedCallback(async () => {
    if (!formData.name || isEqual(formData, lastSavedDataRef.current) || isSubmitting) {
      return;
    }

    try {
      // FIXED: Proper data mapping with null handling for ENUM fields
      const controlData = {
        name: formData.name,
        code: formData.code,
        controlKey: formData.controlKey ? parseInt(formData.controlKey, 10) : null,

        // FIXED: ENUM fields - send null if empty, otherwise send the value
        controlExecutionMethod: formData.controlExecutionMethod && formData.controlExecutionMethod !== "none" && formData.controlExecutionMethod !== ""
          ? formData.controlExecutionMethod : null,
        organizationalLevel: formData.organizationalLevel && formData.organizationalLevel !== "none" && formData.organizationalLevel !== ""
          ? formData.organizationalLevel : null,
        sampleType: formData.sampleType && formData.sampleType !== "none" && formData.sampleType !== ""
          ? formData.sampleType : null,
        testingFrequency: formData.testingFrequency && formData.testingFrequency !== "none" && formData.testingFrequency !== ""
          ? formData.testingFrequency : null,
        testingMethod: formData.testingMethod && formData.testingMethod !== "none" && formData.testingMethod !== ""
          ? formData.testingMethod : null,

        objective: formData.objective || null,
        executionProcedure: formData.executionProcedure || null,
        operationalCost: formData.operationalCost ? parseFloat(formData.operationalCost) : null,
        testingPopulationSize: formData.testingPopulationSize ? parseInt(formData.testingPopulationSize, 10) : null,
        testingProcedure: formData.testingProcedure || null,
        implementingActionPlan: formData.implementingActionPlan === "none" ? null : formData.implementingActionPlan,

        // FIXED: Foreign key mapping
        businessProcess: formData.businessProcessID === "none" ? null : formData.businessProcessID,
        organizationalProcess: formData.organizationalProcessID === "none" ? null : formData.organizationalProcessID,
        operation: formData.operationID === "none" ? null : formData.operationID,
        application: formData.applicationID === "none" ? null : formData.applicationID,
        entity: formData.entityID === "none" ? null : formData.entityID,
        controlType: formData.controlTypeID === "none" ? null : formData.controlTypeID,
        risk: formData.riskID === "none" ? null : formData.riskID,
        comment: formData.comment || null,
      };

      const result = await dispatch(
        updateControl({
          id: control ? control.controlID : `CTL_${Date.now()}`,
          controlData,
        })
      ).unwrap();
      lastSavedDataRef.current = { ...formData }; // Create a copy to avoid reference issues

    } catch (error) {
      console.error('Auto-save failed:', error);
      toast.error('Échec de la sauvegarde automatique');
    }
  }, 1500); // Optimized debounce time for better performance

  // Optimized auto-save trigger
  useEffect(() => {
    if (isFirstLoad.current) {
      isFirstLoad.current = false;
      lastSavedDataRef.current = { ...formData };
      return;
    }

    // Skip auto-save if this is a manual update (linking operation, risk, etc.)
    if (isManualUpdate.current) {
      isManualUpdate.current = false;
      lastSavedDataRef.current = { ...formData };
      return;
    }

    const hasChanges = !isEqual(formData, lastSavedDataRef.current);

    // Simplified auto-save condition: has name, has changes, not currently submitting
    const shouldAutoSave = formData.name && hasChanges && !isSubmitting;

    if (shouldAutoSave) {
      debouncedAutoSave();
    }
  }, [formData, isSubmitting, debouncedAutoSave]);

  // Warn user about unsaved changes when leaving the page
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        const message = t('admin.controls.edit.features.confirm.leave_unsaved', 'You have unsaved changes. Are you sure you want to leave?');
        e.returnValue = message;
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges, t]);

  // Warn user when navigating away with unsaved changes
  useEffect(() => {
    if (onNavigateAway && hasUnsavedChanges) {
      // This could be used by parent component to show warning
      // when switching tabs or navigating away
    }
  }, [onNavigateAway, hasUnsavedChanges]);

  // Removed debug effects for performance optimization

  // Show loading state only when actually loading and no control data yet
  if (isLoadingData || !control || !dataLoaded) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
          <p className="mt-4 text-gray-600">
            {!control ? t('admin.controls.edit.features.loading.control_data', 'Loading control data...') :
             !dataLoaded ? t('admin.controls.edit.features.loading.reference_data', 'Loading reference data...') :
             t('admin.controls.edit.features.loading.general', 'Loading...')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6 bg-white rounded-lg shadow-sm p-6">

      {/* Description Section */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-3 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-t-lg">
          <FileText className="h-5 w-5 mr-2 text-purple-800" />
          <span className="text-lg font-medium text-purple-800">Descriptions</span>
        </div>
        <div className="p-4">
          <div className="grid grid-cols-1 gap-6">
        <div className="space-y-2">
              <Label htmlFor="objective" className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-gray-500" />
                Objectif
              </Label>
              <Textarea
                id="objective"
                name="objective"
                value={formData.objective || ""}
                onChange={handleRegularInputChange}
                rows="4"
                disabled={false}
                className="resize-none w-full p-2 border rounded-md"
              />
        </div>
        <div className="space-y-2">
              <Label htmlFor="executionProcedure" className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-gray-500" />
                Procédure d'Exécution
              </Label>
              <Textarea
                id="executionProcedure"
                name="executionProcedure"
                value={formData.executionProcedure || ""}
                onChange={handleRegularInputChange}
                rows="4"
                disabled={false}
                className="resize-none w-full p-2 border rounded-md"
              />
        </div>
        <div className="space-y-2">
              <Label htmlFor="comment" className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-gray-500" />
                Commentaire
              </Label>
          <Textarea
            id="comment"
            name="comment"
                value={formData.comment || ""}
            onChange={handleRegularInputChange}
                rows="4"
                disabled={false}
                className="resize-none w-full p-2 border rounded-md"
          />
            </div>
          </div>
        </div>
      </div>

      {/* Basic Information Section */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
          <Database className="h-5 w-5 mr-2 text-blue-800" />
          <span className="text-lg font-medium text-blue-800">Informations de Base</span>
        </div>
        <div className="p-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <InputField
              label="Nom"
            name="name"
            value={formData.name}
            onChange={handleRegularInputChange}
              disabled={false}
              t={t}
            />
            <InputField
              label="Code"
            name="code"
            value={formData.code}
            onChange={handleRegularInputChange}
              disabled={false}
              t={t}
            />
            <div className="mb-4">
              <Label className="flex items-center gap-2 mb-3">
                <Database className="h-4 w-4 text-gray-500" />
                Contrôle Clé
              </Label>
              <div className="flex items-center gap-4">
                <Checkbox
                  id="controlKey"
                  checked={formData.controlKey === "1"}
                  onCheckedChange={checked => handleSelectChange("controlKey", checked ? "1" : "0")}
                />
                <label htmlFor="controlKey" className="ml-2 text-sm text-gray-600">
                  {formData.controlKey === "1" ? "Oui" : "Non"}
                </label>
              </div>
            </div>
            <div className="mb-4">
              <Label className="flex items-center gap-2 mb-3">
                <Activity className="h-4 w-4 text-gray-500" />
                Méthode d'Exécution du Contrôle
              </Label>
              <Select
                value={control.controlExecutionMethod || ""}
                onValueChange={(value) => handleInputChange('controlExecutionMethod', value)}
                disabled={false}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner la méthode d'exécution">
                    {control.controlExecutionMethod ? getDisplayName(controlExecutionMethodOptions, control.controlExecutionMethod) : "Sélectionner la méthode d'exécution"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {controlExecutionMethodOptions.map(option => (
                    <SelectItem key={option.id} value={option.id}>{option.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <InputField
              label="Coût Opérationnel"
            name="operationalCost"
            type="number"
            step="0.01"
            value={formData.operationalCost}
            onChange={handleRegularInputChange}
              disabled={false}
              t={t}
            />
            <div className="mb-4">
              <Label className="flex items-center gap-2 mb-3">
                <Layers className="h-4 w-4 text-gray-500" />
                Niveau Organisationnel
              </Label>
              <Select
                value={control.organizationalLevel || ""}
                onValueChange={(value) => handleInputChange('organizationalLevel', value)}
                disabled={false}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner le niveau organisationnel">
                    {control.organizationalLevel ? getDisplayName(organizationalLevelOptions, control.organizationalLevel) : "Sélectionner le niveau organisationnel"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {organizationalLevelOptions.map(option => (
                    <SelectItem key={option.id} value={option.id}>{option.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
        </div>
        </div>
        </div>

      {/* Testing Details Section */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg">
          <Gauge className="h-5 w-5 mr-2 text-green-800" />
          <span className="text-lg font-medium text-green-800">Détails des Tests</span>
        </div>
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="mb-4">
              <Label className="flex items-center gap-2 mb-3">
                <Database className="h-4 w-4 text-gray-500" />
                Type d'Échantillon
              </Label>
              <Select
                value={control.sampleType || ""}
                onValueChange={(value) => handleInputChange('sampleType', value)}
                disabled={false}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner le type d'échantillon">
                    {control.sampleType ? getDisplayName(sampleTypeOptions, control.sampleType) : "Sélectionner le type d'échantillon"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {sampleTypeOptions.map(option => (
                    <SelectItem key={option.id} value={option.id}>{option.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="mb-4">
              <Label className="flex items-center gap-2 mb-3">
                <Gauge className="h-4 w-4 text-gray-500" />
                Fréquence des Tests
              </Label>
              <Select
                value={control.testingFrequency || ""}
                onValueChange={(value) => handleInputChange('testingFrequency', value)}
                disabled={false}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner la fréquence des tests">
                    {control.testingFrequency ? getDisplayName(testingFrequencyOptions, control.testingFrequency) : "Sélectionner la fréquence des tests"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {testingFrequencyOptions.map(option => (
                    <SelectItem key={option.id} value={option.id}>{option.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="mb-4">
              <Label className="flex items-center gap-2 mb-3">
                <Activity className="h-4 w-4 text-gray-500" />
                Méthode de Test
              </Label>
              <Select
                value={control.testingMethod || ""}
                onValueChange={(value) => handleInputChange('testingMethod', value)}
                disabled={false}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner la méthode de test">
                    {control.testingMethod ? getDisplayName(testingMethodOptions, control.testingMethod) : "Sélectionner la méthode de test"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {testingMethodOptions.map(option => (
                    <SelectItem key={option.id} value={option.id}>{option.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <InputField
              label="Taille de la Population de Test"
            name="testingPopulationSize"
            type="number"
            value={formData.testingPopulationSize}
            onChange={handleRegularInputChange}
              disabled={false}
              min="0"
              step="1"
              t={t}
            />
            <InputField
              label="Procédure de Test"
            name="testingProcedure"
            value={formData.testingProcedure}
            onChange={handleRegularInputChange}
              disabled={false}
              t={t}
          />
        </div>
        </div>
      </div>

      {/* Reference Data Section */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-3 bg-gradient-to-r from-amber-50 to-orange-50 rounded-t-lg">
          <Layers className="h-5 w-5 mr-2 text-amber-800" />
          <span className="text-lg font-medium text-amber-800">Données de Référence</span>
        </div>
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="space-y-2">
              <Label className="flex items-center gap-2 mb-3">
                <Database className="h-4 w-4 text-gray-500" />
                Type de Contrôle
              </Label>
              <div className="flex items-center gap-2">
                <div className="flex-1 p-3 border rounded-lg bg-gray-50">
                  {formData.controlTypeID && formData.controlTypeID !== "none" ? (
                    <span className="text-sm">
                      {selectOptions.controlTypes.find(t => t.id.toString() === formData.controlTypeID)?.name || "Type sélectionné"}
                    </span>
                  ) : (
                    <span className="text-gray-500 text-sm">Aucun type de contrôle lié</span>
                  )}
                </div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsControlTypeModalOpen(true)}
                  disabled={isLinkingControlType}
                  className="px-3 py-2"
                >
                  {isLinkingControlType ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Link className="h-4 w-4" />
                  )}
                </Button>
              </div>
        </div>

        <div className="space-y-2">
              <Label className="flex items-center gap-2 mb-3">
                <Layers className="h-4 w-4 text-gray-500" />
                Processus Métier
              </Label>
              <div className="flex items-center gap-2">
                <div className="flex-1 p-3 border rounded-lg bg-gray-50">
                  {formData.businessProcessID && formData.businessProcessID !== "none" ? (
                    <span className="text-sm">
                      {selectOptions.businessProcesses.find(p => p.id.toString() === formData.businessProcessID)?.name || "Processus sélectionné"}
                    </span>
                  ) : (
                    <span className="text-gray-500 text-sm">Aucun processus métier lié</span>
                  )}
                </div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsBusinessProcessModalOpen(true)}
                  disabled={isLinkingBusinessProcess}
                  className="px-3 py-2"
                >
                  {isLinkingBusinessProcess ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Link className="h-4 w-4" />
                  )}
                </Button>
              </div>
        </div>

        <div className="space-y-2">
              <Label className="flex items-center gap-2 mb-3">
                <Layers className="h-4 w-4 text-gray-500" />
                Processus Organisationnel
              </Label>
              <div className="flex items-center gap-2">
                <div className="flex-1 p-3 border rounded-lg bg-gray-50">
                  {formData.organizationalProcessID && formData.organizationalProcessID !== "none" ? (
                    <span className="text-sm">
                      {selectOptions.organizationalProcesses.find(p => p.id.toString() === formData.organizationalProcessID)?.name || "Processus sélectionné"}
                    </span>
                  ) : (
                    <span className="text-gray-500 text-sm">Aucun processus organisationnel lié</span>
                  )}
                </div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsOrganizationalProcessModalOpen(true)}
                  disabled={isLinkingOrganizationalProcess}
                  className="px-3 py-2"
                >
                  {isLinkingOrganizationalProcess ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Link className="h-4 w-4" />
                  )}
                </Button>
              </div>
        </div>

        <div className="space-y-2">
              <Label className="flex items-center gap-2 mb-3">
                <Activity className="h-4 w-4 text-gray-500" />
                Opération
              </Label>
              <div className="flex items-center gap-2">
                <div className="flex-1 p-3 border rounded-lg bg-gray-50">
                  {formData.operationID && formData.operationID !== "none" ? (
                    <span className="text-sm">
                      {selectOptions.operations.find(o => o.id.toString() === formData.operationID)?.name || "Opération sélectionnée"}
                    </span>
                  ) : (
                    <span className="text-gray-500 text-sm">Aucune opération liée</span>
                  )}
                </div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsOperationModalOpen(true)}
                  disabled={isLinkingOperation}
                  className="px-3 py-2"
                >
                  {isLinkingOperation ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Link className="h-4 w-4" />
                  )}
                </Button>
              </div>
        </div>

        <div className="space-y-2">
              <Label className="flex items-center gap-2 mb-3">
                <Database className="h-4 w-4 text-gray-500" />
                Application
              </Label>
              <div className="flex items-center gap-2">
                <div className="flex-1 p-3 border rounded-lg bg-gray-50">
                  {formData.applicationID && formData.applicationID !== "none" ? (
                    <span className="text-sm">
                      {selectOptions.applications.find(a => a.id.toString() === formData.applicationID)?.name || "Application sélectionnée"}
                    </span>
                  ) : (
                    <span className="text-gray-500 text-sm">Aucune application liée</span>
                  )}
                </div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsApplicationModalOpen(true)}
                  disabled={isLinkingApplication}
                  className="px-3 py-2"
                >
                  {isLinkingApplication ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Link className="h-4 w-4" />
                  )}
                </Button>
              </div>
        </div>

        <div className="space-y-2">
              <Label className="flex items-center gap-2 mb-3">
                <Database className="h-4 w-4 text-gray-500" />
                Entité
              </Label>
              <div className="flex items-center gap-2">
                <div className="flex-1 p-3 border rounded-lg bg-gray-50">
                  {formData.entityID && formData.entityID !== "none" ? (
                    <span className="text-sm">
                      {selectOptions.entities.find(e => e.id.toString() === formData.entityID)?.name || "Entité sélectionnée"}
                    </span>
                  ) : (
                    <span className="text-gray-500 text-sm">Aucune entité liée</span>
                  )}
                </div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEntityModalOpen(true)}
                  disabled={isLinkingEntity}
                  className="px-3 py-2"
                >
                  {isLinkingEntity ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Link className="h-4 w-4" />
                  )}
                </Button>
              </div>
        </div>

        <div className="space-y-2">
              <Label className="flex items-center gap-2 mb-3">
                <Gauge className="h-4 w-4 text-gray-500" />
                Risque Associé
              </Label>
              <div className="flex items-center gap-2">
                <div className="flex-1 p-3 border rounded-lg bg-gray-50">
                  {formData.riskID && formData.riskID !== "none" ? (
                    <span className="text-sm">
                      {selectOptions.risks.find(r => r.id.toString() === formData.riskID)?.name || "Risque sélectionné"}
                    </span>
                  ) : (
                    <span className="text-gray-500 text-sm">Aucun risque lié</span>
                  )}
                </div>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsRiskModalOpen(true)}
                  disabled={isLinkingRisk}
                  className="px-3 py-2"
                >
                  {isLinkingRisk ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Link className="h-4 w-4" />
                  )}
                </Button>
              </div>
        </div>
          </div>
        </div>
      </div>

      {/* Selection Modals */}
      <ControlTypeSelectionModal
        open={isControlTypeModalOpen}
        onClose={() => setIsControlTypeModalOpen(false)}
        onSelect={handleControlTypeSelection}
        controlTypes={controlTypes}
      />

      <BusinessProcessSelectionModal
        open={isBusinessProcessModalOpen}
        onClose={() => setIsBusinessProcessModalOpen(false)}
        onSelect={handleBusinessProcessSelection}
        businessProcesses={businessProcesses}
      />

      <OrganizationalProcessSelectionModal
        open={isOrganizationalProcessModalOpen}
        onClose={() => setIsOrganizationalProcessModalOpen(false)}
        onSelect={handleOrganizationalProcessSelection}
        organizationalProcesses={organizationalProcesses}
      />

      <OperationSelectionModal
        open={isOperationModalOpen}
        onClose={() => setIsOperationModalOpen(false)}
        onSelect={handleOperationSelection}
        operations={operations}
      />

      <ApplicationSelectionModal
        open={isApplicationModalOpen}
        onClose={() => setIsApplicationModalOpen(false)}
        onSelect={handleApplicationSelection}
        applications={applications}
      />

      <EntitySelectionModal
        open={isEntityModalOpen}
        onClose={() => setIsEntityModalOpen(false)}
        onSelect={handleEntitySelection}
        entities={entities}
      />

      <RiskSelectionModal
        open={isRiskModalOpen}
        onClose={() => setIsRiskModalOpen(false)}
        onSelect={handleRiskSelection}
        risks={risks}
      />

    </form>
  );
}

export default React.memo(ControlFeatures);