import { io } from 'socket.io-client';
import { getApiBaseUrl } from './api-config'; // Assuming your API base URL is managed here

// Create socket instance but don't connect immediately
let socket;

// Get the base URL for the Socket.IO connection
// We need to remove the /api part to connect to the root Socket.IO endpoint
const getSocketIoBaseUrl = () => {
  const apiBaseUrl = getApiBaseUrl();
  // Remove /api path if it exists at the end
  const baseUrlWithoutApi = apiBaseUrl.replace(/\/api$/, '');
  console.log('[Socket] Using base URL for Socket.IO:', baseUrlWithoutApi);
  return baseUrlWithoutApi;
};

// Connect to the Socket.IO server
const connectSocket = () => {
  if (socket && socket.connected) {
    console.log('[Socket] Already connected, not reconnecting. Socket ID:', socket.id);
    return socket;
  }

  const SOCKET_URL = getSocketIoBaseUrl();
  console.log('[Socket] Connecting to Socket.IO server at:', SOCKET_URL);
  
  // Get the authentication token
  const token = localStorage.getItem('authToken');
  if (!token) {
    console.error('[Socket] No authentication token found in localStorage');
    return null;
  }

  console.log('[Socket] Token found, initializing connection with token:', token.substring(0, 10) + '...');
  
  // Initialize socket connection with authentication
  socket = io(SOCKET_URL, {
    autoConnect: true,
    reconnection: true,
    reconnectionDelay: 1000,
    reconnectionAttempts: 5,
    auth: {
      token: token ? `Bearer ${token}` : null
    },
    transports: ['websocket', 'polling'] // Try WebSocket first, fall back to polling
  });

  // Add connect event handler
  socket.on("connect", () => {
    console.log('[Socket] Connected successfully! Socket ID:', socket.id);
  });

  // Add disconnect event handler
  socket.on("disconnect", (reason) => {
    console.log('[Socket] Disconnected. Reason:', reason);
  });

  // Add error handler for easier debugging
  socket.on("connect_error", (error) => {
    console.error('[Socket] Connection error:', error.message);
    // Try to reconnect in 3 seconds
    setTimeout(() => {
      console.log('[Socket] Attempting to reconnect after error...');
      socket.connect();
    }, 3000);
  });

  // Add event handler for reconnection attempts
  socket.on("reconnect_attempt", (attempt) => {
    console.log(`[Socket] Attempting to reconnect (attempt ${attempt})...`);
    // Update the auth token in case it has changed
    const updatedToken = localStorage.getItem('authToken');
    if (updatedToken) {
      socket.auth = { token: `Bearer ${updatedToken}` };
    }
  });

  // Add event handler for successful reconnection
  socket.on("reconnect", (attempt) => {
    console.log(`[Socket] Successfully reconnected after ${attempt} attempts`);
  });

  return socket;
};

// Disconnect from the Socket.IO server
const disconnectSocket = () => {
  if (socket) {
    console.log('[Socket] Disconnecting from Socket.IO server');
    socket.disconnect();
    socket = null;
  }
};

// Log the current socket state
const getSocketState = () => {
  if (!socket) return { connected: false, id: null };
  return { 
    connected: socket.connected, 
    id: socket.id
  };
};

export { socket, connectSocket, disconnectSocket, getSocketState };

// export const subscribeToNotification = (callback) => {
//   socket.on('notification', callback);
// };

// export const unsubscribeFromNotification = (callback) => {
//   socket.off('notification', callback);
// };

// export const sendMyEvent = (data) => {
//  socket.emit('myCustomEventFromClient', data);
// }; 