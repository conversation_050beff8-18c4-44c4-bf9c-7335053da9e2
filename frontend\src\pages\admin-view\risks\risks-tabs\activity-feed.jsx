import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, Clock, Settings, FileText, Activity, ArrowLeft, ArrowRight, Loader } from 'lucide-react';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from 'react-i18next';
// API Base URL
const API_BASE_URL = getApiBaseUrl();

export default function ActivityFeed() {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(true);
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);
  const { id: riskId } = useParams();

  // Fetch activities on component mount or when riskId changes
  useEffect(() => {
    if (riskId) {
      fetchActivities();
    }
  }, [riskId]);

  const fetchActivities = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.get(
        `${API_BASE_URL}/risk/${riskId}/activity`,
        { withCredentials: true }
      );

      if (response.data && response.data.success) {
        setActivities(response.data.data || []);
        setCurrentPage(1); // Reset to first page when new data is fetched
      } else {
        setError(t('admin.risks.activity_feed.error_loading', "Failed to load activities"));
      }
    } catch (err) {
      console.error("Error fetching risk activities:", err);
      setError(err.response?.data?.message || t('admin.risks.activity_feed.error_loading', "Failed to load activities"));
    } finally {
      setLoading(false);
    }
  };

  // Get current items for pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = activities.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(activities.length / itemsPerPage);

  // Change page
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Function to get the appropriate icon for an activity type
  const getActivityIcon = (type) => {
    switch(type) {
      case 'creation':
        return <FileText className="h-5 w-5 text-green-500" />;
      case 'update':
        return <Settings className="h-5 w-5 text-blue-500" />;
      case 'transition':
        return <Activity className="h-5 w-5 text-purple-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  // Function to format date nicely
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Render activity item
  const renderActivityItem = (activity) => {
    return (
      <div key={activity.id} className="mb-4 border-b pb-4 last:border-b-0">
        <div className="flex items-start">
          <div className="mr-3 mt-1">
            {getActivityIcon(activity.type)}
          </div>
          <div className="flex-1">
            <div className="text-sm text-gray-500 mb-1">{formatDate(activity.timestamp)}</div>
            <div className="font-medium mb-1">
              {activity.user} {getActivityText(activity)}
            </div>
            {activity.type === 'update' && activity.field && (
              <div className="text-sm bg-gray-50 p-2 rounded mt-1">
                <span className="font-medium">{activity.field}:</span>{" "}
                {activity.oldValue !== undefined && <span className="line-through mr-1">{activity.oldValue}</span>}
                {activity.newValue !== undefined && <span className="text-green-600">{activity.newValue}</span>}
              </div>
            )}
            {activity.details && (
              <div className="text-sm text-gray-600 mt-1">{activity.details}</div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Get text based on activity type
  const getActivityText = (activity) => {
    switch(activity.type) {
      case 'creation':
        return t('admin.risks.activity_feed.created_risk', 'created this risk');
      case 'update':
        return t('admin.risks.activity_feed.updated_risk', 'updated the risk');
      case 'transition':
        return activity.details || t('admin.risks.activity_feed.changed_workflow', 'changed the workflow state');
      default:
        return t('admin.risks.activity_feed.performed_action', 'performed an action');
    }
  };

  return (
    <div className="border rounded-lg">
      <button
        type="button"
        className="w-full flex items-center p-4 bg-gray-50 rounded-t-lg"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center gap-2">
          {isOpen ? (
            <ChevronUp className="h-5 w-5" />
          ) : (
            <ChevronDown className="h-5 w-5" />
          )}
          <span className="text-lg font-medium">{t('admin.risks.activity_feed.title', 'Activity Feed')}</span>
        </div>
      </button>

      {isOpen && (
        <div className="p-4">
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader className="h-6 w-6 animate-spin mr-2" />
              <span>{t('admin.risks.activity_feed.loading', 'Loading activities...')}</span>
            </div>
          ) : error ? (
            <div className="text-red-500 py-4">{error}</div>
          ) : activities.length === 0 ? (
            <div className="text-gray-500 py-4">{t('admin.risks.activity_feed.no_activities', 'No activities recorded yet.')}</div>
          ) : (
            <>
              <div className="space-y-2">
                {currentItems.map(renderActivityItem)}
              </div>

              {/* Pagination controls */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center mt-6 pt-4 border-t">
                  <button
                    onClick={goToPreviousPage}
                    disabled={currentPage === 1}
                    className={`flex items-center ${
                      currentPage === 1
                        ? 'text-gray-300 cursor-not-allowed'
                        : 'text-blue-500 hover:text-blue-700'
                    }`}
                  >
                    <ArrowLeft className="h-4 w-4 mr-1" />
                    {t('admin.risks.activity_feed.previous', 'Previous')}
                  </button>
                  <span className="text-sm text-gray-600">
                    {t('admin.risks.activity_feed.page_info', 'Page {{current}} of {{total}}', { current: currentPage, total: totalPages })}
                  </span>
                  <button
                    onClick={goToNextPage}
                    disabled={currentPage === totalPages}
                    className={`flex items-center ${
                      currentPage === totalPages
                        ? 'text-gray-300 cursor-not-allowed'
                        : 'text-blue-500 hover:text-blue-700'
                    }`}
                  >
                    {t('admin.risks.activity_feed.next', 'Next')}
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
}