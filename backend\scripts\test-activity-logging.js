const db = require('../models');

async function testActivityLogging() {
  try {
    console.log('🧪 Testing Activity Logging...\n');

    // Connect to database
    await db.sequelize.authenticate();
    console.log('✅ Database connected successfully\n');

    // Find a test control
    const testControl = await db.Control.findOne({
      attributes: ['controlID', 'name', 'controlExecutionMethod'],
      limit: 1
    });
    
    if (!testControl) {
      console.log('❌ No controls found to test with');
      return;
    }
    
    console.log(`📋 Using test control: ${testControl.controlID} - ${testControl.name}`);
    console.log(`📋 Current controlExecutionMethod: ${testControl.controlExecutionMethod}`);

    // Test 1: Direct activity log creation
    console.log('\n🧪 Test 1: Direct activity log creation...');
    try {
      const directLog = await db.ControlActivityLog.create({
        control_id: testControl.controlID,
        user: 'Test User',
        type: 'update',
        field: 'controlExecutionMethod',
        old_value: 'Old Method',
        new_value: 'New Method',
        details: 'Méthode d\'Exécution du Contrôle modifié'
      });
      
      console.log('✅ Direct activity log created:', directLog.id);
      
      // Clean up
      await directLog.destroy();
      console.log('✅ Direct test log cleaned up');
      
    } catch (error) {
      console.error('❌ Direct activity log creation failed:', error.message);
    }

    // Test 2: Test the logUpdateActivities function
    console.log('\n🧪 Test 2: Testing logUpdateActivities function...');
    try {
      const { logUpdateActivities } = require('../controllers/data/control-activity-controller');
      
      const testUpdateData = {
        controlExecutionMethod: 'Test Method Change'
      };
      
      const result = await logUpdateActivities(testControl, testUpdateData, 'Test User');
      console.log('✅ logUpdateActivities result:', result);
      
      // Check if logs were created
      const createdLogs = await db.ControlActivityLog.findAll({
        where: {
          control_id: testControl.controlID,
          user: 'Test User'
        },
        order: [['timestamp', 'DESC']],
        limit: 5
      });
      
      console.log(`📊 Found ${createdLogs.length} test logs`);
      createdLogs.forEach(log => {
        console.log(`   - ${log.type}: ${log.field} = ${log.new_value}`);
      });
      
      // Clean up
      await db.ControlActivityLog.destroy({
        where: {
          control_id: testControl.controlID,
          user: 'Test User'
        }
      });
      console.log('✅ Function test logs cleaned up');
      
    } catch (error) {
      console.error('❌ logUpdateActivities function test failed:', error.message);
    }

    // Test 3: Test the logLinkingActivity function
    console.log('\n🧪 Test 3: Testing logLinkingActivity function...');
    try {
      const { logLinkingActivity } = require('../controllers/data/control-activity-controller');
      
      const linkResult = await logLinkingActivity(
        testControl.controlID,
        'Test User',
        'actionPlan',
        'Test Action Plan (AP_123)',
        'link'
      );
      
      console.log('✅ logLinkingActivity result:', linkResult);
      
      // Check if logs were created
      const linkLogs = await db.ControlActivityLog.findAll({
        where: {
          control_id: testControl.controlID,
          user: 'Test User',
          type: 'link'
        }
      });
      
      console.log(`📊 Found ${linkLogs.length} link logs`);
      linkLogs.forEach(log => {
        console.log(`   - ${log.type}: ${log.field} = ${log.new_value}`);
      });
      
      // Clean up
      await db.ControlActivityLog.destroy({
        where: {
          control_id: testControl.controlID,
          user: 'Test User'
        }
      });
      console.log('✅ Link test logs cleaned up');
      
    } catch (error) {
      console.error('❌ logLinkingActivity function test failed:', error.message);
    }

    // Test 4: Check current activity logs count
    console.log('\n🧪 Test 4: Checking current activity logs...');
    const totalLogs = await db.ControlActivityLog.count();
    console.log(`📊 Total activity logs in database: ${totalLogs}`);
    
    if (totalLogs > 0) {
      const recentLogs = await db.ControlActivityLog.findAll({
        limit: 5,
        order: [['timestamp', 'DESC']],
        attributes: ['id', 'control_id', 'user', 'type', 'field', 'details', 'timestamp']
      });
      
      console.log('\n📋 Recent activity logs:');
      recentLogs.forEach(log => {
        console.log(`   - ${log.timestamp}: ${log.user} ${log.type} on ${log.control_id} (${log.field})`);
      });
    }

    console.log('\n🎯 Activity logging test completed!');

  } catch (error) {
    console.error('❌ Error during test:', error);
  } finally {
    await db.sequelize.close();
  }
}

// Run the test
testActivityLogging();
