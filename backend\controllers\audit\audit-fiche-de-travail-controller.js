const db = require('../../models');
const FicheDeTravail = db.FicheDeTravail;
const Question = db.Question;
const AuditActivity = db.AuditActivity;
const AuditMission = db.AuditMission;
const { v4: uuidv4 } = require('uuid');

// Get all work sheets
const getAllFicheDeTravail = async (req, res) => {
  try {
    const fiches = await FicheDeTravail.findAll({
      include: [
        { model: AuditActivity, as: 'auditActivity', attributes: ['id', 'name'] },
        { model: AuditMission, as: 'auditMission', attributes: ['id', 'name'] }
        // Removed questions include for better performance - load separately if needed
      ]
    });
    return res.status(200).json({ success: true, data: fiches });
  } catch (error) {
    console.error('Error fetching work sheets:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch work sheets',
      error: error.message
    });
  }
};

// Get work sheets by audit activity ID
const getFicheDeTravailByActivityId = async (req, res) => {
  try {
    const { activityId } = req.params;
    const fiches = await FicheDeTravail.findAll({
      where: { auditActivityID: activityId },
      include: [
        { model: AuditActivity, as: 'auditActivity', attributes: ['id', 'name'] },
        { model: AuditMission, as: 'auditMission', attributes: ['id', 'name'] }
        // Removed questions include for better performance
      ]
    });
    return res.status(200).json({ success: true, data: fiches });
  } catch (error) {
    console.error('Error fetching work sheets by activity ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch work sheets',
      error: error.message
    });
  }
};

// Create a new work sheet
const createFicheDeTravail = async (req, res) => {
  try {
    const {
      name,
      tailleEchantillon,
      questionnaire,
      tacheDetaillees,
      commentaire,
      auditActivityID,
      auditMissionID
    } = req.body;

    if (!name) {
      return res.status(400).json({ success: false, message: 'Name is required' });
    }
    if (!auditActivityID) {
      return res.status(400).json({ success: false, message: 'Audit activity ID is required' });
    }
    if (!auditMissionID) {
      return res.status(400).json({ success: false, message: 'Audit mission ID is required' });
    }

    // Use Promise.all for parallel validation
    const [auditActivity, auditMission] = await Promise.all([
      AuditActivity.findByPk(auditActivityID),
      AuditMission.findByPk(auditMissionID)
    ]);

    if (!auditActivity) {
      return res.status(404).json({ success: false, message: 'Audit activity not found' });
    }
    if (!auditMission) {
      return res.status(404).json({ success: false, message: 'Audit mission not found' });
    }

    const fiche = await FicheDeTravail.create({
      id: `FDT_${uuidv4().substring(0, 8)}`,
      name,
      tailleEchantillon,
      questionnaire,
      tacheDetaillees,
      commentaire,
      auditActivityID,
      auditMissionID
    });

    return res.status(201).json({
      success: true,
      message: 'Work sheet created successfully',
      data: fiche
    });
  } catch (error) {
    console.error('Error creating work sheet:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create work sheet',
      error: error.message
    });
  }
};

// Get work sheet by ID
const getFicheDeTravailById = async (req, res) => {
  try {
    const { id } = req.params;
    const fiche = await FicheDeTravail.findByPk(id, {
      include: [
        { model: AuditActivity, as: 'auditActivity', attributes: ['id', 'name'] },
        { model: AuditMission, as: 'auditMission', attributes: ['id', 'name'] },
        { 
          model: Question, 
          as: 'questions', 
          attributes: ['id', 'question_text', 'input_type', 'options', 'order_index'],
          order: [['order_index', 'ASC']],
          separate: true
        }
      ]
    });
    if (!fiche) {
      return res.status(404).json({ success: false, message: 'Work sheet not found' });
    }
    return res.status(200).json({ success: true, data: fiche });
  } catch (error) {
    console.error('Error fetching work sheet:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch work sheet',
      error: error.message
    });
  }
};

// Update work sheet
const updateFicheDeTravail = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      tailleEchantillon,
      questionnaire,
      tacheDetaillees,
      commentaire,
      auditActivityID,
      auditMissionID,
      questions
    } = req.body;

    const fiche = await FicheDeTravail.findByPk(id);
    if (!fiche) {
      return res.status(404).json({ success: false, message: 'Work sheet not found' });
    }

    // Use Promise.all for parallel validation if needed
    const validations = [];
    if (auditActivityID && auditActivityID !== fiche.auditActivityID) {
      validations.push(AuditActivity.findByPk(auditActivityID));
    }
    if (auditMissionID && auditMissionID !== fiche.auditMissionID) {
      validations.push(AuditMission.findByPk(auditMissionID));
    }

    if (validations.length > 0) {
      const results = await Promise.all(validations);
      let resultIndex = 0;
      if (auditActivityID && auditActivityID !== fiche.auditActivityID) {
        if (!results[resultIndex++]) {
          return res.status(404).json({ success: false, message: 'New audit activity not found' });
        }
      }
      if (auditMissionID && auditMissionID !== fiche.auditMissionID) {
        if (!results[resultIndex]) {
          return res.status(404).json({ success: false, message: 'New audit mission not found' });
        }
      }
    }

    await fiche.update({
      name: name || fiche.name,
      tailleEchantillon: tailleEchantillon !== undefined ? tailleEchantillon : fiche.tailleEchantillon,
      questionnaire: questionnaire !== undefined ? questionnaire : fiche.questionnaire,
      tacheDetaillees: tacheDetaillees !== undefined ? tacheDetaillees : fiche.tacheDetaillees,
      commentaire: commentaire !== undefined ? commentaire : fiche.commentaire,
      auditActivityID: auditActivityID || fiche.auditActivityID,
      auditMissionID: auditMissionID || fiche.auditMissionID
    });

    // --- Sync Questions ---
    if (Array.isArray(questions)) {
      // Fetch existing questions for this fiche
      const existingQuestions = await Question.findAll({ where: { ficheDeTravailID: id } });
      const existingMap = new Map(existingQuestions.map(q => [q.id, q]));
      const incomingIds = new Set();
      // Upsert questions
      for (let i = 0; i < questions.length; i++) {
        const q = questions[i];
        if (q.id && existingMap.has(q.id)) {
          // Update existing
          await Question.update({
            question_text: q.question_text,
            input_type: q.input_type,
            options: q.options || null,
            order_index: i
          }, { where: { id: q.id } });
          incomingIds.add(q.id);
        } else {
          // Create new
          const newId = q.id && typeof q.id === 'string' && q.id.startsWith('Q_') ? q.id : `Q_${uuidv4().substring(0, 8)}`;
          await Question.create({
            id: newId,
            question_text: q.question_text,
            input_type: q.input_type,
            options: q.options || null,
            order_index: i,
            ficheDeTravailID: id
          });
          incomingIds.add(newId);
        }
      }
      // Delete removed questions
      for (const q of existingQuestions) {
        if (!incomingIds.has(q.id)) {
          await q.destroy();
        }
      }
    }
    // --- End Sync Questions ---

    // Fetch the updated fiche with related data using optimized query
    const updatedFiche = await FicheDeTravail.findByPk(id, {
      include: [
        { model: AuditActivity, as: 'auditActivity', attributes: ['id', 'name'] },
        { model: AuditMission, as: 'auditMission', attributes: ['id', 'name'] },
        { 
          model: Question, 
          as: 'questions', 
          attributes: ['id', 'question_text', 'input_type', 'options', 'order_index'],
          order: [['order_index', 'ASC']],
          separate: true
        }
      ]
    });

    return res.status(200).json({
      success: true,
      message: 'Work sheet updated successfully',
      data: updatedFiche
    });
  } catch (error) {
    console.error('Error updating work sheet:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update work sheet',
      error: error.message
    });
  }
};

// Delete work sheet
const deleteFicheDeTravail = async (req, res) => {
  try {
    const { id } = req.params;
    const fiche = await FicheDeTravail.findByPk(id);
    if (!fiche) {
      return res.status(404).json({ success: false, message: 'Work sheet not found' });
    }
    await fiche.destroy();
    return res.status(200).json({ success: true, message: 'Work sheet deleted successfully' });
  } catch (error) {
    console.error('Error deleting work sheet:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete work sheet',
      error: error.message
    });
  }
};

// Create a new question
const createQuestion = async (req, res) => {
  try {
    const { content, ficheDeTravailID } = req.body;
    if (!content) {
      return res.status(400).json({ success: false, message: 'Content is required' });
    }
    if (!ficheDeTravailID) {
      return res.status(400).json({ success: false, message: 'Work sheet ID is required' });
    }

    const fiche = await FicheDeTravail.findByPk(ficheDeTravailID);
    if (!fiche) {
      return res.status(404).json({ success: false, message: 'Work sheet not found' });
    }

    const maxOrder = await Question.max('order_index', { where: { ficheDeTravailID } }) || 0;
    const question = await Question.create({
      id: `Q_${uuidv4().substring(0, 8)}`,
      content,
      order_index: maxOrder + 1,
      ficheDeTravailID
    });

    // Update questionnaire field with question IDs
    const questions = await Question.findAll({
      where: { ficheDeTravailID },
      attributes: ['id'],
      order: [['order_index', 'ASC']]
    });
    const questionIds = questions.map(q => q.id);
    await fiche.update({ questionnaire: JSON.stringify(questionIds) });

    return res.status(201).json({
      success: true,
      message: 'Question created successfully',
      data: question
    });
  } catch (error) {
    console.error('Error creating question:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create question',
      error: error.message
    });
  }
};

// Reorder questions
const reorderQuestions = async (req, res) => {
  try {
    const { ficheDeTravailID, orderedIds } = req.body;
    if (!ficheDeTravailID) {
      return res.status(400).json({ success: false, message: 'Work sheet ID is required' });
    }
    if (!Array.isArray(orderedIds)) {
      return res.status(400).json({ success: false, message: 'Ordered IDs must be an array' });
    }

    const fiche = await FicheDeTravail.findByPk(ficheDeTravailID);
    if (!fiche) {
      return res.status(404).json({ success: false, message: 'Work sheet not found' });
    }

    await db.sequelize.transaction(async (t) => {
      for (let i = 0; i < orderedIds.length; i++) {
        await Question.update(
          { order_index: i },
          { where: { id: orderedIds[i], ficheDeTravailID }, transaction: t }
        );
      }
      await fiche.update({ questionnaire: JSON.stringify(orderedIds) }, { transaction: t });
    });

    return res.status(200).json({ success: true, message: 'Questions reordered successfully' });
  } catch (error) {
    console.error('Error reordering questions:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to reorder questions',
      error: error.message
    });
  }
};

// Get all questions
const getAllQuestions = async (req, res) => {
  try {
    const questions = await Question.findAll({
      include: [
        {
          model: FicheDeTravail,
          as: 'ficheDeTravail',
          attributes: ['id', 'name'],
          include: [
            { model: AuditActivity, as: 'auditActivity', attributes: ['id', 'name'] },
            { model: AuditMission, as: 'auditMission', attributes: ['id', 'name'] }
          ]
        }
      ],
      order: [['order_index', 'ASC']]
    });
    
    return res.status(200).json({
      success: true,
      data: questions
    });
  } catch (error) {
    console.error('Error fetching questions:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch questions',
      error: error.message
    });
  }
};

// Get questions by fiche ID
const getQuestionsByFicheId = async (req, res) => {
  try {
    const { ficheId } = req.params;
    const questions = await Question.findAll({
      where: { ficheDeTravailID: ficheId },
      order: [['order_index', 'ASC']]
    });
    return res.status(200).json({ success: true, data: questions });
  } catch (error) {
    console.error('Error fetching questions:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch questions',
      error: error.message
    });
  }
};

// Delete question
const deleteQuestion = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the question
    const question = await Question.findByPk(id);
    if (!question) {
      return res.status(404).json({ success: false, message: 'Question not found' });
    }

    // Delete all related FicheTestResponse records first
    const FicheTestResponse = db.FicheTestResponse;
    if (FicheTestResponse) {
      await FicheTestResponse.destroy({
        where: { questionID: id }
      });
    }

    // Remove the answer from all related FicheDeTest (old system)
    const FicheDeTest = db.FicheDeTest;
    const ficheDeTravailID = question.ficheDeTravailID;
    const ficheTests = await FicheDeTest.findAll({ where: { ficheDeTravailID } });
    for (const ficheTest of ficheTests) {
      if (ficheTest.answers && Object.prototype.hasOwnProperty.call(ficheTest.answers, id)) {
        const updatedAnswers = { ...ficheTest.answers };
        delete updatedAnswers[id];
        ficheTest.answers = updatedAnswers;
        await ficheTest.save();
      }
    }

    // Now delete the question
    await question.destroy();

    return res.status(200).json({
      success: true,
      message: 'Question and related answers deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting question:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete question',
      error: error.message
    });
  }
};

module.exports = {
  getAllFicheDeTravail,
  getFicheDeTravailByActivityId,
  createFicheDeTravail,
  getFicheDeTravailById,
  updateFicheDeTravail,
  deleteFicheDeTravail,
  createQuestion,
  reorderQuestions,
  getAllQuestions,
  getQuestionsByFicheId,
  deleteQuestion
};
