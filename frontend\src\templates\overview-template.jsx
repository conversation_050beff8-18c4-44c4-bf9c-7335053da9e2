import { useOutletContext } from "react-router-dom";
// MODIFY: Import your specific icons
import { Tag, Calendar, User, Hash, FileText } from "lucide-react";

function OverviewTemplate() {
  // MODIFY: Update with your specific context property name
  const { item } = useOutletContext();

  // Helper function to format dates
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-GB", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Helper function to render values with N/A styled in grey
  const renderValue = (value) => {
    if (value === "N/A" || value === null || value === undefined) {
      return <span className="text-gray-400">N/A</span>;
    }
    return value;
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6">
        <div>
          <h3 className="text-lg font-semibold text-[#242A33] mb-4">Details</h3>
          <div className="space-y-6">
            {/* MODIFY: Update with your specific item properties */}
            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Tag className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Name</p>
                <p className="font-medium">{renderValue(item.name || "N/A")}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Hash className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Code</p>
                <p className="font-medium">{renderValue(item.code || "N/A")}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <FileText className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Description</p>
                <p className="font-medium">{renderValue(item.description || "N/A")}</p>
              </div>
            </div>

            {/* Add more fields as needed */}
            {item.createdAt && (
              <div className="flex items-start gap-4">
                <div className="bg-gray-50 p-2 rounded-lg">
                  <Calendar className="h-5 w-5 text-[#555F6D]" />
                </div>
                <div>
                  <p className="text-sm text-[#555F6D]">Created At</p>
                  <p className="font-medium">{renderValue(formatDate(item.createdAt))}</p>
                </div>
              </div>
            )}

            {item.createdBy && (
              <div className="flex items-start gap-4">
                <div className="bg-gray-50 p-2 rounded-lg">
                  <User className="h-5 w-5 text-[#555F6D]" />
                </div>
                <div>
                  <p className="text-sm text-[#555F6D]">Created By</p>
                  <p className="font-medium">{renderValue(item.createdBy || "N/A")}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Add additional sections as needed */}
      {/* 
      <div className="grid grid-cols-1 gap-6">
        <div>
          <h3 className="text-lg font-semibold text-[#242A33] mb-4">Related Items</h3>
          <div className="space-y-4">
            {item.relatedItems && item.relatedItems.length > 0 ? (
              item.relatedItems.map((relatedItem) => (
                <div key={relatedItem.id} className="p-4 border border-gray-200 rounded-lg">
                  <p className="font-medium">{relatedItem.name}</p>
                  <p className="text-sm text-[#555F6D]">{relatedItem.description}</p>
                </div>
              ))
            ) : (
              <p className="text-[#555F6D]">No related items found</p>
            )}
          </div>
        </div>
      </div>
      */}
    </div>
  );
}

export default OverviewTemplate;
