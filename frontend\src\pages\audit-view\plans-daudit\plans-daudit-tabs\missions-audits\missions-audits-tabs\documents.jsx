import React, { useState, useContext } from 'react';
import { ChevronUp, ChevronDown, Paperclip } from "lucide-react";
import { useMissionAuditContext } from '@/utils/context-helpers';
import { OutletContext } from '../edit-missions-audits';
import { AuditMissionAttachmentsSection } from '@/components/attachments_audit/AuditMissionAttachmentsSection';

function DocumentsTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const [isAttachmentsOpen, setIsAttachmentsOpen] = useState(true);

  // If no mission data is available yet
  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement des documents...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Attachments Section for Mission */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-violet-50 to-violet-100 rounded-t-lg"
          onClick={() => setIsAttachmentsOpen(!isAttachmentsOpen)}
        >
          <div className="flex items-center gap-2">
            {isAttachmentsOpen ? (
              <ChevronUp className="h-5 w-5 text-violet-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-violet-600" />
            )}
            <Paperclip className="h-5 w-5 text-violet-600 mr-1" />
            <span className="text-lg font-medium text-violet-800">Pièces jointes</span>
          </div>
        </button>
        {isAttachmentsOpen && (
          <div className="p-5 bg-white">
            <AuditMissionAttachmentsSection missionAudit={missionAudit} />
          </div>
        )}
      </div>
    </div>
  );
}

export default DocumentsTab; 