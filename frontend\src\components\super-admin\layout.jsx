import { Outlet, useLocation } from "react-router-dom";
import SuperAdminSideBar from "./sidebar";
import SuperAdminHeader from "./header";
import { useState, useEffect } from "react";

function SuperAdminLayout() {
  const [openSidebar, setOpenSidebar] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const location = useLocation();

  // Handle sidebar collapse state changes
  const handleSidebarCollapseChange = (collapsed) => {
    setIsSidebarCollapsed(collapsed);
  };

  // Listen for window resize to auto-collapse sidebar on small screens
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setIsSidebarCollapsed(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Listen for sidebar state changes from localStorage
  useEffect(() => {
    const handleStorageChange = () => {
      const savedState = localStorage.getItem('sidebarCollapsed');
      if (savedState !== null) {
        setIsSidebarCollapsed(savedState === 'true');
      }
    };

    // Custom event for sidebar state changes
    const handleSidebarStateChanged = () => {
      handleStorageChange();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('sidebarStateChanged', handleSidebarStateChanged);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('sidebarStateChanged', handleSidebarStateChanged);
    };
  }, []);

  // Check if current route is the AI page
  const isAIPage = location.pathname.includes('/incident/ai');

  return (
    <div className={`min-h-screen w-full ${isAIPage ? 'bg-slate-900' : 'bg-gray-100'}`}>
      {/* Fixed Header */}
      <div className="fixed top-0 right-0 left-0 z-20">
        <SuperAdminHeader setOpen={setOpenSidebar} />
      </div>

      {/* Fixed Sidebar */}
      <div className="fixed top-0 left-0 h-full z-30">
        <SuperAdminSideBar
          open={openSidebar}
          setOpen={setOpenSidebar}
          onCollapseChange={handleSidebarCollapseChange}
        />
      </div>

      {/* Main Content Area with proper padding */}
      <div
        className={`pt-[61px] transition-all duration-300 ${isSidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'}`}
      >
        <main className={`${isAIPage ? 'p-0' : ''}`}>
          <Outlet />
        </main>
      </div>
    </div>
  );
}

export default SuperAdminLayout;
