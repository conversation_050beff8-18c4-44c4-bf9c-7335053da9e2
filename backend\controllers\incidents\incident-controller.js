const { Incident, Risk, Control, Entity, BusinessLine, IncidentType, BusinessProcess, OrganizationalProcess, Product, Application, ActionPlan } = require('../../models');
const activityController = require('./activity-controller');
const db = require('../../models');
const { sequelize } = require('../../models');

// Get all incidents
const getAllIncidents = async (req, res) => {
  try {
    const incidents = await Incident.findAll({
      include: [
        { model: Risk, as: 'risk' },
        { model: Control, as: 'control' },
        { model: Entity, as: 'entity' },
        { model: BusinessLine, as: 'businessLine' },
        { model: IncidentType, as: 'incidentType' },
        { model: BusinessProcess, as: 'businessProcess' },
        { model: OrganizationalProcess, as: 'organizationalProcess' },
        { model: Product, as: 'product' },
        { model: Application, as: 'application' },
        { model: ActionPlan, as: 'actionPlan' }
      ]
    });
    res.json({
      success: true,
      data: incidents
    });
  } catch (error) {
    console.error('Error fetching incidents:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch incidents',
      error: error.message
    });
  }
};

// Create new incident
const createIncident = async (req, res) => {
  try {
    const {
      incidentID,
      name,
      description,
      declaredBy,
      declarationDate,
      declarantEntity,
      detectionDate,
      occurrenceDate,
      nearMiss,
      nature,
      impact,
      priority,
      currency,
      grossLoss,
      recoveries,
      provisions,
      controlID,
      riskID,
      entityID,
      businessLineID,
      incidentTypeID,
      businessProcessID,
      organizationalProcessID,
      productID,
      applicationID
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name'].filter(field => !req.body[field])
      });
    }

    const incident = await Incident.create({
      incidentID: incidentID || `INC_${Date.now()}`,
      name,
      description: description || null,
      declaredBy: declaredBy || null,
      declarationDate: declarationDate || new Date(),
      declarantEntity: declarantEntity || null,
      detectionDate: detectionDate || null,
      occurrenceDate: occurrenceDate || null,
      nearMiss: nearMiss || false,
      nature: nature || null,
      impact: impact || null,
      priority: priority || null,
      currency: currency || null,
      grossLoss: grossLoss || null,
      recoveries: recoveries || null,
      provisions: provisions || null,
      controlID: controlID || null,
      riskID: riskID || null,
      entityID: entityID || null,
      businessLineID: businessLineID || null,
      incidentTypeID: incidentTypeID || null,
      businessProcessID: businessProcessID || null,
      organizationalProcessID: organizationalProcessID || null,
      productID: productID || null,
      applicationID: applicationID || null
    });

    // Log the creation activity
    let username = 'Unknown User';
    try {
      if (req.user?.userId) {
        const user = await db.User.findByPk(req.user.userId);
        if (user) {
          username = user.username || user.email || user.name;
        }
      } else {
        username = req.user?.username || req.user?.email || req.user?.name || "Unknown User";
      }
    } catch (err) {
      console.error('Error getting user for activity logging:', err);
    }
    
    await activityController.logCreationActivity(incident, username);

    return res.status(201).json({
      success: true,
      message: 'Incident created successfully',
      data: incident
    });
  } catch (error) {
    console.error('Error creating incident:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create incident'
    });
  }
};

// Get incident by ID
const getIncidentById = async (req, res) => {
  try {
    const { id } = req.params;
    const incident = await Incident.findByPk(id, {
      include: [
        { model: Risk, as: 'risk' },
        { model: Control, as: 'control' },
        { model: Entity, as: 'entity' },
        { model: BusinessLine, as: 'businessLine' },
        { model: IncidentType, as: 'incidentType' },
        { model: BusinessProcess, as: 'businessProcess' },
        { model: OrganizationalProcess, as: 'organizationalProcess' },
        { model: Product, as: 'product' },
        { model: Application, as: 'application' },
        { model: ActionPlan, as: 'actionPlan' }
      ]
    });

    if (!incident) {
      return res.status(404).json({
        success: false,
        message: 'Incident not found'
      });
    }

    res.json({
      success: true,
      data: incident
    });
  } catch (error) {
    console.error('Error fetching incident:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch incident'
    });
  }
};

// Update incident
const updateIncident = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      declaredBy,
      declarationDate,
      declarantEntity,
      detectionDate,
      occurrenceDate,
      nearMiss,
      nature,
      impact,
      priority,
      currency,
      grossLoss,
      recoveries,
      provisions,
      controlID,
      riskID,
      entityID,
      businessLineID,
      incidentTypeID,
      businessProcessID,
      organizationalProcessID,
      productID,
      applicationID,
      actionPlanID
    } = req.body;

    const incident = await Incident.findByPk(id);

    if (!incident) {
      return res.status(404).json({
        success: false,
        message: 'Incident not found'
      });
    }

    // Store the old incident data for activity logging
    const oldIncidentData = { ...incident.get() };

    // Update fields, ensuring that empty strings for foreign keys are converted to null
    await incident.update({
      name: name || incident.name,
      description: description !== undefined ? description : incident.description,
      declaredBy: declaredBy !== undefined ? declaredBy : incident.declaredBy,
      declarationDate: declarationDate !== undefined ? declarationDate : incident.declarationDate,
      declarantEntity: declarantEntity !== undefined ? declarantEntity : incident.declarantEntity,
      detectionDate: detectionDate !== undefined ? detectionDate : incident.detectionDate,
      occurrenceDate: occurrenceDate !== undefined ? occurrenceDate : incident.occurrenceDate,
      nearMiss: nearMiss !== undefined ? nearMiss : incident.nearMiss,
      nature: nature !== undefined ? nature : incident.nature,
      impact: impact !== undefined ? impact : incident.impact,
      priority: priority !== undefined ? priority : incident.priority,
      currency: currency !== undefined ? currency : incident.currency,
      grossLoss: grossLoss !== undefined ? grossLoss : incident.grossLoss,
      recoveries: recoveries !== undefined ? recoveries : incident.recoveries,
      provisions: provisions !== undefined ? provisions : incident.provisions,
      controlID: controlID !== undefined ? (controlID === '' ? null : controlID) : incident.controlID,
      riskID: riskID !== undefined ? (riskID === '' ? null : riskID) : incident.riskID,
      entityID: entityID !== undefined ? (entityID === '' ? null : entityID) : incident.entityID,
      businessLineID: businessLineID !== undefined ? (businessLineID === '' ? null : businessLineID) : incident.businessLineID,
      incidentTypeID: incidentTypeID !== undefined ? (incidentTypeID === '' ? null : incidentTypeID) : incident.incidentTypeID,
      businessProcessID: businessProcessID !== undefined ? (businessProcessID === '' ? null : businessProcessID) : incident.businessProcessID,
      organizationalProcessID: organizationalProcessID !== undefined ? (organizationalProcessID === '' ? null : organizationalProcessID) : incident.organizationalProcessID,
      productID: productID !== undefined ? (productID === '' ? null : productID) : incident.productID,
      applicationID: applicationID !== undefined ? (applicationID === '' ? null : applicationID) : incident.applicationID,
      actionPlanID: actionPlanID !== undefined ? (actionPlanID === '' ? null : actionPlanID) : incident.actionPlanID
    });

    // Log the update activities
    let username = 'Unknown User';
    try {
      if (req.user?.userId) {
        const user = await db.User.findByPk(req.user.userId);
        if (user) {
          username = user.username || user.email || user.name;
        }
      } else {
        username = req.user?.username || req.user?.email || req.user?.name || "Unknown User";
      }
    } catch (err) {
      console.error('Error getting user for activity logging:', err);
    }
    
    await activityController.logUpdateActivities(oldIncidentData, req.body, username);

    res.json({
      success: true,
      message: 'Incident updated successfully',
      data: incident
    });
  } catch (error) {
    console.error('Error updating incident:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update incident: ' + (error.message || error)
    });
  }
};

// Delete incident
const deleteIncident = async (req, res) => {
  // Create a transaction to ensure data consistency
  const transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const incident = await Incident.findByPk(id, { transaction });

    if (!incident) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Incident not found'
      });
    }

    // Import all necessary models
    const { 
      Attachment, 
      Reference, 
      ActionPlan, 
      IncidentActivityLog, 
      IncidentEvent,
      FinancialEntry
    } = require('../../models');
    
    // 1. Delete all activity logs associated with this incident
    const activityLogs = await IncidentActivityLog.findAll({ 
      where: { incident_id: id },
      transaction
    });
    
    if (activityLogs && activityLogs.length > 0) {
      console.log(`Deleting ${activityLogs.length} activity logs for incident ${id}`);
      await Promise.all(activityLogs.map(log => log.destroy({ transaction })));
    }
    
    // 2. Delete all workflow events associated with this incident
    const events = await IncidentEvent.findAll({ 
      where: { incident_id: id },
      transaction
    });
    
    if (events && events.length > 0) {
      console.log(`Deleting ${events.length} workflow events for incident ${id}`);
      await Promise.all(events.map(event => event.destroy({ transaction })));
    }
    
    // 3. Delete all financial entries associated with this incident
    const financialEntries = await FinancialEntry.findAll({ 
      where: { incidentID: id },
      transaction
    });
    
    if (financialEntries && financialEntries.length > 0) {
      console.log(`Deleting ${financialEntries.length} financial entries for incident ${id}`);
      await Promise.all(financialEntries.map(entry => entry.destroy({ transaction })));
    }
    
    // 4. Delete all attachments associated with this incident
    const attachments = await Attachment.findAll({ 
      where: { incidentID: id },
      transaction
    });
    
    if (attachments && attachments.length > 0) {
      console.log(`Deleting ${attachments.length} attachments for incident ${id}`);
      await Promise.all(attachments.map(attachment => attachment.destroy({ transaction })));
    }
    
    // 5. Delete all external references associated with this incident
    const references = await Reference.findAll({ 
      where: { incidentID: id },
      transaction
    });
    
    if (references && references.length > 0) {
      console.log(`Deleting ${references.length} references for incident ${id}`);
      await Promise.all(references.map(reference => reference.destroy({ transaction })));
    }
    
    // 6. Update action plans to remove association with this incident
    // We don't delete action plans, just remove the reference to the incident
    const actionPlans = await ActionPlan.findAll({ 
      where: { incidentID: id },
      transaction
    });
    
    if (actionPlans && actionPlans.length > 0) {
      console.log(`Updating ${actionPlans.length} action plans to remove reference to incident ${id}`);
      await Promise.all(actionPlans.map(actionPlan => 
        actionPlan.update({ incidentID: null }, { transaction })
      ));
    }
    
    // 7. Finally delete the incident
    await incident.destroy({ transaction });
    
    // Commit the transaction
    await transaction.commit();

    res.json({
      success: true,
      message: 'Incident and all its dependent records deleted successfully'
    });
  } catch (error) {
    // Rollback the transaction in case of error
    await transaction.rollback();
    
    console.error('Error deleting incident:', error);
    
    // Provide more descriptive error message for foreign key constraint violations
    if (error.name === 'SequelizeForeignKeyConstraintError') {
      const referencedTable = error.table || error.original?.table || 'another entity';
      
      return res.status(409).json({
        success: false,
        message: `Cannot delete this incident because it is referenced by ${referencedTable}. Please contact the administrator.`,
        detail: error.original?.detail || error.message
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to delete incident',
      error: error.message
    });
  }
};

module.exports = {
  getAllIncidents,
  createIncident,
  getIncidentById,
  updateIncident,
  deleteIncident
};
