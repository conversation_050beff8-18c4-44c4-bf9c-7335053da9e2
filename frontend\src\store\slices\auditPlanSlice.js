import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { 
  getAllAuditPlans, 
  getAuditPlanById, 
  createAuditPlan, 
  updateAuditPlan, 
  deleteAuditPlan 
} from '@/services/audit-plan-service';

// Async thunks
export const fetchAuditPlans = createAsyncThunk(
  'auditPlans/fetchAll',
  async (_, { rejectWithValue, signal }) => {
    try {
      const response = await getAllAuditPlans(signal);
      if (!response) return null; // Request cancelled
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.message);
    } catch (error) {
      if (error.name === 'CanceledError') return null;
      return rejectWithValue(error.message);
    }
  }
);

export const fetchAuditPlanById = createAsyncThunk(
  'auditPlans/fetchById',
  async ({ id, signal }, { rejectWithValue }) => {
    try {
      const response = await getAuditPlanById(id, signal);
      if (!response) return null; // Request cancelled
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.message);
    } catch (error) {
      if (error.name === 'CanceledError') return null;
      return rejectWithValue(error.message);
    }
  }
);

export const addAuditPlan = createAsyncThunk(
  'auditPlans/create',
  async (planData, { rejectWithValue, signal }) => {
    try {
      const response = await createAuditPlan(planData, signal);
      if (!response) return null; // Request was cancelled
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to create audit plan');
    } catch (error) {
      if (error.name === 'CanceledError') {
        return null; // Request was cancelled
      }
      return rejectWithValue(error.message || 'Failed to create audit plan');
    }
  }
);

export const updateAuditPlanById = createAsyncThunk(
  'auditPlans/updateById',
  async ({ id, data, signal }, { rejectWithValue, getState }) => {
    try {
      // Get current state for optimistic update
      const state = getState();
      const currentPlan = state.auditPlans.currentPlan;
      const allPlans = state.auditPlans.plans;

      // Optimistically update the state
      const optimisticUpdate = {
        ...currentPlan,
        ...data,
        // Keep the original data in case we need to revert
        _originalData: currentPlan
      };

      // Make the API call
      const response = await updateAuditPlan(id, data, signal);
      if (!response) return null; // Request cancelled
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.message);
    } catch (error) {
      if (error.name === 'CanceledError') return null;
      return rejectWithValue(error.message);
    }
  }
);

export const deleteAuditPlanById = createAsyncThunk(
  'auditPlans/delete',
  async (id, { rejectWithValue, signal }) => {
    try {
      const response = await deleteAuditPlan(id, signal);
      if (!response) return null; // Request was cancelled
      if (response.success) {
        return id;
      }
      return rejectWithValue(response.message || 'Failed to delete audit plan');
    } catch (error) {
      if (error.name === 'CanceledError') {
        return null; // Request was cancelled
      }
      return rejectWithValue(error.message || 'Failed to delete audit plan');
    }
  }
);

const initialState = {
  plans: [],
  currentPlan: null,
  loading: false,
  error: null,
  lastFetched: null,
  isUpdating: false
};

const auditPlanSlice = createSlice({
  name: 'auditPlans',
  initialState,
  reducers: {
    clearCurrentPlan: (state) => {
      state.currentPlan = null;
      state.lastFetched = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    // Optimistic update reducer
    optimisticUpdate: (state, action) => {
      const { id, data } = action.payload;
      if (state.currentPlan && state.currentPlan.id === id) {
        state.currentPlan = { ...state.currentPlan, ...data };
      }
      const planIndex = state.plans.findIndex(plan => plan.id === id);
      if (planIndex !== -1) {
        state.plans[planIndex] = { ...state.plans[planIndex], ...data };
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch all plans
      .addCase(fetchAuditPlans.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAuditPlans.fulfilled, (state, action) => {
        if (action.payload === null) return; // Request cancelled
        state.loading = false;
        state.plans = action.payload;
        state.lastFetched = Date.now();
      })
      .addCase(fetchAuditPlans.rejected, (state, action) => {
        if (action.payload === null) return; // Request cancelled
        state.loading = false;
        state.error = action.payload;
      })
      // Fetch single plan
      .addCase(fetchAuditPlanById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAuditPlanById.fulfilled, (state, action) => {
        if (action.payload === null) return; // Request cancelled
        state.loading = false;
        state.currentPlan = action.payload;
        state.lastFetched = Date.now();
        // Update in plans array if it exists
        const index = state.plans.findIndex(plan => plan.id === action.payload.id);
        if (index !== -1) {
          state.plans[index] = action.payload;
        }
      })
      .addCase(fetchAuditPlanById.rejected, (state, action) => {
        if (action.payload === null) return; // Request cancelled
        state.loading = false;
        state.error = action.payload;
      })
      // Create plan
      .addCase(addAuditPlan.fulfilled, (state, action) => {
        if (action.payload === null) return; // Request was cancelled
        state.plans.push(action.payload);
      })
      // Update plan
      .addCase(updateAuditPlanById.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateAuditPlanById.fulfilled, (state, action) => {
        if (action.payload === null) return; // Request cancelled
        state.isUpdating = false;
        state.currentPlan = action.payload;
        // Update in plans array if it exists
        const index = state.plans.findIndex(plan => plan.id === action.payload.id);
        if (index !== -1) {
          state.plans[index] = action.payload;
        }
      })
      .addCase(updateAuditPlanById.rejected, (state, action) => {
        if (action.payload === null) return; // Request cancelled
        state.isUpdating = false;
        state.error = action.payload;
        // Revert optimistic update if it exists
        if (state.currentPlan && state.currentPlan._originalData) {
          state.currentPlan = state.currentPlan._originalData;
          const index = state.plans.findIndex(plan => plan.id === state.currentPlan.id);
          if (index !== -1) {
            state.plans[index] = state.currentPlan;
          }
        }
      })
      // Delete plan
      .addCase(deleteAuditPlanById.fulfilled, (state, action) => {
        if (action.payload === null) return; // Request was cancelled
        state.plans = state.plans.filter(plan => plan.id !== action.payload);
        if (state.currentPlan?.id === action.payload) {
          state.currentPlan = null;
          state.lastFetched = null;
        }
      });
  }
});

export const { clearCurrentPlan, clearError, optimisticUpdate } = auditPlanSlice.actions;
export default auditPlanSlice.reducer; 