import React from 'react';
import { Eye } from "lucide-react";

function SuiviTab(props) {
  const missionAudit = props.missionAudit;

  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement du suivi...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <Eye className="h-6 w-6 mr-3 text-[#F62D51]" />
          Suivi
        </h2>
      </div>

      <div className="border rounded-lg shadow-sm p-6">
        <div className="text-center text-gray-500">
          <Eye className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Suivi de la Mission</h3>
          <p className="text-sm">Cette section sera développée prochainement pour gérer le suivi de la mission d'audit.</p>
        </div>
      </div>
    </div>
  );
}

export default SuiviTab;
