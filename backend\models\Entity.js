module.exports = (sequelize, DataTypes) => {
  const Entity = sequelize.define('Entity', {
    entityID: {
      type: DataTypes.STRING,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: DataTypes.STRING,
    localCurrency: DataTypes.STRING,
    code: DataTypes.STRING(50),              // Added
    comment: DataTypes.TEXT,                 // Added
    internalExternal: DataTypes.STRING(50),  // Added
    parentEntityID: {                        // Added
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Entity',
        key: 'entityID',
      },
    },
  }, {
    tableName: 'Entity',
    timestamps: false,
  });

  Entity.associate = function(models) {
    Entity.belongsToMany(models.AuditScope, {
      through: 'AuditScopeEntities',
      foreignKey: 'entityID',
      otherKey: 'auditScopeID',
      as: 'auditScopes'
    });
  };

  return Entity;
};