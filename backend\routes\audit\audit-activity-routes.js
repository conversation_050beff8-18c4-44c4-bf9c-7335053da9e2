const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllAuditActivities,
  getAuditActivitiesByMissionId,
  createAuditActivity,
  getAuditActivityById,
  updateAuditActivity,
  deleteAuditActivity
} = require('../../controllers/audit/audit-activity-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all audit activities
router.get('/', authorizeRoles(['audit_director', 'auditor']), getAllAuditActivities);

// Get audit activities by mission ID
router.get('/mission/:missionId', authorizeRoles(['audit_director', 'auditor']), getAuditActivitiesByMissionId);

// Create new audit activity
router.post('/', authorizeRoles(['audit_director', 'auditor']), createAuditActivity);

// Get audit activity by ID
router.get('/:id',  authorizeRoles(['audit_director', 'auditor']),getAuditActivityById);

// Update audit activity
router.put('/:id', authorizeRoles(['audit_director', 'auditor']), updateAuditActivity);

// Delete audit activity
router.delete('/:id', authorizeRoles(['audit_director', 'auditor']), deleteAuditActivity);

module.exports = router;