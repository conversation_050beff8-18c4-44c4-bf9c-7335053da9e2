import React from "react";
import { useOutletContext } from "react-router-dom";
import { BarChart2 } from "lucide-react";
import { useTranslation } from 'react-i18next';

function RisksReports() {
  const { t } = useTranslation();
  const { risk } = useOutletContext();

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold mb-6">{t('admin.risks.reports.title', 'Reports')}</h2>

      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <BarChart2 className="h-5 w-5 mr-2 text-blue-500" />
            {t('admin.risks.reports.risk_reports', 'Risk Reports')}
          </h3>
        </div>
        <div className="p-6">
          <p className="text-gray-500">{t('admin.risks.reports.placeholder', 'Risk reports and analytics will be displayed here.')}</p>
        </div>
      </div>
    </div>
  );
}

export default RisksReports;