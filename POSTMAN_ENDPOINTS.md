# Postman Endpoints for Audit Mission Rapport Support

## Base URL
```
http://localhost:5001/api
```

## Authentication
All endpoints require authentication. Include the following in your request headers:
```
Content-Type: application/json
<PERSON>ie: token=your_jwt_token_here
```

---

## 1. Audit Mission Rapport Support Endpoints

### 1.1 Get All Rapport Supports
```
GET /audit-mission-rapport-support
```

### 1.2 Get Rapport Support by Mission ID
```
GET /audit-mission-rapport-support/mission/{missionId}
```
**Example:**
```
GET /audit-mission-rapport-support/mission/AM_12345678
```

### 1.3 Get Rapport Support by ID
```
GET /audit-mission-rapport-support/{id}
```
**Example:**
```
GET /audit-mission-rapport-support/AMRS_87654321
```

### 1.4 Create New Rapport Support
```
POST /audit-mission-rapport-support
Content-Type: application/json

{
  "logo": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",
  "signatureElectrique": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",
  "destinataire": "Direction Générale\nService Audit Interne\n123 Rue de l'Audit\n75001 Paris",
  "auditMissionId": "AM_12345678"
}
```

### 1.5 Update Rapport Support
```
PUT /audit-mission-rapport-support/{id}
Content-Type: application/json

{
  "logo": "data:image/png;base64,NEW_LOGO_BASE64_HERE",
  "signatureElectrique": "data:image/png;base64,NEW_SIGNATURE_BASE64_HERE",
  "destinataire": "Updated Destinataire Information",
  "auditMissionId": "AM_12345678"
}
```

### 1.6 Delete Rapport Support
```
DELETE /audit-mission-rapport-support/{id}
```

---

## 2. Enhanced Mission Report Endpoints

### 2.1 Get Enhanced Mission Report (JSON)
```
GET /audit-mission-rapport/enhanced-mission-report/{missionId}
```
**Example:**
```
GET /audit-mission-rapport/enhanced-mission-report/AM_12345678
```

### 2.2 Get Enhanced Mission Report (PDF with Colors)
```
GET /audit-mission-rapport/enhanced-mission-report/{missionId}?format=pdf
```
**Example:**
```
GET /audit-mission-rapport/enhanced-mission-report/AM_12345678?format=pdf
```

### 2.3 Get Original Mission Report (PDF with Colors)
```
GET /audit-mission-rapport/mission-report/{missionId}?format=pdf
```

### 2.4 Get Original Mission Report (DOCX with Colors)
```
GET /audit-mission-rapport/mission-report/{missionId}?format=docx
```

---

## 3. Testing Workflow

### Step 1: Create a Mission (if needed)
```
POST /audit-missions
Content-Type: application/json

{
  "name": "Test Mission for Rapport",
  "categorie": "Audit Interne",
  "principalAudite": "Jean Dupont - Directeur Financier",
  "objectif": "Évaluer les contrôles internes du processus financier",
  "datedebut": "2024-01-15",
  "datefin": "2024-02-15",
  "auditplanID": "AP_12345678"
}
```

### Step 2: Create Rapport Support
```
POST /audit-mission-rapport-support
Content-Type: application/json

{
  "logo": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",
  "signatureElectrique": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",
  "destinataire": "Monsieur le Directeur Général\nSociété ABC\n123 Avenue des Champs\n75008 Paris",
  "auditMissionId": "AM_12345678"
}
```

### Step 3: Generate Enhanced Report
```
GET /audit-mission-rapport/enhanced-mission-report/AM_12345678?format=pdf
```

---

## 4. Sample Base64 Images for Testing

### Small Test Logo (1x1 pixel transparent PNG)
```
data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==
```

### Small Test Signature (1x1 pixel transparent PNG)
```
data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==
```

---

## 5. Expected Response Formats

### Success Response (Create/Update)
```json
{
  "success": true,
  "message": "Rapport support created successfully",
  "data": {
    "id": "AMRS_87654321",
    "logo": "data:image/png;base64,...",
    "signatureElectrique": "data:image/png;base64,...",
    "destinataire": "Direction Générale...",
    "auditMissionId": "AM_12345678",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z",
    "auditMission": {
      "id": "AM_12345678",
      "name": "Test Mission",
      "principalAudite": "Jean Dupont",
      "datedebut": "2024-01-15",
      "datefin": "2024-02-15"
    }
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Audit mission not found",
  "error": "Mission with ID AM_12345678 does not exist"
}
```

---

## 6. Color Scheme Used in Reports

- **Primary Blue**: #2E86AB (Headers, main sections)
- **Orange Accent**: #F18F01 (Decorative lines, sub-headers)
- **Green**: #28A745 (Positive points, strengths)
- **Red**: #DC3545 (Negative points, weaknesses)
- **Dark Gray**: #333333 (Labels, bold text)
- **Light Gray**: #666666 (Content text)
- **White**: #FFFFFF (Text on colored backgrounds)

---

## 7. Testing Notes

1. **Authentication**: Make sure you're logged in and have the proper JWT token
2. **Mission ID**: Use an existing mission ID from your database
3. **Base64 Images**: For real testing, use actual base64 encoded images
4. **File Downloads**: PDF and DOCX endpoints will return file downloads
5. **CORS**: Requests should come from http://localhost:5173 (frontend)

---

## 8. Troubleshooting

### Common Issues:
- **401 Unauthorized**: Check your authentication token
- **404 Not Found**: Verify the mission ID exists
- **400 Bad Request**: Check required fields in request body
- **500 Internal Server Error**: Check server logs for detailed error messages
