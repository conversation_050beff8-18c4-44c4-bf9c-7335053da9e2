const { sequelize } = require('../../../models/index');
const db = require('../../../models');
const AuditMissionRapport = require('../../../models/Audit/rapport/auditmissionrapport')(sequelize);
const { AuditMissionRapportSupport, AuditMission } = db;
const { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, WidthType, BorderStyle } = require('docx');
const PDFDocument = require('pdfkit');

const escapeText = (str) => {
  if (typeof str !== 'string') return str || 'N/A';
  return str.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
};

// Helper function to add logo to PDF pages
const addLogoToPage = (doc, logoBase64, x = 50, y = 50, width = 100, height = 50) => {
  if (logoBase64) {
    try {
      // Remove data URL prefix if present
      const base64Data = logoBase64.replace(/^data:image\/[a-z]+;base64,/, '');
      const logoBuffer = Buffer.from(base64Data, 'base64');
      doc.image(logoBuffer, x, y, { width, height });
    } catch (error) {
      console.error('Error adding logo to PDF:', error);
    }
  }
};

// Helper function to add electronic signature to PDF
const addSignatureToPage = (doc, signatureBase64, x, y, width = 150, height = 75) => {
  if (signatureBase64) {
    try {
      // Remove data URL prefix if present
      const base64Data = signatureBase64.replace(/^data:image\/[a-z]+;base64,/, '');
      const signatureBuffer = Buffer.from(base64Data, 'base64');
      doc.image(signatureBuffer, x, y, { width, height });
    } catch (error) {
      console.error('Error adding signature to PDF:', error);
    }
  }
};

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    return dateString;
  }
};

// Generate enhanced mission report with front page and sommaire
exports.getEnhancedMissionReport = async (req, res) => {
  const { missionId } = req.params;
  const { format } = req.query;

  // Set CORS headers immediately
  res.set({
    'Access-Control-Allow-Origin': 'http://localhost:5173',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, x-user-id',
    'Access-Control-Allow-Credentials': 'true'
  });

  console.log('[EnhancedAuditMissionRapport] Processing request for mission:', missionId, 'format:', format);

  try {
    // Get mission data
    const mission = await AuditMission.findByPk(missionId);
    if (!mission) {
      return res.status(404).json({ message: 'Mission not found' });
    }

    // Get rapport support data
    const rapportSupport = await AuditMissionRapportSupport.findOne({
      where: { auditMissionId: missionId }
    });

    // Get existing report data
    const reportData = await AuditMissionRapport.getMissionReport(missionId);
    console.log('[EnhancedAuditMissionRapport] Report data retrieved, rows:', reportData?.length);

    if (!reportData || reportData.length === 0) {
      return res.status(404).json({ message: 'No data found for the specified mission ID' });
    }

    if (format === 'pdf') {
      const doc = new PDFDocument({ margin: 50 });
      const chunks = [];

      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => {
        const pdfBuffer = Buffer.concat(chunks);
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="rapport-mission-${missionId}.pdf"`);
        res.send(pdfBuffer);
      });

      // FRONT PAGE
      // Add a colored header background
      doc.rect(0, 0, 612, 120).fill('#2E86AB'); // Blue header

      // Add logo to top left if available
      if (rapportSupport?.logo) {
        addLogoToPage(doc, rapportSupport.logo, 50, 30, 120, 60);
      }

      // Add white text on blue background
      doc.fontSize(24).font('Helvetica-Bold').fillColor('#FFFFFF');
      doc.text('RAPPORT DE MISSION D\'AUDIT', 50, 180, { align: 'center', width: 500 });

      // Add decorative line
      doc.rect(50, 220, 500, 3).fill('#F18F01'); // Orange accent line

      doc.moveDown(3);

      // Mission details with colors
      doc.fontSize(18).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('Mission:', 50, 280);
      doc.fontSize(16).font('Helvetica').fillColor('#333333');
      doc.text(escapeText(mission.name), 50, 310);
      doc.moveDown();

      // Date range with color
      doc.fontSize(18).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('Période:', 50, 350);
      doc.fontSize(16).font('Helvetica').fillColor('#333333');
      const startDate = formatDate(mission.datedebut);
      const endDate = formatDate(mission.datefin);
      doc.text(`Du ${startDate} au ${endDate}`, 50, 380);
      doc.moveDown();

      // Destinataire with color
      if (rapportSupport?.destinataire) {
        doc.fontSize(18).font('Helvetica-Bold').fillColor('#2E86AB');
        doc.text('Destinataire:', 50, 420);
        doc.fontSize(16).font('Helvetica').fillColor('#333333');
        doc.text(escapeText(rapportSupport.destinataire), 50, 450);
        doc.moveDown();
      }

      // Principal Audité with color
      if (mission.principalAudite) {
        doc.fontSize(18).font('Helvetica-Bold').fillColor('#2E86AB');
        doc.text('Principal Audité:', 50, 490);
        doc.fontSize(16).font('Helvetica').fillColor('#333333');
        doc.text(escapeText(mission.principalAudite), 50, 520);
      }

      // Add decorative footer line
      doc.rect(50, 600, 500, 2).fill('#F18F01');

      // Add electronic signature to bottom right if available
      if (rapportSupport?.signatureElectrique) {
        addSignatureToPage(doc, rapportSupport.signatureElectrique, 400, 650, 150, 75);
      }

      // Add new page for sommaire
      doc.addPage();

      // Add colored header for sommaire
      doc.rect(0, 0, 612, 100).fill('#2E86AB'); // Blue header

      // Add logo to sommaire page
      if (rapportSupport?.logo) {
        addLogoToPage(doc, rapportSupport.logo, 50, 20, 120, 60);
      }

      // SOMMAIRE PAGE
      doc.fontSize(24).font('Helvetica-Bold').fillColor('#FFFFFF');
      doc.text('SOMMAIRE', 50, 130, { align: 'center', width: 500 });

      // Add decorative line
      doc.rect(50, 170, 500, 3).fill('#F18F01'); // Orange accent line
      doc.moveDown(3);

      // Sommaire items with alternating colors
      const sommaire = [
        '1. Introduction .................................................... 3',
        '2. Résumé ......................................................... 4',
        '   2.1 Objectif de la mission d\'Audit ........................... 4',
        '   2.2 Points forts .............................................. 4',
        '   2.3 Points faibles ............................................ 4',
        '3. Contexte ....................................................... 5',
        '   3.1 Managers opérationnels audités ........................... 5',
        '   3.2 Coûts de la mission d\'Audit .............................. 5',
        '4. Détails de la mission .......................................... 6',
        '   4.1 Équipe d\'audit ............................................ 6',
        '   4.2 Méthodologie .............................................. 6',
        '5. Constats et Recommandations .................................... 7',
        '6. Conclusion ..................................................... 8'
      ];

      let yPosition = 200;
      sommaire.forEach((item, index) => {
        // Alternate colors for better readability
        const isMainSection = !item.startsWith('   ');
        if (isMainSection) {
          doc.fontSize(14).font('Helvetica-Bold').fillColor('#2E86AB');
        } else {
          doc.fontSize(12).font('Helvetica').fillColor('#666666');
        }
        doc.text(item, 50, yPosition);
        yPosition += isMainSection ? 28 : 22;
      });

      // Add new page for the actual report content
      doc.addPage();

      // Add logo to content pages
      if (rapportSupport?.logo) {
        addLogoToPage(doc, rapportSupport.logo, 50, 50, 120, 60);
      }

      // Continue with existing report content...
      // [The rest of the existing PDF generation code would go here]

      doc.end();
    } else {
      // Return JSON data including rapport support
      const enhancedData = {
        mission: {
          id: mission.id,
          name: mission.name,
          principalAudite: mission.principalAudite,
          datedebut: mission.datedebut,
          datefin: mission.datefin
        },
        rapportSupport: rapportSupport ? {
          logo: rapportSupport.logo,
          signatureElectrique: rapportSupport.signatureElectrique,
          destinataire: rapportSupport.destinataire
        } : null,
        reportData: reportData[0]
      };

      res.json(enhancedData);
    }
  } catch (error) {
    console.error('[EnhancedAuditMissionRapport] Error:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

exports.getMissionReport = async (req, res) => {
  const { missionId } = req.params;
  const { format } = req.query;

  // Set CORS headers immediately
  res.set({
    'Access-Control-Allow-Origin': 'http://localhost:5173',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, x-user-id',
    'Access-Control-Allow-Credentials': 'true'
  });

  console.log('[AuditMissionRapport] Processing request for mission:', missionId, 'format:', format);

  try {
    const reportData = await AuditMissionRapport.getMissionReport(missionId);
    console.log('[AuditMissionRapport] Report data retrieved, rows:', reportData?.length);

    if (!reportData || reportData.length === 0) {
      return res.status(404).json({ message: 'No data found for the specified mission ID' });
    }

    const autresParticipants = [
      ...new Set(
        reportData
          .flatMap(row => [row.activity_responsable, row.constat_responsable])
          .filter(id => id !== null && id !== undefined)
      ),
    ].join(', ');

    const auditedElements = [
      reportData[0].risk_names,
      reportData[0].entity_names,
      reportData[0].control_names,
      reportData[0].incident_names,
      reportData[0].organizational_process_names,
    ]
      .filter(name => name)
      .join(', ');

    if (format === 'pdf') {
      console.log('[AuditMissionRapport] Starting PDF generation for mission:', missionId);

      try {
        let missionName = reportData[0].mission_name || missionId;
        missionName = missionName.replace(/[^a-zA-Z0-9_-]/g, '_');

        // Set PDF headers before starting generation
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename=mission_audit_report_${missionName}.pdf`);

        const doc = new PDFDocument({ margin: 50 });

        // Pipe directly to response
        doc.pipe(res);

        console.log('[AuditMissionRapport] PDF document created and piped to response');

      // Title with color
      doc.fontSize(20).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('Rapport de Mission d\'Audit', { align: 'center' });

      // Add decorative line
      doc.rect(50, doc.y + 10, 500, 3).fill('#F18F01');
      doc.moveDown(3);

      // Section 1: Diffusion with colors
      doc.fontSize(16).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('1. Diffusion');
      doc.moveDown(0.5);

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Chef de mission: `);
      doc.fontSize(12).font('Helvetica').fillColor('#666666');
      doc.text(`${escapeText(reportData[0].chefmission)}`, { continued: false });

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Directeur d'audit: `);
      doc.fontSize(12).font('Helvetica').fillColor('#666666');
      doc.text(`${escapeText(reportData[0].directeuraudit)}`, { continued: false });

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Autre(s) Participant(s): `);
      doc.fontSize(12).font('Helvetica').fillColor('#666666');
      doc.text(`${escapeText(autresParticipants)}`, { continued: false });
      doc.moveDown();

      // Section 2: Résumé with colors
      doc.fontSize(16).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('2. Résumé');
      doc.moveDown(0.5);

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Mission d'audit: `);
      doc.fontSize(12).font('Helvetica').fillColor('#666666');
      doc.text(`${escapeText(reportData[0].mission_name)}`, { continued: false });

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Catégorie: `);
      doc.fontSize(12).font('Helvetica').fillColor('#666666');
      doc.text(`${escapeText(reportData[0].categorie)}`, { continued: false });

      doc.fontSize(12).font('Helvetica-Bold').fillColor('#333333');
      doc.text(`Évaluation: `);
      doc.fontSize(12).font('Helvetica').fillColor('#666666');
      doc.text(`${escapeText(reportData[0].evaluation)}`, { continued: false });
      doc.moveDown();

      doc.fontSize(14).font('Helvetica-Bold').fillColor('#F18F01');
      doc.text('2.1 Objectif de la mission d\'Audit');
      doc.fontSize(12).font('Helvetica').fillColor('#333333');
      doc.text(escapeText(reportData[0].objectif), { indent: 20 });
      doc.moveDown();

      doc.fontSize(14).font('Helvetica-Bold').fillColor('#28A745'); // Green for positive points
      doc.text('2.2 Points forts');
      doc.fontSize(12).font('Helvetica').fillColor('#333333');
      doc.text(`La mission a permis de constater que : ${escapeText(reportData[0].pointfort)}`, { indent: 20 });
      doc.moveDown();

      doc.fontSize(14).font('Helvetica-Bold').fillColor('#DC3545'); // Red for weak points
      doc.text('2.3 Points faibles');
      doc.fontSize(12).font('Helvetica').fillColor('#333333');
      doc.text(`Une faiblesse majeure a été relevée : ${escapeText(reportData[0].pointfaible)}`, { indent: 20 });
      doc.moveDown();

      // Section 3: Contexte with color
      doc.fontSize(16).font('Helvetica-Bold').fillColor('#2E86AB');
      doc.text('3. Contexte');
      doc.moveDown(0.5);

      doc.fontSize(12).font('Helvetica-Bold').text('3.1 Managers opérationnels audités');
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica');
      doc.text(`Élément audité: ${escapeText(auditedElements)}`);
      doc.text(`Propriétaire: ${escapeText(reportData[0].recommendation_responsable)}`);
      doc.moveDown();

      doc.fontSize(12).font('Helvetica-Bold').text('3.2 Coûts de la mission d\'Audit');
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica');
      doc.text(`Charge de travail estimée (Heures): ${escapeText(String(reportData[0].chargedetravailestimee))}`);
      doc.text(`Charge de travail effective (Heures): ${escapeText(String(reportData[0].chargedetravaileffective))}`);
      doc.text(`Total des dépenses: ${escapeText(String(reportData[0].depenses))}`);
      doc.moveDown();

      doc.fontSize(12).font('Helvetica-Bold').text('3.3 Ressources de la mission d\'Audit');
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica');
      doc.text(`Chef de mission: ${escapeText(reportData[0].chefmission)}`);
      doc.text(`Principal audité: ${escapeText(reportData[0].principalAudite)}`);
      doc.text(`Audité(s): ${escapeText(auditedElements)}`);
      doc.text(`Autre(s) Participant(s): ${escapeText(autresParticipants)}`);
      doc.moveDown();

      // Section 4: Avis
      doc.fontSize(14).font('Helvetica-Bold').text('4. Avis concernant la mission d\'Audit', { underline: true });
      doc.moveDown(0.5);
      doc.fontSize(12).font('Helvetica');
      doc.text(`Les objectifs de la mission d'Audit sont : ${escapeText(reportData[0].objectif)}`);
      doc.text('Les sections suivantes présentent les détails du résultat de la mission d\'Audit.');
      doc.moveDown();

      doc.fontSize(12).font('Helvetica-Bold').text('4.1 Points forts');
      doc.fontSize(12).font('Helvetica').text(`La mission a permis de constater que : ${escapeText(reportData[0].pointfort)}`, { indent: 20 });
      doc.moveDown();

      doc.fontSize(12).font('Helvetica-Bold').text('4.2 Points faibles');
      doc.fontSize(12).font('Helvetica').text(`Une faiblesse majeure a été relevée : ${escapeText(reportData[0].pointfaible)}`, { indent: 20 });
      doc.moveDown();

      // Section 5: Constats
      doc.fontSize(14).font('Helvetica-Bold').text('5. Constats', { underline: true });
      doc.moveDown(0.5);

      doc.fontSize(12).font('Helvetica-Bold').text('5.1 Constats urgents');
      doc.moveDown(0.5);
      const urgentFindings = reportData.filter(row => row.constat_impact === 'très fort');
      if (urgentFindings.length === 0) {
        doc.fontSize(12).font('Helvetica').text('Aucun constat urgent trouvé.');
      } else {
        urgentFindings.forEach(row => {
          doc.fontSize(12).font('Helvetica').text(`Constat: ${escapeText(row.constat_name)}`);
          doc.text(`Impact: ${escapeText(row.constat_impact)}`);
          doc.text(`Risque(s): ${escapeText(row.constat_risks)}`);
          doc.moveDown(0.5);
        });
      }
      doc.moveDown();

      doc.fontSize(12).font('Helvetica-Bold').text('5.2 Détails des constats et recommandations');
      doc.moveDown(0.5);
      const findings = reportData.filter(row => row.constat_name);
      findings.forEach(row => {
        doc.fontSize(12).font('Helvetica');
        doc.text(`Constat: ${escapeText(row.constat_name)}`);
        doc.text(`Impact: ${escapeText(row.constat_impact)}`);
        doc.text(`Risque(s): ${escapeText(row.constat_risks)}`);
        doc.text(`Recommandation: ${escapeText(row.recommendation_name)}`);
        doc.text(`Détails: ${escapeText(row.recommendation_details)}`);
        doc.text(`Propriétaire: ${escapeText(row.recommendation_responsable)}`);
        doc.text(`Date de fin: ${escapeText(row.datefin)}`);
        doc.moveDown(0.5);
      });

        // Finalize the PDF document
        console.log('[AuditMissionRapport] Finalizing PDF document');
        doc.end();

      } catch (pdfError) {
        console.error('[AuditMissionRapport] PDF generation error:', pdfError);
        res.status(500).json({ message: 'Failed to generate PDF', error: pdfError.message });
      }

    } else if (format === 'docx') {
      // Get and sanitize mission name for filename
      let missionName = reportData[0].mission_name || missionId;
      missionName = missionName.replace(/[^a-zA-Z0-9_-]/g, '_');
      const filename = `mission_audit_report_${missionName}.docx`;
      const doc = new Document({
        sections: [
          {
            properties: {
              page: {
                margin: {
                  top: 720,
                  right: 720,
                  bottom: 720,
                  left: 720,
                },
              },
            },
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Rapport de Mission d\'Audit',
                    bold: true,
                    size: 32,
                    color: '2E86AB', // Blue color
                  }),
                ],
                alignment: 'center',
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: '1. Diffusion',
                    bold: true,
                    size: 24,
                    color: '2E86AB', // Blue color
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Chef de mission: ',
                    bold: true,
                    color: '333333',
                  }),
                  new TextRun({
                    text: reportData[0].chefmission || 'N/A',
                    color: '666666',
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Directeur d\'audit: ',
                    bold: true,
                    color: '333333',
                  }),
                  new TextRun({
                    text: reportData[0].directeuraudit || 'N/A',
                    color: '666666',
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Autre(s) Participant(s): ',
                    bold: true,
                    color: '333333',
                  }),
                  new TextRun({
                    text: autresParticipants || 'N/A',
                    color: '666666',
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: '2. Résumé',
                    bold: true,
                    size: 24,
                    color: '2E86AB', // Blue color
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Mission d\'audit: ',
                    bold: true,
                    color: '333333',
                  }),
                  new TextRun({
                    text: reportData[0].mission_name || 'N/A',
                    color: '666666',
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Catégorie: ',
                    bold: true,
                    color: '333333',
                  }),
                  new TextRun({
                    text: reportData[0].categorie || 'N/A',
                    color: '666666',
                  }),
                ],
              }),
              new Paragraph({
                children: [
                  new TextRun({
                    text: 'Évaluation: ',
                    bold: true,
                    color: '333333',
                  }),
                  new TextRun({
                    text: reportData[0].evaluation || 'N/A',
                    color: '666666',
                  }),
                ],
              }),
              new Paragraph({
                text: '2.1 Objectif de la mission d\'Audit',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(reportData[0].objectif || 'N/A'),
              new Paragraph({
                text: '2.2 Points forts',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(`La mission a permis de constater que : ${reportData[0].pointfort || 'N/A'}`),
              new Paragraph({
                text: '2.3 Points faibles',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(`Une faiblesse majeure a été relevée : ${reportData[0].pointfaible || 'N/A'}`),
              new Paragraph({
                text: '3. Contexte',
                heading: HeadingLevel.HEADING_1,
              }),
              new Paragraph({
                text: '3.1 Managers opérationnels audités',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Élément audité', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Propriétaire', bold: true })],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph(auditedElements || 'N/A')],
                      }),
                      new TableCell({
                        children: [new Paragraph(reportData[0].recommendation_responsable || 'N/A')],
                      }),
                    ],
                  }),
                ],
              }),
              new Paragraph({
                text: '3.2 Coûts de la mission d\'Audit',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Description', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Valeur', bold: true })],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Charge de travail estimée (Heures)')],
                      }),
                      new TableCell({
                        children: [new Paragraph(String(reportData[0].chargedetravailestimee || 'N/A'))],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Charge de travail effective (Heures)')],
                      }),
                      new TableCell({
                        children: [new Paragraph(String(reportData[0].chargedetravaileffective || 'N/A'))],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Total des dépenses')],
                      }),
                      new TableCell({
                        children: [new Paragraph(String(reportData[0].depenses || 'N/A'))],
                      }),
                    ],
                  }),
                ],
              }),
              new Paragraph({
                text: '3.3 Ressources de la mission d\'Audit',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Rôle', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 50, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Nom', bold: true })],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Chef de mission')],
                      }),
                      new TableCell({
                        children: [new Paragraph(reportData[0].chefmission || 'N/A')],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Principal audité')],
                      }),
                      new TableCell({
                        children: [new Paragraph(reportData[0].principalAudite || 'N/A')],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Audité(s)')],
                      }),
                      new TableCell({
                        children: [new Paragraph(auditedElements || 'N/A')],
                      }),
                    ],
                  }),
                  new TableRow({
                    children: [
                      new TableCell({
                        children: [new Paragraph('Autre(s) Participant(s)')],
                      }),
                      new TableCell({
                        children: [new Paragraph(autresParticipants || 'N/A')],
                      }),
                    ],
                  }),
                ],
              }),
              new Paragraph({
                text: '4. Avis concernant la mission d\'Audit',
                heading: HeadingLevel.HEADING_1,
              }),
              new Paragraph(`Les objectifs de la mission d'Audit sont : ${reportData[0].objectif || 'N/A'}`),
              new Paragraph('Les sections suivantes présentent les détails du résultat de la mission d\'Audit.'),
              new Paragraph({
                text: '4.1 Points forts',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(`La mission a permis de constater que : ${reportData[0].pointfort || 'N/A'}`),
              new Paragraph({
                text: '4.2 Points faibles',
                heading: HeadingLevel.HEADING_2,
              }),
              new Paragraph(`Une faiblesse majeure a été relevée : ${reportData[0].pointfaible || 'N/A'}`),
              new Paragraph({
                text: '5. Constats',
                heading: HeadingLevel.HEADING_1,
              }),
              new Paragraph({
                text: '5.1 Constats urgents',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 33, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Constat', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 33, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Impact', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 34, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Risque(s)', bold: true })],
                      }),
                    ],
                  }),
                  ...reportData
                    .filter(row => row.constat_impact === 'très fort')
                    .map(
                      row =>
                        new TableRow({
                          children: [
                            new TableCell({
                              children: [new Paragraph(row.constat_name || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.constat_impact || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.constat_risks || 'N/A')],
                            }),
                          ],
                        })
                    ),
                  ...(reportData.filter(row => row.constat_impact === 'très fort').length === 0
                    ? [
                        new TableRow({
                          children: [
                            new TableCell({
                              children: [new Paragraph('Aucun constat urgent trouvé.')],
                              columnSpan: 3,
                            }),
                          ],
                        }),
                      ]
                    : []),
                ],
              }),
              new Paragraph({
                text: '5.2 Détails des constats et recommandations',
                heading: HeadingLevel.HEADING_2,
              }),
              new Table({
                width: {
                  size: 100,
                  type: WidthType.PERCENTAGE,
                },
                borders: {
                  top: { style: BorderStyle.SINGLE, size: 1 },
                  bottom: { style: BorderStyle.SINGLE, size: 1 },
                  left: { style: BorderStyle.SINGLE, size: 1 },
                  right: { style: BorderStyle.SINGLE, size: 1 },
                },
                rows: [
                  new TableRow({
                    children: [
                      new TableCell({
                        width: { size: 14, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Constat', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 14, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Impact', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 14, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Risque(s)', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 14, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Recommandation', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 14, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Détails', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 14, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Propriétaire', bold: true })],
                      }),
                      new TableCell({
                        width: { size: 16, type: WidthType.PERCENTAGE },
                        children: [new Paragraph({ text: 'Date de fin', bold: true })],
                      }),
                    ],
                  }),
                  ...reportData
                    .filter(row => row.constat_name)
                    .map(
                      row =>
                        new TableRow({
                          children: [
                            new TableCell({
                              children: [new Paragraph(row.constat_name || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.constat_impact || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.constat_risks || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.recommendation_name || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.recommendation_details || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.recommendation_responsable || 'N/A')],
                            }),
                            new TableCell({
                              children: [new Paragraph(row.datefin || 'N/A')],
                            }),
                          ],
                        })
                    ),
                ],
              }),
            ],
          },
        ],
      });

      const buffer = await Packer.toBuffer(doc);
      console.log('[AuditMissionRapport] DOCX buffer size:', buffer.length);
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
      res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
      res.send(buffer);
    } else {
      // Return JSON data when no format is specified
      res.status(200).json(reportData);
    }
  } catch (error) {
    console.error('[AuditMissionRapport] Error generating report:', error);
    res.status(500).json({ message: 'Error generating report', error: error.message });
  }
};