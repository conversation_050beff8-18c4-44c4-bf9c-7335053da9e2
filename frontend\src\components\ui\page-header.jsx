import React from 'react';
import { FileText, Search } from 'lucide-react';

/**
 * PageHeader - A reusable header component for page titles
 *
 * @param {Object} props
 * @param {string} props.title - The main title of the page
 * @param {string} props.description - Description text below the title
 * @param {string} props.section - The section name (for breadcrumb)
 * @param {string} props.currentPage - The current page name (for breadcrumb)
 * @param {string} props.searchPlaceholder - Placeholder text for the search input
 * @param {string} props.searchValue - Value for the search input
 * @param {Function} props.onSearchChange - Function to handle search input changes
 * @param {React.ReactNode} props.icon - Custom icon to display instead of default FileText
 * @param {React.ReactNode} props.actions - Additional actions to display in the header
 */
const PageHeader = ({
  title,
  description,
  section = 'Home',
  currentPage,
  searchPlaceholder = 'Search...',
  searchValue = '',
  onSearchChange,
  icon: Icon = FileText,
  actions
}) => {
  return (
    <div className="mb-6">
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* Top accent bar */}
        <div className="h-1.5 bg-[#1a2942]"></div>

        <div className="py-4 px-6">
          {/* Title section with geometric accent */}
          <div className="flex items-start gap-6">
            {/* Left geometric accent */}
            <div className="hidden md:block relative min-w-[5rem] h-16">
              <div className="absolute top-0 left-0 w-10 h-10 bg-[#1a2942] rounded-lg transform rotate-45 translate-x-3"></div>
              <div className="absolute bottom-0 right-0 w-6 h-6 bg-[#F62D51] rounded-lg transform rotate-45 -translate-y-1"></div>
            </div>

            {/* Title content */}
            <div className="flex-1">
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <Icon className="h-3 w-3" />
                <span>{section}</span>
                {currentPage && (
                  <>
                    <span>/</span>
                    <span className="text-[#F62D51] font-medium">{currentPage}</span>
                  </>
                )}
              </div>

              <h1 className="text-[#1a2942] text-xl font-bold leading-tight mt-1">
                {title}
                <span className="ml-2 inline-block w-2 h-2 bg-[#F62D51] rounded-full align-middle"></span>
              </h1>

              {description && (
                <p className="text-gray-600 mt-1 max-w-2xl whitespace-nowrap overflow-hidden text-ellipsis">
                  {description}
                </p>
              )}
            </div>

            {/* Search section */}
            {onSearchChange && (
              <div className="hidden md:block">
                <div className="relative w-64">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder={searchPlaceholder}
                    className="block w-full pl-10 pr-4 py-1.5 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#1a2942] focus:border-transparent"
                    value={searchValue}
                    onChange={onSearchChange}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Mobile search - only visible on small screens */}
          {onSearchChange && (
            <div className="mt-3 md:hidden">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder={searchPlaceholder}
                  className="block w-full pl-10 pr-4 py-1.5 text-sm border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#1a2942] focus:border-transparent"
                  value={searchValue}
                  onChange={onSearchChange}
                />
              </div>
            </div>
          )}

          {/* Optional actions slot */}
          {actions && (
            <div className="mt-4 pt-3 border-t border-gray-100">
              {actions}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PageHeader;
