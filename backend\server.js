const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const fileUpload = require('express-fileupload');
require('dotenv').config();

// Add these lines for Socket.IO
const http = require('http');
const { Server } = require('socket.io');
const jwt = require('jsonwebtoken'); // Added for socket auth
const { instrument } = require("@socket.io/admin-ui"); // Added for Admin UI
const socketUtils = require('./utils/socket-io'); // Import our socket-io utility

console.log("[Server.js] Attempting to require auth routes from './routes/auth/auth-routes.js'...");
let authRoutes;
try {
  authRoutes = require('./routes/auth/auth-routes.js');
  if (typeof authRoutes !== 'function') {
    console.error("[Server.js] CRITICAL ERROR: authRoutes is NOT a function (expected Express Router). Check export in auth-routes.js");
  }
} catch (e) {
  console.error("[Server.js] CRITICAL ERROR: Failed to require auth routes:", e);
}

// Process routes
const businessProcessRoutes = require('./routes/processes/business-process-routes');
const organizationalProcessRoutes = require('./routes/processes/organizational-process-routes');
const operationRoutes = require('./routes/processes/operation-routes');

// Data routes
const entityRoutes = require('./routes/data/entity-routes');
const applicationRoutes = require('./routes/data/application-routes');
const controlRoutes = require('./routes/data/control-routes');
const controlTypeRoutes = require('./routes/data/control-type-routes');
const businessLineRoutes = require('./routes/data/business-line-routes');
const productRoutes = require('./routes/data/product-routes');
const controlQuestionRoutes = require('./routes/control-questions');
const controlQuestionAssignmentRoutes = require('./routes/control-question-assignments');
const controlMethodeExecutionRoutes = require('./routes/control-methode-execution');
const campagneRoutes = require('./routes/campagnes');
const campagneResponseRoutes = require('./routes/campagne-responses');

// Risk routes
const riskTypeRoutes = require('./routes/risks/risk-type-routes');
const riskRoutes = require('./routes/risks/risk-routes');
const workflowRoutes = require('./routes/risks/workflow-routes');
const riskContributorRoutes = require('./routes/risks/contributor-routes');

// Incident routes
const incidentTypeRoutes = require('./routes/incidents/incident-type-routes');
const incidentRoutes = require('./routes/incidents/incident-routes');
const incidentContributorRoutes = require('./routes/incidents/contributor-routes');

// Action Plan routes
const actionPlanRoutes = require('./routes/action-plans/action-plan-routes');

// Action routes
const actionRoutes = require('./routes/actions/action-routes');

// User routes
const userRoutesPublic = require('./routes/users/user-routes-public');
const userRoutes = require('./routes/users/user-routes');
const roleRoutes = require('./routes/users/role-routes');

// Activity routes
const activityRoutes = require('./routes/activities/activity-routes');

// Import routes for action plan attachments
const actionPlanAttachmentRoutes = require('./routes/action-plan-attachment-routes');

// Import rapport routes
const rapportRoutes = require('./routes/rapport');
const missionChartsRoutes = require('./routes/audit/rapport/mission-charts-routes');

// Upload routes
const uploadRoutes = require('./routes/uploads/upload-routes');
const riskUploadRoutes = require('./routes/uploads/risk-upload-routes');

// Reference routes
const referenceRoutes = require('./routes/reference-routes');

// Financial routes
const financialRoutes = require('./routes/financial/financial-routes');
const incidentFinancialRoutes = require('./routes/financial/incident-financial-routes');

// Import notification routes
const notificationRoutes = require('./routes/notifications/notification-routes');

// Import the report email routes
const reportEmailRoutes = require('./routes/reports/report-email-routes');

// Add this with the other route imports
const auditPlanRoutes = require('./routes/audit/audit-plan-routes');
const auditMissionRoutes = require('./routes/audit/audit-mission-routes');
const auditActivityRoutes = require('./routes/audit/audit-activity-routes');
const auditConstatRoutes = require('./routes/audit/audit-constat-routes');
const auditScopeRoutes = require('./routes/audit/audit-scope-routes');
const auditRecommendationRoutes = require('./routes/audit/audit-recommendation-routes');
const auditMissionRapportRoutes = require('./routes/audit/rapport/audit-mission-rapport-routes.js');
const auditFicheDeTravailRoutes = require('./routes/audit/audit-fiche-de-travail-routes');
const auditFicheDeTestRoutes = require('./routes/audit/audit-fiche-de-test-routes');
const auditTreeRoutes = require('./routes/audit/audit-tree-routes');
const auditFicheTestResponseRoutes = require('./routes/audit/fiche-test-response-routes');
const auditStatisticsRoutes = require('./routes/audit/audit-statistics-routes');

// Add this with the other route imports
const databaseExportRoutes = require('./routes/database-export-routes');
const databaseImportRoutes = require('./routes/database-import-routes');

// Import the new routes
const allAttachmentRoutes = require('./routes/uploads/all-attachment-routes');

// Import email routes
const emailRoutes = require('./routes/email-routes');

// Import Gemini routes
const geminiRoutes = require('./routes/gemini-routes');
const ttsRoutes = require('./routes/tts-routes');

// Create Express app
const app = express();

// Add CORS middleware first
app.use(cors({
  origin: 'http://localhost:5173', // Specifically allow the frontend origin
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type', 
    'Authorization', 
    'X-Requested-With', 
    'x-user-id',
    'Cache-Control',
    'Pragma'
  ]
}));

// Add middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));
app.use(cookieParser());
app.use(fileUpload({
    createParentPath: true,
    limits: { fileSize: 50 * 1024 * 1024 }, // 50MB max file size
    abortOnLimit: true
}));

// Add a specific CORS handler for email routes
app.use('/api/reports/email', (req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:5173');
  res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, x-user-id');
  res.header('Access-Control-Allow-Credentials', 'true');
  
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  
  next();
});

// Register the email routes
app.use('/api/reports/email', reportEmailRoutes);

// Add cookie debugging middleware
app.use((req, res, next) => {
  // Cookie Debug logs - commented out to reduce console noise
  // console.log(`[Cookie Debug] Request path: ${req.path}`);
  // console.log(`[Cookie Debug] Request cookies:`, req.cookies);
  
  const originalSetCookie = res.setHeader;
  res.setHeader = function(name, value) {
    if (name === 'Set-Cookie') {
      // console.log('[Cookie Debug] Setting cookie:', value);
    }
    return originalSetCookie.apply(this, arguments);
  };
  
  next();
});

// Add CORS debugging middleware
app.use((req, res, next) => {
  console.log(`[CORS Debug] ${req.method} ${req.path} - Origin: ${req.headers.origin || 'No Origin'}`);
  next();
});

// Routes
if (authRoutes && typeof authRoutes === 'function') {
  app.use('/api/auth', authRoutes);
} else {
  console.error("[Server.js] SKIPPING /api/auth route registration because authRoutes is invalid or failed to load.");
}

// Process routes
app.use('/api/businessProcesses', businessProcessRoutes);
app.use('/api/organizationalProcesses', organizationalProcessRoutes);
app.use('/api/operations', operationRoutes);

// Data routes
app.use('/api/entities', entityRoutes);
app.use('/api/applications', applicationRoutes);
app.use('/api/users-public', userRoutesPublic);
app.use('/api', controlQuestionRoutes);
app.use('/api/control-assignments', controlQuestionAssignmentRoutes);
app.use('/api', controlMethodeExecutionRoutes);
app.use('/api/controls', controlRoutes);
app.use('/api/controlTypes', controlTypeRoutes);
app.use('/api/businessLines', businessLineRoutes);
app.use('/api/products', productRoutes);
app.use('/api/campagnes', campagneRoutes);
app.use('/api', campagneResponseRoutes);

// Risk routes
app.use('/api/riskTypes', riskTypeRoutes);
app.use('/api/risk', riskRoutes);
app.use('/api/risks', workflowRoutes);
app.use('/api/risks', riskContributorRoutes);

// Incident routes
app.use('/api/incidentTypes', incidentTypeRoutes);
app.use('/api/incidents', incidentRoutes);
app.use('/api/incidents', incidentContributorRoutes);

// Action Plan routes
app.use('/api/actionPlans', actionPlanRoutes);
app.use('/api/actions', actionRoutes);

// User routes
app.use('/api/users', userRoutes);
app.use('/api/roles', roleRoutes);

// Activity routes
app.use('/api/activities', activityRoutes);

// Use rapport routes
app.use('/api', rapportRoutes);
app.use('/api/rapport', missionChartsRoutes);

// Upload routes
app.use('/api/uploads', uploadRoutes);
app.use('/api/risk-uploads', riskUploadRoutes);
// uload action plan attachments
app.use('/api/action-plan-attachments', actionPlanAttachmentRoutes);

// Reference routes
app.use('/api/references', referenceRoutes);

// Financial routes
app.use('/api/financial', financialRoutes);
app.use('/api/financial-v2', incidentFinancialRoutes);

// Use notification routes
app.use('/api/notifications', notificationRoutes);

// Add this with the other route registrations
app.use('/api/audit-plans', auditPlanRoutes);
app.use('/api/audit-missions', auditMissionRoutes);
app.use('/api/audit-activities', auditActivityRoutes);
app.use('/api/audit-constats', auditConstatRoutes);
app.use('/api/audit-scopes', auditScopeRoutes);
app.use('/api/audit-recommendations', auditRecommendationRoutes);
app.use('/api/audit-mission-rapports', auditMissionRapportRoutes);
app.use('/api/audit-fiches-de-travail', auditFicheDeTravailRoutes);
app.use('/api/audit/fiches-de-test', auditFicheDeTestRoutes);
app.use('/api/audit/fiches-de-travail', require('./routes/audit/audit-fiche-de-travail-routes'));
app.use('/api/audit-tree', auditTreeRoutes);
app.use('/api/audit/fiches-de-test-attachments', require('./routes/audit/attachment/fiche-de-test-Attachment-routes'));
app.use('/api/audit/fiches-de-test-responses', auditFicheTestResponseRoutes);
app.use('/api/audit-statistics', auditStatisticsRoutes);
app.use('/api/exports/database', databaseExportRoutes);
app.use('/api/imports/database', databaseImportRoutes);

// Add this with the other route definitions
app.use('/api/all-attachments', allAttachmentRoutes);

// Import email routes
app.use('/api/email', emailRoutes);

// Import Gemini routes
app.use('/api/gemini', geminiRoutes);
app.use('/api/tts', ttsRoutes);

const PORT = process.env.PORT || 5000;

// Sync ControlQuestion, ControlMethodeExecution, and CampagneResponse models to create tables if they don't exist
const db = require('./models');
if (db.ControlQuestion) {
  db.ControlQuestion.sync({ alter: true })
    .then(() => {
      console.log('[Database] ControlQuestion table synced successfully');
    })
    .catch(err => {
      console.error('[Database] Error syncing ControlQuestion table:', err);
    });
} else {
  console.log('[Database] ControlQuestion model not found in db object');
}

if (db.Campagne) {
  db.Campagne.sync({ alter: true })
    .then(() => {
      console.log('[Database] Campagne table synced successfully');
    })
    .catch(err => {
      console.error('[Database] Error syncing Campagne table:', err);
    });
} else {
  console.log('[Database] Campagne model not found in db object');
}

if (db.CampagneResponse) {
  db.CampagneResponse.sync({ alter: true })
    .then(() => {
      console.log('[Database] CampagneResponse table synced successfully');
    })
    .catch(err => {
      console.error('[Database] Error syncing CampagneResponse table:', err);
    });
} else {
  console.log('[Database] CampagneResponse model not found in db object');
}

if (db.AllAttachment) {
  db.AllAttachment.sync({ alter: true })
    .then(() => {
      console.log('[Database] AllAttachment table synced successfully');
    })
    .catch(err => {
      console.error('[Database] Error syncing AllAttachment table:', err);
    });
} else {
  console.log('[Database] AllAttachment model not found in db object');
}

if (db.ControlMethodeExecution) {
  db.ControlMethodeExecution.sync({ alter: true })
    .then(() => {
      console.log('[Database] ControlMethodeExecution table synced successfully');
    })
    .catch(err => {
      console.error('[Database] Error syncing ControlMethodeExecution table:', err);
    });
} else {
  console.log('[Database] ControlMethodeExecution model not found in db object');
}

if (db.ControlQuestionAssignment) {
  db.ControlQuestionAssignment.sync({ alter: true })
    .then(() => {
      console.log('[Database] ControlQuestionAssignment table synced successfully');
    })
    .catch(err => {
      console.error('[Database] Error syncing ControlQuestionAssignment table:', err);
    });
} else {
  console.log('[Database] ControlQuestionAssignment model not found in db object');
}

if (!db.ControlQuestion && !db.ControlMethodeExecution) {
  console.log('[Database] Available models:', Object.keys(db));
}

// Create HTTP server and integrate Socket.IO
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: function(origin, callback) {
      // For development, allow all origins
      if (process.env.NODE_ENV === 'development') {
        return callback(null, true);
      }
      
      const allowedOrigins = [
        'http://localhost:5173',
        'http://localhost:5001',
        'http://*************:5173'
      ];
      
      if (!origin) return callback(null, true);
      
      if (allowedOrigins.indexOf(origin) !== -1 || origin.match(/^http:\/\/192\.168\.\d+\.\d+:5173$/)) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'x-user-id']
  }
});

// Register the Socket.IO instance with our utility
socketUtils.setIo(io);

// Socket.IO Admin UI
instrument(io, {
  auth: false, // Disable auth for admin UI for local development ease, secure if exposed publicly
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',
});

// Socket.IO Authentication Middleware
io.use((socket, next) => {
  let token = socket.handshake.auth.token || socket.handshake.headers.authorization;
  
  console.log('[Socket.IO Auth] Received connection attempt with auth:', 
    token ? (typeof token === 'string' ? `${token.substring(0, 15)}...` : 'Invalid token format') : 'No token');
  
  // Check if token has Bearer prefix and remove it
  if (token && token.startsWith('Bearer ')) {
    token = token.slice(7); // Remove 'Bearer ' prefix
    console.log('[Socket.IO Auth] Removed Bearer prefix from token');
  }

  if (!token) {
    console.log('[Socket.IO Auth] No token provided');
    return next(new Error('Authentication error: No token provided'));
  }

  console.log('[Socket.IO Auth] Attempting to verify token');
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    socket.user = decoded; // Attach user information to the socket
    
    if (!socket.user || !socket.user.userId) {
      console.log('[Socket.IO Auth] Token decoded but userId missing', JSON.stringify(socket.user));
      return next(new Error('Authentication error: Invalid token payload'));
    }
    
    console.log('[Socket.IO Auth] Successfully authenticated user:', socket.user.userId);
    next();
  } catch (error) {
    console.error('[Socket.IO Auth] Token verification error:', error.message);
    if (error.name === 'TokenExpiredError') {
      return next(new Error('Authentication error: Token expired'));
    }
    if (error.name === 'JsonWebTokenError') {
      return next(new Error(`Authentication error: ${error.message}`));
    }
    return next(new Error('Authentication error: Invalid token'));
  }
});

io.on('connection', (socket) => {
  // This runs *after* the io.use() middleware
  if (!socket.user || !socket.user.userId) {
    console.log('[Socket.IO Connection] User not authenticated or userId missing on socket object. Disconnecting.');
    socket.disconnect(true); // Force disconnect if auth middleware somehow passed without userId
    return;
  }

  const userId = socket.user.userId.toString();
  console.log(`[Socket.IO Connection] User connected: ${socket.id}, userId: ${userId}`);

  // Join a room specific to the user
  socket.join(`user-${userId}`);
  console.log(`[Socket.IO Connection] Socket ${socket.id} joined room user-${userId}`);

  // Send a welcome event to confirm connection success
  socket.emit('welcome', { 
    message: 'Successfully connected to Vitalis notification system',
    userId: userId,
    socketId: socket.id
  });

  socket.on('disconnect', () => {
    console.log(`[Socket.IO Connection] User disconnected: ${socket.id}, userId: ${userId}`);
  });

  // Handle errors
  socket.on('error', (error) => {
    console.error(`[Socket.IO Connection] Socket error for user ${userId}:`, error);
  });
});

server.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
    console.log(`Socket.IO is listening on port ${PORT}`);
});

module.exports = { app }; // No longer export io directly







