import { getApiBaseUrl } from '@/utils/api-config';

const API_BASE_URL = getApiBaseUrl();

// Get all questions for a specific control
export const getQuestionsByControlId = async (controlId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/controls/${controlId}/questions`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching control questions:', error);
    throw error;
  }
};

// Create a new question for a control
export const createControlQuestion = async (controlId, questionData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/controls/${controlId}/questions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(questionData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error creating control question:', error);
    throw error;
  }
};

// Update a question
export const updateControlQuestion = async (questionId, questionData) => {
  try {
    const response = await fetch(`${API_BASE_URL}/controls/questions/${questionId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(questionData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error updating control question:', error);
    throw error;
  }
};

// Delete a question
export const deleteControlQuestion = async (questionId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/controls/questions/${questionId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error deleting control question:', error);
    throw error;
  }
};

// Reorder questions for a control
export const reorderControlQuestions = async (controlId, questionIds) => {
  try {
    const response = await fetch(`${API_BASE_URL}/controls/${controlId}/questions/reorder`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ questionIds })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error reordering control questions:', error);
    throw error;
  }
};
