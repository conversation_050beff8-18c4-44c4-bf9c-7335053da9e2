const db = require('../../models');
const { User, Role } = db;
const AuditPlan = db.AuditPlan;
const { v4: uuidv4 } = require('uuid');

// Get all audit plans - OPTIMIZED VERSION
const getAllAuditPlans = async (req, res) => {
  try {
    console.log('AuditPlan model:', AuditPlan); // Debug log

    if (!AuditPlan) {
      console.error('AuditPlan model is undefined');
      return res.status(500).json({
        success: false,
        message: 'AuditPlan model is not defined'
      });
    }

    // Add pagination support
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    // Check if this is for statistics only (welcome page)
    const statsOnly = req.query.statsOnly === 'true';

    if (statsOnly) {
      // For statistics, only return counts grouped by status
      const statsQuery = `
        SELECT
          status,
          COUNT(*) as count
        FROM "AuditPlans"
        GROUP BY status
      `;

      const stats = await db.sequelize.query(statsQuery, {
        type: db.sequelize.QueryTypes.SELECT
      });

      return res.status(200).json({
        success: true,
        data: stats,
        isStats: true
      });
    }

    // Optimized query with minimal includes and specific attributes
    const auditPlans = await AuditPlan.findAndCountAll({
      attributes: [
        'id', 'name', 'description', 'status', 'datedebut',
        'datefin', 'avancement', 'calendrier', 'directeuraudit',
        'createdAt', 'updatedAt'
      ],
      include: [
        {
          model: User,
          as: 'director',
          attributes: ['id', 'username', 'email'],
          required: false // LEFT JOIN instead of INNER JOIN
        }
      ],
      limit,
      offset,
      order: [['createdAt', 'DESC']] // Add ordering for consistent results
    });

    return res.status(200).json({
      success: true,
      data: auditPlans.rows,
      pagination: {
        total: auditPlans.count,
        page,
        limit,
        totalPages: Math.ceil(auditPlans.count / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching audit plans:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit plans',
      error: error.message
    });
  }
};

// Create a new audit plan
const createAuditPlan = async (req, res) => {
  try {
    const { name, status, datedebut, datefin, description, avancement, calendrier } = req.body;
    
    // Get the user ID from the authenticated user in the request
    const userId = req.user.id || req.user.userId;
    
    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID not found in token'
      });
    }

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Name is required for audit plan'
      });
    }

    // Validate dates if both are provided
    if (datedebut && datefin && new Date(datefin) <= new Date(datedebut)) {
      return res.status(400).json({
        success: false,
        message: 'Date de fin doit être après la date de début'
      });
    }

    // Validate calendrier if provided
    if (calendrier !== undefined && calendrier !== null) {
      if (!Number.isInteger(Number(calendrier)) || calendrier < 1900 || calendrier > 2100) {
        return res.status(400).json({
          success: false,
          message: 'Calendrier must be a valid year between 2000 and 2100'
        });
      }
    }

    // Create the audit plan
    const auditPlan = await AuditPlan.create({
      id: `AP_${uuidv4().substring(0, 8)}`, // Generate a unique ID with prefix
      name,
      description: description || null,
      status: status || 'Planned',
      datedebut: datedebut || null,
      datefin: datefin || null,
      avancement: avancement || null,
      calendrier: calendrier || null,
      directeuraudit: userId
    });

    return res.status(201).json({
      success: true,
      message: 'Audit plan created successfully',
      data: auditPlan
    });
  } catch (error) {
    console.error('Error creating audit plan:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create audit plan',
      error: error.message
    });
  }
};

// Get audit plan by ID
const getAuditPlanById = async (req, res) => {
  try {
    const { id } = req.params;

    // Optimized query with specific attributes
    const auditPlan = await AuditPlan.findByPk(id, {
      attributes: [
        'id', 'name', 'description', 'status', 'datedebut',
        'datefin', 'avancement', 'calendrier', 'directeuraudit',
        'createdAt', 'updatedAt'
      ],
      include: [
        {
          model: User,
          as: 'director',
          attributes: ['id', 'username', 'email'],
          required: false
        }
      ]
    });

    if (!auditPlan) {
      return res.status(404).json({
        success: false,
        message: 'Audit plan not found'
      });
    }

    return res.status(200).json({
      success: true,
      data: auditPlan
    });
  } catch (error) {
    console.error('Error fetching audit plan:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit plan',
      error: error.message
    });
  }
};

// Update audit plan
const updateAuditPlan = async (req, res) => {
  try {
    const { id } = req.params;
    let { name, status, datedebut, datefin, description, avancement, calendrier } = req.body;
    
    const auditPlan = await AuditPlan.findByPk(id);
    
    if (!auditPlan) {
      return res.status(404).json({
        success: false,
        message: 'Audit plan not found'
      });
    }

    // Helper to check valid date
    const isValidDate = (d) => d && !isNaN(new Date(d).getTime()) && d !== 'Invalid date';

    // Clean up dates: if invalid or empty, set to null
    datedebut = isValidDate(datedebut) ? datedebut : null;
    datefin = isValidDate(datefin) ? datefin : null;

    // Validate dates if both are provided
    if (datedebut && datefin && new Date(datefin) <= new Date(datedebut)) {
      return res.status(400).json({
        success: false,
        message: 'Date de fin doit être après la date de début'
      });
    }

    // Validate calendrier if provided
    if (calendrier !== undefined && calendrier !== null) {
      if (!Number.isInteger(Number(calendrier)) || calendrier < 1900 || calendrier > 2100) {
        return res.status(400).json({
          success: false,
          message: 'Calendrier must be a valid year between 1900 and 2100'
        });
      }
    }
    
    // Update the audit plan
    await auditPlan.update({
      name: name || auditPlan.name,
      description: description !== undefined ? description : auditPlan.description,
      status: status || auditPlan.status,
      datedebut: datedebut || auditPlan.datedebut,
      datefin: datefin !== undefined ? datefin : auditPlan.datefin,
      avancement: avancement || auditPlan.avancement,
      calendrier: calendrier !== undefined ? calendrier : auditPlan.calendrier
    });
    
    return res.status(200).json({
      success: true,
      message: 'Audit plan updated successfully',
      data: auditPlan
    });
  } catch (error) {
    console.error('Error updating audit plan:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update audit plan',
      error: error.message
    });
  }
};

// Delete audit plan
const deleteAuditPlan = async (req, res) => {
  try {
    const { id } = req.params;
    
    const auditPlan = await AuditPlan.findByPk(id);
    
    if (!auditPlan) {
      return res.status(404).json({
        success: false,
        message: 'Audit plan not found'
      });
    }
    
    await auditPlan.destroy();
    
    return res.status(200).json({
      success: true,
      message: 'Audit plan deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting audit plan:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete audit plan',
      error: error.message
    });
  }
};

module.exports = {
  getAllAuditPlans,
  createAuditPlan,
  getAuditPlanById,
  updateAuditPlan,
  deleteAuditPlan
};



