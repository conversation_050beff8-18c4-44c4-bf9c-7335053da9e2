module.exports = (sequelize, DataTypes) => {
  const IncidentEvent = sequelize.define('IncidentEvent', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    incident_id: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Incident',
        key: 'incidentID',
      },
    },
    timestamp: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    step: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    user: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    transition: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  }, {
    tableName: 'IncidentEvents',
    freezeTableName: true,
    timestamps: false,
  });

  IncidentEvent.associate = (models) => {
    IncidentEvent.belongsTo(models.Incident, {
      foreignKey: 'incident_id',
      targetKey: 'incidentID',
      as: 'incident',
    });
  };

  return IncidentEvent;
}; 