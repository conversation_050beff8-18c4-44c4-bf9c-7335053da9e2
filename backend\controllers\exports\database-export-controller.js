const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');
const db = require('../../models');
const { Sequelize } = require('sequelize');

// Create exports directory if it doesn't exist
const exportsDir = path.join(__dirname, '../../exports');
if (!fs.existsSync(exportsDir)) {
  fs.mkdirSync(exportsDir);
}

// Get list of all tables in the database
const getTablesList = async (req, res) => {
  try {
    const [results] = await db.sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `);

    const tables = results.map(row => row.table_name);
    
    res.status(200).json({
      success: true,
      data: tables
    });
  } catch (error) {
    console.error('Error fetching tables list:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch tables list',
      error: error.message
    });
  }
};

// Export a single table to Excel
const exportTableToExcel = async (req, res) => {
  try {
    const { tableName } = req.params;
    
    // Validate table name to prevent SQL injection
    const [tableCheck] = await db.sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = :tableName
    `, {
      replacements: { tableName },
      type: Sequelize.QueryTypes.SELECT
    });
    
    if (!tableCheck) {
      return res.status(404).json({
        success: false,
        message: `Table "${tableName}" not found`
      });
    }
    
    // Get table data
    const [rows] = await db.sequelize.query(`SELECT * FROM "${tableName}"`);
    
    if (rows.length === 0) {
      return res.status(200).json({
        success: false,
        message: `Table "${tableName}" is empty`
      });
    }
    
    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(rows);
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, tableName);
    
    // Generate filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${tableName}_${timestamp}.xlsx`;
    const filePath = path.join(exportsDir, filename);
    
    // Write to file
    XLSX.writeFile(workbook, filePath);
    
    // Send file to client
    res.download(filePath, filename, (err) => {
      if (err) {
        console.error('Error sending file:', err);
      }
      
      // Delete file after sending
      fs.unlink(filePath, (unlinkErr) => {
        if (unlinkErr) {
          console.error('Error deleting temporary file:', unlinkErr);
        }
      });
    });
  } catch (error) {
    console.error('Error exporting table:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export table',
      error: error.message
    });
  }
};

// Export multiple tables to Excel (one workbook with multiple sheets)
const exportMultipleTablesToExcel = async (req, res) => {
  try {
    const { tables } = req.body;
    
    if (!tables || !Array.isArray(tables) || tables.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Please provide an array of table names'
      });
    }
    
    // Create workbook
    const workbook = XLSX.utils.book_new();
    
    // Process each table
    for (const tableName of tables) {
      try {
        // Validate table name to prevent SQL injection
        const [tableCheck] = await db.sequelize.query(`
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public' AND table_name = :tableName
        `, {
          replacements: { tableName },
          type: Sequelize.QueryTypes.SELECT
        });
        
        if (!tableCheck) {
          console.warn(`Table "${tableName}" not found, skipping`);
          continue;
        }
        
        // Get table data
        const [rows] = await db.sequelize.query(`SELECT * FROM "${tableName}"`);
        
        if (rows.length === 0) {
          console.warn(`Table "${tableName}" is empty, adding empty sheet`);
          const emptyWorksheet = XLSX.utils.json_to_sheet([]);
          XLSX.utils.book_append_sheet(workbook, emptyWorksheet, tableName);
          continue;
        }
        
        // Create worksheet
        const worksheet = XLSX.utils.json_to_sheet(rows);
        
        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(workbook, worksheet, tableName);
      } catch (tableError) {
        console.error(`Error processing table "${tableName}":`, tableError);
        // Continue with other tables
      }
    }
    
    // Check if any sheets were added
    if (workbook.SheetNames.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No valid tables were found'
      });
    }
    
    // Generate filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `database_export_${timestamp}.xlsx`;
    const filePath = path.join(exportsDir, filename);
    
    // Write to file
    XLSX.writeFile(workbook, filePath);
    
    // Send file to client
    res.download(filePath, filename, (err) => {
      if (err) {
        console.error('Error sending file:', err);
      }
      
      // Delete file after sending
      fs.unlink(filePath, (unlinkErr) => {
        if (unlinkErr) {
          console.error('Error deleting temporary file:', unlinkErr);
        }
      });
    });
  } catch (error) {
    console.error('Error exporting multiple tables:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export tables',
      error: error.message
    });
  }
};

// Export entire database to Excel (one workbook with multiple sheets)
const exportEntireDatabase = async (req, res) => {
  try {
    // Get all table names
    const [tables] = await db.sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `);
    
    if (tables.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No tables found in database'
      });
    }
    
    // Create workbook
    const workbook = XLSX.utils.book_new();
    
    // Process each table
    for (const table of tables) {
      const tableName = table.table_name;
      
      try {
        // Get table data
        const [rows] = await db.sequelize.query(`SELECT * FROM "${tableName}"`);
        
        // Create worksheet (even if empty)
        const worksheet = rows.length > 0 
          ? XLSX.utils.json_to_sheet(rows)
          : XLSX.utils.json_to_sheet([]);
        
        // Ensure sheet name is valid (max 31 chars, no special chars)
        let sheetName = tableName.substring(0, 31);
        
        // Add worksheet to workbook
        try {
          XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
        } catch (sheetError) {
          // If there's an error with the sheet name, use a generic name
          console.warn(`Error adding sheet "${sheetName}", using generic name:`, sheetError);
          XLSX.utils.book_append_sheet(workbook, worksheet, `Table_${workbook.SheetNames.length + 1}`);
        }
      } catch (tableError) {
        console.error(`Error processing table "${tableName}":`, tableError);
        // Continue with other tables
      }
    }
    
    // Check if any sheets were added
    if (workbook.SheetNames.length === 0) {
      return res.status(500).json({
        success: false,
        message: 'Failed to export any tables'
      });
    }
    
    // Generate filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `full_database_export_${timestamp}.xlsx`;
    const filePath = path.join(exportsDir, filename);
    
    // Write to file
    XLSX.writeFile(workbook, filePath);
    
    // Send file to client
    res.download(filePath, filename, (err) => {
      if (err) {
        console.error('Error sending file:', err);
      }
      
      // Delete file after sending
      fs.unlink(filePath, (unlinkErr) => {
        if (unlinkErr) {
          console.error('Error deleting temporary file:', unlinkErr);
        }
      });
    });
  } catch (error) {
    console.error('Error exporting database:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export database',
      error: error.message
    });
  }
};

module.exports = {
  getTablesList,
  exportTableToExcel,
  exportMultipleTablesToExcel,
  exportEntireDatabase
};