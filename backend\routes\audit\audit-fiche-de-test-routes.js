const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllFicheDeTest,
  getFicheDeTestById,
  getFicheDeTestByFicheDeTravailId,
  createFicheDeTest,
  updateFicheDeTest,
  deleteFicheDeTest
} = require('../../controllers/audit/audit-fiche-de-test-controller');

router.use(verifyToken);

// General routes
router.get('/', authorizeRoles(['audit_director', 'auditor']), getAllFicheDeTest);
router.post('/', authorizeRoles(['audit_director', 'auditor']), createFicheDeTest);

// Get fiches de test by fiche de travail ID
router.get('/ficheDeTravail/:ficheDeTravailId', authorizeRoles(['audit_director', 'auditor']), getFicheDeTestByFicheDeTravailId);

// ID specific routes
router.get('/:id', authorizeRoles(['audit_director', 'auditor']), getFicheDeTestById);
router.put('/:id', authorizeRoles(['audit_director', 'auditor']), updateFicheDeTest);
router.delete('/:id', authorizeRoles(['audit_director', 'auditor']), deleteFicheDeTest);

// NOTE: The controller now supports 'answers' in create/update requests and responses.

module.exports = router;