const sequelize = require('./config/database');

async function testConnection() {
  try {
    console.log('Tentative de connexion à la base de données...');
    await sequelize.authenticate();
    console.log('Connexion réussie !');
  } catch (error) {
    console.error('Erreur de connexion:', error.message);
    console.error('Details:', {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      database: process.env.DB_NAME,
      user: process.env.DB_USER
    });
  } finally {
    await sequelize.close();
  }
}

testConnection();
