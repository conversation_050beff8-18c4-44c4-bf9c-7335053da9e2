// backend/models/Role.js
module.exports = (sequelize, DataTypes) => {
  const Role = sequelize.define('Role', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    // Internal code for system use (e.g., 'grc_admin', 'grc_manager')
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    }
  }, {
    tableName: 'Roles',
    freezeTableName: true,
    timestamps: true
  });

  Role.associate = (models) => {
    // Role has many Users through UserRole
    Role.belongsToMany(models.User, {
      through: 'UserRole',
      foreignKey: 'roleId',
      otherKey: 'userId',
      as: 'users'
    });

    // Direct association with UserRole
    Role.hasMany(models.UserRole, {
      foreignKey: 'roleId',
      as: 'userRoles'
    });
  };

  return Role;
};
