const { BusinessLine } = require('../models');

const businessLines = [
  // Corporate & Investment Banking
  {
    businessLineID: 'BL_CIB_001',
    name: 'Corporate Banking',
    description: 'Financial services for large corporations and institutions'
  },
  {
    businessLineID: 'BL_CIB_002',
    name: 'Investment Banking',
    description: 'Advisory services, capital raising, and securities underwriting'
  },
  {
    businessLineID: 'BL_CIB_003',
    name: 'Global Markets',
    description: 'Trading and sales of financial instruments'
  },

  // Retail Banking
  {
    businessLineID: 'BL_RET_001',
    name: 'Personal Banking',
    description: 'Banking services for individual customers'
  },
  {
    businessLineID: 'BL_RET_002',
    name: 'Small Business Banking',
    description: 'Financial services for small and medium enterprises'
  },
  {
    businessLineID: 'BL_RET_003',
    name: 'Consumer Lending',
    description: 'Personal loans, mortgages, and credit products'
  },

  // Asset Management
  {
    businessLineID: 'BL_AM_001',
    name: 'Wealth Management',
    description: 'Investment management for high-net-worth individuals'
  },
  {
    businessLineID: 'BL_AM_002',
    name: 'Asset Management',
    description: 'Investment management for institutional clients'
  },
  {
    businessLineID: 'BL_AM_003',
    name: 'Private Banking',
    description: 'Exclusive banking services for wealthy individuals'
  },

  // Insurance
  {
    businessLineID: 'BL_INS_001',
    name: 'Life Insurance',
    description: 'Life insurance products and services'
  },
  {
    businessLineID: 'BL_INS_002',
    name: 'Property & Casualty',
    description: 'Insurance for property and liability risks'
  },
  {
    businessLineID: 'BL_INS_003',
    name: 'Reinsurance',
    description: 'Insurance for insurance companies'
  },

  // Support Functions
  {
    businessLineID: 'BL_SUP_001',
    name: 'Information Technology',
    description: 'IT infrastructure and digital solutions'
  },
  {
    businessLineID: 'BL_SUP_002',
    name: 'Operations',
    description: 'Back-office operations and processing'
  },
  {
    businessLineID: 'BL_SUP_003',
    name: 'Risk Management',
    description: 'Risk assessment and control'
  },
  {
    businessLineID: 'BL_SUP_004',
    name: 'Human Resources',
    description: 'Personnel management and development'
  },
  {
    businessLineID: 'BL_SUP_005',
    name: 'Finance',
    description: 'Financial planning and accounting'
  },
  {
    businessLineID: 'BL_SUP_006',
    name: 'Legal & Compliance',
    description: 'Legal services and regulatory compliance'
  },

  // Digital Banking
  {
    businessLineID: 'BL_DIG_001',
    name: 'Digital Banking',
    description: 'Online and mobile banking services'
  },
  {
    businessLineID: 'BL_DIG_002',
    name: 'Fintech Solutions',
    description: 'Financial technology products and services'
  },

  // Treasury
  {
    businessLineID: 'BL_TRS_001',
    name: 'Treasury Services',
    description: 'Cash management and liquidity solutions'
  },
  {
    businessLineID: 'BL_TRS_002',
    name: 'Capital Markets',
    description: 'Securities and derivatives trading'
  }
];

async function seedBusinessLines() {
  try {
    // Clear existing records
    await BusinessLine.destroy({ where: {} });
    
    // Insert new records
    await BusinessLine.bulkCreate(businessLines);
    
    console.log('Successfully seeded business lines');
  } catch (error) {
    console.error('Error seeding business lines:', error);
  }
}

// Run the seeding function
seedBusinessLines();

module.exports = seedBusinessLines;

