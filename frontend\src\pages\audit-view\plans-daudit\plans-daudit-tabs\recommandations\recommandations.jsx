import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { ListChecks, Search, Eye, Edit, Trash2, Plus, Loader2, AlertCircle } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import TablePagination from "@/components/ui/table-pagination";
import { toast } from "sonner";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";
import { useApiRequest, withApiErrorHandling } from '@/hooks/useApiRequest';
import userService from "@/services/userService";
import recommandationIcon from '@/assets/recommandation.png';

function RecommandationsTab({ auditPlan }) {
  const navigate = useNavigate();
  const { makeRequest, cancelAllRequests } = useApiRequest();

  const [recommendations, setRecommendations] = useState([]);
  const [allUsers, setAllUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Fetch all users from the system first using user service
  useEffect(() => {
    const fetchAllUsers = async () => {
      try {
        const users = await userService.fetchUsers({ silent: true });
        setAllUsers(users);
      } catch (error) {
        console.warn("Could not fetch users:", error.message);
        setAllUsers([]);
      }
    };

    fetchAllUsers();

    // Cleanup on unmount
    return () => {
      cancelAllRequests();
    };
  }, [cancelAllRequests]);

  // Fetch recommendations for this specific plan
  useEffect(() => {
    const fetchRecommendations = withApiErrorHandling(async () => {
      if (!auditPlan?.id) return;

      setLoading(true);
      const response = await makeRequest({
        method: 'get',
        url: `${getApiBaseUrl()}/audit-recommendations/audit-plan/${auditPlan.id}`,
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      }, {
        retries: 2,
        onError: (error) => {
          if (!axios.isCancel(error)) {
            console.error("Error fetching recommendations:", error);
            if (error.response?.status !== 404) {
              toast.error("Erreur lors du chargement des recommandations");
            }
          }
        }
      });

      if (response && response.data.success && response.data.data) {
        const recommendationsData = response.data.data;

        // Map recommendations with user data using user service
        const recommendationsWithResponsable = recommendationsData.map((recommendation, index) => {
          if (recommendation.responsableId) {
            // Use user service to get user details with fallback
            const user = userService.getUserById(recommendation.responsableId);
            return {
              ...recommendation,
              responsable: user,
              // Add unique identifier to prevent duplicate keys
              uniqueId: `rec-${recommendation.id}-${recommendation.constatId}-${index}`
            };
          }
          return {
            ...recommendation,
            uniqueId: `rec-${recommendation.id}-${recommendation.constatId}-${index}`
          };
        });

        setRecommendations(recommendationsWithResponsable);
      } else {
        setRecommendations([]);
      }
      setLoading(false);
    }, {
      fallbackValue: null,
      autoRefresh: true, // Auto refresh on critical errors
      refreshDelay: 5000,
      onError: (error) => {
        setLoading(false);
        setRecommendations([]);
        if (!axios.isCancel(error)) {
          console.error("Critical error fetching recommendations:", error);
        }
      }
    });

    if (auditPlan?.id && allUsers.length >= 0) {
      fetchRecommendations();
    }

    // Cleanup on unmount or dependency change
    return () => {
      cancelAllRequests();
    };
  }, [auditPlan?.id, allUsers, makeRequest, cancelAllRequests]);

  // Filter recommendations based on search query
  const filteredRecommendations = recommendations.filter(rec =>
    rec.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    rec.constatName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    rec.activityName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    rec.missionName?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Pagination
  const totalPages = Math.ceil(filteredRecommendations.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, filteredRecommendations.length);
  const currentRecommendations = filteredRecommendations.slice(startIndex, endIndex);

  const handleRowClick = (recommendation) => {
    navigate(`/audit/plans-daudit/${auditPlan.id}/missions-audits/${recommendation.missionId}/activites/${recommendation.activityId}/constats/${recommendation.constatId}/recommandations/${recommendation.id}`);
  };

  if (!auditPlan) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
          <p className="text-gray-500">Chargement des informations du plan d'audit...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <img src={recommandationIcon} alt="Recommandation" className="h-6 w-6 mr-2 flex-shrink-0" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Recommandations</h2>
            <p className="text-sm text-gray-600">
              Plan d'audit: <span className="font-medium">{auditPlan.name}</span>
            </p>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Rechercher par recommandation, constat, activité ou mission..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Table */}
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table className="min-w-full">
              <TableHeader>
                <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recommandation</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Responsable</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Constat</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activité</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mission</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan d'audit</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="divide-y divide-gray-200">
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="px-4 py-10 text-center">
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F62D51]"></div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : currentRecommendations.length > 0 ? (
                  currentRecommendations.map((recommendation, index) => (
                    <TableRow
                      key={recommendation.uniqueId || `rec-${recommendation.id}-${recommendation.constatId}-${index}-${Math.random()}`}
                      className="hover:bg-gray-50/50 cursor-pointer"
                      onClick={() => handleRowClick(recommendation)}
                    >
                      <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                        <div className="flex items-center">
                          <img src={recommandationIcon} alt="Recommandation" className="h-4 w-4 mr-2 flex-shrink-0" />
                          <span>{recommendation.name || 'Sans nom'}</span>
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                        {recommendation.responsable ? (
                          <div className="flex items-center">
                            <div className="h-6 w-6 rounded-full bg-[#F62D51] text-white text-xs flex items-center justify-center mr-2">
                              {recommendation.responsable.username ? recommendation.responsable.username.charAt(0).toUpperCase() : 'U'}
                            </div>
                            <span className={recommendation.responsable.username?.startsWith('Utilisateur') ? 'text-gray-500 italic' : ''}>
                              {recommendation.responsable.username || recommendation.responsable.email || 'Utilisateur inconnu'}
                            </span>
                          </div>
                        ) : recommendation.responsableId ? (
                          <div className="flex items-center">
                            <div className="h-6 w-6 rounded-full bg-gray-400 text-white text-xs flex items-center justify-center mr-2">
                              U
                            </div>
                            <span className="text-gray-500 italic">Utilisateur {recommendation.responsableId}</span>
                          </div>
                        ) : (
                          <span className="text-gray-400">Non assigné</span>
                        )}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{recommendation.constatName || '-'}</TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{recommendation.activityName || '-'}</TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{recommendation.missionName || '-'}</TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{recommendation.planName || auditPlan.name}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="px-4 py-10 text-center text-sm text-gray-500">
                      Aucune recommandation trouvée.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <TablePagination
        totalPages={totalPages}
        currentPage={currentPage}
        onPageChange={setCurrentPage}
        hasItems={filteredRecommendations.length > 0}
        totalItems={filteredRecommendations.length}
        itemsPerPage={itemsPerPage}
        startIndex={startIndex}
        endIndex={endIndex}
      />
    </div>
  );
}

export default RecommandationsTab;
