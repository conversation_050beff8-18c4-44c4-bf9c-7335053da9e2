#### Objective
Enhance the application by adding dedicated functionality for the "Audit Director" and "Auditor" roles. This includes creating a separate sidebar, content sections, and routing, ensuring that only users with these roles can access the new audit features. The implementation should align with the existing admin interface for consistency.

#### Background
The application currently supports the following 10 roles:  
- GRC Administrator  
- GRC Manager  
- Risk Manager  
- GRC Contributor  
- Incident Manager  
- Internal Controller  
- Compliance Manager  
- Audit Director  
- Auditor  

The focus of this task is to provide a tailored interface for the "Audit Director" and "Auditor" roles, distinct from the existing functionality for other roles.

#### Requirements
1. Roles and Access Control  
   - Restrict access to the new audit features to only "Audit Director" and "Auditor" roles.  
   - Leverage the existing `Users`, `Roles`, and `UserRoles` tables to manage role-based permissions.  

2. Audit Sidebar  
   - Create a new sidebar for the "Audit Director" and "Auditor" roles, modeled after the existing admin sidebar.  
   - Include an "Audit" section within this sidebar.  
   - Add a subsection under "Audit" called "Plans d'audit" that links to a dedicated page.  

3. File Structure  
   - Use the three files in `frontend\src\components\admin-view` as a reference for structure and styling.  
   - Create a new folder `frontend\src\components\audit-view` to store audit-related components (e.g., the audit sidebar).  
   - Create a new folder `frontend\src\pages\audit-view` to store audit-related pages.  
   - Place a `.jsx` file for the "Plans d'audit" page in `frontend\src\pages\audit-view`.  

4. Routing  
   - Update `app.js` to implement role-based routing and access control.  
   - Define the base path for audit-related routes as `/audit/...` (e.g., `/audit/plans-daudit`).  
   - Ensure clicking "Plans d'audit" in the sidebar navigates to the corresponding page in `frontend\src\pages\audit-view`.  

5. Functionality  
   - The audit sidebar and "Plans d'audit" page should only be accessible to users with the "Audit Director" or "Auditor" roles.  
   - Maintain separation from the existing interfaces for other roles (e.g., admin sidebar).  

#### Implementation Steps
1. Audit Sidebar Development  
   - Create a new sidebar component in `frontend\src\components\audit-view`, using the admin sidebar files in `frontend\src\components\admin-view` as a template.  
   - Add an "Audit" section to the sidebar.  
   - Include a "Plans d'audit" subsection with a navigation link to `/audit/plans-daudit`.  

2. Audit Page Development  
   - Create a new `.jsx` file for the "Plans d'audit" page in `frontend\src\pages\audit-view`.  
   - Design the page to load when the `/audit/plans-daudit` route is accessed.  

3. Role-Based Access Control in `app.js`  
   - Modify `app.js` to query the user's role from the `UserRoles` table.  
   - Conditionally render the audit sidebar only for users with "Audit Director" or "Auditor" roles.  
   - Restrict access to routes starting with `/audit/...` to these roles, redirecting unauthorized users as needed.  

4. Integration and Testing  
   - Integrate the new sidebar and page into the application’s main flow.  
   - Test navigation from the sidebar to the "Plans d'audit" page.  
   - Verify that only "Audit Director" and "Auditor" roles can see the audit sidebar and access `/audit/...` routes.  

#### Additional Considerations
- Ensure the audit sidebar’s design and behavior (e.g., styling, interactivity) match the admin sidebar for a consistent user experience.  
- Avoid conflicts with existing role-based functionality for the other eight roles.  
- Use placeholder content in the "Plans d'audit" page initially, allowing for future expansion.  

#### Deliverables
- A new audit sidebar component in `frontend\src\components\audit-view`.  
- A "Plans d'audit" page in `frontend\src\pages\audit-view` as a `.jsx` file.  
- Updated `app.js` with role-based routing and access control for audit features.  

#### Suggested Next Steps
- Start by copying the admin sidebar structure from `frontend\src\components\admin-view` to create the audit sidebar in `frontend\src\components\audit-view`.  
- Build a basic "Plans d'audit" page in `frontend\src\pages\audit-view` with a simple layout.  
- Implement role checks in `app.js` to secure the audit features. 