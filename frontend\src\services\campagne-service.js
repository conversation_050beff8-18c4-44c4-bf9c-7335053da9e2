import axios from 'axios';
import { getApiBaseUrl } from '../utils/api-config';

const API_BASE_URL = getApiBaseUrl();

// Get all campagnes (filtered by user access)
export const getAllCampagnes = async (params = {}) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/campagnes`, {
      params: {
        page: params.page,
        limit: params.limit,
        search: params.search,
        statut: params.statut,
        sortBy: params.sortBy,
        sortOrder: params.sortOrder
      },
      withCredentials: true
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching campagnes:', error);
    throw error;
  }
};

// Get single campagne by ID
export const getCampagneById = async (id) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/campagnes/${id}`, {
      withCredentials: true
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching campagne:', error);
    throw error;
  }
};

// Create new campagne
export const createCampagne = async (campagneData) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/campagnes`, campagneData, {
      withCredentials: true
    });

    return response.data;
  } catch (error) {
    console.error('Error creating campagne:', error);
    throw error;
  }
};

// Update campagne
export const updateCampagne = async (id, campagneData) => {
  try {
    const response = await axios.put(`${API_BASE_URL}/campagnes/${id}`, campagneData, {
      withCredentials: true
    });

    return response.data;
  } catch (error) {
    console.error('Error updating campagne:', error);
    throw error;
  }
};

// Delete campagne
export const deleteCampagne = async (id) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/campagnes/${id}`, {
      withCredentials: true
    });

    return response.data;
  } catch (error) {
    console.error('Error deleting campagne:', error);
    throw error;
  }
};

// Assign users to campagne
export const assignUsersToCampagne = async (campagneId, userIds) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/campagnes/${campagneId}/assign-users`,
      { userIds },
      { withCredentials: true }
    );

    return response.data;
  } catch (error) {
    console.error('Error assigning users to campagne:', error);
    throw error;
  }
};

// Get users assigned to campagne
export const getCampagneUsers = async (campagneId) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/campagnes/${campagneId}/users`, {
      withCredentials: true
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching campagne users:', error);
    throw error;
  }
};

// Remove user from campagne
export const removeUserFromCampagne = async (campagneId, userId) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/campagnes/${campagneId}/users/${userId}`, {
      withCredentials: true
    });

    return response.data;
  } catch (error) {
    console.error('Error removing user from campagne:', error);
    throw error;
  }
};

// Create a campagne and assign users (for "Lancer la Campagne" functionality)
export const createCampagneWithUsers = async (campagneData, userIds) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/campagnes/create-with-users`, {
      campagneData,
      userIds
    }, {
      withCredentials: true
    });

    return response.data;
  } catch (error) {
    console.error('Error creating campagne with users:', error);
    throw error;
  }
};

// Find or create campagne for control (for "Lancer la Campagne" with update logic)
export const findOrCreateCampagneForControl = async (controlId, campagneData, userIds) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/campagnes/find-or-create-for-control`, {
      controlId,
      campagneData,
      userIds
    }, {
      withCredentials: true
    });

    return response.data;
  } catch (error) {
    console.error('Error finding or creating campagne for control:', error);
    throw error;
  }
};

export default {
  getAllCampagnes,
  getCampagneById,
  createCampagne,
  updateCampagne,
  deleteCampagne,
  assignUsersToCampagne,
  getCampagneUsers,
  removeUserFromCampagne,
  createCampagneWithUsers,
  findOrCreateCampagneForControl
};
