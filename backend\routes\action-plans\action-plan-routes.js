const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllActionPlans,
  createActionPlan,
  getActionPlanById,
  updateActionPlan,
  deleteActionPlan
} = require('../../controllers/action-plans/action-plan-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all action plans
router.get('/', getAllActionPlans);

// Create new action plan
router.post('/', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager','audit_director', 'auditor']), createActionPlan);

// Get action plan by ID
router.get('/:id', getActionPlanById);

// Update action plan
router.put('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager','audit_director', 'auditor']), updateActionPlan);

// Delete action plan
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager','audit_director', 'auditor']), deleteActionPlan);

module.exports = router;
