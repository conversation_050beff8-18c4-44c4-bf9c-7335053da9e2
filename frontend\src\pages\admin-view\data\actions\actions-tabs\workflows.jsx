import { useState } from "react";
import { useOutletContext } from "react-router-dom";
import { GitBranch, ArrowRight, FileText, ClipboardCheck } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

function ActionWorkflows() {
  const { action } = useOutletContext();
  
  // Mock related items - would be replaced with real data from API
  const [relatedItems] = useState([
    {
      id: "AP_1",
      type: "action_plan",
      name: "Security Enhancement Plan",
      status: "In Progress"
    },
    {
      id: "RISK_1",
      type: "risk",
      name: "Data Breach Risk",
      level: "High"
    },
    {
      id: "INC_1",
      type: "incident",
      name: "Server Outage Incident",
      status: "Resolved"
    }
  ]);
  
  // Get item icon
  const getItemIcon = (type) => {
    switch (type) {
      case "action_plan":
        return <ClipboardCheck className="h-5 w-5 text-blue-500" />;
      case "risk":
        return <FileText className="h-5 w-5 text-red-500" />;
      case "incident":
        return <FileText className="h-5 w-5 text-yellow-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };
  
  // Get item type label
  const getItemTypeLabel = (type) => {
    switch (type) {
      case "action_plan":
        return "Action Plan";
      case "risk":
        return "Risk";
      case "incident":
        return "Incident";
      default:
        return "Unknown";
    }
  };
  
  // Get badge color
  const getBadgeColor = (type, status) => {
    if (type === "risk") {
      switch (status) {
        case "High":
          return "bg-red-100 text-red-800";
        case "Medium":
          return "bg-yellow-100 text-yellow-800";
        case "Low":
          return "bg-green-100 text-green-800";
        default:
          return "bg-gray-100 text-gray-800";
      }
    } else {
      switch (status) {
        case "Completed":
        case "Resolved":
          return "bg-green-100 text-green-800";
        case "In Progress":
          return "bg-blue-100 text-blue-800";
        case "Not Started":
          return "bg-gray-100 text-gray-800";
        default:
          return "bg-gray-100 text-gray-800";
      }
    }
  };
  
  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">Related Workflows</h2>
      
      {relatedItems.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <p className="text-gray-500">No related workflows found for this action.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {relatedItems.map((item) => (
            <div key={item.id} className="flex items-start p-4 border rounded-lg hover:bg-gray-50">
              <div className="mr-4 mt-1">
                {getItemIcon(item.type)}
              </div>
              <div className="flex-1">
                <div className="flex items-center">
                  <Badge className="mr-2 bg-gray-100 text-gray-800">
                    {getItemTypeLabel(item.type)}
                  </Badge>
                  <Badge className={getBadgeColor(item.type, item.status)}>
                    {item.status || item.level}
                  </Badge>
                </div>
                <div className="font-medium mt-1">{item.name}</div>
                <div className="text-sm text-gray-500 mt-1">ID: {item.id}</div>
              </div>
              <Button variant="ghost" size="sm" className="mt-1">
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
      
      <div className="mt-8">
        <h3 className="text-lg font-semibold mb-4">Process Flow</h3>
        <div className="p-6 border rounded-lg bg-gray-50 flex items-center justify-center">
          <div className="flex items-center">
            <div className="p-3 bg-white rounded-lg border shadow-sm">
              <ClipboardCheck className="h-6 w-6 text-blue-500" />
              <div className="text-xs font-medium mt-1 text-center">Action Plan</div>
            </div>
            <ArrowRight className="h-5 w-5 mx-4 text-gray-400" />
            <div className="p-3 bg-white rounded-lg border shadow-sm border-blue-200 ring-2 ring-blue-500">
              <GitBranch className="h-6 w-6 text-blue-500" />
              <div className="text-xs font-medium mt-1 text-center">Current Action</div>
            </div>
            <ArrowRight className="h-5 w-5 mx-4 text-gray-400" />
            <div className="p-3 bg-white rounded-lg border shadow-sm">
              <FileText className="h-6 w-6 text-green-500" />
              <div className="text-xs font-medium mt-1 text-center">Completion</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ActionWorkflows;
