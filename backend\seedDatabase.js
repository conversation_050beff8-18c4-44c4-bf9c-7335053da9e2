const db = require('./models');
const sequelize = require('./config/database');

async function seedDatabase() {
    try {
        await sequelize.sync({ force: true }); // Use with caution, drops tables
        await db.Entity.bulkCreate([
            { entityID: 'FrEntity123', name: 'France Entity', type: 'Branch', localCurrency: 'EUR' },
            { entityID: 'USEntity456', name: 'USA Entity', type: 'Branch', localCurrency: 'USD' },
        ]);
        await db.Risk.bulkCreate([
            { riskID: 'RiskOp', name: 'Operational Risk', code: 'OP001', comment: 'Operational risk details', parentRiskType: null },
        ]);
        await db.Control.bulkCreate([
            { controlID: 'Control123', name: 'Test Control', code: 'TC001', comment: 'Test control details', parentControlType: null },
        ]);
        await db.BusinessLine.bulkCreate([
            { businessLineID: 'BL3', name: 'Business Line 3', description: 'Description for BL3' },
        ]);
        await db.IncidentType.bulkCreate([
            { incidentTypeID: 'IT1', name: 'Incident Type 1', description: 'Description for IT1' },
        ]);
        await db.BusinessProcess.bulkCreate([
            { businessProcessID: 'BP4', name: 'Business Process 4', description: 'Description for BP4' },
        ]);
        await db.OrganizationalProcess.bulkCreate([
            { organizationalProcessID: 'OP2', name: 'Org Process 2', description: 'Description for OP2' },
        ]);
        await db.Product.bulkCreate([
            { productID: 'P3', name: 'Product 3', description: 'Description for P3' },
        ]);
        await db.Application.bulkCreate([
            { applicationID: 'A3', name: 'Application 3', description: 'Description for A3' },
        ]);
        console.log('Database seeded successfully');
    } catch (error) {
        console.error('Error seeding database:', error);
    }
}

seedDatabase();