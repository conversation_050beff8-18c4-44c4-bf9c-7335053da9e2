import { useState, useEffect, useRef, useMemo, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2,Search, CheckCircle } from "lucide-react";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";
import { toast } from "sonner";
import { useApiRequest, withApiErrorHandling } from '@/hooks/useApiRequest';
import { BarChart, Users, UserCheck, User, ChevronUp, ChevronDown } from "lucide-react";
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { debounce } from "lodash";
import { fetchAuditMissionById, updateAuditMission, clearCurrentMission, clearError } from '@/store/slices/audit/auditMissionsSlice';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

// Frappe Gantt loader
const loadFrappeGantt = () => {
  return new Promise((resolve, reject) => {
    if (window.Gantt) {
      resolve();
      return;
    }

    // Load CSS first
    const cssLink = document.createElement("link");
    cssLink.rel = "stylesheet";
    cssLink.href = "https://cdn.jsdelivr.net/npm/frappe-gantt@0.6.1/dist/frappe-gantt.css";
    document.head.appendChild(cssLink);

    // Then load JS
    const script = document.createElement("script");
    script.src = "https://cdn.jsdelivr.net/npm/frappe-gantt@0.6.1/dist/frappe-gantt.min.js";
    script.async = false;
    script.onload = () => {
      if (window.Gantt) resolve();
      else reject(new Error("Frappe Gantt not available after script load"));
    };
    script.onerror = () => reject(new Error("Failed to load Frappe Gantt"));
    document.head.appendChild(script);
  });
};

function PlanificationTab({ missionAudit: propMissionAudit }) {
  const { makeRequest, cancelAllRequests } = useApiRequest();

  // Filters
  const [periode, setPeriode] = useState("Mois");
  const [dateDebut, setDateDebut] = useState("");
  const [dateFin, setDateFin] = useState("");

  // State management
  const [activities, setActivities] = useState([]);
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const ganttRef = useRef(null);
  const ganttInstanceRef = useRef(null);

  // Section open/close states
  const [isGanttOpen, setIsGanttOpen] = useState(true);
  const [isCompetencesOpen, setIsCompetencesOpen] = useState(true);
  const [isEquipeOpen, setIsEquipeOpen] = useState(true);
  const [isInterlocuteurOpen, setIsInterlocuteurOpen] = useState(true);

  // Redux and navigation
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { missionAuditId } = useParams();
  const abortControllerRef = useRef(null);
  const isMountedRef = useRef(true);
  // Redux state for mission
  const { currentItem: missionAuditRedux, loading: missionLoading, error: missionError, lastFetched } = useSelector(
    (state) => state.auditMissions
  );
  // Use prop if provided, else redux
  const missionAudit = propMissionAudit || missionAuditRedux;
  // --- User selection logic (shared for both fields) ---
  const [users, setUsers] = useState([]);
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [isAuditedModalOpen, setIsAuditedModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [auditedSearchTerm, setAuditedSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedAudited, setSelectedAudited] = useState(null);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  // --- State for characteristics (for auto-save) ---
  const [characteristics, setCharacteristics] = useState({
    leadAuditor: '',
    mainAudited: '',
    mainAuditedId: ''
  });
  const [initialLoad, setInitialLoad] = useState(true);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  // Fetch activities data
  useEffect(() => {
    const fetchActivitiesData = withApiErrorHandling(async () => {
      if (!missionAudit?.id) return;

      setIsLoading(true);

      try {
        const activitiesResponse = await makeRequest({
          method: 'get',
          url: `${getApiBaseUrl()}/audit-activities/mission/${missionAudit.id}`,
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' }
        }, {
          retries: 2,
          onError: (error) => {
            if (!axios.isCancel(error)) {
              console.error("Error fetching activities:", error);
              toast.error("Erreur lors du chargement des activités");
            }
          }
        });

        if (activitiesResponse && activitiesResponse.data.success) {
          setActivities(activitiesResponse.data.data);
        } else {
          setActivities([]);
        }
      } catch (error) {
        if (!axios.isCancel(error)) {
          console.error("Error in activities request:", error);
        }
        setActivities([]);
      }

      setIsLoading(false);
    }, {
      fallbackValue: null,
      autoRefresh: true,
      refreshDelay: 5000,
      onError: (error) => {
        setIsLoading(false);
        setActivities([]);
        if (!axios.isCancel(error)) {
          console.error("Critical error fetching activities data:", error);
          toast.error("Erreur lors du chargement des données");
        }
      }
    });

    if (missionAudit?.id) {
      fetchActivitiesData();
    }

    // Cleanup on unmount or dependency change
    return () => {
      cancelAllRequests();
    };
  }, [missionAudit?.id, makeRequest, cancelAllRequests]);
  // Fetch users
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setIsLoadingUsers(true);
        const response = await axios.get(`${getApiBaseUrl()}/users`);
        if (response.data.success && Array.isArray(response.data.data)) {
          setUsers(response.data.data);
        } else {
          setUsers([]);
          toast.error('Données des utilisateurs non valides');
        }
      } catch (error) {
        setUsers([]);
        toast.error('Erreur lors du chargement des utilisateurs');
      } finally {
        setIsLoadingUsers(false);
      }
    };
    fetchUsers();
  }, []);
  // Fetch mission data with cancellation (if using redux)
  useEffect(() => {
    if (!propMissionAudit) {
      if (abortControllerRef.current) abortControllerRef.current.abort();
      abortControllerRef.current = new AbortController();
      if (missionAuditId && (!missionAudit || missionAudit.id !== missionAuditId || !lastFetched)) {
        dispatch(fetchAuditMissionById({ id: missionAuditId, signal: abortControllerRef.current.signal }))
          .unwrap()
          .catch((error) => {
            if (error && error.name !== 'CanceledError') {
              toast.error(error || 'Erreur lors du chargement de la mission');
              navigate(-1);
            }
          });
      }
      return () => {
        if (abortControllerRef.current) abortControllerRef.current.abort();
        if (!location.pathname.includes('/missions-audits/')) {
          dispatch(clearCurrentMission());
        }
      };
    }
  }, [missionAuditId, dispatch, navigate, missionAudit, lastFetched, location, propMissionAudit]);
  // Show error toast if Redux error
  useEffect(() => {
    if (missionError) {
      toast.error(missionError);
      dispatch(clearError());
    }
  }, [missionError, dispatch]);
  // Update characteristics when mission data changes
  useEffect(() => {
    if (missionAudit && !isLoadingUsers) {
      let leadAuditorValue = '';
      let selectedUserData = null;
      if (missionAudit.chefmission !== null && missionAudit.chefmission !== undefined) {
        leadAuditorValue = missionAudit.chefmission.toString();
        const userData = users.find(user => user.id === missionAudit.chefmission);
        if (userData) {
          selectedUserData = userData;
        }
      }
      let mainAuditedValue = '';
      let mainAuditedIdValue = '';
      let selectedAuditedData = null;
      if (missionAudit.principalAudite !== null && missionAudit.principalAudite !== undefined) {
        const principalAuditeStr = missionAudit.principalAudite.toString();
        const principalAuditeInt = parseInt(principalAuditeStr);
        if (!isNaN(principalAuditeInt) && principalAuditeInt.toString() === principalAuditeStr) {
          mainAuditedIdValue = principalAuditeStr;
          const auditedUserData = users.find(user => user.id === principalAuditeInt);
          if (auditedUserData) {
            selectedAuditedData = auditedUserData;
            mainAuditedValue = auditedUserData.username || auditedUserData.email;
          }
        } else {
          mainAuditedValue = principalAuditeStr;
        }
      }
      setSelectedUser(selectedUserData);
      setSelectedAudited(selectedAuditedData);
      setCharacteristics({
        leadAuditor: leadAuditorValue,
        mainAudited: mainAuditedValue,
        mainAuditedId: mainAuditedIdValue
      });
    }
  }, [missionAudit, users, isLoadingUsers]);
  useEffect(() => {
    if (missionAudit && !isLoadingUsers && initialLoad) {
      setInitialLoad(false);
    }
  }, [missionAudit, isLoadingUsers, initialLoad]);
  // --- Handlers for user selection ---
  const handleUserSelect = (user) => {
    setSelectedUser(user);
    setCharacteristics(prev => {
      const newCharacteristics = { ...prev, leadAuditor: user.id.toString() };
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
    setIsUserModalOpen(false);
    setSearchTerm('');
  };
  const handleAuditedUserSelect = (user) => {
    setSelectedAudited(user);
    setCharacteristics(prev => {
      const newCharacteristics = {
        ...prev,
        mainAudited: user.username || user.email,
        mainAuditedId: user.id.toString()
      };
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
    setIsAuditedModalOpen(false);
    setAuditedSearchTerm('');
  };
  const handleUserRemove = () => {
    setSelectedUser(null);
    setCharacteristics(prev => {
      const newCharacteristics = { ...prev, leadAuditor: '' };
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
  };
  const handleAuditedUserRemove = () => {
    setSelectedAudited(null);
    setCharacteristics(prev => {
      const newCharacteristics = {
        ...prev,
        mainAudited: '',
        mainAuditedId: ''
      };
      debouncedAutoSave(newCharacteristics);
      return newCharacteristics;
    });
  };
  // --- Auto-save logic ---
  const autoSave = useCallback(
    async (currentCharacteristics) => {
      if (!missionAudit?.id || !isMountedRef.current) return;
      setIsAutoSaving(true);
      try {
        const updateData = {
          chefmission: currentCharacteristics.leadAuditor ? parseInt(currentCharacteristics.leadAuditor) : null,
          principalAudite: currentCharacteristics.mainAuditedId
            ? parseInt(currentCharacteristics.mainAuditedId)
            : currentCharacteristics.mainAudited
        };
        await dispatch(updateAuditMission({ id: missionAudit.id, missionData: updateData })).unwrap();
      } catch (error) {
        if (error && error.name !== 'CanceledError') {
          toast.error(error.message || 'Erreur lors de la sauvegarde');
        }
      } finally {
        if (isMountedRef.current) setIsAutoSaving(false);
      }
    },
    [dispatch, missionAudit]
  );
  const debouncedAutoSave = useMemo(() => debounce(autoSave, 1000), [autoSave]);
  // --- Filtered users ---
  const filteredUsers = users.filter(user =>
    user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );
  const filteredAuditedUsers = users.filter(user =>
    user.username?.toLowerCase().includes(auditedSearchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(auditedSearchTerm.toLowerCase())
  );

  // Helper function to format dates for Gantt
  const formatDateForGantt = (dateString) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  };

  // Helper function to calculate progress
  const calculateProgress = (activity) => {
    if (activity.chargedetravaileffective && activity.chargedetravailestimee) {
      return Math.min(100, Math.round((activity.chargedetravaileffective / activity.chargedetravailestimee) * 100));
    }
    return activity.status === 'Completed' ? 100 : activity.status === 'In Progress' ? 50 : 0;
  };

  // Gantt rendering
  useEffect(() => {
    if (activities.length === 0) return;

    loadFrappeGantt().then(() => {
      if (!ganttRef.current) return;
      ganttRef.current.innerHTML = "";

      // Filter activities by date if filters are set
      let filtered = activities.filter(activity => {
        const startDate = formatDateForGantt(activity.datedebut);
        const endDate = formatDateForGantt(activity.datefin);

        // Skip activities without valid dates
        if (!startDate || !endDate) return false;

        // Apply date filters
        if (dateDebut && endDate < dateDebut) return false;
        if (dateFin && startDate > dateFin) return false;

        return true;
      });

      // Map to Gantt tasks
      const tasks = filtered.map(activity => ({
        id: activity.id,
        name: "", // Empty name as we show names in the left column
        start: formatDateForGantt(activity.datedebut),
        end: formatDateForGantt(activity.datefin),
        progress: calculateProgress(activity),
        dependencies: "",
        custom_class: `gantt-task-${activity.id}`
      }));

      if (tasks.length === 0) {
        ganttRef.current.innerHTML = '<div class="text-center py-8 text-gray-500">Aucune activité à afficher pour la période sélectionnée</div>';
        return;
      }

      try {
        ganttInstanceRef.current = new window.Gantt(ganttRef.current, tasks, {
          view_mode: periode === "Année" ? "Year" : periode === "Trimestre" ? "Month" : "Day",
          bar_height: 24,
          padding: 24,
          column_width: 36,
          language: "fr",
          show_label: false,
          on_click: (task) => {
            const activity = activities.find(a => a.id === task.id);
            if (activity) {
              setSelectedActivity(activity);
            }
          }
        });
      } catch (error) {
        console.error("Error rendering Gantt chart:", error);
        ganttRef.current.innerHTML = '<div class="text-center py-8 text-red-500">Erreur lors du rendu du diagramme de Gantt</div>';
      }
    }).catch(error => {
      console.error("Error loading Frappe Gantt:", error);
      ganttRef.current.innerHTML = '<div class="text-center py-8 text-red-500">Erreur lors du chargement du diagramme de Gantt</div>';
    });
  }, [activities, periode, dateDebut, dateFin]);

  // Helper function to handle activity click
  const handleActivityClick = (activity) => {
    setSelectedActivity(activity);
  };

  if (!missionAudit) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">Chargement des informations de la mission...</p>
      </div>
    );
  }

  const ganttStyles = `
    .gantt svg {
      background: white;
    }
    .gantt .grid-background {
      fill: white;
    }
    .gantt .bar {
      fill: #2563eb;
      stroke: #1e40af;
    }
    .gantt .bar-progress {
      fill: #60a5fa;
      stroke: #1e40af;
    }
    .gantt text {
      fill: #1e293b;
    }
    .gantt .grid-header, .gantt .grid-row {
      fill: #fff;
      stroke: #e2e8f0;
      stroke-width: 1;
    }
    .gantt .grid-header {
      stroke: #cbd5e1;
      stroke-width: 2;
    }
    .gantt .bar-label {
      display: none;
    }
    .activity-list {
      width: 200px;
      border-right: 1px solid #e2e8f0;
      padding-right: 8px;
      display: flex;
      flex-direction: column;
    }
    .activity-header {
      height: 62px;
      border-bottom: 1px solid #e2e8f0;
    }
    .activity-item {
      height: 48px;
      display: flex;
      align-items: center;
      padding-left: 8px;
      border-bottom: 1px solid #e2e8f0;
      box-sizing: border-box;
      line-height: 24px;
      cursor: pointer;
    }
    .activity-item:hover {
      background-color: #f1f5f9;
    }
    .activity-item.selected {
      background-color: #dbeafe;
    }
    .gantt-container {
      flex: 1;
      overflow-x: auto;
      min-height: 48px;
    }
  `;

  // Filter activities for display in the list
  const filteredActivities = activities.filter(activity => {
    const startDate = formatDateForGantt(activity.datedebut);
    const endDate = formatDateForGantt(activity.datefin);

    // Skip activities without valid dates
    if (!startDate || !endDate) return false;

    // Apply date filters
    if (dateDebut && endDate < dateDebut) return false;
    if (dateFin && startDate > dateFin) return false;

    return true;
  });

  return (
    <div className="space-y-6 py-4">
      <style>{ganttStyles}</style>
      {/* Section Gantt */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsGanttOpen(!isGanttOpen)}
        >
          <div className="flex items-center gap-2">
            {isGanttOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <BarChart className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">Gantt</span>
          </div>
        </button>
        {isGanttOpen && (
          <div className="p-5 bg-white">
            {/* Filters */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Période</label>
                  <Select value={periode} onValueChange={setPeriode}>
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner une période" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Jour">Jour</SelectItem>
                      <SelectItem value="Mois">Mois</SelectItem>
                      <SelectItem value="Trimestre">Trimestre</SelectItem>
                      <SelectItem value="Année">Année</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date de début</label>
                  <Input
                    type="date"
                    value={dateDebut}
                    onChange={(e) => setDateDebut(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date de fin</label>
                  <Input
                    type="date"
                    value={dateFin}
                    onChange={(e) => setDateFin(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div className="flex items-end">
                  <button
                    onClick={() => {
                      setDateDebut("");
                      setDateFin("");
                      setPeriode("Mois");
                    }}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Réinitialiser
                  </button>
                </div>
              </div>
            </div>
            {/* Gantt Chart */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  Planification des Activités - {missionAudit.name}
                </h3>
                <p className="text-sm text-gray-500 mt-1">
                  {filteredActivities.length} activité{filteredActivities.length > 1 ? 's' : ''} affichée{filteredActivities.length > 1 ? 's' : ''}
                </p>
              </div>
              {isLoading ? (
                <div className="flex items-center justify-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-600 mr-3" />
                  <p className="text-gray-500">Chargement des activités...</p>
                </div>
              ) : filteredActivities.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-gray-500">Aucune activité trouvée pour cette mission.</p>
                </div>
              ) : (
                <div className="flex">
                  {/* Activities List */}
                  <div className="activity-list">
                    <div className="activity-header flex items-center justify-center bg-gray-50 border-b border-gray-200">
                      <span className="text-sm font-medium text-gray-700">Activités</span>
                    </div>
                    {filteredActivities.map((activity) => (
                      <div
                        key={activity.id}
                        className={`activity-item ${selectedActivity?.id === activity.id ? 'selected' : ''}`}
                        onClick={() => handleActivityClick(activity)}
                        title={activity.name}
                      >
                        <span className="text-sm text-gray-900 truncate">{activity.name}</span>
                      </div>
                    ))}
                  </div>
                  {/* Gantt Chart Container */}
                  <div className="gantt-container">
                    <div ref={ganttRef} className="gantt"></div>
                  </div>
                </div>
              )}
            </div>
            {/* Activity Details */}
            {selectedActivity && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mt-4">
                <h4 className="text-lg font-medium text-gray-900 mb-4">Détails de l'activité</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Nom</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedActivity.name}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedActivity.description || 'Non renseignée'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Date de début</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedActivity.datedebut ? new Date(selectedActivity.datedebut).toLocaleDateString('fr-FR') : 'Non définie'}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Date de fin</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedActivity.datefin ? new Date(selectedActivity.datefin).toLocaleDateString('fr-FR') : 'Non définie'}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Charge estimée</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedActivity.chargedetravailestimee || 'Non renseignée'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Charge effective</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedActivity.chargedetravaileffective || 'Non renseignée'}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      {/* Section Compétences */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-green-50 to-teal-50 rounded-t-lg"
          onClick={() => setIsCompetencesOpen(!isCompetencesOpen)}
        >
          <div className="flex items-center gap-2">
            {isCompetencesOpen ? (
              <ChevronUp className="h-5 w-5 text-green-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-green-600" />
            )}
            <UserCheck className="h-5 w-5 text-green-600 mr-1" />
            <span className="text-lg font-medium text-green-800">Compétences</span>
          </div>
        </button>
        {isCompetencesOpen && (
          <div className="p-5 bg-white">
            <div className="flex items-center justify-center">
              <p className="text-gray-500 italic">Cette section est vide</p>
            </div>
          </div>
        )}
      </div>
      {/* Section Équipe intervenante */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-purple-50 to-violet-50 rounded-t-lg"
          onClick={() => setIsEquipeOpen(!isEquipeOpen)}
        >
          <div className="flex items-center gap-2">
            {isEquipeOpen ? (
              <ChevronUp className="h-5 w-5 text-purple-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-purple-600" />
            )}
            <Users className="h-5 w-5 text-purple-600 mr-1" />
            <span className="text-lg font-medium text-purple-800">Équipe intervenante</span>
          </div>
        </button>
        {isEquipeOpen && (
          <div className="p-5 bg-white">
            {/* Chef de mission field (with all logic) */}
            <div className="max-w-xl">
              <Label htmlFor="leadAuditor">Chef de mission</Label>
              <div className="flex gap-2 mt-2">
                <Dialog open={isUserModalOpen} onOpenChange={setIsUserModalOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="flex-1 justify-start"
                      type="button"
                      disabled={isLoadingUsers}
                    >
                      {isLoadingUsers ? (
                        <div className="flex items-center gap-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-600" />
                        </div>
                      ) : selectedUser ? (
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <span>{selectedUser.username || selectedUser.email}</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <span>Sélectionner un chef de mission</span>
                        </div>
                      )}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-md">
                    <DialogHeader>
                      <DialogTitle>Sélectionner un chef de mission</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="relative">
                        <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Rechercher un utilisateur..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      <div className="max-h-60 overflow-y-auto space-y-2">
                        {isLoadingUsers ? (
                          <div className="flex items-center justify-center py-8">
                            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-600" />
                          </div>
                        ) : filteredUsers.length > 0 ? (
                          filteredUsers.map(user => (
                            <div
                              key={user.id}
                              className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                              onClick={() => handleUserSelect(user)}
                            >
                              <div className="flex items-center gap-3">
                                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                  <User className="h-4 w-4 text-blue-600" />
                                </div>
                                <div>
                                  <p className="font-medium">{user.username}</p>
                                  <p className="text-sm text-gray-500">{user.email}</p>
                                </div>
                              </div>
                              {selectedUser?.id === user.id && (
                                <CheckCircle className="h-5 w-5 text-green-500" />
                              )}
                            </div>
                          ))
                        ) : (
                          <p className="text-center text-gray-500 py-4">
                            {searchTerm ? 'Aucun utilisateur trouvé' : 'Aucun utilisateur disponible'}
                          </p>
                        )}
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
                {selectedUser && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleUserRemove}
                    className="px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                    type="button"
                  >
                    ×
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
      {/* Section Interlocuteur */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-rose-50 to-red-50 rounded-t-lg"
          onClick={() => setIsInterlocuteurOpen(!isInterlocuteurOpen)}
        >
          <div className="flex items-center gap-2">
            {isInterlocuteurOpen ? (
              <ChevronUp className="h-5 w-5 text-rose-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-rose-600" />
            )}
            <User className="h-5 w-5 text-rose-600 mr-1" />
            <span className="text-lg font-medium text-rose-800">Interlocuteurs</span>
          </div>
        </button>
        {isInterlocuteurOpen && (
          <div className="p-5 bg-white">
            {/* Principal audité field (with all logic) */}
            <div className="max-w-xl">
              <Label htmlFor="mainAudited">Principal audité</Label>
              <div className="flex gap-2 mt-2">
                <Dialog open={isAuditedModalOpen} onOpenChange={setIsAuditedModalOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="flex-1 justify-start"
                      type="button"
                      disabled={isLoadingUsers}
                    >
                      {isLoadingUsers ? (
                        <div className="flex items-center gap-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-600" />
                        </div>
                      ) : selectedAudited ? (
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <span>{selectedAudited.username || selectedAudited.email}</span>
                        </div>
                      ) : characteristics.mainAudited ? (
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <span>{characteristics.mainAudited}</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          <span>Sélectionner un principal audité</span>
                        </div>
                      )}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-md">
                    <DialogHeader>
                      <DialogTitle>Sélectionner un principal audité</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="relative">
                        <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Rechercher un utilisateur..."
                          value={auditedSearchTerm}
                          onChange={(e) => setAuditedSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      <div className="max-h-60 overflow-y-auto space-y-2">
                        {isLoadingUsers ? (
                          <div className="flex items-center justify-center py-8">
                            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-600" />
                          </div>
                        ) : filteredAuditedUsers.length > 0 ? (
                          filteredAuditedUsers.map(user => (
                            <div
                              key={user.id}
                              className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                              onClick={() => handleAuditedUserSelect(user)}
                            >
                              <div className="flex items-center gap-3">
                                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                  <User className="h-4 w-4 text-blue-600" />
                                </div>
                                <div>
                                  <p className="font-medium">{user.username}</p>
                                  <p className="text-sm text-gray-500">{user.email}</p>
                                </div>
                              </div>
                              {selectedAudited?.id === user.id && (
                                <CheckCircle className="h-5 w-5 text-green-500" />
                              )}
                            </div>
                          ))
                        ) : (
                          <p className="text-center text-gray-500 py-4">
                            {auditedSearchTerm ? 'Aucun utilisateur trouvé' : 'Aucun utilisateur disponible'}
                          </p>
                        )}
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
                {(selectedAudited || characteristics.mainAudited) && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAuditedUserRemove}
                    className="px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                    type="button"
                  >
                    ×
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default PlanificationTab;
