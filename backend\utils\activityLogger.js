const { Activity } = require('../models');

/**
 * Log user activity
 * @param {Object} user - The user performing the action
 * @param {string} action - The action performed (e.g., 'created', 'updated', 'deleted')
 * @param {string} entityType - The type of entity (e.g., 'Risk', 'Incident', 'User')
 * @param {string} entityId - The ID of the entity
 * @param {Object} details - Additional details about the action
 */
const logActivity = async (user, action, entityType, entityId, details = {}) => {
  try {
    if (!user || !user.id) {
      console.error('Cannot log activity: User information missing');
      return;
    }

    await Activity.create({
      userId: user.id,
      username: user.username,
      action,
      entityType,
      entityId,
      details: JSON.stringify(details)
    });
  } catch (error) {
    console.error('Error logging activity:', error);
    // Don't throw the error to prevent disrupting the main operation
  }
};

module.exports = { logActivity };
