const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class Notification extends Model {
    static associate(models) {
      // Define association with User model
      this.belongsTo(models.User, { 
        foreignKey: 'user_id', 
        as: 'user' 
      });
    }
  }
  
  Notification.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    type: {
      type: DataTypes.STRING, // 'risk', 'incident', etc.
      allowNull: false
    },
    entity_id: {
      type: DataTypes.STRING, // Changed from DataTypes.INTEGER to STRING
      allowNull: false
    },
    entity_name: {
      type: DataTypes.STRING, // Name of the related entity
      allowNull: true
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    assigned_by: {
      type: DataTypes.STRING, // Username or email of assigner
      allowNull: true
    },
    is_read: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'Notification',
    tableName: 'Notifications',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });
  
  return Notification;
}; 