import { useContext } from 'react';
import { OutletContext } from '@/pages/audit-view/plans-daudit/plans-daudit-tabs/missions-audits/edit-missions-audits';

/**
 * Utility function to get mission audit context data from either 
 * React Router's useOutletContext or our custom OutletContext
 * @returns {Object} An object containing the missionAudit property
 */

// Helper to access mission audit context data
export const useMissionAuditContext = () => {
  const context = useContext(OutletContext);
  if (context === undefined) {
    // Return empty object as fallback if context is not available
    return { missionAudit: null };
  }
  return context;
}; 