import { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate, Outlet, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  ArrowLeft,
  Loader2,
  FileText,
  Edit,
  Activity,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import DetailHeader from "@/components/ui/detail-header";
import TabBar from "@/components/ui/tabs/tab-bar";
import TabContent from "@/components/ui/tabs/tab-content";
import { toast } from "sonner";
import { hasPermission } from "@/store/auth-slice";
import { getCampagneById } from "@/services/campagne-service";

// Placeholder icon - you can replace with actual campagne icon
import controlIcon from "@/assets/campagne.png";

function EditCampagnes() {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isTabChanging, setIsTabChanging] = useState(false);

  // Mock campagne data - replace with actual Redux store later
  const [currentCampagne, setCurrentCampagne] = useState(null);

  // Get permissions
  const canUpdate = useSelector((state) => hasPermission(state, 'update'));
  const canDelete = useSelector((state) => hasPermission(state, 'delete'));

  // Get the current tab from the URL
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    if (path.includes('/caracteristiques')) {
      return 'caracteristiques';
    }
    if (path.includes('/execution')) {
      return 'execution';
    }
    return 'vue-ensemble';
  }, [location.pathname]);

  const [activeTab, setActiveTab] = useState(getCurrentTab());

  // Update active tab when location changes
  useEffect(() => {
    setActiveTab(getCurrentTab());
  }, [location, getCurrentTab]);

  // Fetch real campagne data from API
  useEffect(() => {
    const fetchCampagneDetails = async () => {
      setLoading(true);
      try {
        console.log('🔍 [FRONTEND DEBUG] Fetching campagne with ID:', id);

        const response = await getCampagneById(id);

        console.log('🔍 [FRONTEND DEBUG] getCampagneById response:', {
          success: response.success,
          data: response.data,
          dataKeys: response.data ? Object.keys(response.data) : 'No data'
        });

        if (response.success && response.data) {
          setCurrentCampagne(response.data);
          setError(null);
        } else {
          throw new Error(response.message || 'Erreur lors du chargement de la campagne');
        }
      } catch (error) {
        console.error('Error fetching campagne:', error);
        setError('Échec du chargement des données de la campagne. Veuillez réessayer.');
        toast.error('Erreur lors du chargement de la campagne');
      } finally {
        setLoading(false);
      }
    };

    fetchCampagneDetails();
  }, [id]);

  const handleBackToCampagnes = () => {
    navigate("/admin/campagnes");
  };

  const tabs = [
    // 1. Vue d'ensemble
    { id: "vue-ensemble", label: 'Vue d\'ensemble', icon: <FileText className="h-4 w-4" /> },
    // 2. Contexte - Only show if user has update permission
    ...(canUpdate ? [
      { id: "caracteristiques", label: 'Contexte', icon: <Edit className="h-4 w-4" /> },
    ] : []),
    // 3. Execution - Only show if user has update permission
    ...(canUpdate ? [
      { id: "execution", label: 'Exécution', icon: <Activity className="h-4 w-4" /> },
    ] : []),
  ];

  // Navigate to tab with improved error handling
  const navigateToTab = (tabId) => {
    if (isTabChanging) {
      console.log("Tab change in progress, ignoring request");
      return;
    }

    // Define restricted tabs that require update permission
    const restrictedTabs = ["caracteristiques", "execution"];

    // Check permissions for restricted tabs
    if (restrictedTabs.includes(tabId) && !canUpdate) {
      toast.error("Vous n'avez pas la permission d'accéder à cet onglet");
      return;
    }

    setIsTabChanging(true);

    try {
      let targetPath;
      switch (tabId) {
        case "vue-ensemble":
          targetPath = `/admin/campagnes/edit/${id}`;
          break;
        case "caracteristiques":
          targetPath = `/admin/campagnes/edit/${id}/caracteristiques`;
          break;
        case "execution":
          targetPath = `/admin/campagnes/edit/${id}/execution`;
          break;
        default:
          targetPath = `/admin/campagnes/edit/${id}`;
      }

      // Navigate to the new tab
      navigate(targetPath);
    } catch (error) {
      console.error("Error navigating to tab:", error);
      toast.error("Erreur lors du changement d'onglet. Veuillez réessayer.");
    } finally {
      setTimeout(() => {
        setIsTabChanging(false);
      }, 300);
    }
  };

  // Refresh campagne data
  const refreshCampagne = async () => {
    setLoading(true);
    try {
      console.log('🔄 [DEBUG] Refreshing campagne data for ID:', id);

      const response = await getCampagneById(id);

      if (response.success && response.data) {
        setCurrentCampagne(response.data);
        setError(null);
        console.log('✅ [DEBUG] Campagne data refreshed successfully');
      } else {
        throw new Error(response.message || 'Erreur lors du rafraîchissement');
      }
    } catch (err) {
      console.error("Failed to refresh campagne:", err);
      toast.error("Échec de l'actualisation des données de la campagne");
      setError('Échec de l\'actualisation des données de la campagne. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  // Add effect to check URL and redirect if necessary
  useEffect(() => {
    // Define paths that require update permission
    const restrictedPaths = [
      `/admin/campagnes/edit/${id}/caracteristiques`,
    ];

    // If user doesn't have update permission and tries to access a restricted path
    if (!canUpdate && restrictedPaths.some(path => location.pathname === path)) {
      toast.error("Vous n'avez pas la permission d'accéder à cette page");
      navigate(`/admin/campagnes/edit/${id}`);
    }
  }, [location.pathname, canUpdate, id, navigate]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!currentCampagne) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          Campagne non trouvée
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      {/* Header */}
      <div className="mb-6">
        <DetailHeader
          title={currentCampagne?.name || 'Campagne sans nom'}
          icon={<img src={controlIcon} alt="Campagne" className="h-6 w-6" />}
          metadata={[
            currentCampagne.code
              ? `Code: ${currentCampagne.code}`
              : 'Code: N/A',
            currentCampagne.statut
              ? `Statut: ${currentCampagne.statut}`
              : 'Statut: N/A',
          ].filter(Boolean)}
          onBack={handleBackToCampagnes}
          backLabel="Retour aux Campagnes"
        />
      </div>

      {/* Tabs */}
      <TabBar
        activeTab={activeTab}
        tabs={tabs}
        onTabChange={navigateToTab}
        className="mb-6"
      />

      {/* Tab Content */}
      <TabContent>
        <Outlet
          context={{
            campagne: currentCampagne,
            refreshCampagne,
            permissions: {
              canUpdate,
              canDelete
            },
            // Add navigation handler for unsaved changes warning
            onNavigateAway: (hasUnsavedChanges) => {
              if (hasUnsavedChanges) {
                return window.confirm('You have unsaved changes. Are you sure you want to leave?');
              }
              return true;
            }
          }}
        />
      </TabContent>
    </div>
  );
}

export default EditCampagnes;