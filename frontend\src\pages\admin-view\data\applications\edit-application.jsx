import { useState, useEffect } from "react";
import { useParams, useNavigate, Outlet } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  ArrowLeft,
  Loader2,
  Server,
  FileText,
  Edit
} from "lucide-react";
import { getApplicationById, reset } from "@/store/slices/applicationSlice";
import { Button } from "@/components/ui/button";
import TabBar from "@/components/ui/tabs/tab-bar";
import TabContent from "@/components/ui/tabs/tab-content";
import { useTranslation } from "react-i18next";

function EditApplication() {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const { currentApplication, isLoading, isError } = useSelector((state) => state.application);
  const [activeTab, setActiveTab] = useState("overview");

  // Fetch application data on component mount
  useEffect(() => {
    if (id) {
      dispatch(getApplicationById(id));
    }

    return () => {
      dispatch(reset());
    };
  }, [dispatch, id]);

  // Handle back button click
  const handleBack = () => {
    navigate("/admin/data/applications");
  };

  // Handle tab change
  const handleTabChange = (value) => {
    setActiveTab(value);
    navigate(`/admin/data/applications/${id}/${value}`);
  };

  // Define tabs
  const [tabs, setTabs] = useState([
    { id: "overview", label: t('common.tabs.overview', 'Overview'), icon: <FileText className="h-4 w-4" /> },
    { id: "features", label: t('common.tabs.features', 'Features'), icon: <Edit className="h-4 w-4" /> }
  ]);

  // Update tabs when language changes
  useEffect(() => {
    setTabs([
      { id: "overview", label: t('common.tabs.overview', 'Overview'), icon: <FileText className="h-4 w-4" /> },
      { id: "features", label: t('common.tabs.features', 'Features'), icon: <Edit className="h-4 w-4" /> }
    ]);
  }, [t]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {t('admin.applications.error.loading', 'Error loading application data. Please try again.')}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={handleBack}
          className="flex items-center text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('admin.applications.back_to_list', 'Back to Applications')}
        </Button>
      </div>

      {currentApplication ? (
        <>
          <div className="bg-white p-6 rounded-lg shadow mb-6">
            <div className="flex items-center mb-2">
              <Server className="h-5 w-5 mr-2 text-gray-600" />
              <h1 className="text-2xl font-semibold">{currentApplication.name}</h1>
            </div>

            <div className="flex flex-wrap gap-2 mb-4">
              {currentApplication.version && (
                <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {t('admin.applications.version', 'Version')}: {currentApplication.version}
                </span>
              )}
              {currentApplication.vendor && (
                <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  {t('admin.applications.vendor', 'Vendor')}: {currentApplication.vendor}
                </span>
              )}
              {currentApplication.status && (
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  currentApplication.status === 'active' ? 'bg-green-100 text-green-800' :
                  currentApplication.status === 'inactive' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {t('admin.applications.status', 'Status')}: {t(`admin.applications.status.${currentApplication.status}`, currentApplication.status)}
                </span>
              )}
            </div>

            {currentApplication.description && (
              <p className="text-gray-600 mb-2">{currentApplication.description}</p>
            )}
          </div>

          <div className="mt-6">
            <TabBar
              activeTab={activeTab}
              onTabChange={handleTabChange}
              tabs={tabs}
              className="mb-6"
            />

            <TabContent>
              <Outlet context={{ application: currentApplication }} />
            </TabContent>
          </div>
        </>
      ) : (
        <div className="bg-white p-6 rounded-lg shadow">
          <p className="text-gray-500">{t('admin.applications.error.not_found', 'No application found with ID: {{id}}', { id })}</p>
        </div>
      )}
    </div>
  );
}

export default EditApplication;