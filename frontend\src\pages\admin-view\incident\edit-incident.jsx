import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  ChevronLeft,
  Loader2,
  AlertTriangle,
  FileText,
  List,
  Activity,
  GitBranchPlus,
  Paperclip,
  Info,
  Calendar,
  User,
  Building,
  Clock,
  Check,
  CreditCard,
  DollarSign,
  Briefcase,
  Layers,
  Package,
  Database,
  ShieldAlert,
  Workflow,
  ExternalLink,
  Bookmark,
  Lightbulb,
  Trash2,
  Users
} from 'lucide-react';
import PageHeader from '@/components/ui/page-header';
import { Button } from "@/components/ui/button";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import TabBar from "@/components/ui/tabs/tab-bar";
import { CharacteristicsSection } from '@/components/incident/CharacteristicsSection';
import { FinancialAnalysisSectionV2 } from '@/components/incident/FinancialAnalysisSectionV2';
import { ActionPlanSection } from "@/components/incident/ActionPlanSection";
import { ActivityFeedSection } from "@/components/incident/ActivityFeedSection";
import { WorkflowSection } from "@/components/incident/WorkflowSection";
import { AttachmentsSection } from "@/components/incident/AttachmentsSection";
import { useSelector } from 'react-redux';
import { hasPermission } from '@/store/auth-slice';
import { getApiBaseUrl } from '../../../utils/api-config';
import { useTranslation } from "react-i18next";
const API_BASE_URL = getApiBaseUrl();
// Format date to YYYY-MM-DD
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';
  return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD
};

function EditIncident() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id, tab } = useParams();
  const location = useLocation();
  const state = useSelector(state => state);

  // Permission checks
  const canUpdate = hasPermission(state, 'update');
  const canDelete = hasPermission(state, 'delete');

  const [incident, setIncident] = useState(null);
  const [workflowState, setWorkflowState] = useState(null);
  const [loading, setLoading] = useState(true); // Add the missing loading state
  const [error, setError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [referenceData, setReferenceData] = useState({
    entities: [],
    businessLines: [],
    incidentTypes: [],
    businessProcesses: [],
    organizationalProcesses: [],
    products: [],
    applications: [],
    controls: [],
    risks: []
  });
  const [contributors, setContributors] = useState([]);
  const [loadingContributors, setLoadingContributors] = useState(false);

  // Determine active tab from URL or default to overview
  const activeTab = tab || 'overview';

  // Define tabs with permission checks
  const tabs = [
    { id: "overview", label: t('admin.incidents.tabs.overview', 'Overview'), icon: <FileText className="h-4 w-4" /> },
    // Only show these tabs if user has update permission
    ...(canUpdate ? [
      { id: "features", label: t('admin.incidents.tabs.contexte', 'Contexte'), icon: <List className="h-4 w-4" /> },
    { id: "financial", label: t('admin.incidents.tabs.financial', 'Financial Analysis'), icon: <Activity className="h-4 w-4" /> },
    { id: "action-plan", label: t('admin.incidents.tabs.action_plan', 'Action Plan'), icon: <GitBranchPlus className="h-4 w-4" /> },
    { id: "activity", label: t('admin.incidents.tabs.activity', 'Activity Feed'), icon: <Activity className="h-4 w-4" /> },
    ] : []),
    { id: "workflow", label: t('admin.incidents.tabs.workflow', 'Workflow'), icon: <GitBranchPlus className="h-4 w-4" /> }
  ];

  // Helper to get the base path (admin or super-admin)
  const getBasePath = useCallback(() => {
    return location.pathname.includes('/super-admin') ? '/super-admin' : '/admin';
  }, [location.pathname]);

  // Navigate to tab
  const navigateToTab = (tabId) => {
    // Get the current path to determine if we're in audit view
    const isAuditView = window.location.pathname.startsWith('/audit');
    const basePath = isAuditView ? '/audit' : '/admin';

    // Check if tab requires update permission
    const restrictedTabs = ["features", "financial", "action-plan", "activity"];

    if (restrictedTabs.includes(tabId) && !canUpdate) {
      toast.error(t('admin.incidents.edit.no_permission', "You don't have permission to access this tab"));
      return;
    }

    switch (tabId) {
      case "overview":
        navigate(`${basePath}/incident/edit/${id}`);
        break;
      case "features":
        navigate(`${basePath}/incident/edit/${id}/features`);
        break;
      case "financial":
        navigate(`${basePath}/incident/edit/${id}/financial`);
        break;
      case "action-plan":
        navigate(`${basePath}/incident/edit/${id}/action-plan`);
        break;
      case "activity":
        navigate(`${basePath}/incident/edit/${id}/activity`);
        break;
      case "workflow":
        navigate(`${basePath}/incident/edit/${id}/workflow`);
        break;
      default:
        navigate(`${basePath}/incident/edit/${id}`);
    }
  };

  // Effect to check permissions on URL change
  useEffect(() => {
    const restrictedTabs = ["features", "financial", "action-plan", "activity"];
    const currentTab = tab; // This is the tab from the URL

    if (currentTab && restrictedTabs.includes(currentTab) && !canUpdate) {
      toast.error(t('admin.incidents.edit.no_permission', "You don't have permission to access this page"));
      const basePath = getBasePath();
      navigate(`${basePath}/incident/edit/${id}`);
    }
  }, [tab, canUpdate, id, navigate, getBasePath, t]);

  // Back button handler
  const handleBack = useCallback((e) => {
    e.preventDefault();
    const path = window.location.pathname;
    if (path.includes('/super-admin')) {
      navigate('/super-admin/incident');
    } else {
      navigate('/admin/incident');
    }
  }, [navigate]);

  // Handle delete with permission check
  const _handleDelete = useCallback(() => {
    if (!canDelete) {
      toast.error(t('admin.incidents.edit.no_permission', "You don't have permission to delete incidents"));
      return;
    }

    if (window.confirm(t('admin.incidents.edit.delete_confirm', 'Are you sure you want to delete this incident? This action cannot be undone.'))) {
      axios.delete(`http://localhost:5001/incidents/${id}`, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      })
      .then(response => {
        if (response.data.success) {
          toast.success(t('admin.incidents.edit.delete_success', "Incident deleted successfully"));
          // Navigate back to incidents list
          const basePath = getBasePath();
          navigate(`${basePath}/incident`);
        } else {
          toast.error(response.data.message || t('admin.incidents.edit.delete_error', "Failed to delete incident"));
        }
      })
      .catch(err => {
        toast.error(err.response?.data?.message || t('admin.incidents.edit.delete_error_details', "Error deleting incident: {0}"), {
          values: {
            error: err.response?.data?.error || 'No error details'
          }
        });
        console.error("Error deleting incident:", err);
      });
    }
  }, [id, navigate, getBasePath, canDelete, t]);

  // Fetch reference data
  useEffect(() => {
    const fetchReferenceData = async () => {
      try {
        const [
          entitiesRes,
          businessLinesRes,
          incidentTypesRes,
          businessProcessesRes,
          organizationalProcessesRes,
          productsRes,
          applicationsRes,
          controlsRes,
          risksRes
        ] = await Promise.all([
          axios.get(`${API_BASE_URL}/entities`, {
            withCredentials: true,
            headers: {
              'Content-Type': 'application/json',
              'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'include'
          }),
          axios.get(`${API_BASE_URL}/businessLines`, {
            withCredentials: true,
            headers: {
              'Content-Type': 'application/json',
              'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'include'
          }),
          axios.get(`${API_BASE_URL}/incidentTypes`, {
            withCredentials: true,
            headers: {
              'Content-Type': 'application/json',
              'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'include'
          }),
          axios.get(`${API_BASE_URL}/businessProcesses`, {
            withCredentials: true,
            headers: {
              'Content-Type': 'application/json',
              'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'include'
          }),
          axios.get(`${API_BASE_URL}/organizationalProcesses`, {
            withCredentials: true,
            headers: {
              'Content-Type': 'application/json',
              'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'include'
          }),
          axios.get(`${API_BASE_URL}/products`, {
            withCredentials: true,
            headers: {
              'Content-Type': 'application/json',
              'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'include'
          }),
          axios.get(`${API_BASE_URL}/applications`, {
            withCredentials: true,
            headers: {
              'Content-Type': 'application/json',
              'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'include'
          }),
          axios.get(`${API_BASE_URL}/controls`, {
            withCredentials: true,
            headers: {
              'Content-Type': 'application/json',
              'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'include'
          }),
          axios.get(`${API_BASE_URL}/risk`, {
            withCredentials: true,
            headers: {
              'Content-Type': 'application/json',
              'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'include'
          })
        ]);

        setReferenceData({
          entities: entitiesRes.data.success ? entitiesRes.data.data || [] : [],
          businessLines: businessLinesRes.data.success ? businessLinesRes.data.data || [] : [],
          incidentTypes: incidentTypesRes.data.success ? incidentTypesRes.data.data || [] : [],
          businessProcesses: businessProcessesRes.data.success ? businessProcessesRes.data.data || [] : [],
          organizationalProcesses: organizationalProcessesRes.data.success ? organizationalProcessesRes.data.data || [] : [],
          products: productsRes.data.success ? productsRes.data.data || [] : [],
          applications: applicationsRes.data.success ? applicationsRes.data.data || [] : [],
          controls: controlsRes.data.success ? controlsRes.data.data || [] : [],
          risks: risksRes.data.success ? risksRes.data.data || [] : []
        });
      } catch (error) {
        console.error('Error fetching reference data:', error);
        toast.error(t('admin.incidents.edit.reference_data_error', 'Failed to load reference data'));
      }
    };

    fetchReferenceData();
  }, [t]);

  // Fetch incident data on page load
  useEffect(() => {
    if (!id) return;

    const loadIncidentData = async () => {
      try {
        setLoading(true);
        setError(null);

        // console.log('Starting to load incident data for ID:', id);
        await fetchIncident(id);
        // console.log('Successfully loaded incident data, now fetching workflow state');
        await fetchWorkflowState(id);
        // console.log('All data loaded successfully');
      } catch (error) {
        console.error('Failed to load incident data:', error);
        setError(t('admin.incidents.edit.load_data_error', 'Failed to load incident data. Please refresh the page or contact support.'));
      } finally {
        setLoading(false);
      }
    };

    loadIncidentData();
  }, [id, t]);

  // Function to fetch workflow state
  const fetchWorkflowState = async (incidentId) => {
    try {
      // console.log('Fetching workflow state for incident:', incidentId);
      const response = await axios.get(`${API_BASE_URL}/incidents/${incidentId}/workflow/state`, {
        withCredentials: true
      });

      // console.log('Workflow state response status:', response.status);

      if (response.data && response.data.success) {
        // console.log('Workflow state:', response.data.data.current_state);
        setWorkflowState(response.data.data.current_state);
      } else {
        console.error('Failed to get workflow state:', response.data);
      }
    } catch (error) {
      console.error('Error fetching workflow state:', error);
      console.error('Workflow error details:', error.response?.data || 'No response data');
    }
  };

  // Fetch financial data
  const fetchFinancialData = async (incidentId) => {
    try {
      // console.log('Fetching financial data for incident:', incidentId);
      const financialResponse = await axios.get(`${API_BASE_URL}/financial-v2/incident/${incidentId}`, {
              withCredentials: true,
        headers: { 'Content-Type': 'application/json' },
      });

      // console.log('Financial data response status:', financialResponse.status);

            if (financialResponse.data.success) {
              const financialData = financialResponse.data.data;
        // console.log('Received financial data:', financialData);

              // Transform backend financial entries to match the frontend format
        const transformedData = {};
              Object.keys(financialData).forEach(category => {
          transformedData[category] = financialData[category].map(entry => {
                const idField = `${category.slice(0, -1)}ID`; // Convert 'losses' to 'lossID', etc.
            return {
                  id: entry[idField],
                  name: entry.name,
                  amount: Number(entry.amount) || 0,
                  localAmount: entry.localAmount ? Number(entry.localAmount) : 0,
                  currency: entry.currency || 'XOF'
            };
          });
        });

        // console.log("Transformed financial data:", transformedData);

        // Update incident state with fetched financial data
        setIncident(prev => {
          if (!prev) {
            console.error('Cannot update financial data - incident is null');
            return prev;
          }
          return {
            ...prev,
            losses: transformedData.losses || [],
            gains: transformedData.gains || [],
            financialRecoveries: transformedData.recoveries || [],
            financialProvisions: transformedData.provisions || []
          };
        });
      } else {
        console.error('Failed to get financial data:', financialResponse.data);
            }
          } catch (err) {
            console.error('Error fetching financial entries:', err);
      console.error('Financial error details:', err.response?.data || 'No response data');
    }
  };

  // Fetch incident data
  const fetchIncident = async (incidentId) => {
    try {
      // console.log('Starting incident fetch for ID:', incidentId);

      // Get auth headers with token
      const token = localStorage.getItem('authToken');
      const headers = {
        'Content-Type': 'application/json'
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
        // console.log('Using token for incident fetch');
        } else {
        // console.log('No token available for incident fetch');
      }

      const response = await axios.get(`${API_BASE_URL}/incidents/${incidentId}`, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      });

      // console.log('Incident fetch response status:', response.status);

      if (response.data.success) {
        // console.log('Incident fetch successful, processing data');
        // Format dates
        const incidentData = response.data.data;

        if (!incidentData) {
          throw new Error('No incident data returned from server');
        }

        if (incidentData.declarationDate) {
          incidentData.declarationDate = formatDate(incidentData.declarationDate);
        }
        if (incidentData.detectionDate) {
          incidentData.detectionDate = formatDate(incidentData.detectionDate);
        }
        if (incidentData.occurrenceDate) {
          incidentData.occurrenceDate = formatDate(incidentData.occurrenceDate);
        }

        // console.log('Incident data processed, setting state');
        setIncident(incidentData);

        // Fetch financial data
        await fetchFinancialData(incidentData.incidentID);

        return incidentData;
      } else {
        console.error('Server returned success: false for incident fetch:', response.data);
        throw new Error(response.data.message || 'Failed to fetch incident data');
      }
    } catch (err) {
      console.error('Error fetching incident:', err);
      console.error('Error details:', err.response?.data || 'No response data');
      throw err; // Re-throw to be caught by the caller
    }
  };

  const handleInputChange = (field, value) => {
    setIncident((prev) => {
      if (!prev) return prev;

      const newState = {
        ...prev,
        [field]: field === 'nearMiss'
          ? value === 'true' || value === true
          : ['grossLoss', 'recoveries', 'provisions'].includes(field)
            ? value === '' ? 0 : Number(value)
            : ['entityID', 'controlID', 'businessLineID', 'incidentTypeID', 'businessProcessID', 'organizationalProcessID', 'productID', 'applicationID', 'riskID'].includes(field)
              ? String(value)
              : value
      };
      return newState;
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitting || !incident) return;

    setIsSubmitting(true);
    try {
      const formattedIncident = {
        ...incident,
        nearMiss: Boolean(incident.nearMiss),
        declarationDate: incident.declarationDate ? new Date(incident.declarationDate).toISOString() : null,
        detectionDate: incident.detectionDate ? new Date(incident.detectionDate).toISOString() : null,
        occurrenceDate: incident.occurrenceDate ? new Date(incident.occurrenceDate).toISOString() : null,
        entityID: incident.entityID || null,
        businessLineID: incident.businessLineID || null,
        incidentTypeID: incident.incidentTypeID || null,
        businessProcessID: incident.businessProcessID || null,
        organizationalProcessID: incident.organizationalProcessID || null,
        productID: incident.productID || null,
        applicationID: incident.applicationID || null,
        controlID: incident.controlID || null,
        riskID: incident.riskID ? String(incident.riskID) : null,
        currency: incident.currency || 'XOF',
        grossLoss: incident.grossLoss ? Number(incident.grossLoss) : 0,
        recoveries: incident.recoveries ? Number(incident.recoveries) : 0,
        provisions: incident.provisions ? Number(incident.provisions) : 0
      };

      // Remove financial arrays before sending incident data
      const { ...incidentToSave } = formattedIncident;

      // Update the incident
      const response = await axios.put(`${API_BASE_URL}/incidents/${incident.incidentID}`, incidentToSave, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' },
      });

      // If incident update was successful, update financial entries
      if (response.data.success) {
        try {
          // console.log("Saving financial entries to API:", {
          //   losses: incident.losses || [],
          //   gains: incident.gains || [],
          //   recoveries: incident.financialRecoveries || [],
          //   provisions: incident.financialProvisions || []
          // });

          // Update financial entries using the new API endpoint
          const financialResponse = await axios.put(`${API_BASE_URL}/financial-v2/incident/${incident.incidentID}`, {
            losses: incident.losses || [],
            gains: incident.gains || [],
            recoveries: incident.financialRecoveries || [],
            provisions: incident.financialProvisions || []
          }, {
            withCredentials: true,
            headers: { 'Content-Type': 'application/json' },
          });

          // console.log("Financial entries save response:", financialResponse.data);

          if (financialResponse.data.success) {
            toast.success(t('admin.incidents.edit.financial_entries_updated', 'Financial entries updated successfully'));
          } else {
            toast.error(t('admin.incidents.edit.financial_entries_update_error', 'Failed to update financial entries: {0}'), {
              values: {
                error: financialResponse.data.message || 'No error message'
              }
            });
          }
        } catch (financialError) {
          console.error('Error updating financial entries:', financialError);
          toast.error(t('admin.incidents.edit.financial_entries_update_error_details', 'Incident was updated but there was an error updating financial entries: {0}'), {
            values: {
              error: financialError.response?.data?.message || financialError.message || 'No error details'
            }
          });
        }

        // Update incident data in state with the latest data from server
        const updatedIncident = response.data.data;

        // Format dates for display
        if (updatedIncident.declarationDate) {
          updatedIncident.declarationDate = formatDate(updatedIncident.declarationDate);
        }
        if (updatedIncident.detectionDate) {
          updatedIncident.detectionDate = formatDate(updatedIncident.detectionDate);
        }
        if (updatedIncident.occurrenceDate) {
          updatedIncident.occurrenceDate = formatDate(updatedIncident.occurrenceDate);
        }

        // Preserve the financial data in the state
        updatedIncident.losses = incident.losses || [];
        updatedIncident.gains = incident.gains || [];
        updatedIncident.financialRecoveries = incident.financialRecoveries || [];
        updatedIncident.financialProvisions = incident.financialProvisions || [];

        // Update the state with the formatted incident data
        setIncident(prev => ({
          ...prev,
          ...updatedIncident
        }));

        toast.success(t('admin.incidents.edit.incident_updated', 'Incident updated successfully'));

        // Fetch the latest financial data to ensure it's in sync with the database
        fetchFinancialData(incident.incidentID);

        // Navigate to overview tab after successful save
        const basePath = getBasePath();
        navigate(`${basePath}/incident/edit/${id}`);
      } else {
        toast.error(response.data.message || t('admin.incidents.edit.incident_update_error', 'Failed to update incident'));
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.response?.data?.error || t('admin.incidents.edit.incident_update_error', 'Failed to update incident');
      toast.error(errorMessage);
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Fetch contributors when incident is loaded
  useEffect(() => {
    const fetchContributors = async () => {
      if (!incident?.incidentID) return;

      setLoadingContributors(true);
      try {
        const response = await axios.get(`${API_BASE_URL}/incidents/${incident.incidentID}/contributors`, {
          withCredentials: true,
          headers: { "Content-Type": "application/json" }
        });

        if (response.data.success) {
          setContributors(response.data.data || []);
        }
      } catch (error) {
        console.error("Error loading contributors:", error);
      } finally {
        setLoadingContributors(false);
      }
    };

    if (incident?.incidentID) {
      fetchContributors();
    }
  }, [incident?.incidentID]);

  // Show loader while incident data is being fetched
  if (loading) {
    return (
      <div className="h-screen w-full flex items-center justify-center bg-white">
        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
      </div>
    );
  }

  // Show error if there was a problem fetching the incident
  if (error && !incident) {
    return (
      <div className="h-screen w-full flex flex-col items-center justify-center bg-white">
        <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
        <h2 className="text-xl font-semibold text-gray-800 mb-2">{t('admin.incidents.edit.error_loading_incident', 'Error Loading Incident')}</h2>
        <p className="text-gray-600">{error}</p>
        <Button
          className="mt-4 bg-[#F62D51] hover:bg-red-700"
          onClick={handleBack}
        >
          {t('admin.incidents.edit.back_to_incidents', 'Back to Incidents')}
        </Button>
      </div>
    );
  }

  // Render the content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-8">
            
            {/* Impact and Priority Section */}
            <div className="flex flex-col md:flex-row gap-3">
              {/* Impact Card */}
              <div className={`rounded-lg shadow-md overflow-hidden w-fit ${
                incident.impact === 'Very High' ? 'bg-gradient-to-br from-red-500 to-red-600' :
                incident.impact === 'High' ? 'bg-gradient-to-br from-orange-500 to-orange-600' :
                incident.impact === 'Medium' ? 'bg-gradient-to-br from-yellow-500 to-yellow-600' :
                incident.impact === 'Low' ? 'bg-gradient-to-br from-green-500 to-green-600' :
                incident.impact === 'Very Low' ? 'bg-gradient-to-br from-blue-500 to-blue-600' :
                'bg-gradient-to-br from-gray-500 to-gray-600'
              }`}>
                <div className="p-3">
                  <div className="flex items-center gap-2">
                    <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-white/20 flex items-center justify-center">
                      <AlertTriangle className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xs font-medium text-white/80">{t('admin.incidents.edit.impact', 'Impact')}</h3>
                      <div className="text-lg font-bold text-white">
                        {incident.impact || 'N/A'}
                      </div>
                    </div>
                    <div className="text-xs font-medium text-white/90 ml-2">
                      {incident.impact === 'Very High' ? 'Critical' :
                       incident.impact === 'High' ? 'Significant' :
                       incident.impact === 'Medium' ? 'Moderate' :
                       incident.impact === 'Low' ? 'Minimal' :
                       incident.impact === 'Very Low' ? 'Negligible' :
                       'Not specified'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Priority Card */}
              <div className={`rounded-lg shadow-md overflow-hidden w-fit ${
                incident.priority === 'High' ? 'bg-gradient-to-br from-red-500 to-red-600' :
                incident.priority === 'Medium' ? 'bg-gradient-to-br from-orange-500 to-orange-600' :
                incident.priority === 'Low' ? 'bg-gradient-to-br from-green-500 to-green-600' :
                'bg-gradient-to-br from-gray-500 to-gray-600'
              }`}>
                <div className="p-3">
                  <div className="flex items-center gap-2">
                    <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-white/20 flex items-center justify-center">
                      <AlertTriangle className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xs font-medium text-white/80">{t('admin.incidents.edit.priority', 'Priority')}</h3>
                      <div className="text-lg font-bold text-white">
                        {incident.priority || 'N/A'}
                      </div>
                    </div>
                    <div className="text-xs font-medium text-white/90 ml-2">
                      {incident.priority === 'High' ? 'Urgent' :
                       incident.priority === 'Medium' ? 'Normal' :
                       incident.priority === 'Low' ? 'Low' :
                       'Not specified'}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Key Info Section */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="bg-gray-50 px-4 py-3 border-b">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                  <Info className="h-5 w-5 mr-2 text-blue-500" />
                  {t('admin.incidents.edit.key_information', 'Key Information')}
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 p-4">
                <div className="flex items-start space-x-3">
                  <div className="bg-purple-50 p-2 rounded-full">
                    <FileText className="h-5 w-5 text-purple-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.name', 'Name')}</p>
                    <p className="text-base font-semibold">{incident.name || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-emerald-50 p-2 rounded-full">
                    <Bookmark className="h-5 w-5 text-emerald-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.status', 'Status')}</p>
                    <div className="flex flex-col space-y-1">
                      {incident.status && (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      incident.status === 'Open'
                        ? 'bg-green-100 text-green-800'
                        : incident.status === 'Closed'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                    }`}>
                          {incident.status}
                    </span>
                      )}

                      {workflowState && (
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          workflowState === 'Validated'
                            ? 'bg-green-100 text-green-800'
                            : workflowState === 'Rejected'
                              ? 'bg-red-100 text-red-800'
                              : workflowState === 'Closed'
                                ? 'bg-gray-100 text-gray-800'
                                : 'bg-blue-100 text-blue-800'
                        }`}>
                          {workflowState}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Contributors information */}
                <div className="flex items-start space-x-3">
                  <div className="bg-indigo-50 p-2 rounded-full">
                    <Users className="h-5 w-5 text-indigo-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.contributors', 'Contributors')}</p>
                    <div>
                      {loadingContributors ? (
                        <p className="text-sm text-gray-400">{t('admin.incidents.edit.loading_contributors', 'Loading contributors...')}</p>
                      ) : contributors.length > 0 ? (
                        <div className="flex flex-col space-y-1">
                          {contributors.slice(0, 3).map(contributor => (
                            <p key={contributor.id} className="text-sm">{contributor.contributor?.username || t('admin.incidents.edit.unknown_user', 'Unknown User')}</p>
                          ))}
                          {contributors.length > 3 && (
                            <p className="text-xs text-gray-500">+{contributors.length - 3} more</p>
                          )}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500">{t('admin.incidents.edit.none', 'None')}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Incident Details Section */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="bg-gray-50 px-4 py-3 border-b">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                  <Clock className="h-5 w-5 mr-2 text-indigo-500" />
                  {t('admin.incidents.edit.incident_details', 'Incident Details')}
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
                <div className="flex items-start space-x-3">
                  <div className="bg-amber-50 p-2 rounded-full">
                    <User className="h-5 w-5 text-amber-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.declared_by', 'Declared By')}</p>
                    <p className="text-base">{incident.declaredBy || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-green-50 p-2 rounded-full">
                    <Calendar className="h-5 w-5 text-green-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.declaration_date', 'Declaration Date')}</p>
                    <p className="text-base">{incident.declarationDate || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-teal-50 p-2 rounded-full">
                    <Building className="h-5 w-5 text-teal-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.declarant_entity', 'Declarant\'s Entity')}</p>
                    <p className="text-base">{incident.declarantEntity || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-sky-50 p-2 rounded-full">
                    <Clock className="h-5 w-5 text-sky-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.detection_date', 'Detection Date')}</p>
                    <p className="text-base">{incident.detectionDate || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-violet-50 p-2 rounded-full">
                    <Clock className="h-5 w-5 text-violet-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.occurrence_date', 'Occurrence Date')}</p>
                    <p className="text-base">{incident.occurrenceDate || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-fuchsia-50 p-2 rounded-full">
                    <Check className="h-5 w-5 text-fuchsia-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.near_miss', 'Near Miss')}</p>
                    <p className="text-base">{incident.nearMiss ? 'Yes' : 'No'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-lime-50 p-2 rounded-full">
                    <Lightbulb className="h-5 w-5 text-lime-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.nature', 'Nature')}</p>
                    <p className="text-base">{incident.nature || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-red-50 p-2 rounded-full">
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.impact', 'Impact')}</p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      incident.impact === 'Very High' ? 'bg-red-100 text-red-800' :
                      incident.impact === 'High' ? 'bg-orange-100 text-orange-800' :
                      incident.impact === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                      incident.impact === 'Low' ? 'bg-green-100 text-green-800' :
                      incident.impact === 'Very Low' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>{incident.impact || 'N/A'}</span>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-orange-50 p-2 rounded-full">
                    <AlertTriangle className="h-5 w-5 text-orange-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.priority', 'Priority')}</p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      incident.priority === 'High' ? 'bg-red-100 text-red-800' :
                      incident.priority === 'Medium' ? 'bg-orange-100 text-orange-800' :
                      incident.priority === 'Low' ? 'bg-green-100 text-green-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>{incident.priority || 'N/A'}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Financial Information Section */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="bg-gray-50 px-4 py-3 border-b">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                  <DollarSign className="h-5 w-5 mr-2 text-green-500" />
                  {t('admin.incidents.edit.financial_information', 'Financial Information')}
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 p-4">
                <div className="flex items-start space-x-3">
                  <div className="bg-emerald-50 p-2 rounded-full">
                    <CreditCard className="h-5 w-5 text-emerald-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.currency', 'Currency')}</p>
                    <p className="text-base">{incident.currency || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-red-50 p-2 rounded-full">
                    <DollarSign className="h-5 w-5 text-red-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.gross_loss', 'Gross Loss')}</p>
                    <p className="text-base font-medium">{incident.grossLoss?.toLocaleString() || '0'} {incident.currency}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-green-50 p-2 rounded-full">
                    <DollarSign className="h-5 w-5 text-green-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.recoveries', 'Recoveries')}</p>
                    <p className="text-base font-medium">{incident.recoveries?.toLocaleString() || '0'} {incident.currency}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-blue-50 p-2 rounded-full">
                    <DollarSign className="h-5 w-5 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.provisions', 'Provisions')}</p>
                    <p className="text-base font-medium">{incident.provisions?.toLocaleString() || '0'} {incident.currency}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* References Section */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="bg-gray-50 px-4 py-3 border-b">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                  <ExternalLink className="h-5 w-5 mr-2 text-purple-500" />
                  {t('admin.incidents.edit.references', 'References')}
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
                <div className="flex items-start space-x-3">
                  <div className="bg-indigo-50 p-2 rounded-full">
                    <GitBranchPlus className="h-5 w-5 text-indigo-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.action_plan', 'Action Plan')}</p>
                    <p className="text-base">{incident.actionPlan?.name || 'None'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-orange-50 p-2 rounded-full">
                    <AlertTriangle className="h-5 w-5 text-orange-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.risk', 'Risk')}</p>
                    <p className="text-base">{incident.risk?.name || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-teal-50 p-2 rounded-full">
                    <ShieldAlert className="h-5 w-5 text-teal-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.control', 'Control')}</p>
                    <p className="text-base">{incident.control?.name || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-blue-50 p-2 rounded-full">
                    <Building className="h-5 w-5 text-blue-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.entity', 'Entity')}</p>
                    <p className="text-base">{incident.entity?.name || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-green-50 p-2 rounded-full">
                    <Briefcase className="h-5 w-5 text-green-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.business_line', 'Business Line')}</p>
                    <p className="text-base">{incident.businessLine?.name || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-red-50 p-2 rounded-full">
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.incident_type', 'Incident Type')}</p>
                    <p className="text-base">{incident.incidentType?.name || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-amber-50 p-2 rounded-full">
                    <Workflow className="h-5 w-5 text-amber-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.business_process', 'Business Process')}</p>
                    <p className="text-base">{incident.businessProcess?.name || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-violet-50 p-2 rounded-full">
                    <Workflow className="h-5 w-5 text-violet-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.organizational_process', 'Organizational Process')}</p>
                    <p className="text-base">{incident.organizationalProcess?.name || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-sky-50 p-2 rounded-full">
                    <Package className="h-5 w-5 text-sky-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.product', 'Product')}</p>
                    <p className="text-base">{incident.product?.name || 'N/A'}</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-indigo-50 p-2 rounded-full">
                    <Database className="h-5 w-5 text-indigo-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{t('admin.incidents.edit.application', 'Application')}</p>
                    <p className="text-base">{incident.application?.name || 'N/A'}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Description Section */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="bg-gray-50 px-4 py-3 border-b">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-gray-500" />
                  {t('admin.incidents.edit.description', 'Description')}
                </h3>
              </div>

              <div className="p-4">
                <p className="text-base text-gray-700 whitespace-pre-line">{incident.description || t('admin.incidents.edit.no_description', 'No description provided')}</p>
              </div>
            </div>

            {/* Attachments Section */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="bg-gray-50 px-4 py-3 border-b">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                  <Paperclip className="h-5 w-5 mr-2 text-gray-500" />
                  {t('admin.incidents.edit.attachments', 'Attachments')}
                </h3>
              </div>

              <div className="p-4">
                <AttachmentsSection incident={incident} />
              </div>
            </div>
          </div>
        );
      case 'features':
        return (
          <CharacteristicsSection
            incident={incident}
            handleInputChange={handleInputChange}
            entities={referenceData.entities}
            businessLines={referenceData.businessLines}
            businessProcesses={referenceData.businessProcesses}
            organizationalProcesses={referenceData.organizationalProcesses}
            products={referenceData.products}
            applications={referenceData.applications}
            controls={referenceData.controls}
            risks={referenceData.risks}
            incidentTypes={referenceData.incidentTypes}
          />
        );
      case 'financial':
        return (
          <FinancialAnalysisSectionV2
            incident={incident}
            handleInputChange={handleInputChange}
          />
        );
      case 'action-plan':
        return (
          <ActionPlanSection
            incident={incident}
            handleInputChange={handleInputChange}
          />
        );
      case 'activity':
        return (
          <ActivityFeedSection
            incident={incident}
            handleInputChange={handleInputChange}
          />
        );
      case 'workflow':
        return (
          <WorkflowSection />
        );
      default:
        return <div>{t('admin.incidents.edit.select_tab', 'Select a tab')}</div>;
    }
  };

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      {/* Back button at the top */}
      <div className="mb-4">
        <Button
          variant="outline"
          className="flex items-center gap-2"
          onClick={handleBack}
        >
          <ChevronLeft className="h-4 w-4" />
          {t('admin.incidents.edit.back_to_incidents', 'Back to Incidents')}
        </Button>
      </div>

      {/* Page Header */}
      <PageHeader
        title={t('admin.incidents.edit.edit_incident', 'Edit Incident')}
        description={t('admin.incidents.edit.modify_incident_details', 'Modify incident details, update impact assessment, and adjust related information as needed.')}
        section={t('admin.incidents.edit.incident', 'Incident')}
        currentPage={t('admin.incidents.edit.edit', 'Edit')}
        icon={AlertTriangle}
      />

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-800 rounded-lg">
          {error}
        </div>
      )}

      {/* Tab Bar Outside Content Area */}
      <TabBar
        activeTab={activeTab}
        tabs={tabs}
        onTabChange={navigateToTab}
        className="mb-6 mt-6"
      />

      {/* Content Area */}
      <div className="space-y-6 bg-white rounded-lg p-6 shadow-sm">
        {/* Section Content */}
        <div className="transition-all duration-300">
          {renderTabContent()}
        </div>

        {/* Form buttons - Only show in features tab */}
        {activeTab === 'features' && (
          <div className="flex justify-end gap-4 pt-6 mt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleBack}
              className="px-6"
            >
              {t('admin.incidents.edit.cancel', 'Cancel')}
            </Button>
            <Button
              type="button"
              onClick={handleSubmit}
              className="bg-[#F62D51] hover:bg-red-700 px-6"
              disabled={isSubmitting}
            >
              {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              {t('admin.incidents.edit.save_changes', 'Save Changes')}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

export default EditIncident;
