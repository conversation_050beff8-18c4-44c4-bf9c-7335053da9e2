# Internationalization (i18n) Implementation Guide for Vitalis Project

## Overview

This guide explains how to implement internationalization (i18n) in the Vitalis project, focusing on translating React components from English to French. The project uses the `react-i18next` library for handling translations.

## Translation Files Structure

- **English translations**: `frontend/src/locales/en/translation.json`
- **French translations**: `frontend/src/locales/fr/translation.json`
- Both files should maintain the same structure and key hierarchy

## Key Structure

- Keys are organized hierarchically (e.g., `admin.controls.features.title`)
- Follow the pattern: `[section].[subsection].[component].[element]`
- Example: `admin.controls.overview.basic_information`

## Implementation Process

1. Identify all text strings in a component that need translation
2. Add translation keys to both English and French translation files
3. Replace hardcoded text with `t()` function calls in the component
4. Test the component in both languages

## Using the Translation Function

1. Import the hook: `import { useTranslation } from "react-i18next";`
2. Initialize in component: `const { t } = useTranslation();`
3. Replace text: `<span>{t('admin.controls.overview.title', 'Control Overview')}</span>`
   - The second parameter is the default text (fallback if key not found)

## Best Practices

- Keep translation keys consistent across similar components
- Use the same key structure for related elements
- Always provide a default English text as fallback
- Translate all UI elements including buttons, labels, placeholders, and error messages
- When translating a new file, update both English and French translation files

## Files to Modify

### Component Files

- Add `useTranslation` import
- Initialize the hook in the component
- Replace all hardcoded text with `t()` function calls

### Translation Files

- Add new keys to both English and French translation files
- Maintain the same structure in both files
- Ensure proper nesting of keys

## Common Translation Patterns

### Section Titles

- `admin.[section].[subsection].title`

### Form Fields

- Labels: `admin.[section].[subsection].form.[field_name]`
- Placeholders: `admin.[section].[subsection].form.[field_name]_placeholder`

### Buttons

- `admin.[section].[subsection].buttons.[action]`

### Messages

- Success: `admin.[section].[subsection].success.[action]`
- Error: `admin.[section].[subsection].error.[action]`

### Dialogs

- Title: `admin.[section].[subsection].dialog.[dialog_name]_title`
- Description: `admin.[section].[subsection].dialog.[dialog_name]_description`

## Example Implementation

```jsx
// Before translation
import React from 'react';

function ControlOverview() {
  return (
    <div>
      <h2>Control Overview</h2>
      <p>Basic Information</p>
      <button>Save Changes</button>
    </div>
  );
}

// After translation
import React from 'react';
import { useTranslation } from "react-i18next";

function ControlOverview() {
  const { t } = useTranslation();

  return (
    <div>
      <h2>{t('admin.controls.overview.title', 'Control Overview')}</h2>
      <p>{t('admin.controls.overview.basic_information', 'Basic Information')}</p>
      <button>{t('admin.controls.overview.buttons.save', 'Save Changes')}</button>
    </div>
  );
}
```

## Translation JSON Example

```json
// English (translation.json)
{
  "admin": {
    "controls": {
      "overview": {
        "title": "Control Overview",
        "basic_information": "Basic Information",
        "buttons": {
          "save": "Save Changes"
        }
      }
    }
  }
}

// French (translation.json)
{
  "admin": {
    "controls": {
      "overview": {
        "title": "Aperçu du Contrôle",
        "basic_information": "Informations de Base",
        "buttons": {
          "save": "Enregistrer les Modifications"
        }
      }
    }
  }
}
```

## Testing

After implementing translations:

1. Switch between languages using the language selector
2. Verify all text elements are properly translated
3. Check for any missing translations or formatting issues
4. Ensure placeholders and error messages are also translated

## Methodical Approach for Large Files

When translating large files with multiple components:

1. Translate one section at a time
2. Update both English and French translation files for each section
3. Test each section before moving to the next
4. Use consistent key naming across related components

## Common Translations for Vitalis

### Common UI Elements

- Buttons: "Save", "Cancel", "Delete", "Edit", "Add", "Close"
- Status: "Active", "Inactive", "Pending", "Completed"
- Messages: "Successfully saved", "Error occurred", "Are you sure?"

### Domain-Specific Terms

- "Risk", "Control", "Incident", "Action Plan", "Workflow"
- "Assessment", "Validation", "Rejection", "Approval"
- "Contributors", "Participants", "Activity", "Timeline"

Remember to maintain consistency in terminology across the application.