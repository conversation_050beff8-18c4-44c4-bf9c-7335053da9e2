const { Reference } = require('../../models');

// Get all references for an incident
const getReferences = async (req, res) => {
  try {
    const { incidentID } = req.query;
    
    if (!incidentID) {
      return res.status(400).json({
        success: false,
        message: 'Incident ID is required'
      });
    }

    const references = await Reference.findAll({
      where: { incidentID },
      order: [['createdAt', 'DESC']]
    });

    return res.json({
      success: true,
      data: references
    });
  } catch (error) {
    console.error('Error getting references:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to get references'
    });
  }
};

// Create a new reference
const createReference = async (req, res) => {
  try {
    const { incidentID, url, description } = req.body;

    if (!incidentID) {
      return res.status(400).json({
        success: false,
        message: 'Incident ID is required'
      });
    }

    if (!url) {
      return res.status(400).json({
        success: false,
        message: 'URL is required'
      });
    }

    // Ensure URL has protocol prefix
    let formattedUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      formattedUrl = `https://${url}`;
    }

    // Extract hostname for name if no description is provided
    let name;
    try {
      const urlObj = new URL(formattedUrl);
      name = urlObj.hostname;
    } catch (e) {
      name = 'External link';
    }

    const reference = await Reference.create({
      incidentID,
      url: formattedUrl,
      name,
      description: description || null
    });

    return res.json({
      success: true,
      data: reference,
      message: 'Reference created successfully'
    });
  } catch (error) {
    console.error('Error creating reference:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create reference'
    });
  }
};

// Delete a reference
const deleteReference = async (req, res) => {
  try {
    const { id } = req.params;

    const reference = await Reference.findByPk(id);

    if (!reference) {
      return res.status(404).json({
        success: false,
        message: 'Reference not found'
      });
    }

    await reference.destroy();

    return res.json({
      success: true,
      message: 'Reference deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting reference:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to delete reference'
    });
  }
};

module.exports = {
  getReferences,
  createReference,
  deleteReference
}; 