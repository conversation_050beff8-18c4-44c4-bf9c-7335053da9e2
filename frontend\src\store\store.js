import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./auth-slice";
import referenceDataReducer from "./reference-data-slice";
import actionPlanReducer from "./slices/actionPlanSlice";
import actionReducer from "./slices/actionSlice";
import riskReducer from "./slices/riskSlice";
import userReducer from "./slices/userSlice";
import controlReducer from "./slices/controlSlice";
import controlTypeReducer from "./slices/controlTypeSlice";
import businessProcessReducer from "./slices/businessProcessSlice";
import organizationalProcessReducer from "./slices/organizationalProcessSlice";
import operationReducer from "./slices/operationSlice";
import applicationReducer from "./slices/applicationSlice";
import entityReducer from "./slices/entitySlice";
import businessLineReducer from "./slices/businessLineSlice";
import auditPlanReducer from "./slices/auditPlanSlice";
import auditPlanCharacteristicsReducer from './slices/auditPlanCharacteristicsSlice';
import { auditMissionsReducer } from './slices/audit';

const store = configureStore({
    reducer: {
        auth: authReducer,
        referenceData: referenceDataReducer,
        actionPlan: actionPlanReducer,
        action: actionReducer,
        risk: riskReducer,
        user: userReducer,
        control: controlReducer,
        controlType: controlTypeReducer,
        businessProcess: businessProcessReducer,
        organizationalProcess: organizationalProcessReducer,
        operation: operationReducer,
        application: applicationReducer,
        entity: entityReducer,
        businessLine: businessLineReducer,
        auditPlans: auditPlanReducer,
        auditPlanCharacteristics: auditPlanCharacteristicsReducer,
        auditMissions: auditMissionsReducer,
    },
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            serializableCheck: false,
        }),
});

export default store;