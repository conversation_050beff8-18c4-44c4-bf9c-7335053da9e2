import { useState } from "react";
import { Calendar as CalendarIcon, X } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Button } from "./button";
import { Calendar } from "./calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "./popover";
import { Separator } from "./separator";

export function DateRangePicker({
  className,
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  onClear,
}) {
  const [isOpen, setIsOpen] = useState(false);

  // Format the date range for display
  const formatDateRange = () => {
    try {
      if (startDate && endDate) {
        return `${format(startDate, "MMM d, yyyy")} - ${format(endDate, "MMM d, yyyy")}`;
      }
      if (startDate) {
        return `From ${format(startDate, "MMM d, yyyy")}`;
      }
      if (endDate) {
        return `Until ${format(endDate, "MMM d, yyyy")}`;
      }
      return "Select date range";
    } catch (error) {
      console.error("Error formatting date range:", error);
      return "Select date range";
    }
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            size="sm"
            className={cn(
              "justify-between text-left font-normal h-9",
              !startDate && !endDate && "text-muted-foreground"
            )}
          >
            <div className="flex items-center gap-2 truncate">
              <CalendarIcon className="h-3.5 w-3.5 text-muted-foreground" />
              <span className="truncate">{formatDateRange()}</span>
            </div>
            {(startDate || endDate) && (
              <X
                className="h-3.5 w-3.5 text-muted-foreground hover:text-foreground"
                onClick={(e) => {
                  e.stopPropagation();
                  onClear();
                }}
              />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex flex-col sm:flex-row gap-2 p-3">
            <div className="space-y-2">
              <div className="text-sm font-medium">Start Date</div>
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={onStartDateChange}
                initialFocus
              />
            </div>
            <Separator className="sm:h-auto" orientation="vertical" />
            <div className="space-y-2">
              <div className="text-sm font-medium">End Date</div>
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={onEndDateChange}
                initialFocus
                disabled={(date) => startDate && date < startDate}
              />
            </div>
          </div>
          <div className="flex items-center justify-end gap-2 p-3 pt-0">
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                onClear();
                setIsOpen(false);
              }}
            >
              Clear
            </Button>
            <Button
              size="sm"
              onClick={() => setIsOpen(false)}
            >
              Apply
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
