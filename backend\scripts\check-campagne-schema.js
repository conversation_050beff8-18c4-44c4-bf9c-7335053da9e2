const { Sequelize } = require('sequelize');
require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });

// Create Sequelize instance
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: console.log
  }
);

async function checkCampagneSchema() {
  try {
    console.log('🔍 Checking Campagne table schema...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Check if Campagnes table exists
    const [tableExists] = await sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'Campagnes'
    `);

    if (tableExists.length === 0) {
      console.log('❌ Campagnes table does not exist');
      return;
    }

    console.log('✅ Campagnes table exists');

    // Get all columns in Campagnes table
    const [columns] = await sequelize.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'Campagnes' 
      ORDER BY ordinal_position
    `);

    console.log('📋 Campagnes table columns:');
    columns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });

    // Check specifically for controlId column
    const hasControlId = columns.some(col => col.column_name === 'controlId');
    console.log(`\n🔍 controlId column exists: ${hasControlId ? '✅ YES' : '❌ NO'}`);

    // Get sample data from Campagnes table
    const [sampleData] = await sequelize.query(`
      SELECT * FROM "Campagnes" LIMIT 3
    `);

    console.log('\n📊 Sample campagne data:');
    sampleData.forEach((campagne, index) => {
      console.log(`  Campagne ${index + 1}:`, {
        campagneID: campagne.campagneID,
        name: campagne.name,
        code: campagne.code,
        description: campagne.description ? campagne.description.substring(0, 50) + '...' : null,
        statut: campagne.statut,
        controlId: campagne.controlId,
        createdAt: campagne.createdAt,
        updatedAt: campagne.updatedAt
      });
    });

    if (!hasControlId) {
      console.log('\n⚠️  controlId column is missing. Running migration...');
      
      // Try to add the column
      try {
        await sequelize.query(`
          ALTER TABLE "Campagnes" 
          ADD COLUMN "controlId" VARCHAR(255)
        `);
        console.log('✅ controlId column added successfully');
        
        // Add index
        await sequelize.query(`
          CREATE INDEX IF NOT EXISTS "campagnes_control_id_index" 
          ON "Campagnes" ("controlId")
        `);
        console.log('✅ Index added for controlId column');
        
      } catch (alterError) {
        if (alterError.message.includes('already exists')) {
          console.log('⚠️  controlId column already exists');
        } else {
          console.error('❌ Error adding controlId column:', alterError.message);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error checking schema:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
    console.log('\nDatabase connection closed');
  }
}

// Run the check
checkCampagneSchema();
