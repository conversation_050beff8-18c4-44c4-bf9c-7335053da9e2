import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

const API_BASE_URL = getApiBaseUrl();

// Fetch DMR (Risk Control Level) options
export const useDMROptions = () => {
  return useQuery({
    queryKey: ['dmrOptions'],
    queryFn: async () => {
      // This is a static list, so we can return it directly
      return [
        { value: '1', label: 'Very Strong' },
        { value: '4', label: 'Strong' },
        { value: '9', label: 'Medium' },
        { value: '16', label: 'Weak' },
        { value: '25', label: 'Very Weak' }
      ];
    },
    staleTime: Infinity, // This data never changes
  });
};

// Fetch risk levels
export const useRiskLevels = () => {
  return useQuery({
    queryKey: ['riskLevels'],
    queryFn: async () => {
      // This is a static list, so we can return it directly
      return [
        { value: '1', label: 'Very Low' },
        { value: '2', label: 'Low' },
        { value: '3', label: 'Medium' },
        { value: '4', label: 'High' },
        { value: '5', label: 'Very High' }
      ];
    },
    staleTime: Infinity, // This data never changes
  });
};

// Fetch incident types
export const useIncidentTypes = () => {
  return useQuery({
    queryKey: ['incidentTypes'],
    queryFn: async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/incidentTypes`, {
          withCredentials: true,
          headers: { 'x-user-id': '1' }
        });
        
        // Transform the data to the format we need
        const incidentTypes = response.data.data || [];
        return incidentTypes.map(type => ({
          value: type.incidentTypeID.toString(),
          label: type.name || `Type ${type.incidentTypeID}`
        }));
      } catch (error) {
        console.error('Error fetching incident types:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Fetch incidents
export const useIncidents = () => {
  return useQuery({
    queryKey: ['incidents'],
    queryFn: async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/incidents`, {
          withCredentials: true,
          headers: { 'x-user-id': '1' }
        });
        
        console.log("Incidents API response:", response.data);
        
        // Transform the data to the format we need
        const incidents = response.data.data || [];
        
        // Make sure we have valid data
        if (!Array.isArray(incidents)) {
          console.error("Incidents data is not an array:", incidents);
          return [];
        }
        
        const formattedIncidents = incidents.map(incident => ({
          value: incident.incidentID?.toString() || incident.id?.toString(),
          label: incident.name || `Incident ${incident.incidentID || incident.id || 'Unknown'}`
        }));
        
        console.log("Formatted incidents for dropdown:", formattedIncidents);
        return formattedIncidents;
      } catch (error) {
        console.error('Error fetching incidents:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Fetch entities
export const useEntities = () => {
  return useQuery({
    queryKey: ['entities'],
    queryFn: async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/entities`, {
          withCredentials: true,
          headers: { 'x-user-id': '1' }
        });
        
        // Transform the data to the format we need
        const entities = response.data.data || [];
        return entities.map(entity => ({
          value: entity.entityID.toString(),
          label: entity.name || `Entity ${entity.entityID}`
        }));
      } catch (error) {
        console.error('Error fetching entities:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Fetch business processes
export const useBusinessProcesses = () => {
  return useQuery({
    queryKey: ['businessProcesses'],
    queryFn: async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/businessProcesses`, {
          withCredentials: true,
          headers: { 'x-user-id': '1' }
        });
        
        // Transform the data to the format we need
        const processes = response.data.data || [];
        return processes.map(process => ({
          value: process.businessProcessID.toString(),
          label: process.name || `Process ${process.businessProcessID}`
        }));
      } catch (error) {
        console.error('Error fetching business processes:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Fetch organizational processes
export const useOrganizationalProcesses = () => {
  return useQuery({
    queryKey: ['organizationalProcesses'],
    queryFn: async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/organizationalProcesses`, {
          withCredentials: true,
          headers: { 'x-user-id': '1' }
        });
        
        // Transform the data to the format we need
        const processes = response.data.data || [];
        return processes.map(process => ({
          value: process.organizationalProcessID.toString(),
          label: process.name || `Process ${process.organizationalProcessID}`
        }));
      } catch (error) {
        console.error('Error fetching organizational processes:', error);
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Fetch risks filtered by DMR
export const useRisksByDMR = (dmrValues = []) => {
  return useQuery({
    queryKey: ['risksByDMR', dmrValues],
    queryFn: async () => {
      try {
        // Skip if no DMR values are selected
        if (!dmrValues.length) return [];
        
        const response = await axios.get(`${API_BASE_URL}/risk`, {
          withCredentials: true,
          headers: { 'x-user-id': '1' }
        });
        
        const risks = response.data.data || [];
        
        // Filter risks by DMR values
        return risks.filter(risk => {
          const dmr = parseInt(risk.DMR);
          return dmrValues.includes(dmr.toString());
        });
      } catch (error) {
        console.error('Error fetching risks by DMR:', error);
        return [];
      }
    },
    enabled: dmrValues.length > 0, // Only run the query if DMR values are selected
  });
};
