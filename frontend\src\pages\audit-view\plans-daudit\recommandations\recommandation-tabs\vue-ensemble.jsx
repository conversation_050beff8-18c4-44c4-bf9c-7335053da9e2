import React, { useState, useEffect } from "react";
import {
  ChevronUp,
  ChevronDown,
  FileText,
  AlertCircle,
  Clock,
  CheckCircle2,
  XCircle,
  BarChart,
  Target,
  User,
  UserCircle,
  Tag,
  Info,
  Calendar,
  Loader2
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useCustomOutletContext } from "../edit-recommandation";

function VueEnsembleTab() {
  const { recommandation } = useCustomOutletContext();
  const [isIdentificationOpen, setIsIdentificationOpen] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [error] = useState(null);

  // Calculate KPIs based on real data
  const calculateKPIs = () => {
    // For now, we'll use placeholder values since we don't have actual progress data
    // TODO: Implement real KPI calculations when the backend provides this data
    return {
    avancement: {
        value: 0, // This should come from the action plan progress
      label: "Avancement",
      icon: <BarChart className="h-4 w-4" />,
      color: "from-blue-500 to-blue-600"
    },
    timing: {
        value: calculateTimingScore(),
      label: "Timing",
      icon: <Clock className="h-4 w-4" />,
      color: "from-green-500 to-green-600"
    }
  };
  };

  // Calculate timing score based on planned vs actual dates
  const calculateTimingScore = () => {
    if (!recommandation?.actionPlan) return 0;

    const plan = recommandation.actionPlan;
    const now = new Date();
    const plannedEnd = plan.plannedEndDate ? new Date(plan.plannedEndDate) : null;
    
    if (!plannedEnd) return 0;

    // If we're past the planned end date, score is 0
    if (now > plannedEnd) return 0;

    // Calculate percentage of time remaining
    const totalDuration = plannedEnd - new Date(plan.plannedBeginDate);
    const remainingDuration = plannedEnd - now;
    const score = Math.round((remainingDuration / totalDuration) * 100);

    return Math.max(0, Math.min(100, score));
  };

  // Format date to French locale
  const formatDate = (dateString) => {
    if (!dateString) return 'Non définie';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Priority color mapping
  const prioriteColors = {
    'très fort': 'bg-red-100 text-red-800',
    'fort': 'bg-orange-100 text-orange-800',
    'moyen': 'bg-yellow-100 text-yellow-800',
    'faible': 'bg-green-100 text-green-800',
    'très faible': 'bg-blue-100 text-blue-800'
  };

  // Check if recommendation is delayed
  const isDelayed = () => {
    if (!recommandation?.actionPlan?.plannedEndDate) return false;
    const plannedEnd = new Date(recommandation.actionPlan.plannedEndDate);
    return new Date() > plannedEnd;
  };

  // Helper to get responsable name from EquipeIntervenante
  const getResponsableName = () => {
    if (recommandation && Array.isArray(recommandation.equipeIntervenantes) && recommandation.equipeIntervenantes.length > 0) {
      const eq = recommandation.equipeIntervenantes[0];
      return eq.user?.username || eq.user?.email || 'Non assigné';
    }
    return 'Non assigné';
  };

  useEffect(() => {
    if (recommandation) {
      setIsLoading(false);
    }
  }, [recommandation]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Erreur de chargement</h3>
        <p className="text-gray-600">{error}</p>
      </div>
    );
  }

  const kpis = calculateKPIs();
  const isRecommendationDelayed = isDelayed();

  return (
    <div className="space-y-6 py-4">
      {/* KPIs Section */}
      <div className="flex flex-wrap gap-4 mb-6">
        {Object.entries(kpis).map(([key, kpi]) => (
          <div key={key} className={`rounded-lg shadow-md overflow-hidden bg-gradient-to-br ${kpi.color} w-52`}>
            <div className="p-2 text-white">
              <div className="flex items-center gap-2">
                {kpi.icon}
                <h3 className="text-xs font-medium">{kpi.label}</h3>
              </div>
              <div className="mt-1 text-xl font-bold">{kpi.value}%</div>
              <div className="w-full bg-white/30 h-1.5 rounded-full mt-1 mb-1">
                <div
                  className="h-full bg-white rounded-full"
                  style={{ width: `${kpi.value}%` }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Identification Section */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsIdentificationOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isIdentificationOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <FileText className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">Identification</span>
          </div>
        </button>
        {isIdentificationOpen && (
          <div className="p-5 bg-white">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-start space-x-3">
                  <div className="bg-emerald-50 p-2 rounded-full"><FileText className="h-5 w-5 text-emerald-500" /></div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Nom</p>
                  <p className="text-base font-semibold">{recommandation.name}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="bg-purple-50 p-2 rounded-full"><Tag className="h-5 w-5 text-purple-500" /></div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Code</p>
                  <p className="text-base font-semibold">{recommandation.code || 'Non défini'}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="bg-pink-50 p-2 rounded-full"><UserCircle className="h-5 w-5 text-pink-500" /></div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Responsable</p>
                  <p className="text-base font-semibold">{getResponsableName()}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="bg-yellow-50 p-2 rounded-full"><User className="h-5 w-5 text-yellow-500" /></div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Correspondant</p>
                  <p className="text-base font-semibold">{recommandation.actionPlan?.assignee?.username || 'Non assigné'}</p>
                </div>
              </div>
            </div>
            <div className="space-y-4 mt-6">
                <div className="flex items-start space-x-3">
                  <div className="bg-orange-50 p-2 rounded-full"><AlertCircle className="h-5 w-5 text-orange-500" /></div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Priorité</p>
                    <div className="mt-1">
                    <Badge className={prioriteColors[recommandation.priorite]}>
                      {recommandation.priorite}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="bg-red-50 p-2 rounded-full">
                  {isRecommendationDelayed ? <XCircle className="h-5 w-5 text-red-500" /> : <CheckCircle2 className="h-5 w-5 text-green-500" />}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">En retard</p>
                    <div className="mt-1">
                    {isRecommendationDelayed ? (
                        <Badge className="bg-red-100 text-red-800">
                          <XCircle className="h-4 w-4 mr-1" /> En retard
                        </Badge>
                      ) : (
                        <Badge className="bg-green-100 text-green-800">
                          <CheckCircle2 className="h-4 w-4 mr-1" /> À jour
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="bg-blue-50 p-2 rounded-full"><Calendar className="h-5 w-5 text-blue-500" /></div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Date limite</p>
                  <p className="text-base font-semibold">{formatDate(recommandation.actionPlan?.plannedEndDate)}</p>
                </div>
              </div>
            </div>

            {/* Description in full width at the bottom */}
            <div className="mt-6 pt-6 border-t">
              <div className="flex items-start space-x-3">
                <div className="bg-amber-50 p-2 rounded-full"><FileText className="h-5 w-5 text-amber-500" /></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-500 mb-2">Description</p>
                  <div className="p-4 bg-amber-50 border border-amber-100 rounded-lg">
                    <p className="text-gray-800">{recommandation.description || 'Aucune description disponible'}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Details section */}
            {recommandation.details && (
              <div className="mt-6 pt-6 border-t">
                <div className="flex items-start space-x-3">
                  <div className="bg-indigo-50 p-2 rounded-full"><Info className="h-5 w-5 text-indigo-500" /></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-500 mb-2">Détails</p>
                    <div className="p-4 bg-indigo-50 border border-indigo-100 rounded-lg">
                      <p className="text-gray-800">{recommandation.details}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Planification section */}
            {recommandation.planification && (
              <div className="mt-6 pt-6 border-t">
                <div className="flex items-start space-x-3">
                  <div className="bg-teal-50 p-2 rounded-full"><Target className="h-5 w-5 text-teal-500" /></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-500 mb-2">Planification</p>
                    <div className="p-4 bg-teal-50 border border-teal-100 rounded-lg">
                      <p className="text-gray-800">{recommandation.planification}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default VueEnsembleTab;