// backend/models/User.js
module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    username: {
      type: DataTypes.STRING,
      allowNull: false
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false
    }
    // Role field has been removed - now using many-to-many relationship with Role model
  }, {
    tableName: 'Users',
    freezeTableName: true,
    timestamps: true
  });

  User.associate = (models) => {
    // User has many Roles through UserRole
    User.belongsToMany(models.Role, {
      through: 'UserRole',
      foreignKey: 'userId',
      otherKey: 'roleId',
      as: 'roles'
    });

    // User has many Activities
    User.hasMany(models.Activity, {
      foreignKey: 'userId',
      as: 'activities'
    });

    // User has many Actions (as assignee)
    User.hasMany(models.Action, {
      foreignKey: 'assigneeId',
      as: 'assignedActions'
    });

    // User has many Risks as contributor through RiskContributor
    if (models.Risk) {
      User.belongsToMany(models.Risk, {
        through: models.RiskContributor,
        foreignKey: 'user_id',
        otherKey: 'risk_id',
        as: 'contributedRisks'
      });

      // RiskContributor relationships
      User.hasMany(models.RiskContributor, {
        foreignKey: 'user_id',
        as: 'riskContributions'
      });

      User.hasMany(models.RiskContributor, {
        foreignKey: 'assigned_by',
        as: 'riskAssignments'
      });
    }

    // User has many Incidents as contributor through IncidentContributor
    if (models.Incident) {
      User.belongsToMany(models.Incident, {
        through: models.IncidentContributor,
        foreignKey: 'user_id',
        otherKey: 'incident_id',
        as: 'contributedIncidents'
      });

      // IncidentContributor relationships
      User.hasMany(models.IncidentContributor, {
        foreignKey: 'user_id',
        as: 'incidentContributions'
      });

      User.hasMany(models.IncidentContributor, {
        foreignKey: 'assigned_by',
        as: 'incidentAssignments'
      });
    }

    // User has many Controls through ControlQuestionAssignment
    if (models.Control && models.ControlQuestionAssignment) {
      User.belongsToMany(models.Control, {
        through: models.ControlQuestionAssignment,
        foreignKey: 'userId',
        otherKey: 'controlId',
        as: 'assignedControls'
      });

      // Direct associations with ControlQuestionAssignment
      User.hasMany(models.ControlQuestionAssignment, {
        foreignKey: 'userId',
        as: 'controlAssignments'
      });

      User.hasMany(models.ControlQuestionAssignment, {
        foreignKey: 'assignedBy',
        as: 'controlAssignmentsMade'
      });
    }

    // User has many Campagnes through UserCampagne
    if (models.Campagne && models.UserCampagne) {
      User.belongsToMany(models.Campagne, {
        through: models.UserCampagne,
        foreignKey: 'userID',
        otherKey: 'campagneID',
        as: 'assignedCampagnes'
      });

      User.hasMany(models.UserCampagne, {
        foreignKey: 'userID',
        as: 'campagneAssignments'
      });

      User.hasMany(models.UserCampagne, {
        foreignKey: 'assignedBy',
        as: 'campagneAssignmentsMade'
      });
    }
  };

  // Instance method to check if user has a specific role
  User.prototype.hasRole = function(roleCode) {
    return this.roles && this.roles.some(role => role.code === roleCode);
  };

  // Instance method to get primary role
  User.prototype.getPrimaryRole = function() {
    if (!this.roles || this.roles.length === 0) {
      return 'grc_contributor'; // Default role
    }

    // Priority order: GRC Administrator > GRC Manager > Other specialized roles > GRC Contributor
    const rolePriority = {
      'grc_admin': 1,
      'grc_manager': 2,
      'risk_manager': 3,
      'incident_manager': 4,
      'internal_controller': 5,
      'compliance_manager': 6,
      'audit_director': 7,
      'auditor': 8,
      'grc_contributor': 9
    };

    // Sort roles by priority and return the highest priority role code
    const sortedRoles = [...this.roles].sort((a, b) =>
      (rolePriority[a.code] || 999) - (rolePriority[b.code] || 999)
    );

    return sortedRoles[0].code;
  };

  // Instance method to get legacy role (for backward compatibility)
  User.prototype.getLegacyRole = function() {
    const primaryRole = this.getPrimaryRole();

    // Map new role codes to old role names for backward compatibility
    const roleCodeMap = {
      'grc_admin': 'super_admin',
      'grc_manager': 'admin',
      'risk_manager': 'admin', // Map Risk Manager to admin for backward compatibility
      'incident_manager': 'admin', // Map Incident Manager to admin for backward compatibility
      'internal_controller': 'admin', // Map Internal Controller to admin for backward compatibility
      'compliance_manager': 'admin', // Map Compliance Manager to admin for backward compatibility
      'audit_director': 'admin', // Map Audit Director to admin for backward compatibility
      'auditor': 'admin', // Map Auditor to admin for backward compatibility
      'grc_contributor': 'user'
    };

    return roleCodeMap[primaryRole] || 'user';
  };

  return User;
};