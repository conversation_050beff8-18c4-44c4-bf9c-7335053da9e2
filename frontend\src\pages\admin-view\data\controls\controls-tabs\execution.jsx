import { useState, useEffect, useCallback, useRef } from "react";
import { useOutletContext } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { Loader2, Save, Play, Plus, Calendar, Target, Users, BarChart3, Trash2, Arrow<PERSON>p, ArrowDown, GripVertical, Edit, Eye } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import {
  getQuestionsByControlId,
  createControlQuestion,
  updateControlQuestion,
  deleteControlQuestion,
  reorderControlQuestions
} from "@/services/control-question-service";
import {
  getControlMethodeExecution,
  createOrUpdateControlMethodeExecution
} from "@/services/control-methode-execution-service";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import UserAssignmentModal from "../../../../../components/control/UserAssignmentModal";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import isEqual from 'lodash/isEqual';
import {
  DndContext,
  PointerSensor,
  DragOverlay,
  closestCenter,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { useDraggable, useDroppable } from '@dnd-kit/core';
import {
  SortableContext,
  useSortable,
  arrayMove,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// Options for select fields
const frequenceOptions = [
  { id: "cotidienne", name: "Cotidienne" },
  { id: "hebdomadaire", name: "Hebdomadaire" },
  { id: "mensuel", name: "Mensuel" },
  { id: "a_chaque_transaction", name: "À chaque transaction" },
  { id: "bi_mensuel", name: "Bi-mensuel" }
];

const methodeOptions = [
  { id: "controle_par_sondage", name: "Contrôle par sondage" },
  { id: "controle_systematique", name: "Contrôle systématique" },
  { id: "observation", name: "Observation" }
];

const calendrierOptions = [
  { id: "campagne_execution", name: "Campagne d'exécution" },
  { id: "campagne_hebdomadaire", name: "Campagne hebdomadaire" },
  { id: "campagne_mensuel", name: "Campagne mensuel" },
  { id: "campagne_cotidienne", name: "Campagne cotidienne" }
];

// Restrict QUESTION_TYPES to only allowed types
const QUESTION_TYPES = [
  { type: 'text', label: 'Text libre' },
  { type: 'number', label: 'Nombre' },
  { type: 'date', label: 'Date' },
  { type: 'radio', label: 'Choix unique' },
  { type: 'checkbox', label: 'Choix multiple' },
  { type: 'file', label: 'Document métier (pièce jointe)' },
  { type: 'url', label: 'Référence (URL)' },
];



// Debounce utility
function useDebouncedCallback(callback, delay) {
  const timeoutRef = useRef();
  return (...args) => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  };
}

// End drop zone component
function EndDropZone() {
  const { isOver, setNodeRef } = useDroppable({
    id: 'end-drop-zone'
  });

  return (
    <div
      ref={setNodeRef}
      style={{
        minHeight: 40,
        borderRadius: 8,
        border: isOver ? '2px solid #3b82f6' : '2px dashed #e5e7eb',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: isOver ? '#3b82f6' : '#9ca3af',
        fontSize: '14px',
        transition: 'all 0.2s ease',
        background: isOver ? '#eff6ff' : '#fafbfc'
      }}
    >
      {isOver ? 'Relâchez pour ajouter à la fin' : 'Déposez ici pour ajouter à la fin'}
    </div>
  );
}

// Simple droppable component
function SimpleDroppable({ id, children }) {
  const { isOver, setNodeRef } = useDroppable({
    id: id,
  });

  return (
    <div ref={setNodeRef}>
      {children}
    </div>
  );
}

// Drop indicator component
function DropIndicator({ position, show }) {
  const { isOver, setNodeRef } = useDroppable({
    id: `drop-indicator-${position}`
  });

  if (!show && !isOver) return null;

  return (
    <div
      ref={setNodeRef}
      style={{
        height: isOver ? 8 : 4,
        background: isOver
          ? 'linear-gradient(90deg, #3b82f6, #1d4ed8)'
          : 'transparent',
        borderRadius: 2,
        margin: '8px 0',
        transition: 'all 0.2s ease',
        boxShadow: isOver ? '0 0 8px rgba(59, 130, 246, 0.5)' : 'none',
        border: isOver ? 'none' : '2px dashed #e5e7eb',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '12px',
        color: isOver ? 'white' : '#9ca3af'
      }}
    >
      {isOver && 'Relâchez ici'}
    </div>
  );
}



function DraggableType({ type, label }) {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: `type-${type}`
  });

  const style = {
    transform: transform ? CSS.Translate.toString(transform) : undefined,
    opacity: isDragging ? 0.7 : 1,
    padding: '12px 16px',
    background: '#ffffff',
    color: '#374151',
    borderRadius: 8,
    cursor: 'grab',
    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
    transition: 'all 0.2s ease',
    border: '2px solid #e5e7eb',
    userSelect: 'none',
    touchAction: 'none'
  };

  // Icon mapping for different question types
  const getIcon = (type) => {
    switch (type) {
      case 'text': return '📝';
      case 'number': return '🔢';
      case 'date': return '📅';
      case 'radio': return '🔘';
      case 'checkbox': return '☑️';
      case 'file': return '📎';
      case 'url': return '🔗';
      default: return '❓';
    }
  };

  // Simple mock preview text
  const getMockPreview = (type) => {
    switch (type) {
      case 'text': return '[ Votre réponse... ]';
      case 'number': return '[ 0 ]';
      case 'date': return '[ jj/mm/aaaa ]';
      case 'radio': return '○ Option 1  ○ Option 2';
      case 'checkbox': return '☐ Option 1  ☐ Option 2';
      case 'file': return '[ Choisir un fichier ]';
      case 'url': return '[ https://... ]';
      default: return '[ Réponse ]';
    }
  };

  return (
    <div ref={setNodeRef} style={style} {...listeners} {...attributes}>
      <div style={{ display: 'flex', alignItems: 'center', gap: 12, marginBottom: 8 }}>
        <span style={{ fontSize: '16px' }}>{getIcon(type)}</span>
        <span style={{ fontWeight: '500', fontSize: '14px' }}>{label}</span>
      </div>

      {/* Mock preview */}
      <div style={{
        fontSize: '12px',
        color: '#9ca3af',
        padding: '6px 8px',
        background: '#f9fafb',
        borderRadius: 4,
        border: '1px dashed #d1d5db'
      }}>
        {getMockPreview(type)}
      </div>
    </div>
  );
}

function SortableQuestion({ id, question, onEdit, onDelete }) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id });
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    border: '1px solid #ddd',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    background: '#fff',
    boxShadow: isDragging ? '0 4px 12px rgba(0,0,0,0.15)' : '0 1px 3px rgba(0,0,0,0.1)',
  };

  const handleEditField = (field, value) => {
    onEdit(question.id, { ...question, [field]: value });
  };

  // Get question type label
  const getQuestionTypeLabel = (type) => {
    const questionType = QUESTION_TYPES.find(qt => qt.type === type);
    return questionType ? questionType.label : type;
  };

  return (
    <div ref={setNodeRef} style={style}>
      {/* Header with drag handle and delete button */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <div
            {...attributes}
            {...listeners}
            style={{
              cursor: 'grab',
              padding: '4px 8px',
              background: '#f3f4f6',
              borderRadius: 4,
              fontSize: '12px',
              fontWeight: 'bold',
              color: '#6b7280'
            }}
          >
            ⋮⋮ {getQuestionTypeLabel(question.type)}
          </div>
        </div>
        <Button
          size="sm"
          variant="destructive"
          onClick={(e) => {
            e.stopPropagation();
            onDelete(question.id);
          }}
        >
          <Trash2 size={16} />
        </Button>
      </div>

      {/* Question Title - This becomes the main question name */}
      <div style={{ marginBottom: 12 }}>
        <Label className="text-sm font-medium text-gray-700">Question</Label>
        <Input
          value={question.title || ''}
          onChange={e => handleEditField('title', e.target.value)}
          placeholder="Entrez votre question..."
          className="mt-1"
        />
      </div>

      {/* Type-specific fields - Only for choice-based questions */}
      {(question.type === 'radio' || question.type === 'checkbox') && (
        <div>
          <Label className="text-sm font-medium text-gray-700">
            Options (une par ligne)
          </Label>
          <Textarea
            value={question.options?.join('\n') || ''}
            onChange={e => handleEditField('options', e.target.value.split('\n').filter(opt => opt.trim()))}
            placeholder="Option 1&#10;Option 2&#10;Option 3"
            className="mt-1"
            rows={3}
          />
        </div>
      )}
    </div>
  );
}

function DndQuestionBuilder({ questions, setQuestions, onEditQuestion, onDeleteQuestion, controlId, onSaveToBackend }) {
  const [activeId, setActiveId] = useState(null);

  // Simple droppable area - exactly like the working test
  const { isOver, setNodeRef } = useDroppable({
    id: 'question-builder'
  });

  // Handle drag start
  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  // Handle drag over for visual feedback
  const handleDragOver = (event) => {
    const { active, over } = event;
    const activeIdStr = String(active.id);

    // Only provide visual feedback for new question types being dragged
    if (activeIdStr.startsWith('type-') && over) {
      // You can add additional visual feedback logic here if needed
    }
  };

  // Handle drag end
  const handleDragEnd = async (event) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) {
      console.log('❌ No drop target found');
      return;
    }

    console.log('✅ Drop target found:', over.id);

    // If dragging a type from the palette to the builder area
    if (active.id.startsWith('type-')) {
      // Accept drops on the builder area OR on existing questions
      const isValidDropTarget = over.id === 'question-builder' ||
                               questions.some(q => q.id === over.id);

      if (isValidDropTarget) {
        const type = active.id.replace('type-', '');

        // Map frontend types to backend types
        const backendTypeMap = {
          'text': 'text',
          'number': 'number',
          'date': 'date',
          'radio': 'radio',
          'checkbox': 'checkbox',
          'file': 'file',
          'url': 'url'
        };

        // Create question data for backend
        const questionData = {
          question_text: `Nouvelle question ${type}`,
          input_type: backendTypeMap[type] || type,
          options: ['radio', 'checkbox'].includes(type) ? ['Option 1', 'Option 2', 'Option 3'] : null
        };

        try {
          // Save to backend if controlId is provided
          if (controlId && onSaveToBackend) {
            await onSaveToBackend(questionData);
          } else {
            // Add to local state for preview
            const newQuestion = {
              id: `q_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
              type,
              title: questionData.question_text,
              options: questionData.options,
            };

            console.log('📝 Adding new question:', newQuestion);
            setQuestions(prev => [...prev, newQuestion]);
          }
        } catch (error) {
          console.error('Error saving question:', error);
        }
        return;
      }
    }

    // If reordering questions within the builder
    if (!active.id.startsWith('type-') && !over.id.startsWith('type-') && active.id !== over.id) {
      const oldIndex = questions.findIndex(q => q.id === active.id);
      const newIndex = questions.findIndex(q => q.id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newQuestions = arrayMove(questions, oldIndex, newIndex);
        setQuestions(newQuestions);
      }
    }
  };

  // Get the active item for drag overlay
  const getActiveItem = () => {
    if (!activeId) return null;

    if (activeId.startsWith('type-')) {
      const type = activeId.replace('type-', '');
      const questionType = QUESTION_TYPES.find(qt => qt.type === type);
      return questionType ? { type: 'question-type', data: questionType } : null;
    }

    const question = questions.find(q => q.id === activeId);
    return question ? { type: 'question', data: question } : null;
  };

  const activeItem = getActiveItem();

  const getQuestionIcon = (type) => {
    switch (type) {
      case 'text': return '📝';
      case 'number': return '🔢';
      case 'date': return '📅';
      case 'radio': return '🔘';
      case 'checkbox': return '☑️';
      case 'file': return '📎';
      case 'url': return '🔗';
      default: return '❓';
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <div
        style={{
          display: 'flex',
          gap: 32,
          marginTop: 40,
          padding: 24,
          background: '#f5f7fa',
          borderRadius: 8,
          minHeight: 500
        }}
      >
        {/* Left: Palette */}
        <div style={{ width: 200, minWidth: 180 }}>
          <h4 style={{ marginBottom: 16, fontWeight: 'bold', color: '#374151' }}>Types de questions</h4>
          {QUESTION_TYPES.map(qt => (
            <DraggableType key={qt.type} type={qt.type} label={qt.label} />
          ))}
        </div>

        {/* Right: Builder - Separate droppable from sortable */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <h4 style={{
            marginBottom: 20,
            fontWeight: 'bold',
            color: '#374151',
            borderBottom: '1px solid #e5e7eb',
            paddingBottom: 8
          }}>
            🎯 Zone de construction ({questions.length} question{questions.length !== 1 ? 's' : ''})
          </h4>

          {/* Ultra-simple droppable - EXACTLY like SimpleDroppable */}
          <div
            ref={setNodeRef}
            style={{
              color: isOver ? 'green' : undefined,
              border: isOver ? '2px solid green' : '2px dashed gray',
              padding: '20px',
              margin: '10px',
              minHeight: '400px',
              background: isOver ? '#f0f8ff' : '#fff',
              flex: 1
            }}
          >
            Question Builder Drop Zone - {questions.length} questions
            {isOver && <div style={{ color: 'green', fontWeight: 'bold' }}>HOVERING!</div>}

            {/* Show questions below the drop zone */}
            {questions.length > 0 && (
              <div style={{ marginTop: '20px' }}>
                <h5>Created Questions:</h5>
                {questions.map((q, index) => (
                  <div key={q.id} style={{
                    padding: '10px',
                    margin: '5px 0',
                    border: '1px solid #ccc',
                    borderRadius: '4px',
                    background: '#f9f9f9'
                  }}>
                    {index + 1}. {q.type} - {q.title || 'Untitled'}
                    <button
                      onClick={() => onDeleteQuestion(q.id)}
                      style={{ marginLeft: '10px', color: 'red' }}
                    >
                      Delete
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Drag Overlay */}
      <DragOverlay>
        {activeItem && activeItem.type === 'question-type' && (
          <div style={{
            border: '2px solid #2196f3',
            borderRadius: 8,
            padding: 12,
            background: '#ffffff',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            display: 'flex',
            alignItems: 'center',
            gap: 8,
          }}>
            <span style={{ fontSize: '16px' }}>
              {getQuestionIcon(activeItem.data.type)}
            </span>
            <span style={{ fontWeight: '500', color: '#374151' }}>
              {activeItem.data.label}
            </span>
          </div>
        )}
        {activeItem && activeItem.type === 'question' && (
          <div style={{
            border: '2px solid #2196f3',
            borderRadius: 8,
            padding: 12,
            background: '#ffffff',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            minWidth: 200,
          }}>
            <strong>{activeItem.data.type.toUpperCase()}</strong>
            <div style={{ marginTop: 4, fontSize: '14px', color: '#6b7280' }}>
              {activeItem.data.title || 'Question sans titre'}
            </div>
          </div>
        )}
      </DragOverlay>
    </DndContext>
  );
}

function ControlExecution() {
  const { control, permissions } = useOutletContext();
  const { canUpdate } = permissions;
  const { t } = useTranslation();
  const [isSaving, setIsSaving] = useState(false);

  // State for execution data
  const [executionData, setExecutionData] = useState({
    frequenceExecution: "",
    methode: "",
    calendrierPilotage: "",
    taillePopulationTotale: "",
    tailleEchantillon: "",
    seuilTauxConformite: "",
    procedureExecution: ""
  });

  // State for questions
  const [questions, setQuestions] = useState([]);
  const [isLoadingQuestions, setIsLoadingQuestions] = useState(false);
  const [deletingQuestionId, setDeletingQuestionId] = useState(null);
  const [reorderingQuestionId, setReorderingQuestionId] = useState(null);

  // --- DND Kit Question Builder State ---
  const [dndQuestions, setDndQuestions] = useState([]);
  const [showDndModal, setShowDndModal] = useState(false);
  const [showUserAssignmentModal, setShowUserAssignmentModal] = useState(false);

  const lastSavedDataRef = useRef(executionData);
  const isFirstLoad = useRef(true);

  // Debounced auto-save with saving indicator
  const debouncedAutoSave = useDebouncedCallback(async () => {
    if (canUpdate && control?.controlID && !isEqual(executionData, lastSavedDataRef.current)) {
      setIsSaving(true);
      try {
        const response = await createOrUpdateControlMethodeExecution(control.controlID, executionData);
        if (response.success) {
          lastSavedDataRef.current = executionData;
        }
      } catch (error) {
        console.error('Auto-save error:', error);
        toast.error('Erreur lors de la sauvegarde automatique');
      } finally {
        setIsSaving(false);
      }
    }
  }, 1000);

  // Auto-save on executionData change
  useEffect(() => {
    if (isFirstLoad.current) {
      isFirstLoad.current = false;
      lastSavedDataRef.current = executionData;
      return;
    }
    if (canUpdate && control?.controlID && !isEqual(executionData, lastSavedDataRef.current)) {
      debouncedAutoSave();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [executionData]);

  // Load questions for the control
  const loadQuestions = useCallback(async () => {
    if (!control?.controlID) return;

    setIsLoadingQuestions(true);
    try {
      const response = await getQuestionsByControlId(control.controlID);
      if (response.success) {
        const mapped = (response.data || []).map(q => ({
          ...q,
          id: String(q.id),
          title: q.question_text,
          type: q.input_type,
        }));
        setQuestions(mapped);
        setDndQuestions(mapped);
      }
    } catch (error) {
      console.error('Error loading questions:', error);
      toast.error(t('admin.controls.execution.error.load_questions', 'Erreur lors du chargement des questions'));
    } finally {
      setIsLoadingQuestions(false);
    }
  }, [control?.controlID, t]);

  // Load execution method data for the control
  const loadExecutionMethod = useCallback(async () => {
    if (!control?.controlID) return;

    try {
      const response = await getControlMethodeExecution(control.controlID);
      if (response.success && response.data) {
        setExecutionData({
          frequenceExecution: response.data.frequenceExecution || "",
          methode: response.data.methode || "",
          calendrierPilotage: response.data.calendrierPilotage || "",
          taillePopulationTotale: response.data.taillePopulationTotale || "",
          tailleEchantillon: response.data.tailleEchantillon || "",
          seuilTauxConformite: response.data.seuilTauxConformite || "",
          procedureExecution: response.data.procedureExecution || ""
        });
      }
    } catch (error) {
      console.error('Error loading execution method:', error);
      // Don't show error toast for 404 (no execution method exists yet)
      if (!error.message.includes('404')) {
        toast.error(t('admin.controls.execution.error.load_execution', 'Erreur lors du chargement de la méthode d\'exécution'));
      }
    }
  }, [control?.controlID, t]);

  // Load initial data from control
  useEffect(() => {
    if (control) {
      // Load questions and execution method for this control
      loadQuestions();
      loadExecutionMethod();
    }
  }, [control, loadQuestions, loadExecutionMethod]);

  // Handle input changes
  const handleInputChange = (field, value) => {
    setExecutionData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle adding a new question
  const handleAddQuestion = async (questionData) => {
    if (!control?.controlID) {
      toast.error(t('admin.controls.execution.error.no_control_id', 'ID de contrôle manquant'));
      return;
    }

    try {
      const response = await createControlQuestion(control.controlID, questionData);
      if (response.success) {
        toast.success(t('admin.controls.execution.success.question_added', 'Question ajoutée avec succès'));
        await loadQuestions(); // Reload questions
      } else {
        throw new Error(response.message || 'Erreur lors de la création de la question');
      }
    } catch (error) {
      console.error('Error adding question:', error);
      toast.error(error.message || t('admin.controls.execution.error.add_question', 'Erreur lors de l\'ajout de la question'));
      throw error;
    }
  };

  // Handle deleting a question
  const handleDeleteQuestion = async (questionId) => {
    setDeletingQuestionId(questionId);
    try {
      const response = await deleteControlQuestion(questionId);
      if (response.success) {
        toast.success(t('admin.controls.execution.success.question_deleted', 'Question supprimée avec succès'));
        await loadQuestions(); // Reload questions
      } else {
        throw new Error(response.message || 'Erreur lors de la suppression de la question');
      }
    } catch (error) {
      console.error('Error deleting question:', error);
      toast.error(error.message || t('admin.controls.execution.error.delete_question', 'Erreur lors de la suppression de la question'));
    } finally {
      setDeletingQuestionId(null);
    }
  };

  // Handle moving question up
  const handleMoveQuestionUp = async (index) => {
    if (index === 0) return;

    setReorderingQuestionId(questions[index].id);
    const newQuestions = [...questions];
    [newQuestions[index - 1], newQuestions[index]] = [newQuestions[index], newQuestions[index - 1]];
    setQuestions(newQuestions);

    try {
      await reorderControlQuestions(control.controlID, newQuestions.map(q => q.id));
    } catch (error) {
      console.error('Error reordering questions:', error);
      toast.error(t('admin.controls.execution.error.reorder_questions', 'Erreur lors du réordonnancement des questions'));
      await loadQuestions(); // Reload on error
    } finally {
      setReorderingQuestionId(null);
    }
  };

  // Handle moving question down
  const handleMoveQuestionDown = async (index) => {
    if (index === questions.length - 1) return;

    setReorderingQuestionId(questions[index].id);
    const newQuestions = [...questions];
    [newQuestions[index], newQuestions[index + 1]] = [newQuestions[index + 1], newQuestions[index]];
    setQuestions(newQuestions);

    try {
      await reorderControlQuestions(control.controlID, newQuestions.map(q => q.id));
    } catch (error) {
      console.error('Error reordering questions:', error);
      toast.error(t('admin.controls.execution.error.reorder_questions', 'Erreur lors du réordonnancement des questions'));
      await loadQuestions(); // Reload on error
    } finally {
      setReorderingQuestionId(null);
    }
  };



  // DND Handlers - removed handleDndAddQuestion to avoid duplication

  // Edit question handler for DND builder
  const handleDndEditQuestion = (id, updated) => {
    setDndQuestions(prev => prev.map(q => q.id === id ? updated : q));
    // Optionally sync with backend
  };

  // Delete question handler for DND builder
  const handleDndDeleteQuestion = (id) => {
    setDndQuestions(prev => prev.filter(q => q.id !== id));
    // Optionally sync with backend
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <Play className="h-6 w-6 mr-3 text-[#F62D51]" />
          {t('admin.controls.execution.title', 'Control Execution')}
        </h2>
      </div>

      {/* Section 1: Étapes de contrôle */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
          <div className="flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">
              {t('admin.controls.execution.control_steps', 'Étapes de contrôle')}
            </span>
          </div>
          {canUpdate && (
            <Button
              variant="outline"
              size="sm"
              className="bg-white hover:bg-gray-50"
              onClick={() => setShowDndModal(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              {t('admin.controls.execution.create_step', 'Créer')}
            </Button>
          )}
        </div>
        <div className="p-6 bg-white">
          {dndQuestions.length > 0 ? (
            <>
              <div style={{
                marginTop: 0,
                padding: 16,
                background: 'linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%)',
                borderRadius: 12,
                border: '1px solid #10b981',
                boxShadow: '0 2px 4px rgba(16, 185, 129, 0.1)',
                display: 'flex',
                alignItems: 'center',
                gap: 12
              }}>
                <div style={{
                  width: 32,
                  height: 32,
                  borderRadius: '50%',
                  background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  boxShadow: '0 2px 4px rgba(16, 185, 129, 0.3)'
                }}>
                  {dndQuestions.length}
                </div>
                <div>
                  <p style={{ fontSize: '14px', color: '#065f46', margin: 0, fontWeight: '600' }}>
                    Question{dndQuestions.length !== 1 ? 's' : ''} créée{dndQuestions.length !== 1 ? 's' : ''}
                  </p>
                  <p style={{ fontSize: '12px', color: '#047857', margin: 0, opacity: 0.8 }}>
                    Formulaire prêt pour les tests de contrôle
                  </p>
                </div>
                <div style={{ marginLeft: 'auto', display: 'flex', alignItems: 'center', gap: 8 }}>
                  {/* Edit Icon */}
                  <div
                    onClick={() => setShowDndModal(true)}
                    style={{
                      width: 24,
                      height: 24,
                      borderRadius: '50%',
                      background: '#6b7280',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '12px',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.background = '#4b5563';
                      e.target.style.transform = 'scale(1.1)';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.background = '#6b7280';
                      e.target.style.transform = 'scale(1)';
                    }}
                    title="Modifier le questionnaire"
                  >
                    <Edit size={12} />
                  </div>

                  {/* Success Checkmark */}
                  <div style={{
                    width: 24,
                    height: 24,
                    borderRadius: '50%',
                    background: '#10b981',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '14px'
                  }}>
                    ✓
                  </div>
                </div>
              </div>

              {/* Lancer la Campagne Button */}
              <div style={{ marginTop: 16, textAlign: 'right' }}>
                <button
                  onClick={() => setShowUserAssignmentModal(true)}
                  style={{
                    padding: '8px 16px',
                    background: 'white',
                    color: '#3b82f6',
                    border: '2px solid #3b82f6',
                    borderRadius: 6,
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = '#3b82f6';
                    e.target.style.color = 'white';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = 'white';
                    e.target.style.color = '#3b82f6';
                  }}
                >
                  Lancer la Campagne
                </button>
              </div>
            </>
          ) : (
            <div style={{
              marginTop: 0,
              padding: 16,
              background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
              borderRadius: 12,
              border: '1px solid #f59e0b',
              boxShadow: '0 2px 4px rgba(245, 158, 11, 0.1)',
              display: 'flex',
              alignItems: 'center',
              gap: 12
            }}>
              <div style={{
                width: 32,
                height: 32,
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '16px',
                fontWeight: 'bold',
                boxShadow: '0 2px 4px rgba(245, 158, 11, 0.3)'
              }}>
                !
              </div>
              <div>
                <p style={{ fontSize: '14px', color: '#92400e', margin: 0, fontWeight: '600' }}>
                  Aucune question créée
                </p>
                <p style={{ fontSize: '12px', color: '#b45309', margin: 0, opacity: 0.8 }}>
                  Utilisez le constructeur pour créer votre formulaire
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Section 2: Méthode d'exécution */}
      <div className="border rounded-lg shadow-sm">
        <div className="w-full flex items-center p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-green-600 mr-1" />
            <span className="text-lg font-medium text-green-800">
              {t('admin.controls.execution.execution_method', 'Méthode d\'exécution')}
            </span>
          </div>
        </div>

        <div className="p-6 bg-white space-y-6">
          {/* Row 1: 3 columns */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Fréquence de l'exécution */}
            <div className="space-y-2">
              <Label htmlFor="frequence-execution" className="text-sm font-medium">
                {t('admin.controls.execution.frequency', 'Fréquence de l\'exécution')}
              </Label>
              <Select
                value={executionData.frequenceExecution}
                onValueChange={(value) => handleInputChange('frequenceExecution', value)}
                disabled={!canUpdate}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('admin.controls.execution.select_frequency', 'Sélectionner une fréquence')} />
                </SelectTrigger>
                <SelectContent>
                  {frequenceOptions.map((option) => (
                    <SelectItem key={option.id} value={option.id}>
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Méthode */}
            <div className="space-y-2">
              <Label htmlFor="methode" className="text-sm font-medium">
                {t('admin.controls.execution.method', 'Méthode')}
              </Label>
              <Select
                value={executionData.methode}
                onValueChange={(value) => handleInputChange('methode', value)}
                disabled={!canUpdate}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('admin.controls.execution.select_method', 'Sélectionner une méthode')} />
                </SelectTrigger>
                <SelectContent>
                  {methodeOptions.map((option) => (
                    <SelectItem key={option.id} value={option.id}>
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Calendrier de pilotage */}
            <div className="space-y-2">
              <Label htmlFor="calendrier-pilotage" className="text-sm font-medium">
                {t('admin.controls.execution.calendar', 'Calendrier de pilotage')}
              </Label>
              <Select
                value={executionData.calendrierPilotage}
                onValueChange={(value) => handleInputChange('calendrierPilotage', value)}
                disabled={!canUpdate}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('admin.controls.execution.select_calendar', 'Sélectionner un calendrier')} />
                </SelectTrigger>
                <SelectContent>
                  {calendrierOptions.map((option) => (
                    <SelectItem key={option.id} value={option.id}>
                      {option.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Row 2: 3 columns */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Taille de la population totale */}
            <div className="space-y-2">
              <Label htmlFor="taille-population" className="text-sm font-medium">
                {t('admin.controls.execution.total_population', 'Taille de la population totale')}
              </Label>
              <Input
                id="taille-population"
                type="number"
                value={executionData.taillePopulationTotale}
                onChange={(e) => handleInputChange('taillePopulationTotale', e.target.value)}
                placeholder={t('admin.controls.execution.enter_population_size', 'Entrer la taille')}
                disabled={!canUpdate}
              />
            </div>

            {/* Taille de l'échantillon */}
            <div className="space-y-2">
              <Label htmlFor="taille-echantillon" className="text-sm font-medium">
                {t('admin.controls.execution.sample_size', 'Taille de l\'échantillon')}
              </Label>
              <div className="relative">
                <Input
                  id="taille-echantillon"
                  type="number"
                  value={executionData.tailleEchantillon}
                  onChange={(e) => handleInputChange('tailleEchantillon', e.target.value)}
                  placeholder={t('admin.controls.execution.enter_percentage', 'Entrer le pourcentage')}
                  disabled={!canUpdate}
                  className="pr-8"
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">%</span>
              </div>
            </div>

            {/* Seuil du taux de conformité */}
            <div className="space-y-2">
              <Label htmlFor="seuil-conformite" className="text-sm font-medium">
                {t('admin.controls.execution.compliance_threshold', 'Seuil du taux de conformité')}
              </Label>
              <div className="relative">
                <Input
                  id="seuil-conformite"
                  type="number"
                  value={executionData.seuilTauxConformite}
                  onChange={(e) => handleInputChange('seuilTauxConformite', e.target.value)}
                  placeholder={t('admin.controls.execution.enter_threshold', 'Entrer le seuil')}
                  disabled={!canUpdate}
                  className="pr-8"
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">%</span>
              </div>
            </div>
          </div>

          {/* Row 3: Full width textarea */}
          <div className="space-y-2">
            <Label htmlFor="procedure-execution" className="text-sm font-medium">
              {t('admin.controls.execution.execution_procedure', 'Procédure d\'exécution')}
            </Label>
            <Textarea
              id="procedure-execution"
              value={executionData.procedureExecution}
              onChange={(e) => handleInputChange('procedureExecution', e.target.value)}
              placeholder={t('admin.controls.execution.enter_procedure', 'Décrire la procédure d\'exécution...')}
              disabled={!canUpdate}
              rows={6}
              className="resize-none"
            />
          </div>

          {/* Auto-save indicator */}
          {canUpdate && (
            <div className="flex justify-end mt-6">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                {isSaving && (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                    <span className="text-blue-600">Sauvegarde en cours...</span>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* DND Question Builder Modal */}
      <DndQuestionBuilderModal
        isOpen={showDndModal}
        onClose={() => setShowDndModal(false)}
        questions={dndQuestions}
        setQuestions={setDndQuestions}
        onEditQuestion={handleDndEditQuestion}
        onDeleteQuestion={handleDndDeleteQuestion}
        controlId={control?.controlID}
        reloadQuestions={loadQuestions}
        control={control}
      />

      {/* User Assignment Modal */}
      <UserAssignmentModal
        isOpen={showUserAssignmentModal}
        onClose={() => setShowUserAssignmentModal(false)}
        controlId={control?.controlID}
        controlName={control?.name}
        onAssignmentComplete={() => {
          setShowUserAssignmentModal(false);
          toast.success("Campagne lancée avec succès! Les utilisateurs assignés ont été notifiés.", {
            duration: 4000,
            style: {
              background: '#d4edda',
              color: '#155724',
              border: '1px solid #c3e6cb',
              padding: '16px',
            },
          });
        }}
      />
    </div>
  );
}

// Update EnhancedDndQuestionBuilder for backend sync and UI cleanup
function EnhancedDndQuestionBuilder({ questions, setQuestions, controlId, reloadQuestions }) {
  const [activeId, setActiveId] = useState(null);

  // Backend sync helpers
  const addQuestionBackend = async (type) => {
    const typeLabel = QUESTION_TYPES.find(qt => qt.type === type)?.label || type;
    const questionData = {
      question_text: `Nouvelle question (${typeLabel})`,
      input_type: type,
      options: ['radio', 'checkbox'].includes(type) ? ['Option 1', 'Option 2'] : null,
    };
    if (!controlId) return;
    const response = await createControlQuestion(controlId, questionData);
    if (response.success) {
      await reloadQuestions?.();
      return response.data;
    }
    return null;
  };
  const updateQuestionBackend = async (question) => {
    if (!question.id) return;

    console.log('Updating question backend:', question);

    let options = null;
    if (['radio', 'checkbox'].includes(question.type)) {
      if (Array.isArray(question.options) && question.options.length > 0) {
        options = question.options.filter(opt => typeof opt === 'string' && opt.trim() !== '');
        if (options.length === 0) options = ['Option 1', 'Option 2'];
      } else {
        options = ['Option 1', 'Option 2'];
      }
    }

    const questionData = {
      question_text: question.title || question.question_text || 'Question sans titre',
      input_type: question.type || question.input_type,
      options,
    };

    console.log('Sending question data:', questionData);

    await updateControlQuestion(question.id, questionData);
    await reloadQuestions?.();
  };
  const deleteQuestionBackend = async (questionId) => {
    await deleteControlQuestion(questionId);
    await reloadQuestions?.();
  };
  const reorderQuestionsBackend = async (newQuestions) => {
    if (!controlId) return;
    await reorderControlQuestions(controlId, newQuestions.map(q => q.id));
    await reloadQuestions?.();
  };

  function handleDragStart(event) {
    setActiveId(event.active.id);
  }

  async function handleDragEnd(event) {
    const { active, over } = event;
    setActiveId(null);
    const activeIdStr = String(active.id);
    const overIdStr = over ? String(over.id) : null;

    if (!over) return;

    // Handle dropping new question types
    if (activeIdStr.startsWith('type-')) {
      const type = activeIdStr.replace('type-', '');

      // Determine insertion position
      let insertIndex = questions.length; // Default to end

      // Handle different drop targets
      if (overIdStr === 'drop-indicator-start') {
        insertIndex = 0; // Insert at the beginning
      } else if (overIdStr === 'end-drop-zone') {
        insertIndex = questions.length; // Insert at the end
      } else if (overIdStr && overIdStr.startsWith('drop-indicator-after-')) {
        // Extract the index from the drop indicator ID
        const afterIndex = parseInt(overIdStr.replace('drop-indicator-after-', ''));
        insertIndex = afterIndex + 1; // Insert after the specified question
      } else if (overIdStr && overIdStr !== 'enhanced-drop-zone' && !overIdStr.startsWith('type-') && !overIdStr.startsWith('drop-indicator-')) {
        // If dropped on an existing question, insert before it
        const targetIndex = questions.findIndex(q => String(q.id) === overIdStr);
        if (targetIndex !== -1) {
          insertIndex = targetIndex;
        }
      }

      // Create the question first
      const backendQuestion = await addQuestionBackend(type);
      if (backendQuestion) {
        // If we need to insert at a specific position (not at the end)
        if (insertIndex < questions.length) {
          // Create a new array with the question inserted at the correct position
          const newQuestions = [...questions];
          newQuestions.splice(insertIndex, 0, backendQuestion);

          // Update local state immediately for better UX
          setQuestions(newQuestions);

          // Update the order in the backend
          await reorderQuestionsBackend(newQuestions);
        } else {
          // Just reload questions if adding to the end
          await reloadQuestions?.();
        }
      }
      return;
    }

    // Handle reordering existing questions
    if (!activeIdStr.startsWith('type-') && !overIdStr.startsWith('type-') && activeIdStr !== overIdStr) {
      const oldIndex = questions.findIndex(q => String(q.id) === activeIdStr);
      const newIndex = questions.findIndex(q => String(q.id) === overIdStr);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newQuestions = arrayMove(questions, oldIndex, newIndex);
        setQuestions(newQuestions);
        await reorderQuestionsBackend(newQuestions);
      }
    }
  }

  const getQuestionIcon = (type) => {
    switch (type) {
      case 'text': return '📝';
      case 'number': return '🔢';
      case 'date': return '📅';
      case 'radio': return '🔘';
      case 'checkbox': return '☑️';
      case 'file': return '📎';
      case 'url': return '🔗';
      default: return '❓';
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleDragOver = (event) => {
    const { active, over } = event;
    const activeIdStr = String(active.id);

    // Only provide visual feedback for new question types being dragged
    if (activeIdStr.startsWith('type-') && over) {
      // You can add additional visual feedback logic here if needed
    }
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <div style={{
        display: 'flex',
        gap: 32,
        padding: 24,
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        borderRadius: 12,
        minHeight: 500,
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)'
      }}>

        {/* Left: Question Types Palette */}
        <div style={{ width: 280, minWidth: 260 }}>

          <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
            {QUESTION_TYPES.map(qt => (
              <DraggableType key={qt.type} type={qt.type} label={qt.label} />
            ))}
          </div>
        </div>

        {/* Right: Drop Zone */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>

          <SimpleDroppable id="enhanced-drop-zone">
            <div style={{
              minHeight: 400,
              padding: 24,
              background: 'white',
              borderRadius: 12,
              boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
              display: 'flex',
              flexDirection: 'column'
            }}>

              {questions.length === 0 && (
                <div style={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  textAlign: 'center',
                  minHeight: 350,
                  border: '2px dashed #e5e7eb',
                  borderRadius: 16,
                  background: 'linear-gradient(135deg, #fafbfc 0%, #f1f5f9 100%)',
                  color: '#64748b',
                  position: 'relative',
                  overflow: 'hidden'
                }}>
                  {/* Background decoration */}
                  <div style={{
                    position: 'absolute',
                    top: -50,
                    right: -50,
                    width: 100,
                    height: 100,
                    borderRadius: '50%',
                    background: 'linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%)',
                    opacity: 0.3
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: -30,
                    left: -30,
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    background: 'linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%)',
                    opacity: 0.2
                  }} />

                  {/* Main content */}
                  <div style={{ position: 'relative', zIndex: 1 }}>
                    <div style={{
                      fontSize: '64px',
                      marginBottom: 20,
                      background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text'
                    }}>
                      📋
                    </div>
                    <h3 style={{
                      fontSize: '28px',
                      fontWeight: '700',
                      marginBottom: 16,
                      color: '#1e293b',
                      letterSpacing: '-0.025em'
                    }}>
                      Construisez votre questionnaire
                    </h3>
                    <p style={{
                      fontSize: '16px',
                      marginBottom: 12,
                      maxWidth: 450,
                      lineHeight: 1.6,
                      color: '#475569'
                    }}>
                      Glissez et déposez les types de questions depuis la palette de gauche pour créer votre formulaire de contrôle personnalisé
                    </p>
                  </div>
                </div>
              )}

              {/* Questions List - Sortable */}
              {questions.length > 0 && (
                <SortableContext items={questions.map(q => q.id)} strategy={verticalListSortingStrategy}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>

                    {/* Drop indicator at the beginning */}
                    <DropIndicator
                      position="start"
                      show={activeId && String(activeId).startsWith('type-')}
                    />

                    {questions.map((q, index) => (
                      <div key={q.id}>
                        <SortableQuestionCard
                          id={q.id}
                          question={q}
                          index={index}
                          activeId={activeId}
                          onEdit={async (updated) => {
                            setQuestions(prev => prev.map(qq => qq.id === updated.id ? updated : qq));
                            await updateQuestionBackend(updated);
                          }}
                          onDelete={async (id) => {
                            setQuestions(prev => prev.filter(qq => qq.id !== id));
                            await deleteQuestionBackend(id);
                          }}
                        />

                        {/* Drop indicator after each question (except the last one) */}
                        {index < questions.length - 1 && (
                          <DropIndicator
                            position={`after-${index}`}
                            show={activeId && String(activeId).startsWith('type-')}
                          />
                        )}
                      </div>
                    ))}

                    {/* Drop zone at the end */}
                    <EndDropZone />
                  </div>
                </SortableContext>
              )}
            </div>
          </SimpleDroppable>
        </div>
      </div>

      {/* Simple Drag Overlay */}
      <DragOverlay>
        {activeId && String(activeId).startsWith('type-') && (
          <div style={{
            padding: '12px 16px',
            background: '#ffffff',
            color: '#374151',
            borderRadius: 8,
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            border: '2px solid #e5e7eb',
            opacity: 0.9
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
              <span style={{ fontSize: '16px' }}>
                {getQuestionIcon(String(activeId).replace('type-', ''))}
              </span>
              <span style={{ fontWeight: '500', fontSize: '14px' }}>
                {QUESTION_TYPES.find(qt => qt.type === String(activeId).replace('type-', ''))?.label}
              </span>
            </div>
          </div>
        )}
      </DragOverlay>
    </DndContext>
  );
}

// Update EnhancedQuestionCard: remove description, textarea, min/max, only show options for radio/checkbox, main title = question title, subtitle = type label
function EnhancedQuestionCard({ question, index, onEdit, onDelete, dragHandle }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedQuestion, setEditedQuestion] = useState(question);
  const getTypeLabel = (type) => {
    const found = QUESTION_TYPES.find(qt => qt.type === type);
    return found ? found.label : type;
  };
  const handleSave = () => {
    onEdit({ ...editedQuestion, id: question.id });
    setIsEditing(false);
  };
  const handleCancel = () => {
    setEditedQuestion(question);
    setIsEditing(false);
  };
  const addOption = () => {
    if (['radio', 'checkbox'].includes(question.type)) {
      setEditedQuestion(prev => ({
        ...prev,
        options: [...(prev.options || []), `Option ${(prev.options?.length || 0) + 1}`]
      }));
    }
  };
  const updateOption = (optionIndex, value) => {
    setEditedQuestion(prev => ({
      ...prev,
      options: prev.options?.map((opt, idx) => idx === optionIndex ? value : opt) || []
    }));
  };
  const removeOption = (optionIndex) => {
    setEditedQuestion(prev => ({
      ...prev,
      options: prev.options?.filter((_, idx) => idx !== optionIndex) || []
    }));
  };
  return (
    <div style={{
      background: 'white',
      border: '1px solid #e5e7eb',
      borderRadius: 12,
      padding: 20,
      boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
      position: 'relative',
      transition: 'all 0.2s ease'
    }}>
      {/* Question Number Badge */}
      <div style={{
        position: 'absolute',
        left: -10,
        top: -10,
        background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
        color: 'white',
        borderRadius: '50%',
        width: 32,
        height: 32,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '14px',
        fontWeight: 'bold',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        {index + 1}
      </div>
      {/* Question Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
        paddingLeft: 16
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          {/* Drag Handle */}
          <div
            {...dragHandle}
            style={{
              cursor: 'grab',
              padding: '4px',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              color: '#9ca3af'
            }}
          >
            <GripVertical className="h-4 w-4" />
          </div>
          <div>
            <h4 style={{
              margin: 0,
              fontSize: '18px',
              fontWeight: '600',
              color: '#1f2937'
            }}>
              {question.title || 'Question sans titre'}
            </h4>
            <p style={{
              margin: 0,
              fontSize: '14px',
              color: '#6b7280'
            }}>
              {getTypeLabel(question.type)}
            </p>
          </div>
        </div>
        <div style={{ display: 'flex', gap: 8 }}>
          <Button
            onClick={() => setIsEditing(!isEditing)}
            variant="outline"
            size="sm"
            style={{ minWidth: 'auto', padding: '6px 12px' }}
          >
            {isEditing ? <Eye className="h-4 w-4" /> : <Edit className="h-4 w-4" />}
          </Button>
          <Button
            onClick={() => onDelete(question.id)}
            variant="outline"
            size="sm"
            style={{
              minWidth: 'auto',
              padding: '6px 12px',
              color: '#dc2626',
              borderColor: '#dc2626'
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
      {/* Question Content */}
      {isEditing ? (
        <div style={{ paddingLeft: 16 }}>
          {/* Title Input */}
          <div style={{ marginBottom: 16 }}>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151',
              marginBottom: 4
            }}>
              Nom de la question
            </label>
            <Input
              value={editedQuestion.title || ''}
              onChange={(e) => setEditedQuestion(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Entrez le nom de la question"
              style={{ width: '100%' }}
            />
          </div>
          {/* Only show options for radio/checkbox */}
          {['radio', 'checkbox'].includes(question.type) && (
            <div style={{ marginBottom: 16 }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 8
              }}>
                <label style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  Options de réponse
                </label>
                <Button
                  onClick={addOption}
                  variant="outline"
                  size="sm"
                >
                  + Ajouter option
                </Button>
              </div>
              {(editedQuestion.options || []).map((option, idx) => (
                <div key={idx} style={{
                  display: 'flex',
                  gap: 8,
                  marginBottom: 8,
                  alignItems: 'center'
                }}>
                  <span style={{ fontSize: '14px', color: '#6b7280', minWidth: '20px' }}>
                    {idx + 1}.
                  </span>
                  <Input
                    value={option}
                    onChange={(e) => updateOption(idx, e.target.value)}
                    placeholder={`Option ${idx + 1}`}
                    style={{ flex: 1 }}
                  />
                  <Button
                    onClick={() => removeOption(idx)}
                    variant="outline"
                    size="sm"
                    style={{
                      minWidth: 'auto',
                      padding: '6px',
                      color: '#dc2626',
                      borderColor: '#dc2626'
                    }}
                  >
                    ×
                  </Button>
                </div>
              ))}
            </div>
          )}
          {/* Save/Cancel Buttons */}
          <div style={{ display: 'flex', gap: 8, justifyContent: 'flex-end' }}>
            <Button onClick={handleCancel} variant="outline">
              Annuler
            </Button>
            <Button onClick={handleSave} style={{ background: '#10b981', color: 'white' }}>
              Sauvegarder
            </Button>
          </div>
        </div>
      ) : (
        <div style={{ paddingLeft: 16 }}>
          {/* Preview Mode */}
          <div style={{
            padding: 16,
            background: '#f9fafb',
            borderRadius: 8,
            border: '1px solid #e5e7eb'
          }}>
            <h5 style={{
              margin: '0 0 8px 0',
              fontSize: '16px',
              color: '#1f2937'
            }}>
              {question.title || 'Question sans titre'}
            </h5>
            {/* Question Preview */}
            <div style={{ marginTop: 12 }}>
              {question.type === 'text' && (
                <Input
                  placeholder={question.title || 'Votre réponse...'}
                  disabled
                  style={{ background: '#f3f4f6' }}
                />
              )}
              {question.type === 'number' && (
                <Input
                  type="number"
                  placeholder={question.title || 'Entrez un nombre'}
                  disabled
                  style={{ background: '#f3f4f6' }}
                />
              )}
              {question.type === 'date' && (
                <Input
                  type="date"
                  disabled
                  style={{ background: '#f3f4f6' }}
                />
              )}
              {question.type === 'radio' && (
                <div>
                  {(question.options || ['Option 1', 'Option 2']).map((option, idx) => (
                    <div key={idx} style={{ marginBottom: 8 }}>
                      <label style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <input type="radio" name={`preview-${question.id}`} disabled />
                        <span style={{ fontSize: '14px' }}>{option}</span>
                      </label>
                    </div>
                  ))}
                </div>
              )}
              {question.type === 'checkbox' && (
                <div>
                  {(question.options || ['Option 1', 'Option 2']).map((option, idx) => (
                    <div key={idx} style={{ marginBottom: 8 }}>
                      <label style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <input type="checkbox" disabled />
                        <span style={{ fontSize: '14px' }}>{option}</span>
                      </label>
                    </div>
                  ))}
                </div>
              )}
              {question.type === 'file' && (
                <div style={{ fontSize: '14px', color: '#6b7280' }}>Pièce jointe (upload désactivé)</div>
              )}
              {question.type === 'url' && (
                <Input
                  type="url"
                  placeholder="https://..."
                  disabled
                  style={{ background: '#f3f4f6' }}
                />
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function SortableQuestionCard({ id, question, index, onEdit, onDelete, activeId }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
    isOver,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  // Show drop indicator when dragging a new question type over this question
  const showDropIndicator = isOver && activeId && String(activeId).startsWith('type-');

  return (
    <div style={{ position: 'relative' }}>
      {/* Drop indicator - shows when dragging a new question type over this question */}
      {showDropIndicator && (
        <div style={{
          position: 'absolute',
          top: -8,
          left: 0,
          right: 0,
          height: 4,
          background: 'linear-gradient(90deg, #3b82f6, #1d4ed8)',
          borderRadius: 2,
          zIndex: 10,
          boxShadow: '0 0 8px rgba(59, 130, 246, 0.5)'
        }} />
      )}

      <div ref={setNodeRef} style={style} {...attributes}>
        <EnhancedQuestionCard
          question={question}
          index={index}
          onEdit={onEdit}
          onDelete={onDelete}
          dragHandle={listeners}
        />
      </div>
    </div>
  );
}

// Update DndQuestionBuilderModal to pass controlId and reloadQuestions
function DndQuestionBuilderModal({ isOpen, onClose, questions, setQuestions, controlId, reloadQuestions, control }) {
  const [showUserAssignmentModal, setShowUserAssignmentModal] = useState(false);

  if (!isOpen) return null;
  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000,
        padding: '20px'
      }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          width: '100vw',
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          overflow: 'hidden'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div style={{
          padding: '16px 24px',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white'
        }}>
          <h2 style={{ margin: 0, fontSize: '20px', fontWeight: '600' }}>
            🎯 Constructeur de Questions Drag & Drop
          </h2>
          <Button
            onClick={onClose}
            variant="ghost"
            style={{
              color: 'white',
              fontSize: '24px',
              padding: '8px',
              minWidth: 'auto',
              height: 'auto'
            }}
          >
            ×
          </Button>
        </div>
        {/* Modal Content */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          padding: '16px',
          background: '#f8fafc'
        }}>
          <EnhancedDndQuestionBuilder
            questions={questions}
            setQuestions={setQuestions}
            controlId={controlId}
            reloadQuestions={reloadQuestions}
          />
        </div>
        {/* Modal Footer */}
        <div style={{
          padding: '12px 24px',
          borderTop: '1px solid #e5e7eb',
          background: 'white',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ fontSize: '14px', color: '#6b7280' }}>
            {questions.length} question{questions.length !== 1 ? 's' : ''} créée{questions.length !== 1 ? 's' : ''}
          </div>
          <div style={{ display: 'flex', gap: '12px' }}>
            <Button variant="outline" onClick={onClose}>
              Fermer
            </Button>
          </div>
        </div>
      </div>

      {/* User Assignment Modal */}
      <UserAssignmentModal
        isOpen={showUserAssignmentModal}
        onClose={() => setShowUserAssignmentModal(false)}
        controlId={controlId}
        controlName={control?.name}
        onAssignmentComplete={() => {
          setShowUserAssignmentModal(false);
          toast.success("Campagne lancée avec succès! Les utilisateurs assignés ont été notifiés.", {
            duration: 4000,
            style: {
              background: '#d4edda',
              color: '#155724',
              border: '1px solid #c3e6cb',
              padding: '16px',
            },
          });
        }}
      />
    </div>
  );
}

export default ControlExecution;
