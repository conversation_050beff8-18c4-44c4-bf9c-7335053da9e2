import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { <PERSON>Lef<PERSON>, Save, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import PageHeader from "@/components/ui/page-header";

function ActionDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [actionPlanId, setActionPlanId] = useState(null);

  // Action state
  const [action, setAction] = useState({
    id: "",
    name: "",
    description: "",
    status: "",
    assignee: "",
    dueDate: "",
    priority: "",
    notes: ""
  });

  // Mock data - will be replaced with API calls in the future
  useEffect(() => {
    // Simulate API call to get action details
    const fetchAction = async () => {
      try {
        setIsLoading(true);
        // Mock data
        if (id === "1") {
          setAction({
            id: "1",
            name: "Update firewall rules",
            description: "Review and update all firewall rules to comply with new security policy",
            status: "In Progress",
            assignee: "John Doe",
            dueDate: "2024-07-15",
            priority: "High",
            notes: "Focus on external facing services first"
          });
          setActionPlanId("AP_001");
        } else if (id === "2") {
          setAction({
            id: "2",
            name: "Conduct security training",
            description: "Organize and conduct security awareness training for all staff",
            status: "Not Started",
            assignee: "Jane Smith",
            dueDate: "2024-08-01",
            priority: "Medium",
            notes: "Prepare materials by July 15"
          });
          setActionPlanId("AP_001");
        } else if (id === "3") {
          setAction({
            id: "3",
            name: "Install security patches",
            description: "Apply latest security patches to all servers",
            status: "Completed",
            assignee: "Mike Johnson",
            dueDate: "2024-06-30",
            priority: "Critical",
            notes: "Completed ahead of schedule"
          });
          setActionPlanId("AP_002");
        } else {
          // New action
          setAction({
            id: "",
            name: "",
            description: "",
            status: "Not Started",
            assignee: "",
            dueDate: "",
            priority: "Medium",
            notes: ""
          });
          // Try to get action plan ID from URL query params
          const urlParams = new URLSearchParams(window.location.search);
          const planId = urlParams.get('planId');
          if (planId) {
            setActionPlanId(planId);
          }
        }
      } catch (error) {
        console.error("Error fetching action:", error);
        toast.error("Failed to load action details");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAction();
  }, [id]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setAction(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle select change
  const handleSelectChange = (name, value) => {
    setAction(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle save
  const handleSave = async (e) => {
    e.preventDefault();
    
    try {
      setIsSaving(true);
      
      // Validate required fields
      if (!action.name) {
        toast.error("Action name is required");
        return;
      }
      
      // In the future, this will dispatch an action to save the action
      // For now, just show a success message
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      toast.success("Action saved successfully");
      
      // Navigate back to the actions list
      if (actionPlanId) {
        navigate(`/admin/data/action-plans/${actionPlanId}/actions`);
      } else {
        navigate(-1);
      }
    } catch (error) {
      console.error("Error saving action:", error);
      toast.error("Failed to save action");
    } finally {
      setIsSaving(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (actionPlanId) {
      navigate(`/admin/data/action-plans/${actionPlanId}/actions`);
    } else {
      navigate(-1);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      <div className="flex items-center mb-4">
        <Button 
          variant="ghost" 
          onClick={handleCancel}
          className="mr-2"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Actions
        </Button>
      </div>

      <PageHeader
        title={action.id ? `Edit Action: ${action.name}` : "Create New Action"}
        description={action.id ? "Update action details" : "Create a new action"}
        section="Data"
        currentPage="Action Details"
        icon={null}
      />

      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSave}>
          <div className="grid gap-6">
            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={action.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={action.description}
                onChange={handleInputChange}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={action.status}
                  onValueChange={(value) => handleSelectChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Not Started">Not Started</SelectItem>
                    <SelectItem value="In Progress">In Progress</SelectItem>
                    <SelectItem value="Completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="assignee">Assignee</Label>
                <Input
                  id="assignee"
                  name="assignee"
                  value={action.assignee}
                  onChange={handleInputChange}
                />
              </div>
              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={action.priority}
                  onValueChange={(value) => handleSelectChange("priority", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Low">Low</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="High">High</SelectItem>
                    <SelectItem value="Critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label htmlFor="dueDate">Due Date</Label>
                <Input
                  id="dueDate"
                  name="dueDate"
                  type="date"
                  value={action.dueDate}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                name="notes"
                value={action.notes}
                onChange={handleInputChange}
                rows={3}
              />
            </div>

            <div className="flex justify-end space-x-2 mt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-[#F62D51] hover:bg-[#d42a49] text-white"
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Action
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

export default ActionDetails;
