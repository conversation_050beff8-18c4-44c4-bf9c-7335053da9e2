const { sequelize } = require('../index');

const getLossesOverTime = async (timePeriod = 'month', start, end) => {
  try {
    let dateFormat;
    switch (timePeriod) {
      case 'day':
        dateFormat = 'YYYY-MM-DD';
        break;
      case 'month':
        dateFormat = 'YYYY-MM';
        break;
      case 'year':
        dateFormat = 'YYYY';
        break;
      default:
        throw new Error('Invalid time period');
    }

    let query = `
      SELECT 
        TO_CHAR("occurrenceDate", '${dateFormat}') AS time_period,
        SUM("grossLoss")::float AS gross_loss,
        SUM("grossLoss" - "recoveries")::float AS net_loss
      FROM 
        "Incident"
      WHERE 
        "grossLoss" IS NOT NULL
        AND "recoveries" IS NOT NULL
        AND "occurrenceDate" IS NOT NULL
    `;

    if (start && end) {
      query += `
        AND "occurrenceDate" BETWEEN :start AND :end
      `;
    }

    query += `
      GROUP BY 
        TO_CHAR("occurrenceDate", '${dateFormat}')
      ORDER BY 
        MIN("occurrenceDate");
    `;

    const replacements = start && end ? { start, end } : {};

    const results = await sequelize.query(query, {
      type: sequelize.QueryTypes.SELECT,
      replacements,
    });

    return results;
  } catch (error) {
    console.error('Error fetching losses over time:', error);
    throw error;
  }
};

module.exports = { getLossesOverTime };