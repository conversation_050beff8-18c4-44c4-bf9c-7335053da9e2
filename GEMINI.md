nothing working im getting "
planification.jsx:91 Error in parallel requests: ReferenceError: abortControllersRef is not defined
    at useApiRequest.js:28:5
    at withApiErrorHandling.fallbackValue (planification.jsx:59:11)
    at useApiRequest.js:121:20
    at planification.jsx:114:7
    at react-stack-bottom-frame (react-dom_client.js?v=8be8c89a:16242:20)
    at runWithFiberInDEV (react-dom_client.js?v=8be8c89a:726:18)
    at commitHookEffectListMount (react-dom_client.js?v=8be8c89a:7767:122)
    at commitHookPassiveMountEffects (react-dom_client.js?v=8be8c89a:7825:60)
    at commitPassiveMountOnFiber (react-dom_client.js?v=8be8c89a:9182:29)
    at recursivelyTraversePassiveMountEffects (react-dom_client.js?v=8be8c89a:9163:13)
overrideMethod @ hook.js:608
withApiErrorHandling.fallbackValue @ planification.jsx:91
await in withApiErrorHandling.fallbackValue
(anonymous) @ useApiRequest.js:121
(anonymous) @ planification.jsx:114
react-stack-bottom-frame @ react-dom_client.js?v=8be8c89a:16242
runWithFiberInDEV @ react-dom_client.js?v=8be8c89a:726
commitHookEffectListMount @ react-dom_client.js?v=8be8c89a:7767
commitHookPassiveMountEffects @ react-dom_client.js?v=8be8c89a:7825
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9182
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9186
flushPassiveEffects @ react-dom_client.js?v=8be8c89a:11119
commitRootImpl @ react-dom_client.js?v=8be8c89a:11070
commitRoot @ react-dom_client.js?v=8be8c89a:10989
commitRootWhenReady @ react-dom_client.js?v=8be8c89a:10477
performWorkOnRoot @ react-dom_client.js?v=8be8c89a:10421
performSyncWorkOnRoot @ react-dom_client.js?v=8be8c89a:11448
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=8be8c89a:11356
processRootScheduleInMicrotask @ react-dom_client.js?v=8be8c89a:11375
(anonymous) @ react-dom_client.js?v=8be8c89a:11459
XMLHttpRequest.send
dispatchXhrRequest @ axios.js?v=8be8c89a:1637
xhr @ axios.js?v=8be8c89a:1517
dispatchRequest @ axios.js?v=8be8c89a:1992
_request @ axios.js?v=8be8c89a:2213
request @ axios.js?v=8be8c89a:2104
Axios.<computed> @ axios.js?v=8be8c89a:2232
wrap @ axios.js?v=8be8c89a:8
getAuditPlanById @ audit-plan-service.js:33
(anonymous) @ auditPlanSlice.js:32
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:2323
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:2357
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:1466
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:1724
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:1557
(anonymous) @ edit-plans-daudit.jsx:79
react-stack-bottom-frame @ react-dom_client.js?v=8be8c89a:16242
runWithFiberInDEV @ react-dom_client.js?v=8be8c89a:726
commitHookEffectListMount @ react-dom_client.js?v=8be8c89a:7767
commitHookPassiveMountEffects @ react-dom_client.js?v=8be8c89a:7825
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9182
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9186
flushPassiveEffects @ react-dom_client.js?v=8be8c89a:11119
commitRootImpl @ react-dom_client.js?v=8be8c89a:11070
commitRoot @ react-dom_client.js?v=8be8c89a:10989
commitRootWhenReady @ react-dom_client.js?v=8be8c89a:10477
performWorkOnRoot @ react-dom_client.js?v=8be8c89a:10421
performSyncWorkOnRoot @ react-dom_client.js?v=8be8c89a:11448
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=8be8c89a:11356
processRootScheduleInMicrotask @ react-dom_client.js?v=8be8c89a:11375
(anonymous) @ react-dom_client.js?v=8be8c89a:11459
XMLHttpRequest.send
dispatchXhrRequest @ axios.js?v=8be8c89a:1637
xhr @ axios.js?v=8be8c89a:1517
dispatchRequest @ axios.js?v=8be8c89a:1992
Promise.then
_request @ axios.js?v=8be8c89a:2195
request @ axios.js?v=8be8c89a:2104
Axios.<computed> @ axios.js?v=8be8c89a:2232
wrap @ axios.js?v=8be8c89a:8
(anonymous) @ index.js:189
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:2323
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:2357
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:1466
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:1724
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:1557
(anonymous) @ App.jsx:208
react-stack-bottom-frame @ react-dom_client.js?v=8be8c89a:16242
runWithFiberInDEV @ react-dom_client.js?v=8be8c89a:726
commitHookEffectListMount @ react-dom_client.js?v=8be8c89a:7767
commitHookPassiveMountEffects @ react-dom_client.js?v=8be8c89a:7825
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9182
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9186
flushPassiveEffects @ react-dom_client.js?v=8be8c89a:11119
(anonymous) @ react-dom_client.js?v=8be8c89a:11042
performWorkUntilDeadline @ react-dom_client.js?v=8be8c89a:35Understand this error
useApiRequest.js:128 API call failed: ReferenceError: abortControllersRef is not defined
    at useApiRequest.js:28:5
    at withApiErrorHandling.fallbackValue (recommandations.jsx:54:30)
    at useApiRequest.js:121:20
    at recommandations.jsx:111:7
    at react-stack-bottom-frame (react-dom_client.js?v=8be8c89a:16242:20)
    at runWithFiberInDEV (react-dom_client.js?v=8be8c89a:726:18)
    at commitHookEffectListMount (react-dom_client.js?v=8be8c89a:7767:122)
    at commitHookPassiveMountEffects (react-dom_client.js?v=8be8c89a:7825:60)
    at commitPassiveMountOnFiber (react-dom_client.js?v=8be8c89a:9182:29)
    at recursivelyTraversePassiveMountEffects (react-dom_client.js?v=8be8c89a:9163:13)
overrideMethod @ hook.js:608
(anonymous) @ useApiRequest.js:128
await in (anonymous)
(anonymous) @ recommandations.jsx:111
react-stack-bottom-frame @ react-dom_client.js?v=8be8c89a:16242
runWithFiberInDEV @ react-dom_client.js?v=8be8c89a:726
commitHookEffectListMount @ react-dom_client.js?v=8be8c89a:7767
commitHookPassiveMountEffects @ react-dom_client.js?v=8be8c89a:7825
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9182
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9186
flushPassiveEffects @ react-dom_client.js?v=8be8c89a:11119
commitRootImpl @ react-dom_client.js?v=8be8c89a:11070
commitRoot @ react-dom_client.js?v=8be8c89a:10989
commitRootWhenReady @ react-dom_client.js?v=8be8c89a:10477
performWorkOnRoot @ react-dom_client.js?v=8be8c89a:10421
performSyncWorkOnRoot @ react-dom_client.js?v=8be8c89a:11448
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=8be8c89a:11356
processRootScheduleInMicrotask @ react-dom_client.js?v=8be8c89a:11375
(anonymous) @ react-dom_client.js?v=8be8c89a:11459
XMLHttpRequest.send
dispatchXhrRequest @ axios.js?v=8be8c89a:1637
xhr @ axios.js?v=8be8c89a:1517
dispatchRequest @ axios.js?v=8be8c89a:1992
_request @ axios.js?v=8be8c89a:2213
request @ axios.js?v=8be8c89a:2104
Axios.<computed> @ axios.js?v=8be8c89a:2232
wrap @ axios.js?v=8be8c89a:8
getAuditPlanById @ audit-plan-service.js:33
(anonymous) @ auditPlanSlice.js:32
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:2323
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:2357
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:1466
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:1724
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:1557
(anonymous) @ edit-plans-daudit.jsx:79
react-stack-bottom-frame @ react-dom_client.js?v=8be8c89a:16242
runWithFiberInDEV @ react-dom_client.js?v=8be8c89a:726
commitHookEffectListMount @ react-dom_client.js?v=8be8c89a:7767
commitHookPassiveMountEffects @ react-dom_client.js?v=8be8c89a:7825
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9182
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9186
flushPassiveEffects @ react-dom_client.js?v=8be8c89a:11119
commitRootImpl @ react-dom_client.js?v=8be8c89a:11070
commitRoot @ react-dom_client.js?v=8be8c89a:10989
commitRootWhenReady @ react-dom_client.js?v=8be8c89a:10477
performWorkOnRoot @ react-dom_client.js?v=8be8c89a:10421
performSyncWorkOnRoot @ react-dom_client.js?v=8be8c89a:11448
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=8be8c89a:11356
processRootScheduleInMicrotask @ react-dom_client.js?v=8be8c89a:11375
(anonymous) @ react-dom_client.js?v=8be8c89a:11459
XMLHttpRequest.send
dispatchXhrRequest @ axios.js?v=8be8c89a:1637
xhr @ axios.js?v=8be8c89a:1517
dispatchRequest @ axios.js?v=8be8c89a:1992
Promise.then
_request @ axios.js?v=8be8c89a:2195
request @ axios.js?v=8be8c89a:2104
Axios.<computed> @ axios.js?v=8be8c89a:2232
wrap @ axios.js?v=8be8c89a:8
(anonymous) @ index.js:189
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:2323
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:2357
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:1466
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:1724
(anonymous) @ @reduxjs_toolkit.js?v=8be8c89a:1557
(anonymous) @ App.jsx:208
react-stack-bottom-frame @ react-dom_client.js?v=8be8c89a:16242
runWithFiberInDEV @ react-dom_client.js?v=8be8c89a:726
commitHookEffectListMount @ react-dom_client.js?v=8be8c89a:7767
commitHookPassiveMountEffects @ react-dom_client.js?v=8be8c89a:7825
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9182
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9265
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9176
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=8be8c89a:9163
commitPassiveMountOnFiber @ react-dom_client.js?v=8be8c89a:9186
flushPassiveEffects @ react-dom_client.js?v=8be8c89a:11119
(anonymous) @ react-dom_client.js?v=8be8c89a:11042
performWorkUntilDeadline @ react-dom_client.js?v=8be8c89a:35Understand this error
recommandations.jsx:105 Critical error fetching recommendations: ReferenceError: abortControllersRef is not defined"