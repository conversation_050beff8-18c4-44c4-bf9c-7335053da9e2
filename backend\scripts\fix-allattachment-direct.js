const { sequelize } = require('../models');

async function fixAllAttachment() {
  try {
    console.log('🔧 Checking AllAttachment table...');
    
    // Check current column type
    const columnInfo = await sequelize.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'AllAttachment' AND column_name = 'campagneID';
    `, { type: sequelize.QueryTypes.SELECT });
    
    console.log('Current column info:', columnInfo);
    
    if (columnInfo.length === 0) {
      console.log('❌ campagneID column not found in AllAttachment table');
      process.exit(1);
    }
    
    if (columnInfo[0].data_type === 'character varying') {
      console.log('✅ AllAttachment campagneID is already a string type');
      process.exit(0);
    }
    
    console.log(`🔄 Converting campagneID from ${columnInfo[0].data_type} to VARCHAR...`);
    
    // Step 1: Check if there are any records
    const recordCount = await sequelize.query(`
      SELECT COUNT(*) as count FROM "AllAttachment" WHERE "campagneID" IS NOT NULL;
    `, { type: sequelize.QueryTypes.SELECT });
    
    console.log(`Found ${recordCount[0].count} records with campagneID`);
    
    // Step 2: If there are records, we need to handle them carefully
    if (parseInt(recordCount[0].count) > 0) {
      console.log('⚠️  Found existing records. Setting them to NULL temporarily...');
      await sequelize.query(`
        UPDATE "AllAttachment" SET "campagneID" = NULL;
      `);
    }
    
    // Step 3: Change column type
    console.log('🔄 Changing column type...');
    await sequelize.query(`
      ALTER TABLE "AllAttachment" 
      ALTER COLUMN "campagneID" TYPE VARCHAR(255) USING "campagneID"::VARCHAR;
    `);
    
    console.log('✅ Successfully changed AllAttachment campagneID to VARCHAR');
    
    // Verify the change
    const newColumnInfo = await sequelize.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'AllAttachment' AND column_name = 'campagneID';
    `, { type: sequelize.QueryTypes.SELECT });
    
    console.log('New column info:', newColumnInfo);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error fixing AllAttachment:', error);
    process.exit(1);
  }
}

fixAllAttachment();
