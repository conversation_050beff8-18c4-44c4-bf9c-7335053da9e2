// Determine the API base URL dynamically
export const getApiBaseUrl = () => {
  // Use environment variable if available
  if (import.meta.env.VITE_API_URL) {
    return import.meta.env.VITE_API_URL;
  }
  
  // Otherwise, use the current hostname with backend port
  const hostname = window.location.hostname;
  return `http://${hostname}:5001/api`;
};

// Get a complete API endpoint URL
export const getApiEndpointUrl = (endpoint) => {
  const baseUrl = getApiBaseUrl();
  // Remove /api from the base URL if the endpoint already includes it
  const base = endpoint.startsWith('api/') ? baseUrl.replace(/\/api$/, '') : baseUrl;
  // Ensure there's a single slash between base and endpoint
  return `${base}/${endpoint.startsWith('/') ? endpoint.substring(1) : endpoint}`;
};

// Get auth headers with token if available
export const getAuthHeaders = () => {
  const headers = {
    'Content-Type': 'application/json'
  };
  
  const token = localStorage.getItem('authToken');
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
};
