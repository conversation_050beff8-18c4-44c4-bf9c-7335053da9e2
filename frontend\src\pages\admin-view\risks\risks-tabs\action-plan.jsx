import { useState, useEffect } from "react";
import { useOutletContext } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { getAllActionPlans } from "../../../../store/slices/actionPlanSlice";
import { Button } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import { Textarea } from "../../../../components/ui/textarea";
import { Loader2, Plus, Link as LinkIcon } from "lucide-react";
import { toast } from "sonner";
import axios from "axios";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../../../../components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../components/ui/select";
import { Label } from "../../../../components/ui/label";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from 'react-i18next';

function RisksActionPlan() {
  const { t } = useTranslation();
  const { risk, refreshRisk } = useOutletContext();
  const dispatch = useDispatch();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [newActionPlan, setNewActionPlan] = useState({
    name: "",
    nature: "",
    comment: ""
  });
  const API_BASE_URL = getApiBaseUrl();
  const { actionPlans, isLoading: actionPlansLoading } = useSelector((state) => state.actionPlan);

  useEffect(() => {
    dispatch(getAllActionPlans());
  }, [dispatch]);

  // Assigned action plans for this risk
  const assignedActionPlans = Array.isArray(risk?.actionPlans) ? risk.actionPlans : [];

  const handleCreateInputChange = (e) => {
    const { name, value } = e.target;
    setNewActionPlan(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setNewActionPlan(prev => ({ ...prev, [name]: value }));
  };

  const handleCreateActionPlan = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      const actionPlanData = {
        ...newActionPlan,
        actionPlanID: `AP_${Date.now()}`,
      };
      const response = await axios.post(
        `${API_BASE_URL}/actionPlans`,
        actionPlanData,
        {
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' },
        }
      );
      if (response.data.success) {
        await axios.post(
          `${API_BASE_URL}/risk/${risk.riskID}/action-plans`,
          { actionPlanIDs: [response.data.data.actionPlanID] },
          { withCredentials: true, headers: { 'Content-Type': 'application/json' } }
        );
        toast.success(t('admin.risks.management.action_plan.created_linked_success', "Action plan created and linked successfully"));
        setNewActionPlan({ name: "", nature: "", comment: "" });
        setIsCreateModalOpen(false);
        dispatch(getAllActionPlans());
        refreshRisk();
      }
    } catch (error) {
      console.error('Error creating action plan:', error);
      toast.error(error?.response?.data?.message || error?.message || t('admin.risks.management.action_plan.create_failed', "Failed to create action plan"));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLinkActionPlan = async (actionPlanID) => {
    setIsSubmitting(true);
    try {
      await axios.post(
        `${API_BASE_URL}/risk/${risk.riskID}/action-plans`,
        { actionPlanIDs: [actionPlanID] },
        { withCredentials: true, headers: { 'Content-Type': 'application/json' } }
      );
      setIsLinkModalOpen(false);
      toast.success(t('admin.risks.management.action_plan.linked_success', "Action plan linked successfully"));
      refreshRisk();
    } catch (error) {
      console.error('Error linking action plan:', error);
      toast.error(error?.response?.data?.message || error?.message || t('admin.risks.management.action_plan.link_failed', "Failed to link action plan"));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUnlinkActionPlan = async (actionPlanID) => {
    if (!window.confirm(t('admin.risks.management.action_plan.confirm_unlink', "Are you sure you want to unlink this action plan?"))) {
      return;
    }
    setIsSubmitting(true);
    try {
      await axios.delete(
        `${API_BASE_URL}/risk/${risk.riskID}/action-plans/${actionPlanID}`,
        { withCredentials: true }
      );
        toast.success(t('admin.risks.management.action_plan.unlinked_success', "Action plan unlinked successfully"));
        refreshRisk();
    } catch (error) {
      console.error('Error unlinking action plan:', error);
      toast.error(error?.response?.data?.message || error?.message || t('admin.risks.management.action_plan.unlink_failed', "Failed to unlink action plan"));
    } finally {
      setIsSubmitting(false);
    }
  };

  if (actionPlansLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
          <span className="mt-4 text-gray-600">{t('admin.risks.management.action_plan.loading', "Loading action plans...")}</span>
        </div>
      </div>
    );
  }

  const filteredActionPlans = actionPlans?.filter(plan =>
    plan.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (plan.comment && plan.comment.toLowerCase().includes(searchQuery.toLowerCase()))
  ) || [];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">{t('admin.risks.management.action_plan.title', "Risk Action Plan")}</h2>
          <div className="flex gap-2">
            <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
              <DialogTrigger asChild>
                <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white">
                  <Plus className="h-4 w-4 mr-2" />
                  {t('admin.risks.management.action_plan.create_action_plan', "Create Action Plan")}
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>{t('admin.risks.management.action_plan.create_modal.title', "Create New Action Plan")}</DialogTitle>
                  <DialogDescription>
                    {t('admin.risks.management.action_plan.create_modal.description', "Fill in the details below to create a new action plan.")}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleCreateActionPlan} className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">{t('admin.risks.management.action_plan.create_modal.name_label', "Name *")}</Label>
                    <Input
                      id="name"
                      name="name"
                      value={newActionPlan.name}
                      onChange={handleCreateInputChange}
                      placeholder={t('admin.risks.management.action_plan.create_modal.name_placeholder', "Enter action plan name")}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nature">{t('admin.risks.management.action_plan.create_modal.nature_label', "Nature")}</Label>
                    <Select
                      value={newActionPlan.nature}
                      onValueChange={(value) => handleSelectChange("nature", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('admin.risks.management.action_plan.create_modal.select_nature', "Select nature")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="preventive">{t('admin.risks.management.action_plan.create_modal.preventive', "Preventive")}</SelectItem>
                        <SelectItem value="corrective">{t('admin.risks.management.action_plan.create_modal.corrective', "Corrective")}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="comment">{t('admin.risks.management.action_plan.create_modal.comment_label', "Comment")}</Label>
                    <Textarea
                      id="comment"
                      name="comment"
                      value={newActionPlan.comment}
                      onChange={handleCreateInputChange}
                      placeholder={t('admin.risks.management.action_plan.create_modal.comment_placeholder', "Enter comment")}
                      rows={3}
                    />
                  </div>
                  <div className="flex justify-end gap-3 mt-6">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsCreateModalOpen(false)}
                    >
                      {t('admin.risks.management.action_plan.create_modal.cancel', "Cancel")}
                    </Button>
                    <Button
                      type="submit"
                      className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {t('admin.risks.management.action_plan.create_modal.creating', "Creating...")}
                        </>
                      ) : (
                        t('admin.risks.management.action_plan.create_modal.create_action_plan', "Create Action Plan")
                      )}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
            <Dialog open={isLinkModalOpen} onOpenChange={setIsLinkModalOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="w-9 h-9 p-0 flex items-center justify-center">
                  <LinkIcon className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>{t('admin.risks.management.action_plan.link_modal.title', "Link Existing Action Plan")}</DialogTitle>
                  <DialogDescription>
                    {t('admin.risks.management.action_plan.link_modal.description', "Select an action plan to link to this risk.")}
                  </DialogDescription>
                </DialogHeader>
                <div className="mt-4">
                  <div className="mb-4">
                    <Input
                      placeholder={t('admin.risks.management.action_plan.link_modal.search_placeholder', "Search action plans...")}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full"
                    />
                  </div>

                  {isSubmitting ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-[#F62D51] mr-2" />
                      <span className="text-gray-500">{t('admin.risks.management.action_plan.link_modal.linking', "Linking action plan...")}</span>
                    </div>
                  ) : (
                    <div className="max-h-[400px] overflow-y-auto">
                      {filteredActionPlans.length > 0 ? (
                        <div className="grid gap-3">
                          {filteredActionPlans.map((plan) => (
                            <div
                              key={plan.actionPlanID}
                              className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                              onClick={() => handleLinkActionPlan(plan.actionPlanID)}
                            >
                              <div className="flex justify-between items-start">
                                <div>
                                  <h3 className="font-medium text-[#242A33]">{plan.name}</h3>
                                  <p className="text-sm text-gray-500 mt-1">
                                    {plan.nature ? `${t('admin.risks.management.action_plan.nature', 'Nature')}: ${plan.nature}` : t('admin.risks.management.action_plan.no_nature', 'No nature specified')}
                                  </p>
                                </div>
                                <span className="text-xs text-gray-400">{plan.actionPlanID}</span>
                              </div>
                              {plan.comment && (
                                <p className="text-sm mt-2 text-gray-600 line-clamp-2">{plan.comment}</p>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          {searchQuery ? t('admin.risks.management.action_plan.link_modal.no_match', 'No action plans match your search') : t('admin.risks.management.action_plan.link_modal.no_available', 'No action plans available')}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>
          </div>
      </div>
      <div className="bg-white rounded-lg shadow-sm">
        {assignedActionPlans.length > 0 ? (
          <div className="space-y-4 p-5">
            {assignedActionPlans.map((plan) => (
              <div key={plan.actionPlanID} className="border border-gray-200 rounded-lg p-4 flex justify-between items-start">
              <div>
                  <h3 className="font-medium text-lg text-[#242A33]">{plan.name}</h3>
                <div className="flex items-center mt-1">
                    {plan.nature && (
                    <span className="text-xs text-gray-500 mr-3">
                        {t('admin.risks.management.action_plan.nature', 'Nature')}: {plan.nature}
                    </span>
                    )}
                    <span className="text-xs text-gray-500">ID: {plan.actionPlanID}</span>
                  </div>
                  {plan.comment && (
                    <div className="mt-2">
                      <h4 className="text-sm font-medium text-gray-500 mb-1">{t('admin.risks.management.action_plan.comment', "Comment")}</h4>
                      <p className="text-[#242A33]">{plan.comment}</p>
                    </div>
                  )}
              </div>
              <Button
                variant="outline"
                size="sm"
                className="text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
                  onClick={() => handleUnlinkActionPlan(plan.actionPlanID)}
                  disabled={isSubmitting}
              >
                {t('admin.risks.management.action_plan.unlink', "Unlink")}
              </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 px-6">
            <h3 className="text-lg font-medium text-[#242A33] mb-2">{t('admin.risks.management.action_plan.no_linked_title', "No Action Plan Linked")}</h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              {t('admin.risks.management.action_plan.no_linked_description', "Link an existing action plan or create a new one to help mitigate this risk.")}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default RisksActionPlan;
