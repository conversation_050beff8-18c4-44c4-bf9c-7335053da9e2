const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllActions,
  getActionsByActionPlanId,
  getActionById,
  createAction,
  updateAction,
  deleteAction,
  deleteMultipleActions
} = require('../../controllers/actions/action-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all actions
router.get('/', getAllActions);

// Get all actions for an action plan
router.get('/action-plan/:actionPlanId', getActionsByActionPlanId);

// Get action by ID
router.get('/:id', getActionById);

// Create new action
router.post('/', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), createAction);

// Update action
router.put('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), updateAction);

// Delete action
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteAction);

// Delete multiple actions
router.post('/delete-multiple', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteMultipleActions);

module.exports = router;
