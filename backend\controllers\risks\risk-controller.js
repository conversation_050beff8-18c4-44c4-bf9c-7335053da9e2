const { Risk, ActionPlan } = require('../../models');
const { v4: uuidv4 } = require('uuid');
const { logCreationActivity, logUpdateActivities } = require('./activity-controller');
const db = require('../../models');
const { sequelize } = require('../../models');

// Get all risks (include actionPlans)
const getAllRisks = async (req, res) => {
  try {
    const risks = await Risk.findAll({
      include: [{ model: ActionPlan, as: 'actionPlans' }]
    });
    res.json({
      success: true,
      data: risks
    });
  } catch (error) {
    console.error('Error fetching risks:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch risks'
    });
  }
};

// Create new risk
const createRisk = async (req, res) => {
  try {
    const {
      name,
      code,
      methodOfIdentification,
      impact,
      DMR,
      probability,
      appetite,
      acceptance,
      avoidance,
      insurance,
      reduction,
      comment,
      mitigatingActionPlan,
      businessProcessID,
      organizationalProcessID,
      operationID,
      applicationID,
      entityID,
      riskTypeID,
      controlID,
      major
    } = req.body;

    console.log('Received risk data:', req.body);

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name'].filter(field => !req.body[field])
      });
    }

    // Validate impact, DMR, and probability if provided
    const validateRange = (value, fieldName) => {
      if (value !== undefined && value !== null && value !== '') {
        const num = Number(value);
        if (isNaN(num) || num < 1 || num > 5) {
          throw new Error(`${fieldName} must be a number between 1 and 5`);
        }
        return num;
      }
      return null;
    };

    // Generate riskID
    const riskID = `RISK_${Date.now()}`;

    const risk = await Risk.create({
      riskID,
      name,
      code,
      methodOfIdentification,
      impact: validateRange(impact, 'impact'),
      DMR: validateRange(DMR, 'DMR'),
      probability: validateRange(probability, 'probability'),
      appetite: validateRange(appetite, 'appetite'),
      acceptance: acceptance !== undefined ? Boolean(acceptance) : false,
      avoidance: avoidance !== undefined ? Boolean(avoidance) : false,
      insurance: insurance !== undefined ? Boolean(insurance) : false,
      reduction: reduction !== undefined ? Boolean(reduction) : false,
      comment,
      mitigatingActionPlan,
      businessProcessID: businessProcessID || null,
      organizationalProcessID: organizationalProcessID || null,
      operationID: operationID || null,
      applicationID: applicationID || null,
      entityID: entityID || null,
      riskTypeID: riskTypeID || null,
      controlID: controlID || null,
      major: major !== undefined ? Boolean(major) : false,
      status: 'Active',
      createdAt: new Date()
    });

    // Log creation activity
    let username = 'Unknown User';
    try {
      if (req.user?.userId) {
        const user = await db.User.findByPk(req.user.userId);
        if (user) {
          username = user.username || user.email || user.name;
        }
      } else {
        username = req.user?.username || req.user?.email || req.user?.name || 'Unknown User';
      }
    } catch (err) {
      console.error('Error getting user for activity logging:', err);
    }
    
    await logCreationActivity(risk, username);

    return res.status(201).json({
      success: true,
      message: 'Risk created successfully',
      data: risk
    });
  } catch (error) {
    console.error('Error creating risk:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create risk'
    });
  }
};

// Get risk by ID (include actionPlans)
const getRiskById = async (req, res) => {
  try {
    const risk = await Risk.findByPk(req.params.id, {
      include: [{ model: ActionPlan, as: 'actionPlans' }]
    });
    if (!risk) {
      return res.status(404).json({
        success: false,
        message: 'Risk not found'
      });
    }
    res.json({
      success: true,
      data: risk
    });
  } catch (error) {
    console.error('Error fetching risk:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch risk'
    });
  }
};

// Update risk
const updateRisk = async (req, res) => {
  try {
    // Get the risk before update
    const risk = await Risk.findByPk(req.params.id);
    if (!risk) {
      return res.status(404).json({
        success: false,
        message: 'Risk not found'
      });
    }

    const {
      name,
      code,
      methodOfIdentification,
      impact,
      DMR,
      probability,
      appetite,
      acceptance,
      avoidance,
      insurance,
      reduction,
      comment,
      mitigatingActionPlan,
      businessProcessID,
      organizationalProcessID,
      operationID,
      applicationID,
      entityID,
      riskTypeID,
      controlID,
      evaluations,
      major
    } = req.body;

    // Validate impact, DMR, and probability if provided
    const validateRange = (value, fieldName) => {
      if (value !== undefined && value !== null && value !== '') {
        const num = Number(value);
        if (isNaN(num) || num < 1 || num > 25) {
          throw new Error(`${fieldName} must be a number between 1 and 25`);
        }
        return num;
      }
      return null;
    };

    // Store the old risk for comparison
    const oldRisk = { ...risk.toJSON() };

    // Prepare update data
    const updateData = {
      name: name || risk.name,
      code: code !== undefined ? code : risk.code,
      methodOfIdentification: methodOfIdentification !== undefined ? methodOfIdentification : risk.methodOfIdentification,
      impact: validateRange(impact, 'impact'),
      DMR: validateRange(DMR, 'DMR'),
      probability: validateRange(probability, 'probability'),
      appetite: validateRange(appetite, 'appetite'),
      acceptance: acceptance !== undefined ? Boolean(acceptance) : risk.acceptance,
      avoidance: avoidance !== undefined ? Boolean(avoidance) : risk.avoidance,
      insurance: insurance !== undefined ? Boolean(insurance) : risk.insurance,
      reduction: reduction !== undefined ? Boolean(reduction) : risk.reduction,
      comment: comment !== undefined ? comment : risk.comment,
      mitigatingActionPlan: mitigatingActionPlan !== undefined ? mitigatingActionPlan : risk.mitigatingActionPlan,
      businessProcessID: businessProcessID !== undefined ? businessProcessID : risk.businessProcessID,
      organizationalProcessID: organizationalProcessID !== undefined ? organizationalProcessID : risk.organizationalProcessID,
      operationID: operationID !== undefined ? operationID : risk.operationID,
      applicationID: applicationID !== undefined ? applicationID : risk.applicationID,
      entityID: entityID !== undefined ? entityID : risk.entityID,
      riskTypeID: riskTypeID !== undefined ? riskTypeID : risk.riskTypeID,
      controlID: controlID !== undefined ? controlID : risk.controlID,
      evaluations: evaluations !== undefined ? evaluations : risk.evaluations,
      major: major !== undefined ? Boolean(major) : risk.major
    };

    // Update the risk
    await risk.update(updateData);

    // Log update activities
    let username = 'Unknown User';
    try {
      if (req.user?.userId) {
        const user = await db.User.findByPk(req.user.userId);
        if (user) {
          username = user.username || user.email || user.name;
        }
      } else {
        username = req.user?.username || req.user?.email || req.user?.name || 'Unknown User';
      }
    } catch (err) {
      console.error('Error getting user for activity logging:', err);
    }
    
    await logUpdateActivities(oldRisk, updateData, username);

    return res.json({
      success: true,
      message: 'Risk updated successfully',
      data: risk
    });
  } catch (error) {
    console.error('Error updating risk:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to update risk'
    });
  }
};

// Delete risk
const deleteRisk = async (req, res) => {
  try {
    const risk = await Risk.findByPk(req.params.id);
    if (!risk) {
      return res.status(404).json({
        success: false,
        message: 'Risk not found'
      });
    }

    // Begin transaction
    const t = await sequelize.transaction();
    
    try {
      // Delete related records that have foreign key constraints
      await db.ActivityLog.destroy({
        where: { risk_id: risk.riskID },
        transaction: t
      });
      
      await db.Event.destroy({
        where: { risk_id: risk.riskID },
        transaction: t
      });
      
      // Delete related risk attachments
      await db.RiskAttachment.destroy({
        where: { riskID: risk.riskID },
        transaction: t
      });

      // Finally, delete the risk itself
      await risk.destroy({ transaction: t });
      
      // Commit transaction
      await t.commit();
      
      res.json({
        success: true,
        message: 'Risk deleted successfully'
      });
    } catch (error) {
      // Rollback in case of error
      await t.rollback();
      throw error;
    }
  } catch (error) {
    console.error('Error deleting risk:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete risk',
      error: error.message
    });
  }
};

// Delete multiple risks
const deleteMultipleRisks = async (req, res) => {
  try {
    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No risk IDs provided for deletion'
      });
    }

    // Delete each risk with its related records
    const deletePromises = ids.map(async (id) => {
      const risk = await Risk.findByPk(id);
      if (!risk) {
        return { id, success: false, message: 'Risk not found' };
      }
      
      // Begin transaction for each risk
      const t = await sequelize.transaction();
      
      try {
        // Delete related records that have foreign key constraints
        await db.ActivityLog.destroy({
          where: { risk_id: risk.riskID },
          transaction: t
        });
        
        await db.Event.destroy({
          where: { risk_id: risk.riskID },
          transaction: t
        });
        
        // Delete related risk attachments
        await db.RiskAttachment.destroy({
          where: { riskID: risk.riskID },
          transaction: t
        });

        // Finally, delete the risk itself
        await risk.destroy({ transaction: t });
        
        // Commit transaction
        await t.commit();
        
        return { id, success: true };
      } catch (error) {
        // Rollback in case of error
        await t.rollback();
        console.error(`Error deleting risk ${id}:`, error);
        return { id, success: false, message: error.message };
      }
    });

    const results = await Promise.all(deletePromises);
    const failedDeletions = results.filter(result => !result.success);

    if (failedDeletions.length > 0) {
      return res.status(207).json({
        success: true,
        message: `Deleted ${results.length - failedDeletions.length} risks, failed to delete ${failedDeletions.length} risks`,
        failedDeletions
      });
    }

    res.json({
      success: true,
      message: `Successfully deleted ${results.length} risks`
    });
  } catch (error) {
    console.error('Error deleting multiple risks:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete risks',
      error: error.message
    });
  }
};

// Add one or more action plans to a risk
const addActionPlansToRisk = async (req, res) => {
  try {
    const { actionPlanIDs } = req.body;
    if (!Array.isArray(actionPlanIDs) || actionPlanIDs.length === 0) {
      return res.status(400).json({ success: false, message: 'No actionPlanIDs provided' });
    }
    const risk = await Risk.findByPk(req.params.id);
    if (!risk) {
      return res.status(404).json({ success: false, message: 'Risk not found' });
    }
    await risk.addActionPlans(actionPlanIDs);
    const updatedRisk = await Risk.findByPk(req.params.id, { include: [{ model: ActionPlan, as: 'actionPlans' }] });
    res.json({ success: true, data: updatedRisk });
  } catch (error) {
    console.error('Error adding action plans to risk:', error);
    res.status(500).json({ success: false, message: error.message || 'Failed to add action plans' });
  }
};

// Remove an action plan from a risk
const removeActionPlanFromRisk = async (req, res) => {
  try {
    const { id, actionPlanID } = req.params;
    const risk = await Risk.findByPk(id);
    if (!risk) {
      return res.status(404).json({ success: false, message: 'Risk not found' });
    }
    await risk.removeActionPlan(actionPlanID);
    const updatedRisk = await Risk.findByPk(id, { include: [{ model: ActionPlan, as: 'actionPlans' }] });
    res.json({ success: true, data: updatedRisk });
  } catch (error) {
    console.error('Error removing action plan from risk:', error);
    res.status(500).json({ success: false, message: error.message || 'Failed to remove action plan' });
  }
};

// Replace all action plans for a risk
const replaceActionPlansForRisk = async (req, res) => {
  try {
    const { actionPlanIDs } = req.body;
    if (!Array.isArray(actionPlanIDs)) {
      return res.status(400).json({ success: false, message: 'actionPlanIDs must be an array' });
    }
    const risk = await Risk.findByPk(req.params.id);
    if (!risk) {
      return res.status(404).json({ success: false, message: 'Risk not found' });
    }
    await risk.setActionPlans(actionPlanIDs);
    const updatedRisk = await Risk.findByPk(req.params.id, { include: [{ model: ActionPlan, as: 'actionPlans' }] });
    res.json({ success: true, data: updatedRisk });
  } catch (error) {
    console.error('Error replacing action plans for risk:', error);
    res.status(500).json({ success: false, message: error.message || 'Failed to replace action plans' });
  }
};

module.exports = {
  getAllRisks,
  createRisk,
  getRiskById,
  updateRisk,
  deleteRisk,
  deleteMultipleRisks,
  addActionPlansToRisk,
  removeActionPlanFromRisk,
  replaceActionPlansForRisk
};


