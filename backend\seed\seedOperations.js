const db = require('../models');
const { v4: uuidv4 } = require('uuid');

// Helper function to get random item from array
const getRandomItem = (array) => {
  if (!array || array.length === 0) return null;
  return array[Math.floor(Math.random() * array.length)];
};

// Sample data for operations
const operationNames = [
  "Financial Reporting Review",
  "Quarterly Compliance Check",
  "Customer Data Validation",
  "Vendor Risk Assessment",
  "Internal Audit Procedure",
  "IT Security Monitoring",
  "Regulatory Filing Process",
  "Data Backup Verification",
  "Access Control Review",
  "Fraud Detection Analysis",
  "Transaction Reconciliation",
  "System Performance Check",
  "Employee Onboarding Process",
  "Contract Approval Workflow",
  "Budget Planning Procedure",
  "Inventory Management Check",
  "Quality Assurance Review",
  "Customer Complaint Handling",
  "Disaster Recovery Test",
  "Compliance Training Verification"
];

const operationCodes = [
  "OP-FIN-001",
  "OP-COMP-002",
  "OP-DATA-003",
  "OP-VEND-004",
  "OP-AUD-005",
  "OP-SEC-006",
  "OP-REG-007",
  "OP-BCK-008",
  "OP-ACC-009",
  "OP-FRD-010",
  "OP-REC-011",
  "OP-SYS-012",
  "OP-HR-013",
  "OP-CONT-014",
  "OP-BUD-015",
  "OP-INV-016",
  "OP-QA-017",
  "OP-CUST-018",
  "OP-DR-019",
  "OP-TRN-020"
];

const operationComments = [
  "Monthly review of financial statements and supporting documentation",
  "Quarterly assessment of compliance with regulatory requirements",
  "Regular validation of customer data accuracy and completeness",
  "Assessment of vendor risks and compliance with contractual obligations",
  "Internal audit procedure to identify control weaknesses",
  "Continuous monitoring of IT security events and incidents",
  "Process for preparing and submitting regulatory filings",
  "Verification of data backup integrity and recoverability",
  "Periodic review of system access controls and permissions",
  "Analysis of transactions to detect potential fraudulent activities",
  "Daily reconciliation of financial transactions across systems",
  "Regular monitoring of system performance and availability",
  "Process for onboarding new employees and provisioning access",
  "Workflow for reviewing and approving contracts",
  "Annual budget planning and approval procedure",
  "Regular inventory counts and reconciliation with records",
  "Quality assurance review of products and services",
  "Process for handling and resolving customer complaints",
  "Regular testing of disaster recovery procedures",
  "Verification of employee compliance training completion"
];

// Main seeding function
async function seedOperations(numOperations = 20) {
  try {
    console.log(`Starting to seed ${numOperations} operations...`);
    
    // Clear existing operations
    await db.Operation.destroy({ where: {} });
    console.log('Cleared existing operations');
    
    // Fetch organizational processes to use as parents
    const organizationalProcesses = await db.OrganizationalProcess.findAll();
    if (organizationalProcesses.length === 0) {
      console.log('No organizational processes found. Please seed organizational processes first.');
      return;
    }
    console.log(`Found ${organizationalProcesses.length} organizational processes to use as parents`);
    
    // Fetch entities to use as references
    const entities = await db.Entity.findAll();
    if (entities.length === 0) {
      console.log('No entities found. Using null for entity references.');
    } else {
      console.log(`Found ${entities.length} entities to use as references`);
    }
    
    // Generate operations
    const operations = [];
    for (let i = 0; i < numOperations; i++) {
      // Ensure we don't exceed our sample data arrays
      const index = i % operationNames.length;
      
      // Always assign a parent organizational process
      const parentOrganizationalProcess = getRandomItem(organizationalProcesses).organizationalProcessID;
      
      // Optionally assign an entity (50% chance)
      const entityID = entities.length > 0 && Math.random() > 0.5 
        ? getRandomItem(entities).entityID 
        : null;
      
      operations.push({
        operationID: `OP_${Date.now()}_${i}`,
        name: operationNames[index],
        code: operationCodes[index],
        comment: operationComments[index],
        parentOrganizationalProcess,
        entityID
      });
    }
    
    // Insert operations into database
    console.log(`Inserting ${operations.length} operations into database...`);
    await db.Operation.bulkCreate(operations);
    
    console.log('Operations seeded successfully!');
    
    // Log a few examples of the created operations
    const createdOperations = await db.Operation.findAll({ limit: 3, include: [
      { model: db.OrganizationalProcess, as: 'organizationalProcess' }
    ]});
    
    console.log('\nExample operations created:');
    createdOperations.forEach(op => {
      console.log(`- ${op.name} (${op.code})`);
      console.log(`  Parent Org Process: ${op.organizationalProcess ? op.organizationalProcess.name : 'None'}`);
      console.log(`  Comment: ${op.comment.substring(0, 50)}${op.comment.length > 50 ? '...' : ''}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('Error seeding operations:', error);
  }
}

// Execute the seeding function
seedOperations();

module.exports = seedOperations;
