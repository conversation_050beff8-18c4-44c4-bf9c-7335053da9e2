require('dotenv').config();
const nodemailer = require('nodemailer');

// Gmail SMTP transporter with better configuration
const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT) || 465,
    secure: true, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    },
    // Additional options for better reliability
    connectionTimeout: 10000, // 10 seconds
    greetingTimeout: 5000, // 5 seconds
    socketTimeout: 10000, // 10 seconds
    // For Gmail specifically
    tls: {
      rejectUnauthorized: false
    }
  });

// For programmatic use (no req/res)
exports.sendEmailDirect = async ({ to, subject, text, html }) => {
  if (!to || !subject || (!text && !html)) {
    throw new Error('to, subject, and text or html are required');
  }

  // Check if email configuration is available
  if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASS) {
    console.warn('[Email] SMTP configuration incomplete, skipping email send');
    throw new Error('SMTP configuration incomplete - email not sent');
  }

  try {
    console.log(`[Email] Attempting to send email to: ${to}`);
    const result = await transporter.sendMail({
      from: process.env.SMTP_USER,
      to,
      subject,
      text,
      html
    });
    console.log(`[Email] Successfully sent email to ${to}, messageId: ${result.messageId}`);
    return result;
  } catch (error) {
    console.error(`[Email] Failed to send email to ${to}:`, error.message);
    // Include more specific error information
    if (error.code === 'ESOCKET') {
      throw new Error(`Email connection failed: Unable to connect to SMTP server (${error.address}:${error.port})`);
    } else if (error.code === 'EAUTH') {
      throw new Error('Email authentication failed: Invalid SMTP credentials');
    } else if (error.code === 'EMESSAGE') {
      throw new Error('Email message error: Invalid email format or content');
    } else {
      throw new Error(`Email sending failed: ${error.message}`);
    }
  }
};

// Send a normal email
exports.sendEmail = async (req, res) => {
  const { to, subject, text, html } = req.body;
  if (!to || !subject || (!text && !html)) {
    return res.status(400).json({ success: false, message: 'to, subject, and text or html are required' });
  } 
  try {
    const info = await transporter.sendMail({
      from: process.env.SMTP_USER, // Use your Gmail address
      to,
      subject,
      text,
      html
    });
    return res.status(200).json({ success: true, message: 'Email sent', info });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Failed to send email', error: error.message });
  }
};

// Send an email with attachment (expects JSON body with base64 attachment)
exports.sendEmailWithAttachment = async (req, res) => {
  const { to, subject, text, html, attachment } = req.body;
  if (!to || !subject || (!text && !html) || !attachment || !attachment.filename || !attachment.content || !attachment.contentType) {
    return res.status(400).json({ success: false, message: 'to, subject, text or html, and valid attachment are required' });
  }
  try {
    const info = await transporter.sendMail({
      from: process.env.SMTP_USER,
      to,
      subject,
      text,
      html,
      attachments: [
        {
          filename: attachment.filename,
          content: Buffer.from(attachment.content, 'base64'),
          contentType: attachment.contentType
        }
      ]
    });
    return res.status(200).json({ success: true, message: 'Email with attachment sent', info });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Failed to send email with attachment', error: error.message });
  }
}; 