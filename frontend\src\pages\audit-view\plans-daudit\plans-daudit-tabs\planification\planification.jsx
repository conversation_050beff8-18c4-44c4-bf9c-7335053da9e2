import { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";
import { toast } from "sonner";
import { useApiRequest, withApiErrorHandling } from '@/hooks/useApiRequest';
import userService from "@/services/userService";
import MissionDetailsModal from "@/components/audit-view/mission-details-modal";

// Frappe Gantt loader
const loadFrappeGantt = () => {
  return new Promise((resolve, reject) => {
    if (window.Gantt) {
      resolve();
      return;
    }
    const script = document.createElement("script");
    script.src = "https://cdn.jsdelivr.net/npm/frappe-gantt@0.6.1/dist/frappe-gantt.min.js";
    script.async = false;
    script.onload = () => {
      if (window.Gantt) resolve();
      else reject(new Error("Frappe Gantt not available after script load"));
    };
    script.onerror = () => reject(new Error("Failed to load Frappe Gantt"));
    document.head.appendChild(script);
  });
};

function PlanificationTab({ auditPlan }) {
  const { makeRequest, cancelAllRequests } = useApiRequest();

  // Filters
  const [periode, setPeriode] = useState("Mois");
  const [dateDebut, setDateDebut] = useState("");
  const [dateFin, setDateFin] = useState("");

  // State management
  const [missions, setMissions] = useState([]);
  const [selectedMission, setSelectedMission] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [users, setUsers] = useState([]);

  const ganttRef = useRef(null);
  const ganttInstanceRef = useRef(null);

  // Fetch missions data
  useEffect(() => {
    const fetchMissionsData = withApiErrorHandling(async () => {
      if (!auditPlan?.id) return;

      setIsLoading(true);

      try {
        // Fetch missions and users in parallel with cancellation support
        const [missionsResponse, usersResponse] = await Promise.all([
          makeRequest({
            method: 'get',
            url: `${getApiBaseUrl()}/audit-missions/plan/${auditPlan.id}`,
            withCredentials: true,
            headers: { 'Content-Type': 'application/json' }
          }, {
            retries: 2,
            onError: (error) => {
              if (!axios.isCancel(error)) {
                console.error("Error fetching missions:", error);
                toast.error("Erreur lors du chargement des missions");
              }
            }
          }),
          // Use user service instead of direct API call
          userService.fetchUsers({ silent: true })
        ]);

        if (missionsResponse && missionsResponse.data.success) {
          setMissions(missionsResponse.data.data);
        } else {
          setMissions([]);
        }

        // usersResponse is now the users array from userService
        if (usersResponse && Array.isArray(usersResponse)) {
          setUsers(usersResponse);
        } else {
          setUsers([]);
        }
      } catch (error) {
        if (!axios.isCancel(error)) {
          console.error("Error in parallel requests:", error);
        }
        setMissions([]);
        setUsers([]);
      }

      setIsLoading(false);
    }, {
      fallbackValue: null,
      autoRefresh: true, // Auto refresh on critical errors
      refreshDelay: 5000,
      onError: (error) => {
        setIsLoading(false);
        setMissions([]);
        setUsers([]);
        if (!axios.isCancel(error)) {
          console.error("Critical error fetching missions data:", error);
          toast.error("Erreur lors du chargement des données");
        }
      }
    });

    if (auditPlan?.id) {
      fetchMissionsData();
    }

    // Cleanup on unmount or dependency change
    return () => {
      cancelAllRequests();
    };
  }, [auditPlan?.id, makeRequest, cancelAllRequests]);

  // Helper function to format dates for Gantt
  const formatDateForGantt = (dateString) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  };

  // Helper function to calculate progress
  const calculateProgress = (mission) => {
    if (mission.avancement) {
      const progressMatch = mission.avancement.match(/(\d+)%/);
      if (progressMatch) {
        return parseInt(progressMatch[1]);
      }
    }
    return mission.etat === 'Completed' ? 100 : mission.etat === 'In Progress' ? 50 : 0;
  };

  // Gantt rendering
  useEffect(() => {
    if (missions.length === 0) return;

    loadFrappeGantt().then(() => {
      if (!ganttRef.current) return;
      ganttRef.current.innerHTML = "";

      // Filter missions by date if filters are set
      let filtered = missions.filter(mission => {
        const startDate = formatDateForGantt(mission.datedebut);
        const endDate = formatDateForGantt(mission.datefin);

        // Skip missions without valid dates
        if (!startDate || !endDate) return false;

        // Apply date filters
        if (dateDebut && endDate < dateDebut) return false;
        if (dateFin && startDate > dateFin) return false;

        return true;
      });

      // Map to Gantt tasks
      const tasks = filtered.map(mission => ({
        id: mission.id,
        name: "", // Empty name as we show names in the left column
        start: formatDateForGantt(mission.datedebut),
        end: formatDateForGantt(mission.datefin),
        progress: calculateProgress(mission),
        dependencies: "",
        custom_class: `gantt-task-${mission.id}`
      }));

      if (tasks.length === 0) {
        ganttRef.current.innerHTML = '<div class="text-center py-8 text-gray-500">Aucune mission à afficher pour la période sélectionnée</div>';
        return;
      }

      try {
        ganttInstanceRef.current = new window.Gantt(ganttRef.current, tasks, {
          view_mode: periode === "Année" ? "Year" : periode === "Trimestre" ? "Month" : "Day",
          bar_height: 24,
          padding: 24,
          column_width: 36,
          language: "fr",
          show_label: false,
          on_click: (task) => {
            const mission = missions.find(m => m.id === task.id);
            if (mission) {
              setSelectedMission(mission);
              setIsModalOpen(true);
            }
          }
        });
      } catch (error) {
        console.error("Error rendering Gantt chart:", error);
        ganttRef.current.innerHTML = '<div class="text-center py-8 text-red-500">Erreur lors du rendu du diagramme de Gantt</div>';
      }
    }).catch(error => {
      console.error("Error loading Frappe Gantt:", error);
      ganttRef.current.innerHTML = '<div class="text-center py-8 text-red-500">Erreur lors du chargement du diagramme de Gantt</div>';
    });
  }, [missions, periode, dateDebut, dateFin]);

  // Helper function to handle mission click
  const handleMissionClick = (mission) => {
    setSelectedMission(mission);
    setIsModalOpen(true);
  };

  if (!auditPlan) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">Chargement des informations du plan d'audit...</p>
      </div>
    );
  }

  const ganttStyles = `
    .gantt svg {
      background: white;
    }
    .gantt .grid-background {
      fill: white;
    }
    .gantt .bar {
      fill: #2563eb;
      stroke: #1e40af;
    }
    .gantt .bar-progress {
      fill: #60a5fa;
      stroke: #1e40af;
    }
    .gantt text {
      fill: #1e293b;
    }
    .gantt .grid-header, .gantt .grid-row {
      fill: #fff;
      stroke: #e2e8f0;
      stroke-width: 1;
    }
    .gantt .grid-header {
      stroke: #cbd5e1;
      stroke-width: 2;
    }
    .gantt .bar-label {
      display: none;
    }
    .mission-list {
      width: 200px;
      border-right: 1px solid #e2e8f0;
      padding-right: 8px;
      display: flex;
      flex-direction: column;
    }
    .mission-header {
      height: 62px;
      border-bottom: 1px solid #e2e8f0;
    }
    .mission-item {
      height: 48px;
      display: flex;
      align-items: center;
      padding-left: 8px;
      border-bottom: 1px solid #e2e8f0;
      box-sizing: border-box;
      line-height: 24px;
      cursor: pointer;
    }
    .mission-item:hover {
      background-color: #f1f5f9;
    }
    .mission-item.selected {
      background-color: #dbeafe;
    }
    .gantt-container {
      flex: 1;
      overflow-x: auto;
      min-height: 48px;
    }
    .mission-details-panel {
      background: white;
      border-radius: 0.5rem;
      padding: 1rem;
      margin-bottom: 1rem;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      border: 1px solid #e2e8f0;
    }
  `;

  // Filter missions for display in the list
  const filteredMissions = missions.filter(mission => {
    const startDate = formatDateForGantt(mission.datedebut);
    const endDate = formatDateForGantt(mission.datefin);

    // Skip missions without valid dates
    if (!startDate || !endDate) return false;

    // Apply date filters
    if (dateDebut && endDate < dateDebut) return false;
    if (dateFin && startDate > dateFin) return false;

    return true;
  });

  return (
    <div className="space-y-4 py-4">
      <style>{ganttStyles}</style>

      {/* Loading state */}
      {isLoading && (
        <div className="text-center py-10">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-500">Chargement des missions...</p>
        </div>
      )}

      {/* Filter row */}
      {!isLoading && (
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex flex-col md:flex-row gap-4 justify-center items-center mb-6">
            <div className="flex flex-col items-start min-w-[180px]">
              <label className="text-sm font-medium mb-1">Période de calendrier</label>
              <Select value={periode} onValueChange={setPeriode}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Période" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Mois">Mois</SelectItem>
                  <SelectItem value="Trimestre">Trimestre</SelectItem>
                  <SelectItem value="Année">Année</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col items-start min-w-[180px]">
              <label className="text-sm font-medium mb-1">Date de début</label>
              <Input type="date" value={dateDebut} onChange={e => setDateDebut(e.target.value)} />
            </div>
            <div className="flex flex-col items-start min-w-[180px]">
              <label className="text-sm font-medium mb-1">Date de fin</label>
              <Input type="date" value={dateFin} onChange={e => setDateFin(e.target.value)} />
            </div>
          </div>

          {/* Two-column layout */}
          <div className="flex">
            {/* Mission names column */}
            <div className="mission-list">
              <div className="mission-header"></div>
              {filteredMissions.map(mission => (
                <div
                  key={mission.id}
                  className={`mission-item ${selectedMission?.id === mission.id ? 'selected' : ''}`}
                  onClick={() => handleMissionClick(mission)}
                >
                  <span className="text-sm text-gray-800 truncate">{mission.name}</span>
                </div>
              ))}
            </div>

            {/* Gantt chart container */}
            <div ref={ganttRef} className="gantt gantt-container" />
          </div>
        </div>
      )}

      {/* Mission Details Modal */}
      <MissionDetailsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        mission={selectedMission}
        users={users}
      />
    </div>
  );
}

export default PlanificationTab;