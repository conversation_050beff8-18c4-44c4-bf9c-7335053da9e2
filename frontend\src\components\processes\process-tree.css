.process-tree-container {
  width: 100%;
  overflow-x: auto;
  padding: 20px;
}

/* Tree View Styles */
.process-tree {
  display: flex;
  flex-direction: column;
  min-width: 800px;
}

.tree-node {
  margin-bottom: 10px;
  position: relative;
}

.tree-children {
  position: relative;
}

.tree-children::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 100%;
  background-color: #e5e7eb;
}

/* Connector lines for the tree */
.tree-connector {
  position: absolute;
  border-top: 1px dashed #e5e7eb;
  top: 50%;
  left: -20px;
  width: 20px;
}

/* Animation for expand/collapse */
.tree-children {
  transition: all 0.3s ease;
}

/* Hover effects */
.tree-node-content:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Mind Map View Styles */
.mind-map-view {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.mind-map-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.mind-map-svg {
  width: 100%;
  height: 100%;
  overflow: visible;
}

/* Node styles */
.mind-map-node {
  cursor: pointer;
  transition: all 0.3s ease;
}

.mind-map-node:hover rect {
  filter: brightness(0.95);
}

/* Connection styles */
.mind-map-connection {
  transition: all 0.3s ease;
}

/* Custom colors for different node types */
.node-business {
  border-color: #10B981;
  color: #10B981;
}

.node-organizational {
  border-color: #F97316;
  color: #F97316;
}

.node-operation {
  border-color: #A855F7;
  color: #A855F7;
}

.node-root {
  border-color: #3B82F6;
  color: #3B82F6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .process-tree {
    min-width: 100%;
  }

  .mind-map-container {
    overflow-x: auto;
    overflow-y: auto;
  }

  .mind-map-svg {
    min-width: 1000px;
    min-height: 800px;
  }
}
