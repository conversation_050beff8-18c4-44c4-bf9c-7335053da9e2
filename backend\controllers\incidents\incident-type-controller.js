const { IncidentType } = require('../../models');

// Get all incident types
const getAllIncidentTypes = async (req, res) => {
  try {
    const incidentTypes = await IncidentType.findAll();
    res.json({
      success: true,
      data: incidentTypes
    });
  } catch (error) {
    console.error('Error fetching incident types:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch incident types'
    });
  }
};

// Create new incident type
const createIncidentType = async (req, res) => {
  try {
    const {
      incidentTypeID,
      name,
      description
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name'].filter(field => !req.body[field])
      });
    }

    const incidentType = await IncidentType.create({
      incidentTypeID: incidentTypeID || `IT_${Date.now()}`,
      name,
      description: description || null
    });

    return res.status(201).json({
      success: true,
      message: 'Incident type created successfully',
      data: incidentType
    });
  } catch (error) {
    console.error('Error creating incident type:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create incident type'
    });
  }
};

// Get incident type by ID
const getIncidentTypeById = async (req, res) => {
  try {
    const { id } = req.params;
    const incidentType = await IncidentType.findByPk(id);
    
    if (!incidentType) {
      return res.status(404).json({
        success: false,
        message: 'Incident type not found'
      });
    }
    
    res.json({
      success: true,
      data: incidentType
    });
  } catch (error) {
    console.error('Error fetching incident type:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch incident type'
    });
  }
};

// Update incident type
const updateIncidentType = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description
    } = req.body;

    const incidentType = await IncidentType.findByPk(id);
    
    if (!incidentType) {
      return res.status(404).json({
        success: false,
        message: 'Incident type not found'
      });
    }

    // Update fields
    await incidentType.update({
      name: name || incidentType.name,
      description: description !== undefined ? description : incidentType.description
    });

    res.json({
      success: true,
      message: 'Incident type updated successfully',
      data: incidentType
    });
  } catch (error) {
    console.error('Error updating incident type:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update incident type'
    });
  }
};

// Delete incident type
const deleteIncidentType = async (req, res) => {
  try {
    const { id } = req.params;
    const incidentType = await IncidentType.findByPk(id);
    
    if (!incidentType) {
      return res.status(404).json({
        success: false,
        message: 'Incident type not found'
      });
    }
    
    await incidentType.destroy();
    
    res.json({
      success: true,
      message: 'Incident type deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting incident type:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete incident type'
    });
  }
};

module.exports = {
  getAllIncidentTypes,
  createIncidentType,
  getIncidentTypeById,
  updateIncidentType,
  deleteIncidentType
};
