import { cn } from "@/lib/utils";

/**
 * TabBar component for navigation between different sections
 * 
 * @param {Object} props
 * @param {string} props.activeTab - The currently active tab
 * @param {Array} props.tabs - Array of tab objects with { id, label, icon, disabled }
 * @param {Function} props.onTabChange - Function to call when a tab is clicked
 * @param {string} props.className - Additional classes for the tab bar
 */
function TabBar({ activeTab, tabs, onTabChange, className }) {
  return (
    <div className={cn("bg-white rounded-lg shadow-sm", className)}>
      <div className="border-b border-gray-200">
        <nav className="flex overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => !tab.disabled && onTabChange(tab.id)}
              disabled={tab.disabled}
              className={cn(
                "px-4 py-4 text-sm font-medium whitespace-nowrap border-b-2 flex items-center",
                activeTab === tab.id
                  ? "border-[#F62D51] text-[#F62D51]"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300",
                tab.disabled && "opacity-50 cursor-not-allowed"
              )}
            >
              {tab.icon && <span className="mr-2">{tab.icon}</span>}
              {tab.label}
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
}

export default TabBar;
