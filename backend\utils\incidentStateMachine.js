/**
 * State Machine for Incident Workflow
 * Defines valid transitions between different states in the incident workflow
 */

// Define valid transitions for each state
const stateTransitions = {
  "Start": {
    "Advance": "To Submit",
    "Reject": "Rejected",
  },
  "To Submit": {
    "Advance": "To Approve",
    "Reject": "Rejected",
    "Reset": "Start"
  },
  "To Approve": {
    "Approve": "To Validate",
    "Reject": "Rejected",
    "Reset": "Start"
  },
  "To Validate": {
    "Validate": "Validated",
    "Reject": "Rejected",
    "Reset": "Start"
  },
  "Validated": {
    "Close": "Closed",
    "Reset": "Start"
  },
  "Rejected": {
    "Reset": "Start",
    "Advance": "To Submit"
  },
  "Closed": {
    "Reset": "Start"
  }
};

/**
 * Validates if a requested transition is allowed from the current state
 * 
 * @param {string} currentState - The current state of the incident
 * @param {string} transition - The transition action being requested
 * @returns {object} - Result object with valid flag and next state (if valid)
 */
const validateTransition = (currentState, transition) => {
  // Check if current state exists in our state machine
  if (!stateTransitions[currentState]) {
    return { 
      valid: false, 
      error: `Invalid current state: ${currentState}` 
    };
  }

  // Check if the transition is valid for the current state
  if (!stateTransitions[currentState][transition]) {
    return { 
      valid: false, 
      error: `Transition '${transition}' is not allowed from state '${currentState}'` 
    };
  }

  // Return the next state if transition is valid
  return {
    valid: true,
    nextState: stateTransitions[currentState][transition]
  };
};

/**
 * Get all possible transitions from a given state
 * 
 * @param {string} state - The state to get transitions for
 * @returns {object} - Available transitions from the state
 */
const getAvailableTransitions = (state) => {
  if (!stateTransitions[state]) {
    return {};
  }
  return stateTransitions[state];
};

/**
 * Get all possible states in the workflow
 * 
 * @returns {array} - Array of all state names
 */
const getAllStates = () => {
  return Object.keys(stateTransitions);
};

module.exports = {
  validateTransition,
  getAvailableTransitions,
  getAllStates
};
