module.exports = (sequelize, DataTypes) => {
  const Event = sequelize.define('Event', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    risk_id: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Risk',
        key: 'riskID',
      },
    },
    timestamp: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    step: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    user: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    transition: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  }, {
    tableName: 'Events',
    freezeTableName: true,
    timestamps: false,
  });

  Event.associate = (models) => {
    Event.belongsTo(models.Risk, {
      foreignKey: 'risk_id',
      targetKey: 'riskID',
      as: 'risk',
    });
  };

  return Event;
}; 