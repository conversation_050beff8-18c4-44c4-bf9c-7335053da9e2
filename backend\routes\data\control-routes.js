const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllControls,
  createControl,
  getControlById,
  updateControl,
  deleteControl,
  addActionPlansToControl,
  removeActionPlanFromControl,
  replaceActionPlansForControl
} = require('../../controllers/data/control-controller');
const {
  getControlActivities
} = require('../../controllers/data/control-activity-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all controls
router.get('/', getAllControls);

// Create new control
router.post('/', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), createControl);

// Get control activities (must be before /:id route to avoid conflicts)
router.get('/:id/activity', getControlActivities);

// Get control by ID
router.get('/:id', getControlById);

// Update control
router.put('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), updateControl);

// Delete control
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteControl);

// Add one or more action plans to a control
router.post('/:id/action-plans', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), addActionPlansToControl);

// Remove an action plan from a control
router.delete('/:id/action-plans/:actionPlanID', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), removeActionPlanFromControl);

// Replace all action plans for a control
router.put('/:id/action-plans', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), replaceActionPlansForControl);

module.exports = router;
