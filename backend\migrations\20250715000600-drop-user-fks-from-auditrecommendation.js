'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('AuditRecommendations', 'responsableId');
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('AuditRecommendations', 'responsableId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: { model: 'Users', key: 'id' }
    });
  }
}; 