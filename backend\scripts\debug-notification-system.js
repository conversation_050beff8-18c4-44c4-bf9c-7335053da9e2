require('dotenv').config();
const { Sequelize } = require('sequelize');
const db = require('../models');
const fs = require('fs');
const path = require('path');

async function debugNotificationSystem() {
  console.log('=============================================');
  console.log('VITALIS NOTIFICATION SYSTEM DEBUG SCRIPT');
  console.log('=============================================');
  
  try {
    // Step 1: Check database connection
    console.log('\n1. CHECKING DATABASE CONNECTION');
    try {
      await db.sequelize.authenticate();
      console.log('✅ Database connection successful');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
    
    // Step 2: Check Notification model exists
    console.log('\n2. CHECKING NOTIFICATION MODEL');
    if (db.Notification) {
      console.log('✅ Notification model exists');
      console.log(`   Model attributes: ${Object.keys(db.Notification.rawAttributes).join(', ')}`);
    } else {
      console.error('❌ Notification model does not exist!');
      throw new Error('Notification model not found');
    }
    
    // Step 3: Check Notifications table exists in database
    console.log('\n3. CHECKING NOTIFICATIONS TABLE');
    try {
      const result = await db.sequelize.query(
        "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'Notifications')",
        { type: Sequelize.QueryTypes.SELECT }
      );
      
      if (result[0].exists) {
        console.log('✅ Notifications table exists in database');
        
        // Count notifications
        const count = await db.Notification.count();
        console.log(`   Total notifications in database: ${count}`);
        
        // Get sample notifications
        if (count > 0) {
          const notifications = await db.Notification.findAll({ limit: 3, order: [['created_at', 'DESC']] });
          console.log(`   Sample notifications:`);
          notifications.forEach(n => {
            console.log(`   - ID: ${n.id}, User: ${n.user_id}, Type: ${n.type}, Entity: ${n.entity_id}, Read: ${n.is_read}`);
          });
        }
      } else {
        console.error('❌ Notifications table does not exist in database!');
        throw new Error('Notifications table not found');
      }
    } catch (error) {
      console.error('❌ Error checking notifications table:', error.message);
      throw error;
    }
    
    // Step 4: Check notification controller
    console.log('\n4. CHECKING NOTIFICATION CONTROLLER');
    const notificationControllerPath = path.join(__dirname, '../controllers/notifications/notification-controller.js');
    
    if (fs.existsSync(notificationControllerPath)) {
      console.log('✅ Notification controller exists');
      
      // Create test notification
      try {
        const notificationController = require('../controllers/notifications/notification-controller');
        const testUser = await db.User.findOne();
        
        if (!testUser) {
          console.error('❌ No users found in database!');
        } else {
          console.log(`   Creating test notification for user ${testUser.id}`);
          
          const testNotification = {
            userId: testUser.id,
            type: 'test',
            id: 9999,
            entityName: 'Debug Script Test',
            message: 'This is a test notification from the debug script',
            assignedBy: 'Debug Script'
          };
          
          const newNotification = await notificationController.createNotification(testNotification);
          
          if (newNotification) {
            console.log('✅ Successfully created test notification in database');
            console.log(`   Notification ID: ${newNotification.id}`);
          } else {
            console.error('❌ Failed to create test notification');
          }
        }
      } catch (error) {
        console.error('❌ Error testing notification controller:', error.message);
      }
    } else {
      console.error('❌ Notification controller file not found!');
    }
    
    // Step 5: Check notification routes
    console.log('\n5. CHECKING NOTIFICATION ROUTES');
    const notificationRoutesPath = path.join(__dirname, '../routes/notifications/notification-routes.js');
    
    if (fs.existsSync(notificationRoutesPath)) {
      console.log('✅ Notification routes file exists');
      
      // Check server.js for route registration
      const serverPath = path.join(__dirname, '../server.js');
      if (fs.existsSync(serverPath)) {
        const serverContent = fs.readFileSync(serverPath, 'utf8');
        if (serverContent.includes('/api/notifications')) {
          console.log('✅ Notification routes are registered in server.js');
        } else {
          console.error('❌ Notification routes are NOT registered in server.js!');
        }
      } else {
        console.error('❌ Server.js file not found!');
      }
    } else {
      console.error('❌ Notification routes file not found!');
    }
    
    // Step 6: Check socket setup
    console.log('\n6. CHECKING SOCKET.IO SETUP');
    const socketUtilPath = path.join(__dirname, '../utils/socket-io.js');
    
    if (fs.existsSync(socketUtilPath)) {
      console.log('✅ Socket.IO utility file exists');
      
      // Check server.js for socket io usage
      const serverPath = path.join(__dirname, '../server.js');
      if (fs.existsSync(serverPath)) {
        const serverContent = fs.readFileSync(serverPath, 'utf8');
        if (serverContent.includes('socketUtils.setIo(io)')) {
          console.log('✅ Socket.IO is properly initialized in server.js');
        } else {
          console.error('❌ Socket.IO initialization not found in server.js!');
        }
        
        if (serverContent.includes('socket.join(`user-${userId}`)')) {
          console.log('✅ Socket.IO user rooms setup found in server.js');
        } else {
          console.error('❌ Socket.IO user rooms setup not found in server.js!');
        }
      }
    } else {
      console.error('❌ Socket.IO utility file not found!');
    }
    
    // Step 7: Check notification functionalities in contributor controllers
    console.log('\n7. CHECKING NOTIFICATION FUNCTIONALITY IN CONTROLLERS');
    
    const riskContribPath = path.join(__dirname, '../controllers/risks/contributor-controller.js');
    const incidentContribPath = path.join(__dirname, '../controllers/incidents/contributor-controller.js');
    
    if (fs.existsSync(riskContribPath)) {
      console.log('✅ Risk contributor controller exists');
      const riskContent = fs.readFileSync(riskContribPath, 'utf8');
      if (riskContent.includes('notificationController.createNotification')) {
        console.log('✅ Risk contributor controller uses notification creation');
      } else {
        console.error('❌ Risk contributor controller does NOT use notification creation!');
      }
      
      if (riskContent.includes('io.to(targetRoom).emit(\'notification\'')) {
        console.log('✅ Risk contributor controller emits socket notifications');
      } else {
        console.error('❌ Risk contributor controller does NOT emit socket notifications!');
      }
    } else {
      console.error('❌ Risk contributor controller not found!');
    }
    
    if (fs.existsSync(incidentContribPath)) {
      console.log('✅ Incident contributor controller exists');
      const incidentContent = fs.readFileSync(incidentContribPath, 'utf8');
      if (incidentContent.includes('notificationController.createNotification')) {
        console.log('✅ Incident contributor controller uses notification creation');
      } else {
        console.error('❌ Incident contributor controller does NOT use notification creation!');
      }
      
      if (incidentContent.includes('io.to(targetRoom).emit(\'notification\'')) {
        console.log('✅ Incident contributor controller emits socket notifications');
      } else {
        console.error('❌ Incident contributor controller does NOT emit socket notifications!');
      }
    } else {
      console.error('❌ Incident contributor controller not found!');
    }
    
    console.log('\n=============================================');
    console.log('DEBUG SCRIPT COMPLETED SUCCESSFULLY');
    console.log('=============================================');
    
  } catch (error) {
    console.error('\n=============================================');
    console.error('DEBUG SCRIPT FAILED');
    console.error('Error:', error.message);
    console.error('=============================================');
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

debugNotificationSystem(); 