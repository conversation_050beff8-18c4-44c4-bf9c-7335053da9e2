module.exports = (sequelize, DataTypes) => {
  const RiskType = sequelize.define('RiskType', {
    riskTypeID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    code: {
      type: DataTypes.STRING,
      allowNull: true
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    parentRiskType: {  // Using parentRiskType instead of parentID
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'RiskType',
        key: 'riskTypeID'
      }
    }
  }, {
    tableName: 'RiskType',
    timestamps: false
  });

  RiskType.associate = (models) => {
    RiskType.belongsTo(models.RiskType, {
      as: 'parent',
      foreignKey: 'parentRiskType',  // Using parentRiskType instead of parentID
      targetKey: 'riskTypeID'
    });
  };

  return RiskType;
};
