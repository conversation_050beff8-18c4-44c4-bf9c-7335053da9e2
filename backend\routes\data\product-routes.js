const express = require('express');
const router = express.Router();
const { verifyToken } = require('../../middleware/auth');
const {
  getAllProducts,
  createProduct,
  getProductById,
  updateProduct,
  deleteProduct
} = require('../../controllers/data/product-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all products
router.get('/', getAllProducts);

// Create new product
router.post('/', createProduct);

// Get product by ID
router.get('/:id', getProductById);

// Update product
router.put('/:id', updateProduct);

// Delete product
router.delete('/:id', deleteProduct);

module.exports = router;
