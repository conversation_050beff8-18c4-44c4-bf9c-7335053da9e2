import { useOutletContext } from "react-router-dom";
import { FileText, Tag } from "lucide-react";
import { useTranslation } from "react-i18next";

function ApplicationOverview() {
  const { application } = useOutletContext();
  const { t } = useTranslation();

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">{t('admin.applications.overview.title', 'Application Overview')}</h2>

      <div className="grid grid-cols-1 gap-8">
        <div>
          <h3 className="text-lg font-medium mb-4">{t('admin.applications.overview.details', 'Details')}</h3>
          <div className="space-y-4">
            <div className="flex items-start">
              <Tag className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.applications.form.name', 'Name')}</p>
                <p className="text-base">{application?.name}</p>
              </div>
            </div>

            <div className="flex items-start">
              <FileText className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">{t('admin.applications.form.comment', 'Comment')}</p>
                <p className="text-base whitespace-pre-wrap">{application?.comment || t('admin.applications.overview.no_comment', 'No comment provided')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ApplicationOverview;
