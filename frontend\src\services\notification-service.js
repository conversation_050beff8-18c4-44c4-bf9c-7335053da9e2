import api from '../utils/axios-config';

// Get all notifications for the current user
export const getUserNotifications = async () => {
  console.log('[Notification Service] Fetching notifications');
  try {
    console.log('[Notification Service] API instance:', api);
    console.log('[Notification Service] Current headers:', api.defaults.headers);
    
    const response = await api.get('/notifications');
    console.log('[Notification Service] Fetch response status:', response.status);
    console.log('[Notification Service] Fetch response data:', response.data);
    return response.data;
  } catch (error) {
    console.error('[Notification Service] Error fetching notifications:', error);
    console.error('[Notification Service] Error details:', error.response?.data || error.message);
    console.error('[Notification Service] Status code:', error.response?.status);
    console.error('[Notification Service] Request URL:', error.config?.url);
    console.error('[Notification Service] Request method:', error.config?.method);
    console.error('[Notification Service] Request headers:', error.config?.headers);
    throw error;
  }
};

// Mark a notification as read
export const markNotificationAsRead = async (notificationId) => {
  console.log(`[Notification Service] Marking notification as read: ${notificationId}`);
  try {
    const response = await api.put(`/notifications/${notificationId}/mark-read`);
    console.log('[Notification Service] Mark read response:', response.data);
    return response.data;
  } catch (error) {
    console.error('[Notification Service] Error marking notification as read:', error);
    console.error('[Notification Service] Error details:', error.response?.data || error.message);
    throw error;
  }
};

// Mark all notifications as read
export const markAllNotificationsAsRead = async () => {
  console.log('[Notification Service] Marking all notifications as read');
  try {
    const response = await api.put('/notifications/mark-all-read');
    console.log('[Notification Service] Mark all read response:', response.data);
    return response.data;
  } catch (error) {
    console.error('[Notification Service] Error marking all notifications as read:', error);
    console.error('[Notification Service] Error details:', error.response?.data || error.message);
    throw error;
  }
};

// Delete a notification
export const deleteNotification = async (notificationId) => {
  console.log(`[Notification Service] Deleting notification: ${notificationId}`);
  try {
    const response = await api.delete(`/notifications/${notificationId}`);
    console.log('[Notification Service] Delete response:', response.data);
    return response.data;
  } catch (error) {
    console.error('[Notification Service] Error deleting notification:', error);
    console.error('[Notification Service] Error details:', error.response?.data || error.message);
    throw error;
  }
}; 