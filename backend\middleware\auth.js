const jwt = require('jsonwebtoken');
const { User, Role } = require('../models');

const verifyToken = (req, res, next) => {
  // Check for token in multiple places
  let token = null;
  
  // First try Authorization header
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.split(' ')[1];
    console.log('[AUTH] Token found in Authorization header');
  }
  
  // If no token in Authorization header, try cookies
  if (!token && req.cookies.token) {
    token = req.cookies.token;
    console.log('[AUTH] Token found in cookies');
  }
  
  // Finally try query params
  if (!token && req.query.token) {
    token = req.query.token;
    console.log('[AUTH] Token found in query params');
  }

  if (!token) {
    console.log('[AUTH] No token found in request');
    console.log('[AUTH] Headers:', JSON.stringify(req.headers, null, 2));
    return res.status(401).json({ success: false, message: 'No token provided' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    console.log('[AUTH] Token verified successfully for user:', decoded.userId);
    next();
  } catch (error) {
    console.error('[AUTH] Token verification error:', error);
    return res.status(401).json({ success: false, message: 'Invalid token' });
  }
};

// Alias for verifyToken to maintain compatibility with existing code
const authenticateToken = verifyToken;

// Middleware to load user roles
const loadUserRoles = async (req, res, next) => {
  try {
    // Skip if no user or if roles are already loaded
    if (!req.user || req.user.roles) {
      return next();
    }

    console.log('[AUTH] Loading roles for user:', req.user.userId);
    const user = await User.findByPk(req.user.userId, {
      include: [
        {
          model: Role,
          as: 'roles',
          through: { attributes: [] }
        }
      ]
    });

    if (!user) {
      console.log('[AUTH] User not found in database:', req.user.userId);
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    // Add roles to the request object
    req.user.roles = user.roles.map(role => ({
      id: role.id,
      name: role.name,
      code: role.code
    }));
    
    console.log('[AUTH] Loaded roles for user:', req.user.userId, 'Roles:', req.user.roles.map(r => r.code));
    next();
  } catch (error) {
    console.error('[AUTH] Error loading user roles:', error);
    return res.status(500).json({
      success: false,
      message: 'Error loading user roles'
    });
  }
};

// Authorize roles middleware
const authorizeRoles = (roleCodes) => {
  return async (req, res, next) => {
    console.log('[AUTH] Checking authorization for roles:', roleCodes);
    
    // Ensure user roles are loaded
    if (!req.user.roles) {
      console.log('[AUTH] Roles not loaded, loading now...');
      await loadUserRoles(req, res, () => {});
    }

    // Convert legacy role codes to new role codes if needed
    const normalizedRoleCodes = roleCodes.map(code => {
      // If it's a legacy role code, convert it to the new format
      if (code === 'super_admin') return 'grc_admin';
      if (code === 'admin') return [
        'grc_manager',
        'risk_manager',
        'incident_manager',
        'internal_controller',
        'compliance_manager',
        'audit_director',
        'auditor'
      ];
      if (code === 'user') return 'grc_contributor';
      return code;
    }).flat();

    console.log('[AUTH] Normalized role codes:', normalizedRoleCodes);
    console.log('[AUTH] User roles:', req.user.roles ? req.user.roles.map(r => r.code) : 'No roles');

    // Check if user has any of the required roles
    const userRoleCodes = req.user.roles ? req.user.roles.map(r => r.code.toLowerCase()) : [];
    const hasRequiredRole = normalizedRoleCodes.some(code => 
      userRoleCodes.includes(code.toLowerCase())
    );

    console.log('[AUTH] Has required role:', hasRequiredRole);

    if (!hasRequiredRole) {
      console.log('[AUTH] Access denied for user:', req.user.userId);
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to perform this action'
      });
    }

    console.log('[AUTH] Access granted for user:', req.user.userId);
    next();
  };
};

module.exports = {
  verifyToken,
  authenticateToken,
  authorizeRoles,
  loadUserRoles
};
