import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export function NewIncidentTypeModal({ open, onClose, onSubmit }) {
  const { t } = useTranslation();
  const [newIncidentType, setNewIncidentType] = useState({
    name: "",
    code: "",
    comment: ""
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    e.stopPropagation();

    // Validate the form
    if (!newIncidentType.name) {
      alert('Incident type name is required');
      return;
    }

    // Call the onSubmit callback
    onSubmit(newIncidentType);

    // Reset the form
    setNewIncidentType({
      name: "",
      code: "",
      comment: ""
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{t('admin.incidents.new_incident_type.title', 'Add New Incident Type')}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('admin.incidents.new_incident_type.name', 'Name')}</label>
            <Input
              value={newIncidentType.name}
              onChange={(e) => setNewIncidentType({ ...newIncidentType, name: e.target.value })}
              required
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">{t('admin.incidents.new_incident_type.code', 'Code')}</label>
            <Input
              value={newIncidentType.code}
              onChange={(e) => setNewIncidentType({ ...newIncidentType, code: e.target.value })}
              placeholder={t('admin.incidents.new_incident_type.code_placeholder', 'Incident type code (optional)')}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">{t('admin.incidents.new_incident_type.comment', 'Comment')}</label>
            <Textarea
              value={newIncidentType.comment}
              onChange={(e) => setNewIncidentType({ ...newIncidentType, comment: e.target.value })}
              placeholder={t('admin.incidents.new_incident_type.comment_placeholder', 'Add any additional comments about this incident type')}
              className="min-h-[100px]"
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              {t('admin.incidents.new_incident_type.cancel', 'Cancel')}
            </Button>
            <Button type="submit" className="bg-[#F62D51] hover:bg-red-700">
              {t('admin.incidents.new_incident_type.add_incident_type', 'Add Incident Type')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
