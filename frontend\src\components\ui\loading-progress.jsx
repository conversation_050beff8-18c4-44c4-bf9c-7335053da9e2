import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';

/**
 * LoadingProgress - A component that shows a loading progress indicator
 * 
 * @param {Object} props
 * @param {boolean} props.isLoading - Whether the component is in loading state
 * @param {number} props.duration - Duration of the loading animation in milliseconds (default: 2000)
 * @param {string} props.message - Optional message to display during loading
 * @param {function} props.onComplete - Callback function when loading completes
 */
const LoadingProgress = ({ 
  isLoading, 
  duration = 2000, 
  message = "Loading data...",
  onComplete = () => {} 
}) => {
  const [progress, setProgress] = useState(0);
  const [showLoader, setShowLoader] = useState(true);

  useEffect(() => {
    if (!isLoading) {
      // If not loading, reset progress
      setProgress(0);
      setShowLoader(false);
      return;
    }

    // Reset state when loading starts
    setProgress(0);
    setShowLoader(true);

    // Calculate interval based on duration
    const interval = duration / 100;
    let currentProgress = 0;

    // Start progress animation
    const timer = setInterval(() => {
      // Increment progress faster at the beginning, slower towards the end
      const increment = currentProgress < 70 ? 2 : currentProgress < 90 ? 1 : 0.5;
      
      currentProgress += increment;
      
      if (currentProgress >= 100) {
        clearInterval(timer);
        setProgress(100);
        
        // Hide loader after a short delay
        setTimeout(() => {
          setShowLoader(false);
          onComplete();
        }, 500);
      } else {
        setProgress(currentProgress);
      }
    }, interval);

    return () => {
      clearInterval(timer);
    };
  }, [isLoading, duration, onComplete]);

  if (!showLoader) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg p-6 w-80 max-w-md">
        <div className="flex items-center mb-4">
          <Loader2 className="h-6 w-6 animate-spin text-[#1a2942] mr-3" />
          <h3 className="text-lg font-medium text-[#1a2942]">{message}</h3>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2">
          <div 
            className="bg-[#1a2942] h-2.5 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        
        <div className="text-right text-sm text-gray-600">
          {Math.round(progress)}%
        </div>
      </div>
    </div>
  );
};

export default LoadingProgress;
