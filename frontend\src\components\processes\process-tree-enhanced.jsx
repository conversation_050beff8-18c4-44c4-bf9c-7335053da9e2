import { useState, useRef, useEffect } from "react";
import { ChevronRight, ChevronDown, FileText, ExternalLink, Eye } from "lucide-react";
import { useNavigate } from "react-router-dom";
import bpsIcon from '@/assets/BPS.png';
import orgIcon from '@/assets/org.png';
import operationIcon from '@/assets/operation.png';
import riskIcon from '@/assets/risk.png';
import incidentIcon from '@/assets/incident.png';
import { Button } from "@/components/ui/button";
// Tooltip components removed to avoid dependency issues

// Inline styles
const styles = {
  processTreeContainer: {
    width: '100%',
    overflowX: 'auto',
    padding: '20px'
  },
  processTree: {
    display: 'flex',
    flexDirection: 'column',
    minWidth: '800px'
  },
  treeNode: {
    marginBottom: '5px',
    position: 'relative'
  },
  treeChildren: {
    position: 'relative',
    paddingLeft: '25px',
    marginLeft: '5px',
    borderLeft: '1px dashed #a0aec0',
    marginTop: '5px',
    marginBottom: '5px'
  },
  mindMapContainer: {
    width: '100%',
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden'
  },
  mindMapSvg: {
    width: '100%',
    height: '100%',
    overflow: 'visible'
  }
}

const ProcessTreeEnhanced = ({ data, onNodeSelect }) => {
  const [viewMode, setViewMode] = useState("tree"); // "tree" or "mindmap"

  return (
    <div style={styles.processTreeContainer}>
      <div className="flex justify-between items-center mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-4 inline-block">
          <h3 className="text-lg font-medium text-slate-800">Process Hierarchy</h3>
        </div>
        <div className="flex space-x-2">
          <button
            className={`px-4 py-2 rounded-md transition-all ${
              viewMode === 'tree' 
                ? 'bg-blue-100 text-blue-700 border border-blue-200' 
                : 'bg-slate-100 text-slate-700 hover:bg-slate-200 border border-slate-200'
            }`}
            onClick={() => setViewMode("tree")}
          >
            Tree View
          </button>
          <button
            className={`px-4 py-2 rounded-md transition-all ${
              viewMode === 'mindmap' 
                ? 'bg-blue-100 text-blue-700 border border-blue-200' 
                : 'bg-slate-100 text-slate-700 hover:bg-slate-200 border border-slate-200'
            }`}
            onClick={() => setViewMode("mindmap")}
          >
            Mind Map
          </button>
        </div>
      </div>

      {viewMode === "tree" ? (
        <div style={styles.processTree}>
          {data.map((node) => (
            <TreeNode key={node.id} node={node} level={0} onSelect={onNodeSelect} />
          ))}
        </div>
      ) : (
        <div style={{ height: '600px' }}>
          <MindMapView data={data} onNodeSelect={onNodeSelect} />
        </div>
      )}
    </div>
  );
};

const TreeNode = ({ node, level, onSelect }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const hasChildren = node.children && node.children.length > 0;
  const navigate = useNavigate();

  const toggleExpand = (e) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  // Handle node click - now expands/collapses the node
  const handleNodeClick = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded);
    }
  };

  // Handle select button click - focus on this process
  const handleSelectClick = (e) => {
    e.stopPropagation();
    if (onSelect) {
      onSelect(node.id);
    }
  };

  // Handle consult button click - navigate to edit page (only for risks and incidents)
  const handleConsultClick = (e) => {
    e.stopPropagation();

    // Check if we're in super-admin context
    const isSuperAdmin = window.location.pathname.includes('/super-admin/');
    const basePath = isSuperAdmin ? '/super-admin' : '/admin';

    // Navigate to the appropriate edit page based on node type
    switch (node.type) {
      case "risk":
        navigate(`${basePath}/risks/edit/${node.id}`);
        break;
      case "incident":
        navigate(`${basePath}/incident/edit/${node.id}`);
        break;
      default:
        console.warn(`No edit route defined for node type: ${node.type}`);
    }
  };

  // Determine the color based on the node type
  const getColor = () => {
    switch (node.type) {
      case "business":
        return "#10B981"; // Green
      case "organizational":
        return "#F97316"; // Orange
      case "operation":
        return "#A855F7"; // Purple
      case "risk":
        return "#F62D51"; // Red
      case "incident":
        return "#3B82F6"; // Blue
      default:
        return "#6B7280"; // Gray
    }
  };

  // Get the appropriate icon based on node type
  const getIcon = () => {
    switch (node.type) {
      case "business":
        return <img src={bpsIcon} className="w-4 h-4" alt="business process" />;
      case "organizational":
        return <img src={orgIcon} className="w-4 h-4" alt="organizational process" />;
      case "operation":
        return <img src={operationIcon} className="w-4 h-4" alt="operation" />;
      case "risk":
        return <img src={riskIcon} className="w-4 h-4" alt="risk" />;
      case "incident":
        return <img src={incidentIcon} className="w-4 h-4" alt="incident" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const color = getColor();
  const nodeIcon = getIcon();

  return (
    <div style={{ ...styles.treeNode }}>
      <div className="flex items-center mb-1">
        {hasChildren ? (
          <button
            onClick={toggleExpand}
            className="mr-1 focus:outline-none flex items-center justify-center w-4 h-4 rounded bg-white border border-slate-200 hover:bg-slate-50"
            aria-label={isExpanded ? "Collapse" : "Expand"}
          >
            {isExpanded ? (
              <ChevronDown className="w-3 h-3 text-slate-500" />
            ) : (
              <ChevronRight className="w-3 h-3 text-slate-500" />
            )}
          </button>
        ) : (
          <div className="w-4 h-4 mr-1" /> // Empty space for alignment
        )}

        <div
          className="flex items-center justify-between py-1 px-2 rounded-md cursor-pointer w-full tree-node"
          style={{
            backgroundColor: 'white',
            borderLeft: `3px solid ${color}`,
            borderTop: '1px solid #e0e0e0',
            borderRight: '1px solid #e0e0e0',
            borderBottom: '1px solid #e0e0e0',
            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
            transition: 'all 0.2s ease'
          }}
          onClick={handleNodeClick}
        >
          <div className="flex items-center">
            <span className="mr-1.5">{nodeIcon}</span>
            <span className="font-medium text-slate-700 text-sm">
              {node.name}
            </span>
            {node.code && (
              <span className="ml-1.5 text-xs text-slate-500">({node.code})</span>
            )}
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-1" onClick={(e) => e.stopPropagation()}>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 rounded-md hover:bg-slate-100"
              onClick={handleSelectClick}
              title="Focus on this process"
            >
              <Eye className="h-3.5 w-3.5 text-slate-500" />
            </Button>

            {/* Only show consult button for risks and incidents */}
            {(node.type === "risk" || node.type === "incident") && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 rounded-md hover:bg-slate-100"
                onClick={handleConsultClick}
                title="Consult this process"
              >
                <ExternalLink className="h-3.5 w-3.5 text-slate-500" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {hasChildren && isExpanded && (
        <div className="tree-children">
          {node.children.map((childNode) => (
            <TreeNode key={childNode.id} node={childNode} level={level + 1} onSelect={onSelect} />
          ))}
        </div>
      )}
    </div>
  );
};

const MindMapView = ({ data, onNodeSelect }) => {
  const svgRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 1000, height: 800 });
  const navigate = useNavigate();

  useEffect(() => {
    if (svgRef.current) {
      const updateDimensions = () => {
        const container = svgRef.current.parentElement;
        if (container) {
          setDimensions({
            width: Math.max(container.clientWidth, 1000),
            height: Math.max(container.clientHeight, 800)
          });
        }
      };

      updateDimensions();
      window.addEventListener('resize', updateDimensions);

      return () => window.removeEventListener('resize', updateDimensions);
    }
  }, []);

  // Calculate positions for the mind map nodes
  const centerX = dimensions.width / 2;
  const centerY = dimensions.height / 2;

  // Root node is centered
  const rootNode = {
    x: centerX,
    y: centerY,
    label: "Process Hierarchy",
    type: "root"
  };

  // Position business processes horizontally from the root
  const businessProcessNodes = data.map((bp, index) => {
    const angle = (index / data.length) * Math.PI * 2;
    const distance = 200;

    return {
      x: centerX + Math.cos(angle) * distance,
      y: centerY + Math.sin(angle) * distance,
      label: bp.name,
      type: "business",
      id: bp.id,
      children: bp.children,
      code: bp.code
    };
  });

  // For each business process, position its organizational processes
  const allNodes = [rootNode, ...businessProcessNodes];
  const connections = [];

  // Add connections from root to business processes
  businessProcessNodes.forEach(bpNode => {
    connections.push({
      from: rootNode,
      to: bpNode,
      type: "business"
    });

    // Position organizational processes, risks, and incidents for this business process
    if (bpNode.children && bpNode.children.length > 0) {
      // Separate children by type
      const orgProcesses = bpNode.children.filter(child => child.type === "organizational");
      const directChildren = bpNode.children.filter(child => child.type === "risk" || child.type === "incident");

      // Position direct risks and incidents
      if (directChildren.length > 0) {
        directChildren.forEach((child, dcIndex) => {
          const dcAngle = ((dcIndex / directChildren.length) * Math.PI * 0.5) +
                        (Math.atan2(bpNode.y - centerY, bpNode.x - centerX) + Math.PI * 0.75);
          const dcDistance = 120;

          const dcNode = {
            x: bpNode.x + Math.cos(dcAngle) * dcDistance,
            y: bpNode.y + Math.sin(dcAngle) * dcDistance,
            label: child.name,
            type: child.type,
            id: child.id,
            code: child.code
          };

          allNodes.push(dcNode);
          connections.push({
            from: bpNode,
            to: dcNode,
            type: child.type
          });
        });
      }

      // Position organizational processes
      const orgProcessNodes = orgProcesses.map((orgProcess, index) => {
        const angle = ((index / orgProcesses.length) * Math.PI) +
                      (Math.atan2(bpNode.y - centerY, bpNode.x - centerX));
        const distance = 150;

        const node = {
          x: bpNode.x + Math.cos(angle) * distance,
          y: bpNode.y + Math.sin(angle) * distance,
          label: orgProcess.name,
          type: "organizational",
          id: orgProcess.id,
          children: orgProcess.children,
          code: orgProcess.code
        };

        allNodes.push(node);
        connections.push({
          from: bpNode,
          to: node,
          type: "organizational"
        });

        // Position operations, risks, and incidents for this organizational process
        if (orgProcess.children && orgProcess.children.length > 0) {
          const childNodes = orgProcess.children.map((child, childIndex) => {
            const childAngle = ((childIndex / orgProcess.children.length) * Math.PI) +
                          (Math.atan2(node.y - bpNode.y, node.x - bpNode.x));
            const childDistance = 100;

            const childNode = {
              x: node.x + Math.cos(childAngle) * childDistance,
              y: node.y + Math.sin(childAngle) * childDistance,
              label: child.name,
              type: child.type,
              id: child.id,
              code: child.code,
              children: child.children || []
            };

            allNodes.push(childNode);
            connections.push({
              from: node,
              to: childNode,
              type: child.type
            });

            // If this is an operation with children (risks or incidents), position them
            if (child.type === "operation" && child.children && child.children.length > 0) {
              child.children.forEach((grandchild, gcIndex) => {
                const gcAngle = ((gcIndex / child.children.length) * Math.PI) +
                              (Math.atan2(childNode.y - node.y, childNode.x - node.x));
                const gcDistance = 70;

                const gcNode = {
                  x: childNode.x + Math.cos(gcAngle) * gcDistance,
                  y: childNode.y + Math.sin(gcAngle) * gcDistance,
                  label: grandchild.name,
                  type: grandchild.type,
                  id: grandchild.id,
                  code: grandchild.code
                };

                allNodes.push(gcNode);
                connections.push({
                  from: childNode,
                  to: gcNode,
                  type: grandchild.type
                });
              });
            }

            return childNode;
          });
        }

        return node;
      });
    }
  });

  // Get color based on node type
  const getColor = (type) => {
    switch (type) {
      case "business":
        return "#10B981"; // Green
      case "organizational":
        return "#F97316"; // Orange
      case "operation":
        return "#A855F7"; // Purple
      case "risk":
        return "#F62D51"; // Red
      case "incident":
        return "#3B82F6"; // Blue
      case "root":
        return "#3B82F6"; // Blue
      default:
        return "#6B7280"; // Gray
    }
  };

  // Get the appropriate icon based on node type
  const getIcon = (type) => {
    switch (type) {
      case "business":
        return bpsIcon;
      case "organizational":
        return orgIcon;
      case "operation":
        return operationIcon;
      case "risk":
        return riskIcon;
      case "incident":
        return incidentIcon;
      default:
        return null;
    }
  };

  // Handle node selection
  const handleNodeSelect = (id) => {
    if (onNodeSelect && id) {
      onNodeSelect(id);
    }
  };

  return (
    <div style={{ ...styles.mindMapContainer, height: "800px" }}>
      <svg
        ref={svgRef}
        width={dimensions.width}
        height={dimensions.height}
        style={styles.mindMapSvg}
      >
        {/* Define drop shadow filter */}
        <defs>
          <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="2" />
            <feOffset dx="0" dy="1" result="offsetblur" />
            <feComponentTransfer>
              <feFuncA type="linear" slope="0.1" />
            </feComponentTransfer>
            <feMerge>
              <feMergeNode />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>

        {/* Draw connections first so they appear behind nodes */}
        {connections.map((connection, index) => {
          const color = getColor(connection.type);
          return (
            <path
              key={`connection-${index}`}
              d={`M${connection.from.x},${connection.from.y} C${(connection.from.x + connection.to.x) / 2},${connection.from.y} ${(connection.from.x + connection.to.x) / 2},${connection.to.y} ${connection.to.x},${connection.to.y}`}
              stroke={color}
              strokeWidth="2"
              fill="none"
              strokeDasharray={connection.type === "root" ? "0" : "0"}
            />
          );
        })}

        {/* Draw nodes */}
        {allNodes.map((node, index) => {
          const color = getColor(node.type);
          const iconSrc = getIcon(node.type);

          // Skip rendering action buttons for root node
          if (node.type === "root") {
            return (
              <g
                key={`node-${index}`}
                transform={`translate(${node.x},${node.y})`}
              >
                {/* Node background */}
                <rect
                  x="-60"
                  y="-15"
                  width="120"
                  height="30"
                  rx="6"
                  ry="6"
                  fill="#2563eb"
                  stroke="#1d4ed8"
                  strokeWidth="1"
                  filter="url(#dropShadow)"
                />

                {/* Node label */}
                <text
                  textAnchor="middle"
                  dominantBaseline="middle"
                  fill="white"
                  fontWeight="500"
                  fontSize="12px"
                >
                  {node.label}
                </text>
              </g>
            );
          }

          return (
            <g
              key={`node-${index}`}
              transform={`translate(${node.x},${node.y})`}
            >
              {/* Node background */}
              <rect
                x="-60"
                y="-15"
                width="120"
                height="30"
                rx="6"
                ry="6"
                fill="white"
                stroke={color}
                strokeWidth="1.5"
                className="cursor-pointer"
                onClick={() => handleNodeSelect(node.id)}
                filter="url(#dropShadow)"
              />

              {/* Icon if available */}
              {iconSrc && (
                <image
                  href={iconSrc}
                  x="-50"
                  y="-10"
                  height="20"
                  width="20"
                />
              )}

              {/* Node label */}
              <text
                textAnchor="middle"
                dominantBaseline="middle"
                fill="#4b5563"
                fontWeight="500"
                fontSize="11px"
                x={iconSrc ? "5" : "0"}
                className="cursor-pointer"
                onClick={() => handleNodeSelect(node.id)}
              >
                {node.label}
              </text>
            </g>
          );
        })}
      </svg>
    </div>
  );
};

export default ProcessTreeEnhanced;
