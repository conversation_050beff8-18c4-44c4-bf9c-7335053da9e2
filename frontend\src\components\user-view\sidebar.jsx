import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import {
  LayoutDashboard,
  AlertTriangle,
  Settings,
  FileText,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import logo2 from "../../assets/whitelogovitalis.png";

const userSidebarMenuItems = [
  {
    id: "dashboard",
    label: "Dashboard",
    path: "/user/home",
    icon: <LayoutDashboard className="h-5 w-5" />,
  },
  {
    id: "incidents",
    label: "Incidents",
    path: "/user/incidents",
    icon: <AlertTriangle className="h-5 w-5" />,
  },
  {
    id: "risks",
    label: "Risks",
    path: "/user/risks",
    icon: <AlertTriangle className="h-5 w-5" />,
  },
  {
    id: "reports",
    label: "Reports",
    path: "/user/reports",
    icon: <FileText className="h-5 w-5" />,
  },
  {
    id: "settings",
    label: "Settings",
    path: "/user/settings",
    icon: <Settings className="h-5 w-5" />,
  },
];

function MenuItem({ item, isActive, onClick }) {
  return (
    <Button
      variant="ghost"
      className={cn(
        "w-full justify-start text-white hover:bg-white hover:text-[#242A33]",
        isActive(item.path) && "bg-red-500 text-white hover:bg-red-500 hover:text-white"
      )}
      onClick={() => onClick(item.path)}
    >
      {item.icon}
      <span className="ml-2">{item.label}</span>
    </Button>
  );
}

function UserSideBar({ open, setOpen }) {
  const navigate = useNavigate();
  const location = useLocation();

  const isActive = (path) => {
    if (path === "/user/home") {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };

  return (
    <>
      {/* Mobile Sidebar */}
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetContent
          side="left"
          className="w-64 bg-[#242A33] text-white border-[#3A424E] p-0"
        >
          <SidebarContent
            navigate={navigate}
            isActive={isActive}
            setOpen={setOpen}
          />
        </SheetContent>
      </Sheet>

      {/* Desktop Sidebar */}
      <aside className="hidden lg:flex flex-col border-r border-[#3A424E] bg-[#242A33] h-screen w-64 fixed">
        <SidebarContent navigate={navigate} isActive={isActive} />
      </aside>
    </>
  );
}

function SidebarContent({ navigate, isActive, setOpen }) {
  const handleNavigate = (path) => {
    navigate(path);
    setOpen?.(false);
  };

  // Get user data from Redux store
  const user = useSelector((state) => state.auth.user);

  return (
    <div className="flex flex-col h-full">
      {/* Logo */}
      <div className="p-4 border-b border-[#3A424E] flex justify-center">
        <div
          onClick={() => handleNavigate("/user/home")}
          className="flex justify-center cursor-pointer w-full"
        >
          <img src={logo2} alt="Logo" className="h-8 w-auto" />
        </div>
      </div>

      {/* Menu Items */}
      <ScrollArea className="flex-1 px-3 py-4">
        <div className="space-y-2">
          {userSidebarMenuItems.map((item) => (
            <MenuItem
              key={item.id}
              item={item}
              isActive={isActive}
              onClick={handleNavigate}
            />
          ))}
        </div>
      </ScrollArea>

      {/* Profile Section */}
      <div className="border-t border-[#3A424E] p-4">
        <div className="flex items-center gap-3">
          <Avatar>
            <AvatarImage src="/placeholder-avatar.jpg" alt={user?.username || 'User'} />
            <AvatarFallback>{user?.username?.charAt(0)?.toUpperCase() || 'U'}</AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <span className="text-sm font-medium text-white">{user?.username || 'User'}</span>
            <span className="text-xs text-gray-400">{user?.email || '<EMAIL>'}</span>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="ml-auto text-white hover:bg-gray-700"
            onClick={() => handleNavigate("/user/profile")}
          >
            <Settings className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export default UserSideBar; 
