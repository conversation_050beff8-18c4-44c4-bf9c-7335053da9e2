import { useState, useEffect, use<PERSON>emo } from "react";
import { Plus, ArrowUpDown, Trash2, Loader2, Building2, Filter, ChevronUp } from "lucide-react"; // Added icons
import { useSelector } from 'react-redux';
import { hasPermission } from '@/store/auth-slice';
import PageHeader from "@/components/ui/page-header";
import axios from "axios";
import { Button } from "../../../../components/ui/button";
import { Checkbox } from "../../../../components/ui/checkbox";
import { Badge } from "../../../../components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
} from "../../../../components/ui/dialog";
import { Input } from "../../../../components/ui/input";
import { Label } from "../../../../components/ui/label";
import { toast } from "sonner";
import TablePagination from "../../../../components/ui/table-pagination";
import FilterPanel from "../../../../components/ui/filter-panel";
import entityIcon from '@/assets/entity.png';
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from "react-i18next";

function EntitiesManagement() {
  const { t } = useTranslation();
  const canCreate = useSelector(state => hasPermission(state, 'create'));
  const canDelete = useSelector(state => hasPermission(state, 'delete'));
  const [entities, setEntities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [selectedEntities, setSelectedEntities] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "asc" });
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const API_BASE_URL = getApiBaseUrl();
  // Filter states
  const [filters, setFilters] = useState({
    type: "all",
    internalExternal: "all",
    localCurrency: "all",
    parentEntityID: "all",
  });

  // Clear all filters
  const clearFilters = () => {
    console.log('Clearing all filters');
    const clearedFilters = {
      type: "all",
      internalExternal: "all",
      localCurrency: "all",
      parentEntityID: "all",
    };
    setFilters(clearedFilters);
    console.log('Current filters before clearing:', filters);
    console.log('Filters cleared to:', clearedFilters);
  };

  const [newEntity, setNewEntity] = useState({
    entityID: "",
    name: "",
    code: "",
    comment: "",
    type: "",
    internalExternal: "Internal",
    localCurrency: "",
    parentEntityID: null,
  });

  const [entityToUpdate, setEntityToUpdate] = useState(null);

  const entityMap = useMemo(() => {
    return new Map(entities.map((e) => [e.entityID, e.name]));
  }, [entities]);

  const fetchEntities = async (signal, retries = 2) => {
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        setLoading(true);
        console.log(`Fetching entities (attempt ${attempt + 1}) at ${new Date().toISOString()}`);
        const response = await axios.get(`${API_BASE_URL}/entities`, {
          withCredentials: true,
          signal,
          headers: { "Content-Type": "application/json" },
          timeout: 120000,
        });

        if (response.data.success) {
          const allEntities = response.data.data || [];
          setEntities(allEntities);
          setError(null);
          console.log(`Fetch successful at ${new Date().toISOString()}`);
          return true;
        }
      } catch (_error) { // Renamed to _error and used
        if (axios.isCancel(_error)) {
          console.log("Request cancelled");
          return false;
        }
        console.error(`Error fetching entities (attempt ${attempt + 1}):`, _error);
        if (attempt === retries) {
          toast.error(_error.message || "Failed to fetch entities after multiple attempts. Please refresh the page.");
          setEntities([]);
          setError(_error.message);
        }
        await new Promise((resolve) => setTimeout(resolve, 2000 * (attempt + 1)));
      } finally {
        setLoading(false);
      }
    }
    return false;
  };

  useEffect(() => {
    const controller = new AbortController();
    fetchEntities(controller.signal);
    return () => controller.abort();
  }, []);

  const handleSort = (key) => {
    setSortConfig((prevConfig) => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === "asc" ? "desc" : "asc",
    }));
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedEntities(filteredEntities.map((entity) => entity.entityID));
    } else {
      setSelectedEntities([]);
    }
  };

  const handleSelectEntity = (id) => {
    setSelectedEntities((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const handleRowClick = (id) => {
    // Navigate to the entity edit page
    window.location.href = `/admin/data/entities/${id}`;
  };

  const handleDeleteSelected = async () => {
    if (selectedEntities.length === 0) {
      toast.error(t('admin.entities.errors.no_selection', 'No entities selected'));
      return;
    }

    const dependentEntities = [];
    selectedEntities.forEach((selectedId) => {
      const dependents = entities.filter((entity) => entity.parentEntityID === selectedId);
      if (dependents.length > 0) {
        const entityName = entityMap.get(selectedId) || selectedId;
        dependentEntities.push({
          id: selectedId,
          name: entityName,
          dependents: dependents.map((dep) => entityMap.get(dep.entityID) || dep.entityID),
        });
      }
    });

    if (dependentEntities.length > 0) {
      const message = dependentEntities
        .map((dep) => t('admin.entities.errors.cannot_delete_parent', 'Cannot delete "{{name}}" (ID: {{id}}) because it is a parent of: {{dependents}}.', {
          name: dep.name,
          id: dep.id,
          dependents: dep.dependents.join(', ')
        }))
        .join(' ');
      toast.error(message, {
        position: "top-center",
        duration: 6000,
        style: {
          background: '#f8d7da',
          color: '#721c24',
          border: '1px solid #f5c6cb',
          padding: '16px',
          maxWidth: '80%',
          textAlign: 'center',
        },
      });
      return;
    }

    if (window.confirm(t('admin.entities.confirm_delete', 'Are you sure you want to delete {{count}} selected entity(s)?', { count: selectedEntities.length }))) {
      try {
        setLoading(true);
        let failedDeletions = [];

        const attemptDelete = async (entityID, retries = 2) => {
          for (let attempt = 0; attempt <= retries; attempt++) {
            try {
              await axios.delete(`${API_BASE_URL}/entities/${entityID}`, {
                withCredentials: true,
                timeout: 60000,
                headers: { "Content-Type": "application/json" },
              });
              return { success: true };
            } catch (error) {
              if (attempt === retries) {
                let userMessage = error.response?.data?.message || 'Unknown error';
                if (userMessage.includes("It is referenced by other records")) {
                  userMessage = `Cannot delete entity ${entityID}: It is referenced by other records. Please reassign or remove dependent records first.`;
                }
                return { success: false, error: userMessage };
              }
              await new Promise((resolve) => setTimeout(resolve, 1000 * (attempt + 1)));
            }
          }
        };

        const batchSize = 3;
        for (let i = 0; i < selectedEntities.length; i += batchSize) {
          const batch = selectedEntities.slice(i, i + batchSize);
          await Promise.all(
            batch.map(async (entityID) => {
              const result = await attemptDelete(entityID);
              if (!result.success) {
                failedDeletions.push({ id: entityID, error: result.error });
              }
              await new Promise((resolve) => setTimeout(resolve, 500));
              return result;
            })
          );
          if (i + batchSize < selectedEntities.length) {
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
        }

        const controller = new AbortController();
        await fetchEntities(controller.signal);
        setSelectedEntities([]);

        if (failedDeletions.length > 0) {
          const errorMessage = failedDeletions.map((f) => f.error).join('; ');
          toast.error(t('admin.entities.errors.delete_failed', 'Failed to delete {{count}} entity(s): {{error}}', {
            count: failedDeletions.length,
            error: errorMessage
          }), {
            duration: 6000,
          });
        } else {
          toast.success(t('admin.entities.success.delete', 'All selected entities deleted successfully'));
        }
      } catch (_error) {
        console.error("Error in deletion process:", _error);
        toast.error(_error.message || t('admin.entities.errors.delete_failed_generic', 'Failed to delete entities'));
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const entityToCreate = {
      ...newEntity,
      entityID: newEntity.entityID || `ENT_${Date.now()}`,
      code: newEntity.code || null,
      comment: newEntity.comment || null,
      type: newEntity.type || null,
      internalExternal: newEntity.internalExternal || "Internal",
      localCurrency: newEntity.localCurrency || null,
      parentEntityID: newEntity.parentEntityID || null,
    };

    try {
      setSubmitting(true);
      const response = await axios.post(
        `${API_BASE_URL}/entities`,
        entityToCreate,
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
          timeout: 60000,
        }
      );

      if (response.data.success) {
        toast.success("Entity created successfully");
        setNewEntity({
          entityID: "",
          name: "",
          code: "",
          comment: "",
          type: "",
          internalExternal: "Internal",
          localCurrency: "",
          parentEntityID: null,
        });
        setIsOpen(false);
        const controller = new AbortController();
        await fetchEntities(controller.signal);
        setCurrentPage(1);
      }
    } catch (error) {
      console.error("Error creating entity:", error);
      toast.error(error.response?.data?.message || "Failed to create entity");
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateSubmit = async (e) => {
    e.preventDefault();

    try {
      setSubmitting(true);
      const response = await axios.put(
        `${API_BASE_URL}/entities/${entityToUpdate.entityID}`,
        entityToUpdate,
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
          timeout: 60000,
        }
      );

      if (response.data.success) {
        toast.success("Entity updated successfully");
        setIsUpdateModalOpen(false);
        setEntityToUpdate(null);
        const controller = new AbortController();
        await fetchEntities(controller.signal);
      }
    } catch (error) {
      console.error("Error updating entity:", error);
      toast.error(error.response?.data?.message || "Failed to update entity");
    } finally {
      setSubmitting(false);
    }
  };

  // Filter entities based on search query and filters
  const filteredEntities = useMemo(() => {
    return entities.filter((entity) => {
      // Apply search query filter
      if (searchQuery) {
        const matchesSearch = Object.values(entity).some(
          (value) => value && value.toString().toLowerCase().includes(searchQuery.toLowerCase())
        );
        if (!matchesSearch) return false;
      }

      // Apply type filter
      if (filters.type && filters.type !== 'all') {
        console.log('Type filter:', {
          filterValue: filters.type,
          entityValue: entity.type,
          match: entity.type === filters.type
        });
        if (entity.type !== filters.type) return false;
      }

      // Apply internal/external filter
      if (filters.internalExternal && filters.internalExternal !== 'all') {
        console.log('Internal/External filter:', {
          filterValue: filters.internalExternal,
          entityValue: entity.internalExternal,
          match: entity.internalExternal === filters.internalExternal
        });
        if (entity.internalExternal !== filters.internalExternal) return false;
      }

      // Apply local currency filter
      if (filters.localCurrency && filters.localCurrency !== 'all') {
        console.log('Local Currency filter:', {
          filterValue: filters.localCurrency,
          entityValue: entity.localCurrency,
          match: entity.localCurrency === filters.localCurrency
        });
        if (entity.localCurrency !== filters.localCurrency) return false;
      }

      // Apply parent entity filter
      if (filters.parentEntityID && filters.parentEntityID !== 'all') {
        console.log('Parent Entity filter:', {
          filterValue: filters.parentEntityID,
          entityValue: entity.parentEntityID,
          match: entity.parentEntityID === filters.parentEntityID
        });
        if (entity.parentEntityID !== filters.parentEntityID) return false;
      }

      return true;
    });
  }, [entities, searchQuery, filters]);

  const sortedEntities = [...filteredEntities];
  if (sortConfig.key) {
    sortedEntities.sort((a, b) => {
      const aValue = a[sortConfig.key] || "";
      const bValue = b[sortConfig.key] || "";
      if (aValue < bValue) return sortConfig.direction === "asc" ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === "asc" ? 1 : -1;
      return 0;
    });
  }

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentEntities = sortedEntities.slice(startIndex, endIndex);
  const totalPages = Math.ceil(sortedEntities.length / itemsPerPage);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  if (loading) {
    return (
      <div className="p-6 flex justify-center items-center h-screen">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
          <span className="mt-4 text-gray-600">{t('admin.entities.loading', 'Loading entities...')}</span>
        </div>
      </div>
    );
  }

  if (error) {
    return <div className="p-6 text-red-500">{t('admin.entities.error', 'Error')}: {error}</div>;
  }

  const columns = [
    { key: "name", label: t('admin.entities.features.form.name', 'Name'), sortable: true },
    { key: "code", label: t('admin.entities.features.form.code', 'Code'), sortable: true },
    { key: "type", label: t('admin.entities.features.form.type', 'Type'), sortable: true },
    { key: "internalExternal", label: t('admin.entities.features.form.internal_external', 'Internal/External'), sortable: true },
    { key: "localCurrency", label: t('admin.entities.features.form.local_currency', 'Local Currency'), sortable: true },
    { key: "parentEntityID", label: t('admin.entities.features.form.parent_entity', 'Parent Entity'), sortable: true },
    { key: "comment", label: t('admin.entities.features.form.comment', 'Comment'), sortable: true },
  ];

  return (
    <div className="p-6">
      <PageHeader
        title={t('admin.entities.title', 'Entities')}
        description={t('admin.entities.description', 'Manage your entities here')}
        section="Structure"
        currentPage={t('admin.entities.title', 'Entities')}
        searchPlaceholder={t('common.header.search', 'Search...')}
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
        icon={Building2}
      />

      {/* Filter Panel */}
      <div className="mb-6 filter-panel-container">
        <FilterPanel
          filters={[
            {
              id: "type",
              label: t('admin.entities.features.form.type', 'Type'),
              component: (
                <Select
                  value={filters.type}
                  onValueChange={(value) => setFilters({ ...filters, type: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.entities.features.form.type', 'Select type')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('common.filters.all', 'All')}</SelectItem>
                    {/* Get unique types from entities */}
                    {Array.from(new Set(entities.map(entity => entity.type).filter(Boolean)))
                      .sort()
                      .map(type => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "internalExternal",
              label: t('admin.entities.features.form.internal_external', 'Internal/External'),
              component: (
                <Select
                  value={filters.internalExternal}
                  onValueChange={(value) => setFilters({ ...filters, internalExternal: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.entities.features.form.internal_external', 'Select status')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('common.filters.all', 'All')}</SelectItem>
                    <SelectItem value="Internal">{t('admin.entities.features.form.internal', 'Internal')}</SelectItem>
                    <SelectItem value="External">{t('admin.entities.features.form.external', 'External')}</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "localCurrency",
              label: t('admin.entities.features.form.local_currency', 'Local Currency'),
              component: (
                <Select
                  value={filters.localCurrency}
                  onValueChange={(value) => setFilters({ ...filters, localCurrency: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.entities.features.form.local_currency', 'Select currency')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('common.filters.all', 'All')}</SelectItem>
                    {/* Get unique currencies from entities */}
                    {Array.from(new Set(entities.map(entity => entity.localCurrency).filter(Boolean)))
                      .sort()
                      .map(currency => (
                        <SelectItem key={currency} value={currency}>
                          {currency}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "parentEntityID",
              label: t('admin.entities.features.form.parent_entity', 'Parent Entity'),
              component: (
                <Select
                  value={filters.parentEntityID}
                  onValueChange={(value) => setFilters({ ...filters, parentEntityID: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.entities.features.form.parent_entity', 'Select parent entity')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('common.filters.all', 'All')}</SelectItem>
                    {entities.map((entity) => (
                      <SelectItem key={entity.entityID} value={entity.entityID}>
                        {entity.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
          ]}
          activeFilters={filters}
          onClearFilters={clearFilters}
        />
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center gap-4 mb-4">
        <div>
          {/* Any other buttons or elements on the left side */}
        </div>
        <div className="flex gap-2">
          {selectedEntities.length > 0 && (
            <Button
              variant="outline"
              className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
                canDelete && !loading
                  ? 'bg-[#F62D51]/90 text-white hover:bg-[#F62D51] hover:shadow-md'
                  : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
              } flex items-center gap-2 px-6 py-2 font-semibold`}
              onClick={handleDeleteSelected}
              disabled={!canDelete || loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  {t('admin.entities.buttons.deleting', 'Deleting...')}
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  {t('admin.entities.buttons.delete', 'Delete')} ({selectedEntities.length})
                </>
              )}
            </Button>
          )}
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            {canCreate && (
              <DialogTrigger asChild>
                <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  {t('admin.entities.buttons.add', 'Add Entity')}
                </Button>
              </DialogTrigger>
            )}
            <DialogContent className="max-w-4xl p-6">
              <DialogHeader>
                <DialogTitle>{t('admin.entities.dialog.create_title', 'Create New Entity')}</DialogTitle>
                <DialogDescription>
                  {t('admin.entities.dialog.create_description', 'Fill in the details below to create a new entity. Fields marked with * are required.')}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-8">
                <div className="grid grid-cols-2 gap-6">
                  <div className="flex flex-col">
                    <Label htmlFor="name" className="mb-2">{t('admin.entities.features.form.name', 'Entity Name *')}</Label>
                    <Input
                      id="name"
                      value={newEntity.name}
                      onChange={(e) => setNewEntity({ ...newEntity, name: e.target.value })}
                      placeholder={t('admin.entities.features.form.name_placeholder', 'Enter entity name')}
                      required
                      className="w-full"
                    />
                  </div>
                  <div className="flex flex-col">
                    <Label htmlFor="code" className="mb-2">{t('admin.entities.features.form.code', 'Code')}</Label>
                    <Input
                      id="code"
                      value={newEntity.code}
                      onChange={(e) => setNewEntity({ ...newEntity, code: e.target.value })}
                      placeholder={t('admin.entities.features.form.code_placeholder', 'Enter entity code')}
                      className="w-full"
                    />
                  </div>
                  <div className="flex flex-col">
                    <Label htmlFor="type" className="mb-2">{t('admin.entities.features.form.type', 'Entity Type')}</Label>
                    <Select
                      value={newEntity.type}
                      onValueChange={(value) => setNewEntity({ ...newEntity, type: value })}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder={t('admin.entities.features.form.type_placeholder', 'Select entity type')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Branch">{t('admin.entities.features.form.branch', 'Branch')}</SelectItem>
                        <SelectItem value="Subsidiary">{t('admin.entities.features.form.subsidiary', 'Subsidiary')}</SelectItem>
                        <SelectItem value="HeadOffice">{t('admin.entities.features.form.head_office', 'Head Office')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex flex-col">
                    <Label htmlFor="internalExternal" className="mb-2">{t('admin.entities.features.form.internal_external', 'Internal/External')}</Label>
                    <Select
                      value={newEntity.internalExternal}
                      onValueChange={(value) => setNewEntity({ ...newEntity, internalExternal: value })}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder={t('admin.entities.features.form.internal_external_placeholder', 'Select internal/external')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Internal">{t('admin.entities.features.form.internal', 'Internal')}</SelectItem>
                        <SelectItem value="External">{t('admin.entities.features.form.external', 'External')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex flex-col">
                    <Label htmlFor="localCurrency" className="mb-2">{t('admin.entities.features.form.local_currency', 'Local Currency')}</Label>
                    <Input
                      id="localCurrency"
                      value={newEntity.localCurrency}
                      onChange={(e) => setNewEntity({ ...newEntity, localCurrency: e.target.value })}
                      placeholder={t('admin.entities.features.form.local_currency_placeholder', 'e.g., USD, EUR')}
                      className="w-full"
                    />
                  </div>
                  <div className="flex flex-col">
                    <Label htmlFor="parentEntityID" className="mb-2">{t('admin.entities.features.form.parent_entity', 'Parent Entity')}</Label>
                    <Select
                      value={newEntity.parentEntityID}
                      onValueChange={(value) => setNewEntity({ ...newEntity, parentEntityID: value })}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder={t('admin.entities.features.form.parent_entity_placeholder', 'Select parent entity')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={null}>{t('admin.entities.features.form.none', 'None')}</SelectItem>
                        {entities.map((entity) => (
                          <SelectItem key={entity.entityID} value={entity.entityID}>
                            {entity.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="comment" className="mb-2">{t('admin.entities.features.form.comment', 'Comment')}</Label>
                  <textarea
                    id="comment"
                    value={newEntity.comment}
                    onChange={(e) => setNewEntity({ ...newEntity, comment: e.target.value })}
                    placeholder={t('admin.entities.features.form.comment_placeholder', 'Enter comment')}
                    className="w-full h-24 p-2 border rounded-md resize-y focus:outline-none focus:ring-2 focus:ring-[#F62D51] focus:border-transparent"
                  />
                </div>
                <Button
                  type="submit"
                  className="w-full bg-[#F62D51] hover:bg-red-700"
                  disabled={submitting}
                >
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('admin.entities.buttons.creating', 'Creating...')}
                    </>
                  ) : (
                    t('admin.entities.buttons.create', 'Create Entity')
                  )}
                </Button>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Dialog open={isUpdateModalOpen} onOpenChange={setIsUpdateModalOpen}>
        <DialogTrigger asChild>
          <div />
        </DialogTrigger>
        <DialogContent className="max-w-3xl p-8">
          <DialogHeader>
            <DialogTitle>{t('admin.entities.dialog.update_title', 'Update Entity')}</DialogTitle>
            <DialogDescription>
              {t('admin.entities.dialog.update_description', 'Modify the fields below to update the entity details.')}
            </DialogDescription>
          </DialogHeader>
          {entityToUpdate && (
            <form onSubmit={handleUpdateSubmit} className="space-y-8">
              <div className="grid grid-cols-2 gap-6">
                <div className="flex flex-col">
                  <Label htmlFor="updateName" className="mb-2">{t('admin.entities.features.form.name', 'Entity Name *')}</Label>
                  <Input
                    id="updateName"
                    value={entityToUpdate.name}
                    onChange={(e) => setEntityToUpdate({ ...entityToUpdate, name: e.target.value })}
                    placeholder={t('admin.entities.features.form.name_placeholder', 'Enter entity name')}
                    required
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="updateCode" className="mb-2">{t('admin.entities.features.form.code', 'Code')}</Label>
                  <Input
                    id="updateCode"
                    value={entityToUpdate.code}
                    onChange={(e) => setEntityToUpdate({ ...entityToUpdate, code: e.target.value })}
                    placeholder={t('admin.entities.features.form.code_placeholder', 'Enter entity code')}
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="updateType" className="mb-2">{t('admin.entities.features.form.type', 'Entity Type')}</Label>
                  <Select
                    value={entityToUpdate.type}
                    onValueChange={(value) => setEntityToUpdate({ ...entityToUpdate, type: value })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={t('admin.entities.features.form.type_placeholder', 'Select entity type')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Branch">{t('admin.entities.features.form.branch', 'Branch')}</SelectItem>
                      <SelectItem value="Subsidiary">{t('admin.entities.features.form.subsidiary', 'Subsidiary')}</SelectItem>
                      <SelectItem value="HeadOffice">{t('admin.entities.features.form.head_office', 'Head Office')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="updateInternalExternal" className="mb-2">{t('admin.entities.features.form.internal_external', 'Internal/External')}</Label>
                  <Select
                    value={entityToUpdate.internalExternal}
                    onValueChange={(value) => setEntityToUpdate({ ...entityToUpdate, internalExternal: value })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={t('admin.entities.features.form.internal_external_placeholder', 'Select internal/external')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Internal">{t('admin.entities.features.form.internal', 'Internal')}</SelectItem>
                      <SelectItem value="External">{t('admin.entities.features.form.external', 'External')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="updateLocalCurrency" className="mb-2">{t('admin.entities.features.form.local_currency', 'Local Currency')}</Label>
                  <Input
                    id="updateLocalCurrency"
                    value={entityToUpdate.localCurrency}
                    onChange={(e) => setEntityToUpdate({ ...entityToUpdate, localCurrency: e.target.value })}
                    placeholder={t('admin.entities.features.form.local_currency_placeholder', 'e.g., USD, EUR')}
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="updateParentEntityID" className="mb-2">{t('admin.entities.features.form.parent_entity', 'Parent Entity')}</Label>
                  <Select
                    value={entityToUpdate.parentEntityID}
                    onValueChange={(value) => setEntityToUpdate({ ...entityToUpdate, parentEntityID: value })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={t('admin.entities.features.form.parent_entity_placeholder', 'Select parent entity')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={null}>{t('admin.entities.features.form.none', 'None')}</SelectItem>
                      {entities.map((entity) => (
                        <SelectItem key={entity.entityID} value={entity.entityID}>
                          {entity.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex flex-col">
                <Label htmlFor="updateComment" className="mb-2">{t('admin.entities.features.form.comment', 'Comment')}</Label>
                <textarea
                  id="updateComment"
                  value={entityToUpdate.comment}
                  onChange={(e) => setEntityToUpdate({ ...entityToUpdate, comment: e.target.value })}
                  placeholder={t('admin.entities.features.form.comment_placeholder', 'Enter comment')}
                  className="w-full h-24 p-2 border rounded-md resize-y focus:outline-none focus:ring-2 focus:ring-[#F62D51] focus:border-transparent"
                />
              </div>
              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  onClick={() => {
                    setIsUpdateModalOpen(false);
                    setEntityToUpdate(null);
                  }}
                  className="px-4 py-2 rounded-lg bg-[#3A424E] text-white hover:bg-[#4A525E] transition-colors"
                >
                  {t('common.buttons.cancel', 'Cancel')}
                </Button>
                <Button
                  type="submit"
                  className="px-4 py-2 rounded-lg bg-red-500 text-white hover:bg-red-600 transition-colors"
                  disabled={submitting}
                >
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('admin.entities.buttons.updating', 'Updating...')}
                    </>
                  ) : (
                    t('admin.entities.buttons.update', 'Update Entity')
                  )}
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>

      <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                  <Checkbox
                    checked={selectedEntities.length === currentEntities.length && currentEntities.length > 0}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all"
                  />
                </th>
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100"
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      {sortConfig.key === column.key && (
                        <ArrowUpDown className="w-4 h-4" />
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {currentEntities.map((entity, index) => (
                <tr
                  key={entity.entityID}
                  className={`hover:bg-gray-50 cursor-pointer ${index % 2 === 0 ? "bg-white" : "bg-gray-50"}`}
                  onClick={() => handleRowClick(entity.entityID)}
                >
                  <td className="px-6 py-4">
                    <Checkbox
                      checked={selectedEntities.includes(entity.entityID)}
                      onCheckedChange={() => handleSelectEntity(entity.entityID)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </td>

                  <td className="px-6 py-4 text-sm whitespace-nowrap">
                    <div className="flex items-center gap-2">
                      <img
                        src={entityIcon}
                        alt="entity"
                        className="w-5 h-5 object-contain"
                      />
                      <span className="font-bold text-[#242A33]">{entity.name}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                    {entity.code || "-"}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                    {entity.type || "-"}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                    {entity.internalExternal || "Internal"}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                    {entity.localCurrency || "-"}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                    {entities.find((e) => e.entityID === entity.parentEntityID)?.name || "-"}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                    {entity.comment || "-"}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">
            {filteredEntities.length} entit{filteredEntities.length !== 1 ? 'ies' : 'y'} found
          </span>
          {/* Show filter badge if any filter is active */}
          {(filters.type !== 'all' ||
            filters.internalExternal !== 'all' ||
            filters.localCurrency !== 'all' ||
            filters.parentEntityID !== 'all') && (
            <Badge
              variant="outline"
              className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 cursor-pointer"
              onClick={() => {
                // Toggle filter panel visibility
                const filterPanel = document.querySelector('.filter-panel-container');
                if (filterPanel) {
                  filterPanel.scrollIntoView({ behavior: 'smooth' });
                }
              }}
            >
              Filtered
              <ChevronUp className="ml-1 h-3 w-3" />
            </Badge>
          )}
        </div>
      </div>

      <TablePagination
        currentPage={currentPage}
        totalPages={totalPages}
        itemsPerPage={itemsPerPage}
        totalItems={filteredEntities.length}
        onPageChange={handlePageChange}
        onItemsPerPageChange={(value) => {
          setItemsPerPage(value);
          setCurrentPage(1);
        }}
        startIndex={startIndex}
        endIndex={endIndex}
      />
    </div>
  );
}

export default EntitiesManagement;
