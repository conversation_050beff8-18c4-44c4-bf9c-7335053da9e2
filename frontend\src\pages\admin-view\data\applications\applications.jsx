import { useState, useEffect } from 'react';
import { Plus, Loader2, Trash2, ArrowUpDown, ChevronLeft, Server } from 'lucide-react';
import axios from 'axios';
import { toast } from 'react-hot-toast';
import { useNavigate, Outlet } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { hasPermission } from '@/store/auth-slice';
import PageHeader from "@/components/ui/page-header";
import { useTranslation } from "react-i18next";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import TablePagination from "../../../../components/ui/table-pagination";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { getApiBaseUrl } from "@/utils/api-config";
function ApplicationsManagement() {
  const { t } = useTranslation();
  const [applications, setApplications] = useState([]);
  const canCreate = useSelector(state => hasPermission(state, 'create'));
  const canDelete = useSelector(state => hasPermission(state, 'delete'));
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedApplications, setSelectedApplications] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const navigate = useNavigate();
  const API_BASE_URL = getApiBaseUrl();
  const [newApplication, setNewApplication] = useState({
    applicationID: '',
    name: '',
    comment: '',
  });

  const fetchApplications = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/applications`, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
      });
      if (response.data.success) {
        setApplications(response.data.data);
      }
    } catch (err) {
      console.error("Error fetching applications:", err);
      toast.error(t('admin.applications.error.fetch_failed', 'Failed to fetch applications'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchApplications();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    const applicationToCreate = {
      ...newApplication,
      applicationID: newApplication.applicationID || `APP_${Date.now()}`,
      comment: newApplication.comment || null,
    };

    try {
      setSubmitting(true);
      const response = await axios.post(
        `${API_BASE_URL}/applications`,
        applicationToCreate,
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
          timeout: 60000,
        }
      );

      if (response.data.success) {
        toast.success(t('admin.applications.success.created', 'Application created successfully'));
        setNewApplication({
          applicationID: '',
          name: '',
          comment: '',
        });
        setIsOpen(false);
        await fetchApplications();
      }
    } catch (error) {
      toast.error(error.response?.data?.message || t('admin.applications.error.create_failed', 'Failed to create application'));
    } finally {
      setSubmitting(false);
    }
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedApplications(filteredApplications.map(app => app.applicationID));
    } else {
      setSelectedApplications([]);
    }
  };

  const handleSelectApplication = (id) => {
    setSelectedApplications(prev => {
      if (prev.includes(id)) {
        return prev.filter(item => item !== id);
      }
      return [...prev, id];
    });
  };

  const handleRowClick = (id) => {
    navigate(`/admin/data/applications/${id}`);
  };

  const handleDeleteSelected = async () => {
    if (selectedApplications.length === 0) {
      toast.error(t('admin.applications.error.none_selected', 'No applications selected'));
      return;
    }

    if (window.confirm(t('admin.applications.confirm.delete', 'Are you sure you want to delete {{count}} selected application(s)?', { count: selectedApplications.length }))) {
      try {
        setLoading(true);
        let failedDeletions = [];

        const attemptDelete = async (id, retries = 2) => {
          for (let attempt = 0; attempt <= retries; attempt++) {
            try {
              await axios.delete(`${API_BASE_URL}/applications/${id}`, {
                withCredentials: true,
                timeout: 60000,
              });
              return { success: true };
            } catch (error) {
              if (attempt === retries) {
                let userMessage = error.response?.data?.message || 'Unknown error';
                if (userMessage.includes("It is referenced by other records")) {
                  userMessage = t('admin.applications.error.delete_referenced', 'Cannot delete application {{id}}: It is referenced by other records. Please reassign or remove dependent records first.', { id });
                }
                return { success: false, error: userMessage };
              }
              await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
            }
          }
        };

        const batchSize = 3;
        for (let i = 0; i < selectedApplications.length; i += batchSize) {
          const batch = selectedApplications.slice(i, i + batchSize);
          await Promise.all(
            batch.map(async (id) => {
              const result = await attemptDelete(id);
              if (!result.success) {
                failedDeletions.push({ id, error: result.error });
              }
              await new Promise(resolve => setTimeout(resolve, 500));
              return result;
            })
          );

          if (i + batchSize < selectedApplications.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        await fetchApplications();
        setSelectedApplications([]);

        if (failedDeletions.length > 0) {
          const errorMessage = failedDeletions.map(f => f.error).join('; ');
          toast.error(t('admin.applications.error.delete_multiple_failed', 'Failed to delete {{count}} application(s): {{message}}', {
            count: failedDeletions.length,
            message: errorMessage
          }), {
            duration: 6000,
          });
        } else {
          toast.success(t('admin.applications.success.deleted', 'All selected applications deleted successfully'));
        }
      } catch (err) {
        console.error("Error during deletion process:", err);
        toast.error(t('admin.applications.error.delete_process', 'An error occurred during the deletion process'));
      } finally {
        setLoading(false);
      }
    }
  };

  const filteredApplications = applications.filter((app) =>
    Object.values(app).some((value) =>
      value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const sortedApplications = [...filteredApplications].sort((a, b) => {
    if (!sortConfig.key) return 0;

    const aValue = a[sortConfig.key] || '';
    const bValue = b[sortConfig.key] || '';

    return sortConfig.direction === 'asc'
      ? aValue.toString().localeCompare(bValue.toString(), undefined, { numeric: true })
      : bValue.toString().localeCompare(aValue.toString(), undefined, { numeric: true });
  });

  const handleSort = (key) => {
    setSortConfig((prevConfig) => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const totalPages = Math.ceil(sortedApplications.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentApplications = sortedApplications.slice(indexOfFirstItem, indexOfLastItem);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const columns = [
    { key: 'name', label: t('admin.applications.columns.name', 'Name'), sortable: true },
    { key: 'comment', label: t('admin.applications.columns.comment', 'Comment'), sortable: true },
  ];

  // Check if we're in a nested route
  const isNestedRoute = window.location.pathname.includes('/applications/');

  // Store tab labels in state so they update when language changes
  const [tabLabels, setTabLabels] = useState({
    overview: t('common.tabs.overview', 'Overview'),
    features: t('common.tabs.features', 'Features')
  });

  // Update tab labels when language changes
  useEffect(() => {
    setTabLabels({
      overview: t('common.tabs.overview', 'Overview'),
      features: t('common.tabs.features', 'Features')
    });
  }, [t]);

  if (isNestedRoute) {
    return (
      <div className="p-6">
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">{tabLabels.overview}</TabsTrigger>
            <TabsTrigger value="features">{tabLabels.features}</TabsTrigger>
          </TabsList>
          <TabsContent value="overview">
            <Outlet />
          </TabsContent>
          <TabsContent value="features">
            <Outlet />
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      <PageHeader
        title={t('admin.applications.title', 'Applications Management')}
        description={t('admin.applications.description', 'Define and manage applications used by your organization.')}
        section={t('admin.sidebar.data', 'Data')}
        currentPage={t('admin.sidebar.applications', 'Applications')}
        searchPlaceholder={t('admin.applications.search_placeholder', 'Search applications...')}
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
        icon={Server}
      />

      <div className="flex justify-between items-center gap-3 mb-4">
        <Button variant="ghost" className="p-2" onClick={() => navigate('/admin/team')}>
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <div className="flex items-center gap-3">
          {selectedApplications.length > 0 && (
            <Button
              variant="outline"
              className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
                canDelete && !loading
                  ? 'bg-[#F62D51]/90 text-white hover:bg-[#F62D51] hover:shadow-md'
                  : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
              } flex items-center gap-2 px-6 py-2 font-semibold`}
              onClick={handleDeleteSelected}
              disabled={!canDelete || loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  {t('common.deleting', 'Deleting...')}
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  {t('common.buttons.delete', 'Delete')} ({selectedApplications.length})
                </>
              )}
            </Button>
          )}
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            {canCreate && (
              <DialogTrigger asChild>
                <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  {t('admin.applications.buttons.add', 'Add Application')}
                </Button>
              </DialogTrigger>
            )}
            <DialogContent className="max-w-3xl p-8">
              <DialogHeader>
                <DialogTitle>{t('admin.applications.dialog.title', 'Add New Application')}</DialogTitle>
                <DialogDescription>
                  {t('admin.applications.dialog.description', 'Fill in the details to create a new application.')}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-8">
                <div className="grid grid-cols-2 gap-6">
                  <div className="flex flex-col">
                    <Label htmlFor="name" className="mb-2">{t('admin.applications.form.name', 'Name')} *</Label>
                    <Input
                      id="name"
                      value={newApplication.name}
                      onChange={(e) => setNewApplication({
                        ...newApplication,
                        name: e.target.value
                      })}
                      placeholder={t('admin.applications.form.name_placeholder', 'Enter application name')}
                      required
                      className="w-full"
                    />
                  </div>
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="comment" className="mb-2">{t('admin.applications.form.comment', 'Comment')}</Label>
                  <Textarea
                    id="comment"
                    value={newApplication.comment}
                    onChange={(e) => setNewApplication({
                      ...newApplication,
                      comment: e.target.value
                    })}
                    placeholder={t('admin.applications.form.comment_placeholder', 'Enter comment')}
                    className="w-full h-24 resize-y"
                  />
                </div>
                <div className="flex justify-end gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsOpen(false)}
                  >
                    {t('common.buttons.cancel', 'Cancel')}
                  </Button>
                  <Button
                    type="submit"
                    className="bg-[#F62D51] hover:bg-red-700"
                    disabled={submitting}
                  >
                    {submitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t('common.creating', 'Creating...')}
                      </>
                    ) : (
                      t('admin.applications.buttons.create', 'Create Application')
                    )}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                      <Checkbox
                        checked={selectedApplications.length === currentApplications.length && currentApplications.length > 0}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all"
                      />
                    </th>
                    {columns.map((column) => (
                      <th
                        key={column.key}
                        className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100"
                        onClick={() => column.sortable && handleSort(column.key)}
                      >
                        <div className="flex items-center gap-1">
                          {column.label}
                          {sortConfig.key === column.key && (
                            <ArrowUpDown className="w-4 h-4" />
                          )}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {currentApplications.map((app, index) => (
                    <tr
                      key={app.applicationID}
                      className={`hover:bg-gray-50 cursor-pointer ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                      onClick={() => handleRowClick(app.applicationID)}
                    >
                      <td className="px-6 py-4">
                        <Checkbox
                          checked={selectedApplications.includes(app.applicationID)}
                          onCheckedChange={() => handleSelectApplication(app.applicationID)}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </td>

                      <td className="px-6 py-4 text-sm font-bold text-[#242A33] whitespace-nowrap">
                        {app.name}
                      </td>
                      <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                        {app.comment || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={filteredApplications.length}
            onPageChange={handlePageChange}
            onItemsPerPageChange={(value) => {
              setItemsPerPage(value);
              setCurrentPage(1);
            }}
            startIndex={indexOfFirstItem}
            endIndex={indexOfLastItem}
          />
        </>
      )}
    </div>
  );
}

export default ApplicationsManagement;