const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Database configuration
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: console.log,
  }
);

async function runMigration() {
  try {
    console.log('Starting ENUM default removal migration...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Import and run the migration
    const migration = require('../migrations/20241227000003-remove-enum-defaults.js');
    
    console.log('Running migration UP...');
    await migration.up(sequelize.getQueryInterface(), Sequelize);
    
    console.log('✅ Migration completed successfully!');
    console.log('ENUM default values have been removed.');
    
    // Verify the changes
    console.log('\nVerifying migration...');
    const [results] = await sequelize.query(`
      SELECT column_name, data_type, column_default, udt_name 
      FROM information_schema.columns 
      WHERE table_name = 'Control' 
      AND column_name IN ('controlExecutionMethod', 'organizationalLevel', 'sampleType', 'testingFrequency', 'testingMethod')
      ORDER BY column_name;
    `);
    
    console.log('Updated column info:');
    results.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type} (${row.udt_name}) - Default: ${row.column_default || 'NULL'}`);
    });
    
    // Show sample data
    console.log('\nSample data after migration:');
    const [sampleData] = await sequelize.query(`
      SELECT "controlID", "controlExecutionMethod", "organizationalLevel", "sampleType", "testingFrequency", "testingMethod"
      FROM "Control" 
      LIMIT 5;
    `);
    
    console.table(sampleData);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run the migration
runMigration();
