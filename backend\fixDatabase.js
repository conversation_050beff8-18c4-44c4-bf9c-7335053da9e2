const sequelize = require('./config/database');
const db = require('./models');

console.log('Loaded models:', Object.keys(db));

async function fixDatabase() {
  try {
    console.log('Starting database fix...');

    // Connect to the database
    await sequelize.authenticate();
    console.log('Connected to database successfully');

    // 1. Check if there are any Controls with invalid Risk references
    const controls = await db.Control.findAll();
    console.log(`Found ${controls.length} controls in the database`);

    // 2. Get all valid risk IDs
    const risks = await db.Risk.findAll();
    console.log(`Found ${risks.length} risks in the database`);

    if (risks.length === 0) {
      console.log('No risks found. Running seed script for risks...');
      const seedRisks = require('./seed/seedRisks');
      await seedRisks();
      console.log('Risks seeded successfully');
    }

    // Get updated list of risks
    const updatedRisks = await db.Risk.findAll();
    const validRiskIds = updatedRisks.map(risk => risk.riskID);
    console.log(`Valid risk IDs: ${validRiskIds.slice(0, 5).join(', ')}${validRiskIds.length > 5 ? '...' : ''}`);

    // 3. Update controls with invalid risk references
    let fixedControlCount = 0;
    for (const control of controls) {
      if (control.risk && !validRiskIds.includes(control.risk)) {
        console.log(`Control ${control.controlID} has invalid risk reference: ${control.risk}`);

        // Set to null or a valid risk ID
        control.risk = null;
        await control.save();
        fixedControlCount++;
      }
    }

    console.log(`Fixed ${fixedControlCount} controls with invalid risk references`);

    // 4. Check for incidents with invalid risk references
    const incidents = await db.Incident.findAll();
    console.log(`Found ${incidents.length} incidents in the database`);

    let fixedIncidentCount = 0;
    for (const incident of incidents) {
      if (incident.riskID && !validRiskIds.includes(incident.riskID)) {
        console.log(`Incident ${incident.incidentID} has invalid risk reference: ${incident.riskID}`);

        // Set to null or a valid risk ID
        incident.riskID = null;
        await incident.save();
        fixedIncidentCount++;
      }
    }

    console.log(`Fixed ${fixedIncidentCount} incidents with invalid risk references`);

    // 5. Try to sync the database again
    console.log('Syncing database...');
    await sequelize.sync({ force: false });
    console.log('Database synced successfully');

    console.log('Database fix completed successfully');
  } catch (error) {
    console.error('Error fixing database:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
    console.log('Database connection closed');
  }
}

// Run the fix
fixDatabase();
