const { Sequelize } = require('sequelize');
require('dotenv').config();

async function checkTables() {
  const sequelize = new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USER,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      dialect: 'postgres',
      logging: false
    }
  );

  try {
    await sequelize.authenticate();
    console.log('Connection established successfully.');

    // Query to get all table names
    const [results] = await sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `);

    console.log('Tables in database:');
    results.forEach(row => {
      console.log(`- ${row.table_name}`);
    });

    // Check if FicheDeTest table exists
    const ficheDeTestTable = results.find(row => 
      row.table_name.toLowerCase() === 'fichedetests' || 
      row.table_name.toLowerCase() === 'fiche_de_tests' ||
      row.table_name.toLowerCase().includes('fiche')
    );

    if (ficheDeTestTable) {
      console.log(`\nFound FicheDeTest table: ${ficheDeTestTable.table_name}`);
      
      // Get columns for this table
      const [columns] = await sequelize.query(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = '${ficheDeTestTable.table_name}'
        ORDER BY ordinal_position;
      `);
      
      console.log('Columns:');
      columns.forEach(col => {
        console.log(`- ${col.column_name} (${col.data_type})`);
      });
    } else {
      console.log('\nFicheDeTest table not found!');
    }

  } catch (error) {
    console.error('Unable to connect to the database:', error);
  } finally {
    await sequelize.close();
  }
}

checkTables();
