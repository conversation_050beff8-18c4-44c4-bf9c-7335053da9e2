const express = require('express');
const router = express.Router();
const { verifyToken } = require('../../middleware/auth');
const {
  uploadFile,
  getAttachments,
  deleteAttachment,
  downloadAttachment
} = require('../../controllers/uploads/upload-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Upload a file
router.post('/', uploadFile);

// Get all attachments for an incident
router.get('/', getAttachments);

// Delete an attachment
router.delete('/:id', deleteAttachment);

// Download an attachment
router.get('/download/:id', downloadAttachment);

module.exports = router;
