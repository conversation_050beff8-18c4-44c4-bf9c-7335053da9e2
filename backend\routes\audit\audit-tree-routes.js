const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAuditTreeByMissionId,
  deleteTreeNode
} = require('../../controllers/audit/audit-tree-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get audit tree structure by mission ID
router.get('/mission/:missionId', authorizeR<PERSON>s(['audit_director', 'auditor']), getAuditTreeByMissionId);

// Delete a tree node
router.delete('/:nodeType/:nodeId', authorizeR<PERSON>s(['audit_director', 'auditor']), deleteTreeNode);

module.exports = router;
