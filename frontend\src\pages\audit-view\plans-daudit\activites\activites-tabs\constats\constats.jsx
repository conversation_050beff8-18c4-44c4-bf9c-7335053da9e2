import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Edit, Trash2, Plus, FilePlus, Paperclip, ChevronUp, ChevronDown, Loader2, AlertCircle } from "lucide-react";
import { useParams, useNavigate, useOutletContext } from "react-router-dom";
import { toast } from "sonner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { getConstatsByActivityId, createAuditConstat, updateAuditConstat, deleteAuditConstat } from '@/services/audit-constat-service';
import axios from 'axios';

// Import constat icon
import constatIcon from '@/assets/constat.png';

function ConstatsTab() {
  const { missionAuditId, activiteId } = useParams();
  const outletContext = useOutletContext(); // Get full outlet context
  const navigate = useNavigate();

  const planId = outletContext?.planId || outletContext?.missionAudit?.planId;
  const [isConstatsOpen, setIsConstatsOpen] = useState(true);
  const [constats, setConstats] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isConstatDialogOpen, setIsConstatDialogOpen] = useState(false);
  const [newConstat, setNewConstat] = useState({
    name: "",
    type: "Point Faible",
    impact: "Moyen",
    description: ""
  });
  const [editingConstat, setEditingConstat] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const impactOptions = [
    { value: "tres fort", label: "Très fort" },
    { value: "fort", label: "Fort" },
    { value: "moyen", label: "Moyen" },
    { value: "faible", label: "Faible" },
    { value: "tres faible", label: "Très faible" }
  ];
  const impactBadgeColors = {
    'tres fort': 'bg-red-100 text-red-800',
    'fort': 'bg-orange-100 text-orange-800',
    'moyen': 'bg-yellow-100 text-yellow-800',
    'faible': 'bg-green-100 text-green-800',
    'tres faible': 'bg-blue-100 text-blue-800',
  };
  const impactCircleColors = {
    'tres fort': 'bg-red-500 text-white',
    'fort': 'bg-orange-500 text-white',
    'moyen': 'bg-yellow-400 text-black',
    'faible': 'bg-green-500 text-white',
    'tres faible': 'bg-blue-500 text-white',
  };
  const typeOptions = [
    { value: "Point Faible", label: "Point Faible" },
    { value: "Point Fort", label: "Point Fort" }
  ];

  const fetchConstats = async (signal) => {
    if (!activiteId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await getConstatsByActivityId(activiteId, signal);
      console.log('Fetched constats:', response);
      if (response.success) {
        const formattedConstats = response.data.map(constat => ({
          ...constat,
          recommandations: constat.recommandations || '',
          risques: constat.risques || '',
          piecesJointes: Array.isArray(constat.piecesJointes) ? constat.piecesJointes : [],
        }));
        setConstats(formattedConstats);
      } else {
        setError(response.message || 'Failed to fetch constats');
        toast.error(response.message || 'Failed to fetch constats');
      }
    } catch (err) {
      if (axios.isCancel(err)) {
        console.log('Constats fetch cancelled');
      } else {
        setError(err.message || 'An error occurred while fetching constats');
        toast.error(err.message || 'An error occurred while fetching constats');
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const abortController = new AbortController();
    fetchConstats(abortController.signal);

    return () => {
      abortController.abort();
    };
  }, [activiteId]);

  const handleAddConstat = async () => {
    if (!newConstat.name.trim()) {
      toast.error("Veuillez saisir le nom du constat");
      setIsSubmitting(false);
      return;
    }

    if (!activiteId) {
      toast.error("Activity ID is missing.");
      setIsSubmitting(false);
      return;
    }

    setIsSubmitting(true);
    setError(null);
    const abortController = new AbortController();

    const formattedImpact = newConstat.impact
      ? newConstat.impact.charAt(0).toLowerCase() + newConstat.impact.slice(1)
      : 'moyen';

    const constatDataToSend = {
      ...newConstat,
      impact: formattedImpact,
      auditActivityID: activiteId,
    };

    try {
      let response;
    if (editingConstat) {
        // Update existing constat
        response = await updateAuditConstat(editingConstat.id, constatDataToSend, abortController.signal);
        console.log('Update constat response:', response);
        if (response.success) {
           // Update the item in the local state array
           setConstats(prev => prev.map(item => 
             item.id === response.data.id ? { 
               ...response.data, 
               recommandations: response.data.recommandations || '',
               risques: response.data.risques || '',
               piecesJointes: Array.isArray(response.data.piecesJointes) ? response.data.piecesJointes : [],
             } : item
           ));
        }
    } else {
        // Create new constat
        response = await createAuditConstat(constatDataToSend, abortController.signal);
        console.log('Create constat response:', response);
         if (response.success) {
            // Add the new item to the local state array
            setConstats(prev => [...prev, { 
              ...response.data, 
              recommandations: response.data.recommandations || '',
              risques: response.data.risques || '',
              piecesJointes: Array.isArray(response.data.piecesJointes) ? response.data.piecesJointes : [],
            }]);
         }
      }

      if (response.success) {
        toast.success(editingConstat ? "Constat mis à jour avec succès!" : "Constat ajouté avec succès!");
        setNewConstat({ name: "", type: "Point Faible", impact: "Moyen", description: "" });
    setEditingConstat(null);
    setIsConstatDialogOpen(false);
        // Removed the immediate refetch: await fetchConstats(abortController.signal);
      } else {
        throw new Error(response.message || 'Failed to save constat');
      }
    } catch (err) {
      if (axios.isCancel(err)) {
        console.log('Constat save request cancelled');
      } else {
        console.error('API call error:', err);
        const errorMessage = err.response?.data?.message || err.message || 'An error occurred while saving constat';
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteConstat = async (id) => {
    if (window.confirm("Êtes-vous sûr de vouloir supprimer ce constat ?")) {
      setIsSubmitting(true);
      setError(null);
      const abortController = new AbortController();

      try {
        const response = await deleteAuditConstat(id, abortController.signal);
        if (response.success) {
          setConstats(prev => prev.filter(item => item.id !== id));
          toast.success("Constat supprimé avec succès!");
        } else {
          setError(response.message || 'Failed to delete constat');
          toast.error(response.message || 'Failed to delete constat');
        }
      } catch (err) {
        if (axios.isCancel(err)) {
          console.log('Constat delete request cancelled');
        } else {
          setError(err.message || 'An error occurred while deleting constat');
          toast.error(err.message || 'An error occurred while deleting constat');
        }
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleEditConstat = (item) => {
    setEditingConstat(item);
    setNewConstat({
      name: item.nom || item.name || '',
      type: item.type || 'Point Faible',
      impact: item.impact ? item.impact.charAt(0).toUpperCase() + item.impact.slice(1) : 'Moyen',
      description: item.description || '',
    });
    setIsConstatDialogOpen(true);
  };

  const handleRowClick = (item) => {
    if (missionAuditId && activiteId && item.id) {
      // Build the navigation URL based on whether planId is available
      let navigationUrl;
      if (planId) {
        // Route with planId: /audit/plans-daudit/{planId}/missions-audits/{missionAuditId}/activites/{activiteId}/constats/{constatId}
        navigationUrl = `/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}/constats/${item.id}`;
      } else {
        // Route without planId: /audit/missions-audits/{missionAuditId}/activites/{activiteId}/constats/{constatId}
        navigationUrl = `/audit/missions-audits/${missionAuditId}/activites/${activiteId}/constats/${item.id}`;
      }

      navigate(navigationUrl);
    } else {
      const missingIds = [];
      if (!missionAuditId) missingIds.push('missionAuditId');
      if (!activiteId) missingIds.push('activiteId');
      if (!item.id) missingIds.push('item.id');

      toast.error(`Missing required IDs for navigation: ${missingIds.join(', ')}`);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51] mr-2" />
        <p className="text-gray-500">Chargement des constats...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-10">
        <AlertCircle className="h-8 w-8 text-red-500 mr-2" />
        <p className="text-red-500">Erreur: {error}</p>
      </div>
    );
  }

  return (
    <div className="border rounded-lg shadow-sm">
      <button
        type="button"
        className="w-full flex items-center p-4 bg-gradient-to-r from-rose-50 to-pink-50 rounded-t-lg"
        onClick={() => setIsConstatsOpen(!isConstatsOpen)}
      >
        <div className="flex items-center gap-2">
          {isConstatsOpen ? (
            <ChevronUp className="h-5 w-5 text-rose-600" />
          ) : (
            <ChevronDown className="h-5 w-5 text-rose-600" />
          )}
          <FilePlus className="h-5 w-5 text-rose-600 mr-1" />
          <span className="text-lg font-medium text-rose-800">Constats</span>
        </div>
      </button>
      {isConstatsOpen && (
        <div className="p-5 bg-white">
          <div className="mb-4 flex justify-end">
            <Button
              className="bg-[#F62D51] hover:bg-[#F62D51]/90"
              onClick={() => {
                setEditingConstat(null);
                setNewConstat({ name: "", type: "Point Faible", impact: "Moyen", description: "" });
                setIsConstatDialogOpen(true);
              }}
              disabled={isSubmitting}
            >
              <Plus className="h-4 w-4 mr-2" />
              Nouveau constat
            </Button>
          </div>
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table className="min-w-full">
                  <TableHeader>
                    <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                      <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
                      <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</TableHead>
                      <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Impact</TableHead>
                      <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recommandations</TableHead>
                      <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Risques</TableHead>
                      <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pièces jointes</TableHead>
                      <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody className="divide-y divide-gray-200">
                    {constats.length > 0 ? (
                      constats.map((item) => (
                        <TableRow
                          key={item.id}
                          className="hover:bg-gray-50/50 cursor-pointer"
                          onClick={() => handleRowClick(item)}
                        >
                          <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                            <div className="flex items-center">
                              <img src={constatIcon} alt="Constat" className="h-4 w-4 mr-2 flex-shrink-0" />
                              <span>{item.name || item.nom}</span>
                            </div>
                          </TableCell>
                          <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{item.type || 'N/A'}</TableCell>
                          <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                            <span className={`inline-block px-2 py-1 rounded text-xs font-semibold ${impactBadgeColors[item.impact] || ''}`}>{impactOptions.find(opt => opt.value === item.impact)?.label || item.impact || 'N/A'}</span>
                          </TableCell>
                          <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{item.recommandations}</TableCell>
                          <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{item.risques}</TableCell>
                          <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                            {item.piecesJointes && item.piecesJointes.length > 0 ? (
                              <div className="flex flex-wrap gap-2">
                                {item.piecesJointes.map((file, idx) => (
                                  <span key={idx} className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 rounded text-xs text-gray-700 border border-gray-200">
                                    <Paperclip className="h-3 w-3 text-gray-400" />
                                    {file.name || file.fileName || file}
                                  </span>
                                ))}
                              </div>
                            ) : (
                              <span className="text-gray-400 text-xs">Aucun fichier</span>
                            )}
                          </TableCell>
                          <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                            <div className="flex justify-end gap-2">
                              <Button size="sm" variant="ghost" className="h-8 w-8 p-0" onClick={e => { e.stopPropagation(); handleEditConstat(item); }} disabled={isSubmitting}>
                                <Edit className="h-4 w-4 text-blue-600" />
                              </Button>
                              <Button size="sm" variant="ghost" className="h-8 w-8 p-0" onClick={e => { e.stopPropagation(); handleDeleteConstat(item.id); }} disabled={isSubmitting}>
                                <Trash2 className="h-4 w-4 text-red-600" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="px-4 py-10 text-center text-sm text-gray-500">
                          Aucun constat trouvé pour cette activité.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      <Dialog open={isConstatDialogOpen} onOpenChange={setIsConstatDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{editingConstat ? "Modifier le constat" : "Nouveau constat"}</DialogTitle>
            <DialogDescription>
              {editingConstat ? "Modifiez les informations du constat" : "Ajoutez un nouveau constat à l'activité"}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="constat-nom">Nom *</Label>
              <Input
                id="constat-nom"
                name="name"
                value={newConstat.name}
                onChange={e => setNewConstat(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Nom du constat"
                disabled={isSubmitting}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="constat-type">Type de constat</Label>
              <Select
                name="type"
                value={newConstat.type}
                onValueChange={value => setNewConstat(prev => ({ ...prev, type: value }))}
                disabled={isSubmitting}
              >
                <SelectTrigger id="constat-type" className="w-full">
                  <SelectValue placeholder="Type de constat" />
                </SelectTrigger>
                <SelectContent>
                {typeOptions.map(opt => (
                    <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="constat-impact">Impact</Label>
              <Select
                name="impact"
                value={newConstat.impact}
                onValueChange={value => setNewConstat(prev => ({ ...prev, impact: value }))}
                disabled={isSubmitting}
              >
                <SelectTrigger id="constat-impact" className="w-full">
                  <SelectValue placeholder="Impact" />
                </SelectTrigger>
                <SelectContent>
                  {impactOptions.map((opt) => (
                    <SelectItem key={opt.value} value={opt.value}>
                      <span className="flex items-center gap-2">
                        <span className={`inline-block w-3 h-3 rounded-full ${impactCircleColors[opt.value]}`}></span>
                        {opt.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="constat-description">Description</Label>
              <Textarea
                id="constat-description"
                name="description"
                value={newConstat.description}
                onChange={e => setNewConstat(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Description du constat"
                rows={3}
                disabled={isSubmitting}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsConstatDialogOpen(false)} disabled={isSubmitting}>
              Annuler
            </Button>
            {editingConstat ? (
              <Button
                className="bg-blue-600 hover:bg-blue-700"
                onClick={handleAddConstat}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Mise à jour...
                  </>
                ) : (
                  "Mettre à jour"
                )}
              </Button>
            ) : (
              <Button
                className="bg-[#F62D51] hover:bg-[#F62D51]/90"
                onClick={handleAddConstat}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Création...
                  </>
                ) : (
                  "Créer constat"
                )}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default ConstatsTab;