import { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate, Outlet, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  ArrowLeft,
  Loader2,
  Shield,
  FileText,
  Edit,
  Activity,
  ClipboardCheck,
  Play,
  TestTube,
  AlertTriangle,
} from "lucide-react";
import { getControlById, reset } from "@/store/slices/controlSlice";
import { Button } from "@/components/ui/button";
import DetailHeader from "@/components/ui/detail-header";
import controlIcon from "@/assets/control.png";
import TabBar from "@/components/ui/tabs/tab-bar";
import TabContent from "@/components/ui/tabs/tab-content";
import { toast } from "sonner";
import { hasPermission } from "@/store/auth-slice";


function EditControl() {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isTabChanging, setIsTabChanging] = useState(false);

  // Get permissions
  const canUpdate = useSelector((state) => hasPermission(state, 'update'));
  const canDelete = useSelector((state) => hasPermission(state, 'delete'));

  const { currentControl, isLoading: controlLoading } = useSelector((state) => state.control);

  // Get the current tab from the URL
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    if (path.includes('/features')) {
      return 'features';
    } else if (path.includes('/evaluation')) {
      return 'evaluation';
    } else if (path.includes('/execution')) {
      return 'execution';
    } else if (path.includes('/defaillances')) {
      return 'defaillances';
    } else if (path.includes('/action-plan')) {
      return 'action-plan';
    } else if (path.includes('/fil-activite')) {
      return 'fil-activite';
    }
    return 'overview';
  }, [location.pathname]);

  const [activeTab, setActiveTab] = useState(getCurrentTab());

  // Update active tab when location changes
  useEffect(() => {
    setActiveTab(getCurrentTab());
  }, [location, getCurrentTab]);

  // Removed debug effect for performance optimization

  // Fetch control data
  useEffect(() => {
    const fetchControlDetails = async () => {
      setLoading(true);
      try {
        await dispatch(getControlById(id)).unwrap();
        setError(null);
      } catch (err) {
        console.error("Failed to fetch control:", err);
        setError(t('admin.controls.edit.error_loading', 'Failed to load control data. Please try again.'));
      } finally {
        setLoading(false);
      }
    };

    fetchControlDetails();

    // Cleanup function to reset control state when unmounting
    return () => {
      dispatch(reset());
    };
  }, [dispatch, id, t]);

  const handleBackToControls = () => {
    navigate("/admin/controls");
  };

  const tabs = [
    // 1. Vue d'ensemble (existing)
    { id: "overview", label: t('admin.controls.edit.tabs.overview', 'Vue d\'ensemble'), icon: <FileText className="h-4 w-4" /> },
    // 2. Préparation (new) - Only show if user has update permission - REMOVED
    // 3. Contexte de contrôle (renamed from Caractéristiques) - Only show if user has update permission
    ...(canUpdate ? [
      { id: "features", label: t('admin.controls.edit.tabs.context', 'Contexte de contrôle'), icon: <Edit className="h-4 w-4" /> },
    ] : []),
    // 4. Évaluation (existing)
    { id: "evaluation", label: t('admin.controls.edit.tabs.evaluation', 'Évaluation'), icon: <ClipboardCheck className="h-4 w-4" /> },
    // 5. Exécution (existing) - Only show if user has update permission
    ...(canUpdate ? [
      { id: "execution", label: t('admin.controls.edit.tabs.execution', 'Exécution'), icon: <Play className="h-4 w-4" /> },
    ] : []),
    // 6. Défaillances (existing) - Only show if user has update permission
    ...(canUpdate ? [
      { id: "defaillances", label: t('admin.controls.edit.tabs.defaillances', 'Défaillances'), icon: <AlertTriangle className="h-4 w-4" /> },
    ] : []),
    // 7. Plan d'action (existing) - Only show if user has update permission
    ...(canUpdate ? [
      { id: "action-plan", label: t('admin.controls.edit.tabs.action_plan', 'Plan d\'action'), icon: <Activity className="h-4 w-4" /> },
    ] : []),
    // 8. Fil d'activité (existing)
    { id: "fil-activite", label: t('admin.controls.edit.tabs.fil_activite', 'Fil d\'activité'), icon: <Activity className="h-4 w-4" /> },
  ];

  // Navigate to tab with improved error handling
  const navigateToTab = (tabId) => {
    if (isTabChanging || controlLoading) {
      // Tab change in progress or control loading, ignoring request
      return;
    }

    // Define restricted tabs that require update permission
    const restrictedTabs = ["preparation", "features", "execution", "defaillances", "action-plan"];

    // Check permissions for restricted tabs
    if (restrictedTabs.includes(tabId) && !canUpdate) {
      toast.error(t('admin.controls.edit.permission_error', "You don't have permission to access this tab"));
      return;
    }

    setIsTabChanging(true);

    try {
      let targetPath;
      switch (tabId) {
        case "overview":
          targetPath = `/admin/controls/edit/${id}`;
          break;
        case "preparation":
          targetPath = `/admin/controls/edit/${id}/preparation`;
          break;
        case "features":
          targetPath = `/admin/controls/edit/${id}/features`;
          break;
        case "evaluation":
          targetPath = `/admin/controls/edit/${id}/evaluation`;
          break;
        case "execution":
          targetPath = `/admin/controls/edit/${id}/execution`;
          break;
        case "defaillances":
          targetPath = `/admin/controls/edit/${id}/defaillances`;
          break;
        case "action-plan":
          targetPath = `/admin/controls/edit/${id}/action-plan`;
          break;
        case "fil-activite":
          targetPath = `/admin/controls/edit/${id}/fil-activite`;
          break;
        default:
          targetPath = `/admin/controls/edit/${id}`;
      }

      // FIXED: Don't refresh control data when switching tabs to preserve unsaved changes
      if (tabId !== activeTab) {
        console.log(`Navigating from ${activeTab} to ${tabId} (without refreshing data)`);
        // Removed: dispatch(getControlById(id)); - This was causing form data to reset
      }

      // Navigate to the new tab
      navigate(targetPath);
    } catch (error) {
      console.error("Error navigating to tab:", error);
      toast.error(t('admin.controls.edit.error_tab_change', "Error changing tabs. Please try again."));
    } finally {
      setTimeout(() => {
        setIsTabChanging(false);
      }, 300);
    }
  };

  // Refresh control data
  const refreshControl = async () => {
    setLoading(true);
    try {
      await dispatch(getControlById(id)).unwrap();
      setError(null);
    } catch (err) {
      console.error("Failed to refresh control:", err);
      toast.error(t('admin.controls.edit.error_refresh', "Failed to refresh control data"));
    } finally {
      setLoading(false);
    }
  };

  // Add effect to check URL and redirect if necessary
  useEffect(() => {
    // Define paths that require update permission
    const restrictedPaths = [
      `/admin/controls/edit/${id}/features`,
      `/admin/controls/edit/${id}/execution`,
      `/admin/controls/edit/${id}/defaillances`,
      `/admin/controls/edit/${id}/action-plan`,
    ];

    // If user doesn't have update permission and tries to access a restricted path
    if (!canUpdate && restrictedPaths.some(path => location.pathname === path)) {
      toast.error(t('admin.controls.edit.permission_error', "You don't have permission to access this page"));
      navigate(`/admin/controls/edit/${id}`);
    }
  }, [location.pathname, canUpdate, id, navigate, t]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!currentControl) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          {t('admin.controls.edit.control_not_found', 'Control not found')}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      {/* Header */}
      <div className="mb-6">
        <DetailHeader
          title={currentControl?.name || t('admin.controls.edit.unnamed', 'Unnamed Control')}
          icon={<img src={controlIcon} alt="Contrôle" className="h-6 w-6" />}
          metadata={[
            currentControl.code
              ? t('admin.controls.edit.metadata.code', { value: currentControl.code })
              : t('admin.controls.edit.metadata.code_na', 'Code: N/A'),
            currentControl.organizationalLevel
              ? t('admin.controls.edit.metadata.organizational_level', { value: currentControl.organizationalLevel })
              : t('admin.controls.edit.metadata.organizational_level_na', 'Organizational Level: N/A'),
          ].filter(Boolean)}
          onBack={handleBackToControls}
          backLabel={t('admin.controls.edit.back_to_controls', 'Back to Controls')}
        />
      </div>

      {/* Tabs */}
      <TabBar
        activeTab={activeTab}
        tabs={tabs}
        onTabChange={navigateToTab}
        className="mb-6"
      />

      {/* Tab Content */}
      <TabContent>
        <Outlet
          context={{
            control: currentControl,
            refreshControl,
            permissions: {
              canUpdate,
              canDelete
            },
            // Add navigation handler for unsaved changes warning
            onNavigateAway: (hasUnsavedChanges) => {
              if (hasUnsavedChanges) {
                return window.confirm('You have unsaved changes. Are you sure you want to leave?');
              }
              return true;
            }
          }}
        />
      </TabContent>
    </div>
  );
}

export default EditControl;
