const express = require('express');
const router = express.Router();
const { verifyToken } = require('../../middleware/auth');
const {
  uploadRiskFile,
  getRiskAttachments,
  deleteRiskAttachment,
  downloadRiskAttachment
} = require('../../controllers/uploads/risk-upload-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Upload a file
router.post('/', uploadRiskFile);

// Get all attachments for a risk
router.get('/', getRiskAttachments);

// Delete an attachment
router.delete('/:id', deleteRiskAttachment);

// Download an attachment
router.get('/download/:id', downloadRiskAttachment);

module.exports = router;
