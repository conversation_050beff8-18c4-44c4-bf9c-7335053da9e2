import { useEffect, useState, useCallback } from "react";
import { useParams, useNavigate, Outlet, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { getBusinessLineById, reset } from "@/store/slices/businessLineSlice";
import { Briefcase, FileText, Edit, Loader2 } from "lucide-react";
import DetailHeader from "@/components/ui/detail-header";
import TabBar from "@/components/ui/tabs/tab-bar";
import TabContent from "@/components/ui/tabs/tab-content";
import { useTranslation } from "react-i18next";

const EditBusinessLine = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { currentBusinessLine, isLoading, isError, error } = useSelector(
    (state) => state.businessLine
  );

  // Get the current tab from the URL
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    if (path.includes('/features')) {
      return 'features';
    }
    return 'overview';
  }, [location.pathname]);

  const [activeTab, setActiveTab] = useState(getCurrentTab());

  // Update active tab when location changes
  useEffect(() => {
    setActiveTab(getCurrentTab());
  }, [location, getCurrentTab]);

  useEffect(() => {
    dispatch(getBusinessLineById(id));
    return () => {
      dispatch(reset());
    };
  }, [dispatch, id]);

  // Handle go back
  const handleGoBack = () => {
    navigate("/admin/data/business-lines");
  };

  const [tabs, setTabs] = useState([
    { id: "overview", label: t('common.tabs.overview', 'Overview'), icon: <FileText className="h-4 w-4" /> },
    { id: "features", label: t('common.tabs.features', 'Features'), icon: <Edit className="h-4 w-4" /> }
  ]);

  // Update tabs when language changes
  useEffect(() => {
    setTabs([
      { id: "overview", label: t('common.tabs.overview', 'Overview'), icon: <FileText className="h-4 w-4" /> },
      { id: "features", label: t('common.tabs.features', 'Features'), icon: <Edit className="h-4 w-4" /> }
    ]);
  }, [t]);

  // Navigate to tab
  const navigateToTab = (tabId) => {
    switch (tabId) {
      case "overview":
        navigate(`/admin/data/business-lines/${id}`);
        break;
      case "features":
        navigate(`/admin/data/business-lines/${id}/features`);
        break;
      default:
        navigate(`/admin/data/business-lines/${id}`);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {t('admin.business_lines.error.loading', 'Error: {{message}}', { message: error })}
        </div>
      </div>
    );
  }

  if (!currentBusinessLine) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          {t('admin.business_lines.error.not_found', 'Business line not found')}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      {/* Header */}
      <DetailHeader
        title={currentBusinessLine.name}
        icon={<Briefcase className="h-6 w-6 text-[#F62D51]" />}
        metadata={[
          currentBusinessLine.description ?
            `${currentBusinessLine.description.substring(0, 100)}${currentBusinessLine.description.length > 100 ? '...' : ''}` :
            t('admin.business_lines.overview.no_description', 'No description')
        ]}
        onBack={handleGoBack}
        backLabel={t('admin.business_lines.back_to_list', 'Back to Business Lines')}
      />

      {/* Tabs */}
      <TabBar
        activeTab={activeTab}
        tabs={tabs}
        onTabChange={navigateToTab}
        className="mb-6"
      />

      {/* Tab Content */}
      <TabContent>
        <Outlet context={{ businessLine: currentBusinessLine }} />
      </TabContent>
    </div>
  );
};

export default EditBusinessLine;