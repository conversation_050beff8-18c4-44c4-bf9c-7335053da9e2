const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllFicheDeTravail,
  getFicheDeTravailByActivityId,
  createFicheDeTravail,
  getFicheDeTravailById,
  updateFicheDeTravail,
  deleteFicheDeTravail,
  createQuestion,
  reorderQuestions,
  getAllQuestions,
  getQuestionsByFicheId,
  deleteQuestion
} = require('../../controllers/audit/audit-fiche-de-travail-controller');

router.use(verifyToken);

// General routes
router.get('/', authorizeRoles(['audit_director', 'auditor']), getAllFicheDeTravail);
router.post('/', authorizeRoles(['audit_director', 'auditor']), createFicheDeTravail);

// Questions routes - must come before /:id routes
router.get('/questions', authorizeRoles(['audit_director', 'auditor']), getAllQuestions);
router.get('/questions/fiche/:ficheId', authorizeRoles(['audit_director', 'auditor']), getQuestionsByFicheId);
router.get('/:ficheId/questions', authorizeRoles(['audit_director', 'auditor']), getQuestionsByFicheId);
router.post('/questions', authorizeRoles(['audit_director', 'auditor']), createQuestion);
router.put('/questions/reorder', authorizeRoles(['audit_director', 'auditor']), reorderQuestions);
router.delete('/questions/:id', authorizeRoles(['audit_director', 'auditor']), deleteQuestion);

// Activity specific route
router.get('/activity/:activityId', authorizeRoles(['audit_director', 'auditor']), getFicheDeTravailByActivityId);

// ID specific routes
router.get('/:id', authorizeRoles(['audit_director', 'auditor']), getFicheDeTravailById);
router.put('/:id', authorizeRoles(['audit_director', 'auditor']), updateFicheDeTravail);
router.delete('/:id', authorizeRoles(['audit_director', 'auditor']), deleteFicheDeTravail);

module.exports = router;
