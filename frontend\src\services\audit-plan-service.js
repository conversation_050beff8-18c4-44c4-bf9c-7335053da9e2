import axios from 'axios';
import { getApiBaseUrl, getAuthHeaders } from '@/utils/api-config';

// Remove /api from base URL since it's already included in getApiBaseUrl()
const API_URL = `${getApiBaseUrl()}/audit-plans`;

// Create axios instance with default config
const axiosInstance = axios.create({
  withCredentials: true
});

// Get all audit plans
export const getAllAuditPlans = async (signal) => {
  try {
    const response = await axiosInstance.get(API_URL, {
      headers: getAuthHeaders(),
      signal
    });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    console.error('Error fetching audit plans:', error);
    throw error;
  }
};

// Get audit plan by ID
export const getAuditPlanById = async (id, signal) => {
  try {
    const response = await axiosInstance.get(
      `${API_URL}/${id}`,
      { 
        headers: getAuthHeaders(),
        signal 
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    console.error('Error fetching audit plan:', error);
    throw error;
  }
};

// Create new audit plan
export const createAuditPlan = async (planData, signal) => {
  try {
    const response = await axiosInstance.post(API_URL, planData, { 
      headers: getAuthHeaders(),
      signal 
    });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    console.error('Error creating audit plan:', error);
    throw error;
  }
};

// Update audit plan
export const updateAuditPlan = async (id, planData, signal) => {
  try {
    const response = await axiosInstance.put(
      `${API_URL}/${id}`,
      planData,
      {
        headers: getAuthHeaders(),
        signal
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    console.error('Error updating audit plan:', error);
    throw error;
  }
};

// Delete audit plan
export const deleteAuditPlan = async (id, signal) => {
  try {
    const response = await axiosInstance.delete(`${API_URL}/${id}`, {
      headers: getAuthHeaders(),
      signal
    });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    console.error('Error deleting audit plan:', error);
    throw error;
  }
};

// Delete multiple audit plans
export const deleteMultipleAuditPlans = async (ids, signal) => {
  try {
    const response = await axiosInstance.post(`${API_URL}/delete-multiple`, { ids }, {
      headers: getAuthHeaders(),
      signal
    });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    console.error('Error deleting multiple audit plans:', error);
    throw error;
  }
}; 