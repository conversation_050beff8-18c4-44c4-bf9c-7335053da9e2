const { sequelize } = require('../models');
const migration = require('../migrations/20250103000000-change-campagne-id-to-string.js');

async function runMigration() {
  try {
    console.log('🚀 Starting campagne ID migration...');
    
    const queryInterface = sequelize.getQueryInterface();
    await migration.up(queryInterface, sequelize.Sequelize);
    
    console.log('✅ Migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
