const db = require('../../models');
const { validateTransition } = require('../../utils/stateMachine');
const { sendEmailDirect } = require('../email-controller');

/**
 * Check if user is a contributor or has a higher role
 *
 * @param {number} userId - User ID
 * @param {string} riskId - Risk ID
 * @returns {Promise<boolean>} True if user is a contributor or has a higher role
 */
const isUserAuthorized = async (userId, riskId) => {
  try {
    // Check if user is a contributor to this risk
    const contributor = await db.RiskContributor.findOne({
      where: {
        risk_id: riskId,
        user_id: userId
      }
    });

    if (contributor) {
      return true;
    }

    // Check user roles
    const user = await db.User.findByPk(userId, {
      include: [
        {
          model: db.Role,
          as: 'roles',
          attributes: ['id', 'code']
        }
      ]
    });

    // If user has a higher role, they are authorized
    if (user && user.roles) {
      // Check role codes
      const hasHigherRoleByCode = user.roles.some(role =>
        ['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager', 'admin', 'super_admin'].includes(role.code)
      );

      if (hasHigherRoleByCode) {
        console.log(`User ${userId} has admin role by code, authorized for risk workflow`);
        return true;
      }

      // Also check role names for backward compatibility
      const hasHigherRoleByName = user.roles.some(role => {
        if (!role.name) return false;
        const normalizedName = role.name.toLowerCase().replace(/\s+/g, '_');
        return ['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager', 'admin', 'super_admin'].some(
          adminRole => normalizedName.includes(adminRole) || adminRole.includes(normalizedName)
        );
      });

      if (hasHigherRoleByName) {
        console.log(`User ${userId} has admin role by name, authorized for risk workflow`);
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Error checking user authorization:', error);
    return false;
  }
};

/**
 * Get the current workflow state for a specific risk
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getWorkflowState = async (req, res) => {
  try {
    const { riskId } = req.params;

    const risk = await db.Risk.findByPk(riskId);
    if (!risk) {
      return res.status(404).json({ success: false, message: 'Risk not found' });
    }

    // Get events related to this risk to build a timeline
    const events = await db.Event.findAll({
      where: { risk_id: riskId },
      order: [['timestamp', 'ASC']]
    });

    return res.status(200).json({
      success: true,
      data: {
        current_state: risk.current_state,
        timeline: events
      }
    });
  } catch (error) {
    console.error('Error in getWorkflowState:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve workflow state',
      error: error.message
    });
  }
};

/**
 * Perform a workflow transition for a risk
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.transitionWorkflow = async (req, res) => {
  try {
    const { riskId } = req.params;
    const { action, message, userId: bodyUserId } = req.body;

    // Get user ID from multiple possible sources
    const userIdToUse = bodyUserId || req.user.id || req.user.userId || req.user._id;

    if (!userIdToUse) {
      console.error('User ID not found in request:', req.user);
      return res.status(400).json({
        success: false,
        message: 'User ID is required but not found in request'
      });
    }

    // Check if the user is authorized (contributor or higher role)
    const authorized = await isUserAuthorized(userIdToUse, riskId);
    if (!authorized) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to perform workflow actions on this risk'
      });
    }

    // Get the risk
    const risk = await db.Risk.findByPk(riskId);
    if (!risk) {
      return res.status(404).json({ success: false, message: 'Risk not found' });
    }

    // Get the user
    const user = await db.User.findByPk(userIdToUse);
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    // Check user's role for certain actions
    const userRoles = user.roles || [];
    const userRoleCodes = userRoles.map(r => r.code || '');

    // Check if user has manager role by code
    const isManagerByCode = userRoleCodes.some(code =>
      ['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager', 'admin', 'super_admin'].includes(code)
    );

    // Check if user has manager role by name
    const isManagerByName = userRoles.some(role => {
      if (!role.name) return false;
      const normalizedName = role.name.toLowerCase().replace(/\s+/g, '_');
      return ['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager', 'admin', 'super_admin'].some(
        adminRole => normalizedName.includes(adminRole) || adminRole.includes(normalizedName)
      );
    });

    const isManager = isManagerByCode || isManagerByName;

    // Add debug logging for roles
    console.log('User role codes for permission check:', {
      userId: userIdToUse,
      username: user.username || user.email,
      userRoles: userRoles.map(r => ({ id: r.id, name: r.name, code: r.code })),
      roleCodes: userRoleCodes,
      isManager,
      requestedAction: action,
      currentState: risk.current_state
    });

    // For approve/validate/reject actions, only managers can perform them
    if (['approve', 'validate', 'reject'].includes(action) && !isManager) {
      return res.status(403).json({
        success: false,
        message: `Only managers can perform the "${action}" action`
      });
    }

    // Validate the requested transition
    const currentState = risk.current_state;
    const result = validateTransition(currentState, action);

    if (!result.valid) {
      return res.status(400).json({
        success: false,
        message: result.message
      });
    }

    // Start transaction
    const t = await db.sequelize.transaction();

    try {
      // Update risk state
      await risk.update({ current_state: result.nextState }, { transaction: t });

      // Log the event
      await db.Event.create({
        risk_id: riskId,
        timestamp: new Date(),
        step: result.nextState,
        user: user.username || user.email,
        transition: action,
        message: message || `Transitioned from ${currentState} to ${result.nextState}`
      }, { transaction: t });

      // Commit transaction
      await t.commit();

      // After committing transaction, send email if state is now 'To Validate'
      if (result.nextState === 'To Validate') {
        // Find all users with the 'risk_manager' role
        const riskManagerRole = await db.Role.findOne({ where: { code: 'risk_manager' } });
        if (riskManagerRole) {
          const userRoles = await db.UserRole.findAll({ where: { roleId: riskManagerRole.id } });
          const userIds = userRoles.map(ur => ur.userId);
          const users = await db.User.findAll({ where: { id: userIds } });
          for (const user of users) {
            if (user.email) {
              await sendEmailDirect({
                to: user.email,
                subject: "Risque à valider",
                text: `Bonjour,\n\nLe risque \"${risk.name}\" nécessite votre validation. Veuillez consulter les détails et le workflow du risque dans l'application.\n\nMerci.`,
                html: `<p>Bonjour,</p><p>Le risque \"<b>${risk.name}</b>\" nécessite votre validation. Veuillez consulter les détails et le workflow du risque dans l'application.</p><p>Merci.</p>`
              });
            }
          }
        }
      }
      return res.status(200).json({
        success: true,
        message: 'Workflow transition successful',
        data: {
          previous_state: currentState,
          current_state: result.nextState,
          action: action
        }
      });
    } catch (error) {
      // Rollback transaction on error
      await t.rollback();
      throw error;
    }
  } catch (error) {
    console.error('Error in transitionWorkflow:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to transition workflow',
      error: error.message
    });
  }
};

/**
 * Get available transitions for a risk's current state
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAvailableTransitions = async (req, res) => {
  try {
    const { riskId } = req.params;

    const risk = await db.Risk.findByPk(riskId);
    if (!risk) {
      return res.status(404).json({ success: false, message: 'Risk not found' });
    }

    // Get available transitions from the state machine
    const availableTransitions = require('../../utils/stateMachine').getAvailableTransitions(risk.current_state);

    return res.status(200).json({
      success: true,
      data: {
        current_state: risk.current_state,
        available_transitions: availableTransitions
      }
    });
  } catch (error) {
    console.error('Error in getAvailableTransitions:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve available transitions',
      error: error.message
    });
  }
};