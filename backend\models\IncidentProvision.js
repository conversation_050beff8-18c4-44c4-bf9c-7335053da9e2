module.exports = (sequelize, DataTypes) => {
  const IncidentProvision = sequelize.define('IncidentProvision', {
    provisionID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    amount: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
    localAmount: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    currency: {
      type: DataTypes.STRING(10),
      allowNull: true,
      defaultValue: 'XOF',
    },
    incidentID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Incident',
        key: 'incidentID',
      },
    },
  }, {
    tableName: 'incident_provision',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['provisionID'],
      },
      {
        fields: ['incidentID'],
      },
    ],
  });

  IncidentProvision.associate = (models) => {
    // IncidentProvision belongs to Incident
    IncidentProvision.belongsTo(models.Incident, {
      foreignKey: 'incidentID',
      as: 'incident',
      onDelete: 'CASCADE',
    });
  };

  return IncidentProvision;
};
