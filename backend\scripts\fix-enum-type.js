const db = require('../models');

async function fixEnumType() {
  try {
    console.log('🔧 Fixing ENUM Type for Control Activity Logs...\n');

    // Connect to database
    await db.sequelize.authenticate();
    console.log('✅ Database connected successfully\n');

    // Check what ENUM types exist
    console.log('📋 Checking existing ENUM types...');
    const existingEnums = await db.sequelize.query(`
      SELECT typname FROM pg_type WHERE typtype = 'e' AND typname LIKE '%activity%';
    `, { type: db.sequelize.QueryTypes.SELECT });

    console.log('📋 Existing activity-related ENUMs:');
    existingEnums.forEach(row => {
      console.log(`   - ${row.typname}`);
    });

    // Check the actual column type
    console.log('\n📋 Checking ControlActivityLogs table column types...');
    const columnInfo = await db.sequelize.query(`
      SELECT column_name, data_type, udt_name 
      FROM information_schema.columns 
      WHERE table_name = 'ControlActivityLogs' 
      AND column_name = 'type';
    `, { type: db.sequelize.QueryTypes.SELECT });

    if (columnInfo.length > 0) {
      console.log('📋 Type column info:');
      columnInfo.forEach(col => {
        console.log(`   - Column: ${col.column_name}, Data Type: ${col.data_type}, UDT Name: ${col.udt_name}`);
      });
    }

    // Check what values are currently in the type column
    console.log('\n📋 Checking current values in type column...');
    const currentValues = await db.sequelize.query(`
      SELECT DISTINCT type FROM "ControlActivityLogs";
    `, { type: db.sequelize.QueryTypes.SELECT });

    console.log('📋 Current type values:');
    currentValues.forEach(row => {
      console.log(`   - ${row.type}`);
    });

    // Try to create the correct ENUM type
    console.log('\n🔧 Creating correct ENUM type...');
    
    try {
      // First, let's see if we need to drop the column constraint
      await db.sequelize.query(`
        ALTER TABLE "ControlActivityLogs" 
        ALTER COLUMN "type" TYPE TEXT;
      `);
      console.log('✅ Converted type column to TEXT');

      // Create the ENUM type with the correct name
      await db.sequelize.query(`
        DROP TYPE IF EXISTS "enum_ControlActivityLogs_type";
      `);
      console.log('✅ Dropped old ENUM type if it existed');

      await db.sequelize.query(`
        CREATE TYPE "enum_ControlActivityLogs_type" AS ENUM ('creation', 'update', 'deletion', 'link', 'unlink');
      `);
      console.log('✅ Created new ENUM type');

      // Apply the ENUM type to the column
      await db.sequelize.query(`
        ALTER TABLE "ControlActivityLogs" 
        ALTER COLUMN "type" TYPE "enum_ControlActivityLogs_type" 
        USING "type"::"enum_ControlActivityLogs_type";
      `);
      console.log('✅ Applied ENUM type to column');

    } catch (enumError) {
      console.error('❌ Error creating ENUM type:', enumError.message);
      
      // Alternative approach: just ensure the column accepts the values we need
      console.log('🔧 Trying alternative approach...');
      await db.sequelize.query(`
        ALTER TABLE "ControlActivityLogs" 
        ALTER COLUMN "type" TYPE VARCHAR(50);
      `);
      console.log('✅ Set type column to VARCHAR(50)');
    }

    // Test creating activity logs with all types
    console.log('\n🧪 Testing all activity types...');
    
    const testControl = await db.Control.findOne({
      attributes: ['controlID', 'name'],
      limit: 1
    });
    
    if (testControl) {
      const testTypes = ['creation', 'update', 'deletion', 'link', 'unlink'];
      
      for (const testType of testTypes) {
        try {
          const testLog = await db.ControlActivityLog.create({
            control_id: testControl.controlID,
            user: 'ENUM Test User',
            type: testType,
            details: `Test ${testType} activity`
          });
          
          console.log(`✅ ${testType} activity log created: ${testLog.id}`);
          
          // Clean up
          await testLog.destroy();
          
        } catch (typeError) {
          console.error(`❌ ${testType} activity log failed:`, typeError.message);
        }
      }
    }

    // Final verification
    console.log('\n📋 Final verification...');
    const finalColumnInfo = await db.sequelize.query(`
      SELECT column_name, data_type, udt_name 
      FROM information_schema.columns 
      WHERE table_name = 'ControlActivityLogs' 
      AND column_name = 'type';
    `, { type: db.sequelize.QueryTypes.SELECT });

    console.log('📋 Final type column info:');
    finalColumnInfo.forEach(col => {
      console.log(`   - Column: ${col.column_name}, Data Type: ${col.data_type}, UDT Name: ${col.udt_name}`);
    });

    // Check if ENUM values are accessible
    try {
      const enumValues = await db.sequelize.query(`
        SELECT unnest(enum_range(NULL::"enum_ControlActivityLogs_type")) AS enum_value;
      `, { type: db.sequelize.QueryTypes.SELECT });
      
      console.log('✅ ENUM values accessible:');
      enumValues.forEach(row => {
        console.log(`   - ${row.enum_value}`);
      });
    } catch (enumCheckError) {
      console.log('⚠️  ENUM values check failed (this is OK if using VARCHAR):', enumCheckError.message);
    }

    console.log('\n🎯 ENUM type fix completed!');

  } catch (error) {
    console.error('❌ Error during ENUM fix:', error);
  } finally {
    await db.sequelize.close();
  }
}

// Run the fix
fixEnumType();
