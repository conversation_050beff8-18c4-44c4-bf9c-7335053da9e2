import { useOutletContext } from "react-router-dom";
import { Tag, Calendar, Hash, FileText, GitBranch } from "lucide-react";
import { useEffect, useState } from "react";

function BusinessProcessesOverview() {
  const { process, businessProcesses } = useOutletContext();
  const [parentBusinessProcess, setParentBusinessProcess] = useState(null);

  // Find parent business process
  useEffect(() => {
    if (process?.parentBusinessProcessID && businessProcesses?.length) {
      const parent = businessProcesses.find(
        p => p.businessProcessID === process.parentBusinessProcessID
      );
      setParentBusinessProcess(parent);
    } else {
      setParentBusinessProcess(null);
    }
  }, [process, businessProcesses]);

  // Helper function to format dates
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-GB", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6">
        <div>
          <h3 className="text-lg font-semibold text-[#242A33] mb-4">Business Process Details</h3>
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Tag className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Name</p>
                <p className="font-medium">{process.name || "N/A"}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Hash className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Code</p>
                <p className="font-medium">{process.code || "N/A"}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <GitBranch className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Parent Business Process</p>
                <p className="font-medium">{parentBusinessProcess ? parentBusinessProcess.name : "None"}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <FileText className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">Comment</p>
                <p className="font-medium">{process.comment || "N/A"}</p>
              </div>
            </div>

            {process.createdAt && (
              <div className="flex items-start gap-4">
                <div className="bg-gray-50 p-2 rounded-lg">
                  <Calendar className="h-5 w-5 text-[#555F6D]" />
                </div>
                <div>
                  <p className="text-sm text-[#555F6D]">Created At</p>
                  <p className="font-medium">{formatDate(process.createdAt)}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default BusinessProcessesOverview;
