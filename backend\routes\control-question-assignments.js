const express = require('express');
const router = express.Router();
const db = require('../models');
const { ControlQuestionAssignment, User, Control } = db;
const notificationController = require('../controllers/notifications/notification-controller');
const socketUtils = require('../utils/socket-io');
const { sendEmailDirect } = require('../controllers/email-controller');

// Get all users assigned to a specific control
router.get('/control/:controlId/users', async (req, res) => {
  try {
    const { controlId } = req.params;

    const assignments = await ControlQuestionAssignment.findAll({
      where: { controlId },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'assignedByUser',
          attributes: ['id', 'username', 'email']
        }
      ],
      order: [['assignedAt', 'DESC']]
    });

    const users = assignments.map(assignment => ({
      id: assignment.user.id,
      username: assignment.user.username,
      email: assignment.user.email,
      assignedAt: assignment.assignedAt,
      assignedBy: assignment.assignedByUser ? {
        id: assignment.assignedByUser.id,
        username: assignment.assignedByUser.username,
        email: assignment.assignedByUser.email
      } : null
    }));

    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Error fetching control assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching control assignments',
      error: error.message
    });
  }
});

// Get all controls assigned to a specific user
router.get('/user/:userId/controls', async (req, res) => {
  try {
    const { userId } = req.params;

    const assignments = await ControlQuestionAssignment.findAll({
      where: { userId },
      include: [
        {
          model: Control,
          as: 'control',
          attributes: ['controlID', 'name', 'code', 'objective']
        },
        {
          model: User,
          as: 'assignedByUser',
          attributes: ['id', 'username', 'email']
        }
      ],
      order: [['assignedAt', 'DESC']]
    });

    const controls = assignments.map(assignment => ({
      controlID: assignment.control.controlID,
      name: assignment.control.name,
      code: assignment.control.code,
      objective: assignment.control.objective,
      assignedAt: assignment.assignedAt,
      assignedBy: assignment.assignedByUser ? {
        id: assignment.assignedByUser.id,
        username: assignment.assignedByUser.username,
        email: assignment.assignedByUser.email
      } : null
    }));

    res.json({
      success: true,
      data: controls
    });
  } catch (error) {
    console.error('Error fetching user control assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching user control assignments',
      error: error.message
    });
  }
});

// Assign multiple users to a control
router.post('/control/:controlId/assign', async (req, res) => {
  const transaction = await db.sequelize.transaction();

  try {
    const { controlId } = req.params;
    const { userIds, assignedBy } = req.body;

    if (!userIds || !Array.isArray(userIds)) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'User IDs array is required'
      });
    }

    // If userIds is empty, it means remove all assignments
    if (userIds.length === 0) {
      console.log('[Control Assignment] Removing all user assignments for control:', controlId);

      // Remove all existing assignments for this control
      const deletedCount = await ControlQuestionAssignment.destroy({
        where: { controlId },
        transaction
      });

      await transaction.commit();
      console.log('[Control Assignment] All assignments removed successfully');

      return res.json({
        success: true,
        message: 'All user assignments removed successfully',
        data: {
          controlId,
          assignedUsers: [],
          assignmentsCount: 0,
          removedCount: deletedCount,
          notificationsSent: 0
        }
      });
    }

    // Verify control exists
    const control = await Control.findByPk(controlId, { transaction });
    if (!control) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Control not found'
      });
    }

    // Verify all users exist
    const users = await User.findAll({
      where: { id: userIds },
      transaction
    });

    if (users.length !== userIds.length) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'One or more users not found'
      });
    }

    // Get existing assignments to determine which are new
    const existingAssignments = await ControlQuestionAssignment.findAll({
      where: { controlId },
      transaction
    });
    const existingUserIds = existingAssignments.map(a => a.userId);

    // Remove existing assignments for this control
    await ControlQuestionAssignment.destroy({
      where: { controlId },
      transaction
    });

    // Create new assignments
    const assignments = userIds.map(userId => ({
      userId,
      controlId,
      assignedBy: assignedBy || null,
      assignedAt: new Date()
    }));

    const createdAssignments = await ControlQuestionAssignment.bulkCreate(assignments, { transaction });

    // Create notifications and send emails for newly assigned users
    const newlyAssignedUserIds = userIds.filter(userId => !existingUserIds.includes(userId));
    const notificationPromises = [];
    const emailPromises = [];

    for (const userId of newlyAssignedUserIds) {
      const user = users.find(u => u.id === userId);

      // Create notification
      const notificationData = {
        userId: userId,
        type: 'control_assignment',
        entityId: controlId,
        entityName: control.name || `Contrôle ${controlId}`,
        message: `Vous avez été assigné(e) au contrôle : ${control.name || controlId}`,
        assignedBy: assignedBy
      };

      console.log('[Control Assignment] Creating notification for user:', userId, notificationData);
      const notificationPromise = notificationController.createNotification(notificationData, { transaction });
      notificationPromises.push(notificationPromise);

      // Prepare email (with error handling)
      if (user && user.email) {
        console.log(`[Control Assignment] Preparing email for user: ${user.email}`);
        const emailPromise = sendEmailDirect({
          to: user.email,
          subject: "Nouveau contrôle assigné",
          text: `Bonjour ${user.username},\n\nVous avez été assigné(e) au contrôle : ${control.name || controlId}. Veuillez consulter les détails du contrôle dans l'application.\n\nMerci.`,
          html: `<p>Bonjour ${user.username},</p><p>Vous avez été assigné(e) au contrôle : <b>${control.name || controlId}</b>. Veuillez consulter les détails du contrôle dans l'application.</p><p>Merci.</p>`
        }).catch(emailError => {
          console.error(`[Control Assignment] Error preparing email for ${user.email}:`, emailError.message);
          return { error: emailError.message, email: user.email };
        });
        emailPromises.push(emailPromise);
      }
    }

    // Wait for all notifications to be created
    const createdNotifications = await Promise.all(notificationPromises);
    console.log('[Control Assignment] Created notifications:', createdNotifications.length);

    // Commit transaction
    await transaction.commit();
    console.log('[Control Assignment] Transaction committed successfully');

    // Send emails (after commit) - don't let email failures crash the server
    if (emailPromises.length > 0) {
      try {
        console.log(`[Control Assignment] Attempting to send ${emailPromises.length} emails...`);
        const emailResults = await Promise.allSettled(emailPromises);

        const successfulEmails = emailResults.filter(result =>
          result.status === 'fulfilled' && !result.value?.error
        ).length;

        const failedEmails = emailResults.filter(result =>
          result.status === 'rejected' || result.value?.error
        );

        console.log(`[Control Assignment] Email results: ${successfulEmails} sent successfully, ${failedEmails.length} failed`);

        if (failedEmails.length > 0) {
          console.warn('[Control Assignment] Failed emails:', failedEmails.map(f =>
            f.status === 'rejected' ? f.reason?.message : f.value?.error
          ));
        }
      } catch (emailError) {
        console.error('[Control Assignment] Unexpected error in email sending:', emailError.message);
      }
    } else {
      console.log('[Control Assignment] No emails to send');
    }

    // Send Socket.IO notifications (after commit)
    try {
      const io = socketUtils.getIo();
      if (io) {
        for (let i = 0; i < newlyAssignedUserIds.length; i++) {
          const userId = newlyAssignedUserIds[i];
          const notification = createdNotifications[i];

          if (notification) {
            const targetRoom = `user-${userId.toString()}`;
            const socketData = {
              userId: userId,
              type: 'control_assignment',
              entityId: controlId,
              entityName: control.name || `Contrôle ${controlId}`,
              message: `Vous avez été assigné(e) au contrôle : ${control.name || controlId}`,
              assignedBy: assignedBy,
              id: notification.id,
              notificationId: notification.id.toString(),
              createdAt: notification.createdAt,
              is_read: false
            };

            io.to(targetRoom).emit('notification', socketData);
            console.log(`[Control Assignment] Socket notification sent to room ${targetRoom}:`, socketData);
          }
        }
      } else {
        console.warn('[Control Assignment] Socket.IO instance not available');
      }
    } catch (socketError) {
      console.error('[Control Assignment] Error emitting socket notifications:', socketError);
    }

    res.json({
      success: true,
      message: `Successfully assigned ${userIds.length} users to control`,
      data: {
        controlId,
        assignedUsers: userIds,
        assignmentsCount: createdAssignments.length,
        newAssignments: newlyAssignedUserIds.length,
        notificationsSent: createdNotifications.length
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error assigning users to control:', error);
    res.status(500).json({
      success: false,
      message: 'Error assigning users to control',
      error: error.message
    });
  }
});

// Remove specific user assignment from control
router.delete('/control/:controlId/user/:userId', async (req, res) => {
  try {
    const { controlId, userId } = req.params;

    const deleted = await ControlQuestionAssignment.destroy({
      where: {
        controlId,
        userId
      }
    });

    if (deleted === 0) {
      return res.status(404).json({
        success: false,
        message: 'Assignment not found'
      });
    }

    res.json({
      success: true,
      message: 'User assignment removed successfully'
    });
  } catch (error) {
    console.error('Error removing user assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Error removing user assignment',
      error: error.message
    });
  }
});

// Remove all user assignments from control
router.delete('/control/:controlId/users', async (req, res) => {
  try {
    const { controlId } = req.params;

    const deleted = await ControlQuestionAssignment.destroy({
      where: { controlId }
    });

    res.json({
      success: true,
      message: `Removed ${deleted} user assignments from control`,
      data: {
        controlId,
        removedCount: deleted
      }
    });
  } catch (error) {
    console.error('Error removing control assignments:', error);
    res.status(500).json({
      success: false,
      message: 'Error removing control assignments',
      error: error.message
    });
  }
});

module.exports = router;
