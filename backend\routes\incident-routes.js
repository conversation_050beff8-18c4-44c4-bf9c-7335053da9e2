const express = require('express');
const router = express.Router();
const incidentController = require('../controllers/incidents/incident-controller');
const workflowController = require('../controllers/incidents/workflow-controller');
const { authJwt } = require('../middleware');

// Ensure user is authenticated for all incident routes
router.use(authJwt.verifyToken);

// Incident CRUD routes
router.post('/', incidentController.create);
router.get('/', incidentController.findAll);
router.get('/:id', incidentController.findOne);
router.put('/:id', incidentController.update);
router.delete('/:id', incidentController.delete);

// Incident workflow routes
router.get('/:incidentId/workflow/state', workflowController.getWorkflowState);
router.get('/:incidentId/workflow/transitions', workflowController.getAvailableTransitions);
router.post('/:incidentId/workflow/transition', workflowController.transitionState);
router.post('/:incidentId/workflow/initialize', workflowController.initializeWorkflow);
router.get('/:incidentId/workflow/history', workflowController.getWorkflowHistory);
router.get('/workflow/states', workflowController.getAllPossibleStates);

module.exports = router; 