import axios from 'axios';
import { getApiEndpointUrl, getAuthHeaders } from '@/utils/api-config';

/**
 * Get an audit activity by ID
 * @param {string} id - The activity ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Object, message?: string}>}
 */
export const getAuditActivityById = async (id, signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl(`audit-activities/${id}`),
      {
        headers: getAuthHeaders(),
        signal
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Get all audit activities for a mission
 * @param {string} missionId - The mission ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Array, message?: string}>}
 */
export const getAuditActivitiesByMission = async (missionId, signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl(`audit-activities/mission/${missionId}`),
      {
        headers: getAuthHeaders(),
        signal
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Create a new audit activity
 * @param {Object} activityData - The activity data
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Object, message?: string}>}
 */
export const createAuditActivity = async (activityData, signal) => {
  try {
    const response = await axios.post(
      getApiEndpointUrl('audit-activities'),
      activityData,
      {
        headers: getAuthHeaders(),
        signal
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Update an audit activity
 * @param {string} id - The activity ID
 * @param {Object} activityData - The updated activity data
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Object, message?: string}>}
 */
export const updateAuditActivity = async (id, activityData, signal) => {
  try {
    const response = await axios.put(
      getApiEndpointUrl(`audit-activities/${id}`),
      activityData,
      {
        headers: getAuthHeaders(),
        signal
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Delete an audit activity
 * @param {string} id - The activity ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, message?: string}>}
 */
export const deleteAuditActivity = async (id, signal) => {
  try {
    const response = await axios.delete(
      getApiEndpointUrl(`audit-activities/${id}`),
      {
        headers: getAuthHeaders(),
        signal
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
}; 