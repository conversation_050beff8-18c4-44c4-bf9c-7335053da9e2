<!doctype html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <link
      rel="icon"
      type="image/png"
      href="/src/assets/whitelogovitalis.png"
      sizes="32x64"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vitalis • GRC</title>
    <!-- Force dark theme -->
    <script>
      (function() {
        // Always use dark theme
        document.documentElement.classList.remove('light');
        document.documentElement.classList.add('dark');
        // Optionally store preference if needed, though it's forced now
        // localStorage.setItem('vitalis-theme', 'dark');
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
