import { FileText, Tag } from "lucide-react";
import { useOutletContext } from "react-router-dom";
import { useTranslation } from "react-i18next";

function BusinessLinesOverview() {
  const { businessLine } = useOutletContext();
  const { t } = useTranslation();
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6">
        <div>
          <h3 className="text-lg font-semibold text-[#242A33] mb-4">{t('admin.business_lines.overview.details', 'Details')}</h3>
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Tag className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm font-medium text-[#555F6D] mb-1">{t('admin.business_lines.form.name', 'Name')}</p>
                <p className="text-base text-[#242A33]">{businessLine?.name}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <FileText className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm font-medium text-[#555F6D] mb-1">{t('admin.business_lines.form.description', 'Description')}</p>
                <p className="text-base text-[#242A33] whitespace-pre-wrap">
                  {businessLine?.description || t('admin.business_lines.overview.no_description', 'No description provided')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default BusinessLinesOverview;
