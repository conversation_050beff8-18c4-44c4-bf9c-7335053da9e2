import { useState } from "react";
import { Calendar as CalendarI<PERSON>, X } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Button } from "./button";
import { Calendar } from "./calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "./popover";
import { Separator } from "./separator";

export function DateRangePickerFilter({
  className,
  value = { start: null, end: null },
  onChange,
}) {
  const [isOpen, setIsOpen] = useState(false);
  const { start, end } = value;

  // Format the date range for display
  const formatDateRange = () => {
    try {
      if (start && end) {
        return `${format(new Date(start), "MMM d, yyyy")} - ${format(new Date(end), "MMM d, yyyy")}`;
      }
      if (start) {
        return `From ${format(new Date(start), "MMM d, yyyy")}`;
      }
      if (end) {
        return `Until ${format(new Date(end), "MMM d, yyyy")}`;
      }
      return "Select date range";
    } catch (error) {
      console.error("Error formatting date range:", error);
      return "Select date range";
    }
  };

  const handleStartDateChange = (date) => {
    onChange({ ...value, start: date });
  };

  const handleEndDateChange = (date) => {
    onChange({ ...value, end: date });
  };

  const handleClear = () => {
    onChange({ start: null, end: null });
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            size="sm"
            className={cn(
              "justify-between text-left font-normal h-9",
              !start && !end && "text-muted-foreground"
            )}
          >
            <div className="flex items-center gap-2 truncate">
              <CalendarIcon className="h-3.5 w-3.5 text-muted-foreground" />
              <span className="truncate">{formatDateRange()}</span>
            </div>
            {(start || end) && (
              <X
                className="h-3.5 w-3.5 text-muted-foreground hover:text-foreground"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClear();
                }}
              />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex flex-col sm:flex-row gap-2 p-3">
            <div className="space-y-2">
              <div className="text-sm font-medium">Start Date</div>
              <Calendar
                mode="single"
                selected={start ? new Date(start) : undefined}
                onSelect={handleStartDateChange}
                initialFocus
              />
            </div>
            <Separator className="sm:h-auto" orientation="vertical" />
            <div className="space-y-2">
              <div className="text-sm font-medium">End Date</div>
              <Calendar
                mode="single"
                selected={end ? new Date(end) : undefined}
                onSelect={handleEndDateChange}
                initialFocus
                disabled={(date) => start && date < new Date(start)}
              />
            </div>
          </div>
          <div className="flex items-center justify-end gap-2 p-3 pt-0">
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                handleClear();
                setIsOpen(false);
              }}
            >
              Clear
            </Button>
            <Button
              size="sm"
              onClick={() => setIsOpen(false)}
            >
              Apply
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
