require('dotenv').config();
const { Sequelize } = require('sequelize');
const db = require('../models');

async function checkNotificationsTable() {
  try {
    console.log('[Check Script] Checking database connection...');
    await db.sequelize.authenticate();
    console.log('[Check Script] Database connection has been established successfully.');
    
    // Check if Notification model exists
    if (!db.Notification) {
      console.error('[Check Script] Notification model not found! Available models:', Object.keys(db));
      return;
    }
    
    console.log('[Check Script] Checking notifications table...');
    const tableExists = await db.sequelize.query(
      "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'Notifications')",
      { type: Sequelize.QueryTypes.SELECT }
    );
    
    if (!tableExists || !tableExists[0].exists) {
      console.error('[Check Script] Notifications table does not exist!');
      return;
    }
    
    console.log('[<PERSON> Script] Notifications table exists, checking records...');
    const count = await db.Notification.count();
    console.log(`[Check Script] Found ${count} notifications in database`);
    
    // Get last 10 notifications
    if (count > 0) {
      const notifications = await db.Notification.findAll({
        limit: 10,
        order: [['created_at', 'DESC']]
      });
      
      console.log('[Check Script] Last 10 notifications:');
      notifications.forEach(notification => {
        console.log(`- ID: ${notification.id}, User ID: ${notification.user_id}, Type: ${notification.type}, Message: ${notification.message.substring(0, 50)}...`);
      });
    }
    
    console.log('[Check Script] Check complete!');
  } catch (error) {
    console.error('[Check Script] Error checking notifications:', error);
  } finally {
    process.exit(0);
  }
}

checkNotificationsTable(); 