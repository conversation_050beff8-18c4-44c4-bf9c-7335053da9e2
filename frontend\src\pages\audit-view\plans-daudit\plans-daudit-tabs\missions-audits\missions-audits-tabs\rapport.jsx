import React, { useState, useEffect, useCallback, useRef } from 'react';

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { FileText, FileDown, Loader2, AlertTriangle, RefreshCw, Mail, Eye, Volume2, Pause, Play, Square, MessageCircle } from "lucide-react";
import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';
import EmailReportModal from '@/components/reports/EmailReportModal';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from '@/components/ui/dialog';
import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, <PERSON> } from 'chart.js';
ChartJS.register(<PERSON><PERSON><PERSON>, Toolt<PERSON>, Legend);
import { jsPDF } from "jspdf";
import * as XLSX from "https://cdn.jsdelivr.net/npm/xlsx@0.18.5/xlsx.mjs";

function RapportTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;


  // State for report functionality
  const [reportData, setReportData] = useState(null);
  const [loadingReport, setLoadingReport] = useState(false);
  const [downloadingPdf, setDownloadingPdf] = useState(false);
  const [downloadingDocx, setDownloadingDocx] = useState(false);
  const [reportError, setReportError] = useState(null);
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [emailAttachment, setEmailAttachment] = useState(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [pdfLoading, setPdfLoading] = useState(false);
  const [pdfError, setPdfError] = useState(null);
  // TTS state
  const [ttsLoading, setTtsLoading] = useState(false);
  const [ttsError, setTtsError] = useState(null);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [spokenText, setSpokenText] = useState('');
  const speechSynthesisRef = React.useRef(window.speechSynthesis);
  const utteranceRef = React.useRef(null);

  // Add audio ref and state for realistic TTS
  const audioRef = useRef(null);
  const [audioUrl, setAudioUrl] = useState(null);
  const [audioPlaying, setAudioPlaying] = useState(false);
  const [audioPaused, setAudioPaused] = useState(false);

  // Gemini conversation state
  const [conversation, setConversation] = useState([]);
  const [convLoading, setConvLoading] = useState(false);
  const [convError, setConvError] = useState(null);
  const [isConvPlaying, setIsConvPlaying] = useState(false);
  const [isConvPaused, setIsConvPaused] = useState(false);
  const [convIndex, setConvIndex] = useState(0);
  const stopReadingRef = React.useRef(false);

  // --- Mission Charts State ---
  const [activitiesChart, setActivitiesChart] = useState({ data: null, loading: false, error: null });
  const [recommendationsChart, setRecommendationsChart] = useState({ data: null, loading: false, error: null });
  const [constatsChart, setConstatsChart] = useState({ data: null, loading: false, error: null });

  // Helper to get a female or male voice (prefer Julie for female, Paul for male)
  const getVoice = (gender) => {
    const voices = window.speechSynthesis.getVoices();
    if (gender === 'female') {
      // Prefer Microsoft Julie, then Hortense, then Google français, then any fr-FR
      return voices.find(v => v.name === 'Microsoft Julie') ||
             voices.find(v => v.name === 'Microsoft Hortense') ||
             voices.find(v => v.name === 'Google français') ||
             voices.find(v => v.lang === 'fr-FR');
    } else {
      // Prefer Microsoft Paul, then any fr-FR male
      return voices.find(v => v.name === 'Microsoft Paul') ||
             voices.find(v => v.lang === 'fr-FR' && v.name.toLowerCase().includes('paul')) ||
             voices.find(v => v.lang === 'fr-FR');
    }
  };

  // Helper to get the full conversation text
  const getConversationText = (conv) => {
    return conv.map(msg => `${msg.speaker}: ${msg.text}`).join('\n');
  };

  // Helper to read the whole conversation aloud with gendered voices
  const readConversationAloud = async (conv, startIdx = 0) => {
    if (!window.speechSynthesis) return;
    stopReadingRef.current = false;
    setIsConvPlaying(true);
    setIsConvPaused(false);
    for (let i = startIdx; i < conv.length; i++) {
      if (stopReadingRef.current) break;
      setConvIndex(i);
      const msg = conv[i];
      const utterance = new window.SpeechSynthesisUtterance(msg.text);
      utterance.lang = 'fr-FR';
      if (msg.speaker && msg.speaker.toLowerCase() === 'alice') {
        utterance.voice = getVoice('female');
      } else if (msg.speaker && msg.speaker.toLowerCase() === 'bob') {
        utterance.voice = getVoice('male');
      }
      await new Promise((resolve) => {
        utterance.onend = resolve;
        utterance.onerror = resolve;
        window.speechSynthesis.speak(utterance);
      });
      while (isConvPaused && !stopReadingRef.current) {
        await new Promise(r => setTimeout(r, 100));
      }
    }
    setIsConvPlaying(false);
    setIsConvPaused(false);
    setConvIndex(0);
  };

  const API_BASE_URL = getApiBaseUrl();

  // Fetch report data for preview
  const fetchReportData = useCallback(async () => {
    if (!missionAudit?.id) return;

    try {
      setLoadingReport(true);
      setReportError(null);

      const response = await axios.get(
        `${API_BASE_URL}/audit-mission-rapports/mission-report/${missionAudit.id}`,
        { withCredentials: true }
      );

      setReportData(response.data);
    } catch (error) {
      console.error('Error fetching report data:', error);
      setReportError('Erreur lors du chargement des données du rapport');
      toast.error('Erreur lors du chargement des données du rapport');
    } finally {
      setLoadingReport(false);
    }
  }, [missionAudit?.id, API_BASE_URL]);

  // Fetch report data when component mounts or mission changes
  useEffect(() => {
    if (missionAudit?.id) {
      fetchReportData();
    }
  }, [missionAudit?.id, fetchReportData]);

  // --- Fetch Mission Charts ---
  useEffect(() => {
    if (!missionAudit?.id) return;
    // Activities
    setActivitiesChart({ data: null, loading: true, error: null });
    axios.get(`${API_BASE_URL}/rapport/mission-charts/${missionAudit.id}/activities`, { withCredentials: true })
      .then(res => {
        const data = res.data.data;
        const labels = data.map(d => d.status || 'N/A');
        const counts = data.map(d => d.count);
        const colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'];
        setActivitiesChart({
          data: {
            labels,
            datasets: [{
              label: 'Activités',
              data: counts,
              backgroundColor: labels.map((_, i) => colors[i % colors.length]),
              borderColor: labels.map((_, i) => colors[i % colors.length]),
              borderWidth: 1,
            }]
          },
          loading: false,
          error: null
        });
      })
      .catch(err => setActivitiesChart({ data: null, loading: false, error: 'Erreur chargement activités' }));
    // Recommendations
    setRecommendationsChart({ data: null, loading: true, error: null });
    axios.get(`${API_BASE_URL}/rapport/mission-charts/${missionAudit.id}/recommendations`, { withCredentials: true })
      .then(res => {
        const data = res.data.data;
        // Group by recommendation_status
        const statusMap = {};
        data.forEach(d => {
          const key = d.recommendation_status || 'N/A';
          statusMap[key] = (statusMap[key] || 0) + d.count;
        });
        const labels = Object.keys(statusMap);
        const counts = Object.values(statusMap);
        const colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'];
        setRecommendationsChart({
          data: {
            labels,
            datasets: [{
              label: 'Recommandations',
              data: counts,
              backgroundColor: labels.map((_, i) => colors[i % colors.length]),
              borderColor: labels.map((_, i) => colors[i % colors.length]),
              borderWidth: 1,
            }]
          },
          loading: false,
          error: null
        });
      })
      .catch(err => setRecommendationsChart({ data: null, loading: false, error: 'Erreur chargement recommandations' }));
    // Constats
    setConstatsChart({ data: null, loading: true, error: null });
    axios.get(`${API_BASE_URL}/rapport/mission-charts/${missionAudit.id}/constats`, { withCredentials: true })
      .then(res => {
        const data = res.data.data;
        const labels = data.map(d => d.type || 'N/A');
        const counts = data.map(d => d.count);
        const colors = ['#22c55e', '#ef4444']; // Green for Point Fort, Red for Point Faible
        setConstatsChart({
          data: {
            labels,
            datasets: [{
              label: 'Constats',
              data: counts,
              backgroundColor: labels.map((l, i) => l === 'Point Fort' ? colors[0] : colors[1]),
              borderColor: labels.map((l, i) => l === 'Point Fort' ? colors[0] : colors[1]),
              borderWidth: 1,
            }]
          },
          loading: false,
          error: null
        });
      })
      .catch(err => setConstatsChart({ data: null, loading: false, error: 'Erreur chargement constats' }));
  }, [missionAudit?.id, API_BASE_URL]);

  // If no mission data is available yet
  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement du rapport d'audit...</p>
      </div>
    );
  }

  // Download PDF report
  const downloadPdfReport = async () => {
    try {
      setDownloadingPdf(true);

      const response = await axios.get(
        `${API_BASE_URL}/audit-mission-rapports/mission-report/${missionAudit.id}?format=pdf`,
        {
          withCredentials: true,
          responseType: 'blob'
        }
      );

      // Create blob link to download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `rapport_audit_${missionAudit.id}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast.success('Rapport PDF téléchargé avec succès');
    } catch (error) {
      console.error('Error downloading PDF:', error);

      // Provide more specific error messages
      if (error.code === 'ERR_NETWORK') {
        toast.error('Erreur réseau lors du téléchargement du PDF. Vérifiez votre connexion.');
      } else if (error.response?.status === 404) {
        toast.error('Rapport non trouvé. Veuillez actualiser et réessayer.');
      } else if (error.response?.status === 401) {
        toast.error('Session expirée. Veuillez vous reconnecter.');
      } else if (error.response?.status >= 500) {
        toast.error('Erreur serveur lors de la génération du PDF. Réessayez plus tard.');
      } else {
        toast.error('Erreur lors du téléchargement du PDF');
      }
    } finally {
      setDownloadingPdf(false);
    }
  };

  // Download DOCX report
  const downloadDocxReport = async () => {
    try {
      setDownloadingDocx(true);

      const response = await axios.get(
        `${API_BASE_URL}/audit-mission-rapports/mission-report/${missionAudit.id}?format=docx`,
        {
          withCredentials: true,
          responseType: 'blob'
        }
      );

      // Create blob link to download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `rapport_audit_${missionAudit.id}.docx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast.success('Rapport DOCX téléchargé avec succès');
    } catch (error) {
      console.error('Error downloading DOCX:', error);

      // Provide more specific error messages
      if (error.code === 'ERR_NETWORK') {
        toast.error('Erreur réseau lors du téléchargement du DOCX. Vérifiez votre connexion.');
      } else if (error.response?.status === 404) {
        toast.error('Rapport non trouvé. Veuillez actualiser et réessayer.');
      } else if (error.response?.status === 401) {
        toast.error('Session expirée. Veuillez vous reconnecter.');
      } else if (error.response?.status >= 500) {
        toast.error('Erreur serveur lors de la génération du DOCX. Réessayez plus tard.');
      } else {
        toast.error('Erreur lors du téléchargement du DOCX');
      }
    } finally {
      setDownloadingDocx(false);
    }
  };

  const openEmailModalWithPDF = async () => {
    try {
      setDownloadingPdf(true);
      const response = await axios.get(
        `${API_BASE_URL}/audit-mission-rapports/mission-report/${missionAudit.id}?format=pdf`,
        {
          withCredentials: true,
          responseType: 'blob'
        }
      );
      const pdfBlob = response.data;
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = reader.result.split(',')[1];
        setEmailAttachment({
          base64,
          filename: `rapport_audit_${missionAudit.id}.pdf`,
          contentType: 'application/pdf',
        });
        setIsEmailModalOpen(true);
      };
      reader.readAsDataURL(pdfBlob);
    } catch (error) {
      toast.error('Erreur lors de la génération du PDF pour l\'email.');
    } finally {
      setDownloadingPdf(false);
    }
  };

  const handleOpenPreview = () => {
    setPdfError(null);
    setPdfLoading(true);
    setIsPreviewOpen(true);
  };

  const handlePdfLoad = () => {
    setPdfLoading(false);
  };

  const handlePdfError = () => {
    setPdfLoading(false);
    setPdfError('Erreur lors du chargement du PDF.');
  };

  const handleReadAloud = async () => {
    setTtsError(null);
    setTtsLoading(true);
    setSpokenText('');
    setAudioUrl(null);
    setAudioPlaying(false);
    setAudioPaused(false);
    try {
      const response = await axios.get(`${API_BASE_URL}/audit-mission-rapports/mission-report/${missionAudit.id}/pdf-text`, { withCredentials: true });
      const text = response.data.text;
      setSpokenText(text);
      if (!text) {
        setTtsError('Impossible de lire le texte extrait.');
        setTtsLoading(false);
        return;
      }
      // Call backend for realistic TTS
      const ttsRes = await axios.post(`${API_BASE_URL}/tts/realistic`, {
        text,
        voiceId: 'fr-FR-Wavenet-A',
        languageCode: 'fr-FR',
        gender: 'Female',
        voiceName: 'Alice'
      }, { withCredentials: true });
      if (ttsRes.data && ttsRes.data.audioUrl) {
        setAudioUrl(ttsRes.data.audioUrl);
        setTimeout(() => {
          if (audioRef.current) {
            audioRef.current.play();
          }
        }, 100);
      } else {
        setTtsError('Erreur lors de la génération de l\'audio.');
      }
    } catch (err) {
      setTtsError('Erreur lors de l\'extraction du texte ou de la génération audio.');
    } finally {
      setTtsLoading(false);
    }
  };

  const handlePauseTTS = () => {
    if (audioRef.current && !audioRef.current.paused) {
      audioRef.current.pause();
      setAudioPaused(true);
      setAudioPlaying(false);
    }
  };
  const handleResumeTTS = () => {
    if (audioRef.current && audioRef.current.paused) {
      audioRef.current.play();
      setAudioPaused(false);
      setAudioPlaying(true);
    }
  };
  const handleStopTTS = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setAudioPlaying(false);
      setAudioPaused(false);
    }
  };

  // Audio event handlers
  useEffect(() => {
    if (!audioRef.current) return;
    const handlePlay = () => setAudioPlaying(true);
    const handlePause = () => setAudioPlaying(false);
    const handleEnded = () => {
      setAudioPlaying(false);
      setAudioPaused(false);
    };
    const audio = audioRef.current;
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('ended', handleEnded);
    return () => {
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [audioUrl]);

  React.useEffect(() => {
    return () => {
      window.speechSynthesis.cancel();
    };
  }, []);

  const handleGenerateConversation = async () => {
    setConvError(null);
    setConvLoading(true);
    setConversation([]);
    stopReadingRef.current = false;
    setIsConvPlaying(false);
    setIsConvPaused(false);
    setConvIndex(0);
    try {
      const response = await axios.get(`${API_BASE_URL}/gemini/conversation/${missionAudit.id}`, { withCredentials: true });
      const conv = response.data.conversation || [];
      setConversation(conv);
      if (conv.length > 0) {
        readConversationAloud(conv);
      }
    } catch (err) {
      setConvError("Erreur lors de la génération de la conversation.");
    } finally {
      setConvLoading(false);
    }
  };

  const handlePlayPauseConv = () => {
    if (!isConvPlaying && conversation.length > 0) {
      if (isConvPaused) {
        setIsConvPaused(false);
        window.speechSynthesis.resume();
      } else {
        readConversationAloud(conversation, convIndex);
      }
    } else if (isConvPlaying && !isConvPaused) {
      setIsConvPaused(true);
      window.speechSynthesis.pause();
    }
  };
  const handleStopConvTTS = () => {
    stopReadingRef.current = true;
    window.speechSynthesis.cancel();
    setIsConvPlaying(false);
    setIsConvPaused(false);
    setConvIndex(0);
  };
  React.useEffect(() => {
    return () => {
      stopReadingRef.current = true;
      window.speechSynthesis.cancel();
    };
  }, []);

  const activitiesChartRef = useRef(null);

  // Helper to export chart with white background
  function getChartImageWithWhiteBg(chartRef) {
    if (!chartRef.current) return null;
    let canvas = chartRef.current.canvas || (chartRef.current.ctx && chartRef.current.ctx.canvas);
    if (!canvas) return null;
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = canvas.width;
    tempCanvas.height = canvas.height;
    const ctx = tempCanvas.getContext('2d');
    ctx.fillStyle = '#fff';
    ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
    ctx.drawImage(canvas, 0, 0);
    return tempCanvas.toDataURL('image/jpeg', 1.0);
  }

  // PDF/Excel/Email for Activities Chart
  const [isEmailModalOpenActivities, setIsEmailModalOpenActivities] = useState(false);
  const [activitiesAttachment, setActivitiesAttachment] = useState(null);
  const downloadActivitiesPDF = async () => {
    if (!activitiesChartRef.current) return;
    const chartImage = getChartImageWithWhiteBg(activitiesChartRef);
    if (!chartImage) return;
    const pdf = new jsPDF({ orientation: 'landscape', unit: 'in', format: 'letter' });
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 0.5;
    const maxWidth = pageWidth - 2 * margin;
    const maxHeight = pageHeight - 2 * margin - 2;
    const imgProps = pdf.getImageProperties(chartImage);
    let pdfWidth = maxWidth;
    let pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
    if (pdfHeight > maxHeight) {
      pdfHeight = maxHeight;
      pdfWidth = (imgProps.width * pdfHeight) / imgProps.height;
    }
    pdf.addImage(chartImage, 'JPEG', margin, margin, pdfWidth, pdfHeight);
    let yPosition = pdfHeight + margin + 0.5;
    pdf.setFontSize(16);
    pdf.setTextColor(45, 55, 72);
    pdf.text('Détails', margin, yPosition);
    yPosition += 0.5;
    pdf.setFontSize(12);
    pdf.setTextColor(113, 128, 150);
    pdf.text("Nombre total d'activités:", margin, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    const total = activitiesChart.data?.datasets[0]?.data?.reduce((a, b) => a + b, 0) || 0;
    pdf.text(`${total}`, margin + 3, yPosition);
    yPosition += 0.3;
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(113, 128, 150);
    pdf.text('Statut le plus fréquent:', margin, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    let mostFrequent = 'N/A';
    if (activitiesChart.data) {
      const maxIdx = activitiesChart.data.datasets[0].data.indexOf(Math.max(...activitiesChart.data.datasets[0].data));
      mostFrequent = activitiesChart.data.labels[maxIdx];
    }
    pdf.text(`${mostFrequent}`, margin + 3, yPosition);
    yPosition += 0.5;
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(55, 65, 81);
    pdf.setFontSize(11);
    if (activitiesChart.data) {
      activitiesChart.data.labels.forEach((label, idx) => {
        if (yPosition > pageHeight - margin) {
          pdf.addPage();
          yPosition = margin;
        }
        pdf.text(`${label}: ${activitiesChart.data.datasets[0].data[idx]} activité(s)`, margin + 0.2, yPosition);
        yPosition += 0.3;
      });
    }
    pdf.save('activites_par_statut.pdf');
  };
  const downloadActivitiesExcel = () => {
    if (!activitiesChart.data) return;
    const excelData = activitiesChart.data.labels.map((label, idx) => ({ Statut: label, Activites: activitiesChart.data.datasets[0].data[idx] }));
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "ActivitesParStatut");
    XLSX.writeFile(workbook, "activites_par_statut.xlsx");
  };
  const handleEmailActivities = async () => {
    if (!activitiesChartRef.current) return;
    const chartImage = getChartImageWithWhiteBg(activitiesChartRef);
    if (!chartImage) return;
    // Generate PDF as attachment
    const pdf = new jsPDF({ orientation: 'landscape', unit: 'in', format: 'letter' });
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 0.5;
    const maxWidth = pageWidth - 2 * margin;
    const maxHeight = pageHeight - 2 * margin - 2;
    const imgProps = pdf.getImageProperties(chartImage);
    let pdfWidth = maxWidth;
    let pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
    if (pdfHeight > maxHeight) {
      pdfHeight = maxHeight;
      pdfWidth = (imgProps.width * pdfHeight) / imgProps.height;
    }
    pdf.addImage(chartImage, 'JPEG', margin, margin, pdfWidth, pdfHeight);
    let yPosition = pdfHeight + margin + 0.5;
    pdf.setFontSize(16);
    pdf.setTextColor(45, 55, 72);
    pdf.text('Détails', margin, yPosition);
    yPosition += 0.5;
    pdf.setFontSize(12);
    pdf.setTextColor(113, 128, 150);
    pdf.text("Nombre total d'activités:", margin, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    const total = activitiesChart.data?.datasets[0]?.data?.reduce((a, b) => a + b, 0) || 0;
    pdf.text(`${total}`, margin + 3, yPosition);
    yPosition += 0.3;
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(113, 128, 150);
    pdf.text('Statut le plus fréquent:', margin, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    let mostFrequent = 'N/A';
    if (activitiesChart.data) {
      const maxIdx = activitiesChart.data.datasets[0].data.indexOf(Math.max(...activitiesChart.data.datasets[0].data));
      mostFrequent = activitiesChart.data.labels[maxIdx];
    }
    pdf.text(`${mostFrequent}`, margin + 3, yPosition);
    yPosition += 0.5;
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(55, 65, 81);
    pdf.setFontSize(11);
    if (activitiesChart.data) {
      activitiesChart.data.labels.forEach((label, idx) => {
        if (yPosition > pageHeight - margin) {
          pdf.addPage();
          yPosition = margin;
        }
        pdf.text(`${label}: ${activitiesChart.data.datasets[0].data[idx]} activité(s)`, margin + 0.2, yPosition);
        yPosition += 0.3;
      });
    }
    // Convert PDF to base64
    const pdfBase64 = btoa(pdf.output('arraybuffer').reduce((data, byte) => data + String.fromCharCode(byte), ''));
    setActivitiesAttachment({ base64: pdfBase64, filename: 'activites_par_statut.pdf', contentType: 'application/pdf' });
    setIsEmailModalOpenActivities(true);
  };

  return (
    <div className="flex flex-col gap-8">
      {/* Activities Section */}
      <div className="bg-white rounded-lg shadow p-6 flex flex-col items-center justify-center w-full">
        <h3 className="text-xl font-semibold mb-4">Progression des Activités</h3>
        {activitiesChart.loading ? (
          <div className="text-center text-gray-400">Chargement...</div>
        ) : activitiesChart.error ? (
          <div className="text-center text-red-500">{activitiesChart.error}</div>
        ) : activitiesChart.data ? (
          <>
            <div className="w-full flex justify-center">
              <div style={{ maxWidth: 320, width: '100%' }}>
                <Doughnut ref={activitiesChartRef} data={activitiesChart.data} options={{ responsive: true, plugins: { legend: { position: 'bottom' } } }} height={250} width={250} />
              </div>
            </div>
            <div className="mt-4">
              <h4 className="text-lg font-semibold">Détails des Activités :</h4>
              <ul className="list-disc pl-5">
                {activitiesChart.data.labels.map((label, idx) => (
                  <li key={idx}>{label}: {activitiesChart.data.datasets[0].data[idx]} activité(s)</li>
                ))}
              </ul>
            </div>
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-2 w-full">
              <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <p className="text-sm text-gray-500 mb-1">Nombre total d'activités</p>
                <p className="text-xl font-bold text-[#1A2942]">{activitiesChart.data.datasets[0].data.reduce((a, b) => a + b, 0)}</p>
              </div>
              <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                <p className="text-sm text-gray-500 mb-1">Statut le plus fréquent</p>
                <p className="text-xl font-bold text-[#1A2942]">{(() => { const maxIdx = activitiesChart.data.datasets[0].data.indexOf(Math.max(...activitiesChart.data.datasets[0].data)); return activitiesChart.data.labels[maxIdx]; })()}</p>
              </div>
            </div>
            <div className="flex gap-4 mt-4">
              <Button onClick={downloadActivitiesPDF} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">Télécharger PDF</Button>
              <Button onClick={downloadActivitiesExcel} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">Télécharger Excel</Button>
              <Button onClick={handleEmailActivities} className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Envoyer par Email
              </Button>
            </div>
            <EmailReportModal
              isOpen={isEmailModalOpenActivities}
              onClose={() => setIsEmailModalOpenActivities(false)}
              defaultSubject="Rapport: Activités par Statut"
              defaultMessage="Veuillez trouver ci-joint le rapport PDF des activités par statut."
              defaultAttachment={activitiesAttachment}
              reportTitle="Activités par Statut"
            />
          </>
        ) : null}
      </div>
      {/* Recommendations Section */}
      <RecommendationsSection
        recommendationsChart={recommendationsChart}
      />
      {/* Constats Section */}
      <ConstatsSection
        constatsChart={constatsChart}
      />
    </div>
  );
}

function RecommendationsSection({ recommendationsChart }) {
  const recommendationsChartRef = useRef(null);
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [attachment, setAttachment] = useState(null);

  // Helper to export chart with white background
  function getChartImageWithWhiteBg(chartRef) {
    if (!chartRef.current) return null;
    let canvas = chartRef.current.canvas || (chartRef.current.ctx && chartRef.current.ctx.canvas);
    if (!canvas) return null;
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = canvas.width;
    tempCanvas.height = canvas.height;
    const ctx = tempCanvas.getContext('2d');
    ctx.fillStyle = '#fff';
    ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
    ctx.drawImage(canvas, 0, 0);
    return tempCanvas.toDataURL('image/jpeg', 1.0);
  }

  // PDF/Excel/Email logic (copy from Activities, adapt for recommendationsChart)
  const downloadRecommendationsPDF = async () => {
    if (!recommendationsChartRef.current) return;
    const chartImage = getChartImageWithWhiteBg(recommendationsChartRef);
    if (!chartImage) return;
    const pdf = new jsPDF({ orientation: 'landscape', unit: 'in', format: 'letter' });
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 0.5;
    const maxWidth = pageWidth - 2 * margin;
    const maxHeight = pageHeight - 2 * margin - 2;
    const imgProps = pdf.getImageProperties(chartImage);
    let pdfWidth = maxWidth;
    let pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
    if (pdfHeight > maxHeight) {
      pdfHeight = maxHeight;
      pdfWidth = (imgProps.width * pdfHeight) / imgProps.height;
    }
    pdf.addImage(chartImage, 'JPEG', margin, margin, pdfWidth, pdfHeight);
    let yPosition = pdfHeight + margin + 0.5;
    pdf.setFontSize(16);
    pdf.setTextColor(45, 55, 72);
    pdf.text('Détails', margin, yPosition);
    yPosition += 0.5;
    pdf.setFontSize(12);
    pdf.setTextColor(113, 128, 150);
    pdf.text("Nombre total de recommandations:", margin, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    const total = recommendationsChart.data?.datasets[0]?.data?.reduce((a, b) => a + b, 0) || 0;
    pdf.text(`${total}`, margin + 3, yPosition);
    yPosition += 0.3;
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(113, 128, 150);
    pdf.text('Statut le plus fréquent:', margin, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    let mostFrequent = 'N/A';
    if (recommendationsChart.data) {
      const maxIdx = recommendationsChart.data.datasets[0].data.indexOf(Math.max(...recommendationsChart.data.datasets[0].data));
      mostFrequent = recommendationsChart.data.labels[maxIdx];
    }
    pdf.text(`${mostFrequent}`, margin + 3, yPosition);
    yPosition += 0.5;
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(55, 65, 81);
    pdf.setFontSize(11);
    if (recommendationsChart.data) {
      recommendationsChart.data.labels.forEach((label, idx) => {
        if (yPosition > pageHeight - margin) {
          pdf.addPage();
          yPosition = margin;
        }
        pdf.text(`${label}: ${recommendationsChart.data.datasets[0].data[idx]} recommandation(s)`, margin + 0.2, yPosition);
        yPosition += 0.3;
      });
    }
    pdf.save('recommandations_par_statut.pdf');
  };
  const downloadRecommendationsExcel = () => {
    if (!recommendationsChart.data) return;
    const excelData = recommendationsChart.data.labels.map((label, idx) => ({ Statut: label, Recommandations: recommendationsChart.data.datasets[0].data[idx] }));
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "RecommandationsParStatut");
    XLSX.writeFile(workbook, "recommandations_par_statut.xlsx");
  };
  const handleEmailRecommendations = async () => {
    if (!recommendationsChartRef.current) return;
    const chartImage = getChartImageWithWhiteBg(recommendationsChartRef);
    if (!chartImage) return;
    // Generate PDF as attachment
    const pdf = new jsPDF({ orientation: 'landscape', unit: 'in', format: 'letter' });
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 0.5;
    const maxWidth = pageWidth - 2 * margin;
    const maxHeight = pageHeight - 2 * margin - 2;
    const imgProps = pdf.getImageProperties(chartImage);
    let pdfWidth = maxWidth;
    let pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
    if (pdfHeight > maxHeight) {
      pdfHeight = maxHeight;
      pdfWidth = (imgProps.width * pdfHeight) / imgProps.height;
    }
    pdf.addImage(chartImage, 'JPEG', margin, margin, pdfWidth, pdfHeight);
    let yPosition = pdfHeight + margin + 0.5;
    pdf.setFontSize(16);
    pdf.setTextColor(45, 55, 72);
    pdf.text('Détails', margin, yPosition);
    yPosition += 0.5;
    pdf.setFontSize(12);
    pdf.setTextColor(113, 128, 150);
    pdf.text("Nombre total de recommandations:", margin, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    const total = recommendationsChart.data?.datasets[0]?.data?.reduce((a, b) => a + b, 0) || 0;
    pdf.text(`${total}`, margin + 3, yPosition);
    yPosition += 0.3;
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(113, 128, 150);
    pdf.text('Statut le plus fréquent:', margin, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    let mostFrequent = 'N/A';
    if (recommendationsChart.data) {
      const maxIdx = recommendationsChart.data.datasets[0].data.indexOf(Math.max(...recommendationsChart.data.datasets[0].data));
      mostFrequent = recommendationsChart.data.labels[maxIdx];
    }
    pdf.text(`${mostFrequent}`, margin + 3, yPosition);
    yPosition += 0.5;
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(55, 65, 81);
    pdf.setFontSize(11);
    if (recommendationsChart.data) {
      recommendationsChart.data.labels.forEach((label, idx) => {
        if (yPosition > pageHeight - margin) {
          pdf.addPage();
          yPosition = margin;
        }
        pdf.text(`${label}: ${recommendationsChart.data.datasets[0].data[idx]} recommandation(s)`, margin + 0.2, yPosition);
        yPosition += 0.3;
      });
    }
    // Convert PDF to base64
    const pdfBase64 = btoa(pdf.output('arraybuffer').reduce((data, byte) => data + String.fromCharCode(byte), ''));
    setAttachment({ base64: pdfBase64, filename: 'recommandations_par_statut.pdf', contentType: 'application/pdf' });
    setIsEmailModalOpen(true);
  };

  return (
    <div className="bg-white rounded-lg shadow p-6 flex flex-col items-center justify-center w-full">
      <h3 className="text-xl font-semibold mb-4">Progression des Recommandations</h3>
      {recommendationsChart.loading ? (
        <div className="text-center text-gray-400">Chargement...</div>
      ) : recommendationsChart.error ? (
        <div className="text-center text-red-500">{recommendationsChart.error}</div>
      ) : recommendationsChart.data ? (
        <>
          <div className="w-full flex justify-center">
            <div style={{ maxWidth: 320, width: '100%' }}>
              <Doughnut ref={recommendationsChartRef} data={recommendationsChart.data} options={{ responsive: true, plugins: { legend: { position: 'bottom' } } }} height={250} width={250} />
            </div>
          </div>
          <div className="mt-4">
            <h4 className="text-lg font-semibold">Détails des Recommandations :</h4>
            <ul className="list-disc pl-5">
              {recommendationsChart.data.labels.map((label, idx) => (
                <li key={idx}>{label}: {recommendationsChart.data.datasets[0].data[idx]} recommandation(s)</li>
              ))}
            </ul>
          </div>
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-2 w-full">
            <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
              <p className="text-sm text-gray-500 mb-1">Nombre total de recommandations</p>
              <p className="text-xl font-bold text-[#1A2942]">{recommendationsChart.data.datasets[0].data.reduce((a, b) => a + b, 0)}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
              <p className="text-sm text-gray-500 mb-1">Statut le plus fréquent</p>
              <p className="text-xl font-bold text-[#1A2942]">{(() => { const maxIdx = recommendationsChart.data.datasets[0].data.indexOf(Math.max(...recommendationsChart.data.datasets[0].data)); return recommendationsChart.data.labels[maxIdx]; })()}</p>
            </div>
          </div>
          <div className="flex gap-4 mt-4">
            <Button onClick={downloadRecommendationsPDF} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">Télécharger PDF</Button>
            <Button onClick={downloadRecommendationsExcel} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">Télécharger Excel</Button>
            <Button onClick={handleEmailRecommendations} className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Envoyer par Email
            </Button>
          </div>
          <EmailReportModal
            isOpen={isEmailModalOpen}
            onClose={() => setIsEmailModalOpen(false)}
            defaultSubject="Rapport: Recommandations par Statut"
            defaultMessage="Veuillez trouver ci-joint le rapport PDF des recommandations par statut."
            defaultAttachment={attachment}
            reportTitle="Recommandations par Statut"
          />
        </>
      ) : null}
    </div>
  );
}

function ConstatsSection({ constatsChart }) {
  const constatsChartRef = useRef(null);
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [attachment, setAttachment] = useState(null);

  // Helper to export chart with white background
  function getChartImageWithWhiteBg(chartRef) {
    if (!chartRef.current) return null;
    let canvas = chartRef.current.canvas || (chartRef.current.ctx && chartRef.current.ctx.canvas);
    if (!canvas) return null;
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = canvas.width;
    tempCanvas.height = canvas.height;
    const ctx = tempCanvas.getContext('2d');
    ctx.fillStyle = '#fff';
    ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
    ctx.drawImage(canvas, 0, 0);
    return tempCanvas.toDataURL('image/jpeg', 1.0);
  }

  // PDF/Excel/Email logic (copy from Activities, adapt for constatsChart)
  const downloadConstatsPDF = async () => {
    if (!constatsChartRef.current) return;
    const chartImage = getChartImageWithWhiteBg(constatsChartRef);
    if (!chartImage) return;
    const pdf = new jsPDF({ orientation: 'landscape', unit: 'in', format: 'letter' });
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 0.5;
    const maxWidth = pageWidth - 2 * margin;
    const maxHeight = pageHeight - 2 * margin - 2;
    const imgProps = pdf.getImageProperties(chartImage);
    let pdfWidth = maxWidth;
    let pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
    if (pdfHeight > maxHeight) {
      pdfHeight = maxHeight;
      pdfWidth = (imgProps.width * pdfHeight) / imgProps.height;
    }
    pdf.addImage(chartImage, 'JPEG', margin, margin, pdfWidth, pdfHeight);
    let yPosition = pdfHeight + margin + 0.5;
    pdf.setFontSize(16);
    pdf.setTextColor(45, 55, 72);
    pdf.text('Détails', margin, yPosition);
    yPosition += 0.5;
    pdf.setFontSize(12);
    pdf.setTextColor(113, 128, 150);
    pdf.text("Nombre total de constats:", margin, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    const total = constatsChart.data?.datasets[0]?.data?.reduce((a, b) => a + b, 0) || 0;
    pdf.text(`${total}`, margin + 3, yPosition);
    yPosition += 0.3;
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(113, 128, 150);
    pdf.text('Type le plus fréquent:', margin, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    let mostFrequent = 'N/A';
    if (constatsChart.data) {
      const maxIdx = constatsChart.data.datasets[0].data.indexOf(Math.max(...constatsChart.data.datasets[0].data));
      mostFrequent = constatsChart.data.labels[maxIdx];
    }
    pdf.text(`${mostFrequent}`, margin + 3, yPosition);
    yPosition += 0.5;
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(55, 65, 81);
    pdf.setFontSize(11);
    if (constatsChart.data) {
      constatsChart.data.labels.forEach((label, idx) => {
        if (yPosition > pageHeight - margin) {
          pdf.addPage();
          yPosition = margin;
        }
        pdf.text(`${label}: ${constatsChart.data.datasets[0].data[idx]} constat(s)`, margin + 0.2, yPosition);
        yPosition += 0.3;
      });
    }
    pdf.save('constats_par_type.pdf');
  };
  const downloadConstatsExcel = () => {
    if (!constatsChart.data) return;
    const excelData = constatsChart.data.labels.map((label, idx) => ({ Type: label, Constats: constatsChart.data.datasets[0].data[idx] }));
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "ConstatsParType");
    XLSX.writeFile(workbook, "constats_par_type.xlsx");
  };
  const handleEmailConstats = async () => {
    if (!constatsChartRef.current) return;
    const chartImage = getChartImageWithWhiteBg(constatsChartRef);
    if (!chartImage) return;
    // Generate PDF as attachment
    const pdf = new jsPDF({ orientation: 'landscape', unit: 'in', format: 'letter' });
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 0.5;
    const maxWidth = pageWidth - 2 * margin;
    const maxHeight = pageHeight - 2 * margin - 2;
    const imgProps = pdf.getImageProperties(chartImage);
    let pdfWidth = maxWidth;
    let pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
    if (pdfHeight > maxHeight) {
      pdfHeight = maxHeight;
      pdfWidth = (imgProps.width * pdfHeight) / imgProps.height;
    }
    pdf.addImage(chartImage, 'JPEG', margin, margin, pdfWidth, pdfHeight);
    let yPosition = pdfHeight + margin + 0.5;
    pdf.setFontSize(16);
    pdf.setTextColor(45, 55, 72);
    pdf.text('Détails', margin, yPosition);
    yPosition += 0.5;
    pdf.setFontSize(12);
    pdf.setTextColor(113, 128, 150);
    pdf.text("Nombre total de constats:", margin, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    const total = constatsChart.data?.datasets[0]?.data?.reduce((a, b) => a + b, 0) || 0;
    pdf.text(`${total}`, margin + 3, yPosition);
    yPosition += 0.3;
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(113, 128, 150);
    pdf.text('Type le plus fréquent:', margin, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    let mostFrequent = 'N/A';
    if (constatsChart.data) {
      const maxIdx = constatsChart.data.datasets[0].data.indexOf(Math.max(...constatsChart.data.datasets[0].data));
      mostFrequent = constatsChart.data.labels[maxIdx];
    }
    pdf.text(`${mostFrequent}`, margin + 3, yPosition);
    yPosition += 0.5;
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(55, 65, 81);
    pdf.setFontSize(11);
    if (constatsChart.data) {
      constatsChart.data.labels.forEach((label, idx) => {
        if (yPosition > pageHeight - margin) {
          pdf.addPage();
          yPosition = margin;
        }
        pdf.text(`${label}: ${constatsChart.data.datasets[0].data[idx]} constat(s)`, margin + 0.2, yPosition);
        yPosition += 0.3;
      });
    }
    // Convert PDF to base64
    const pdfBase64 = btoa(pdf.output('arraybuffer').reduce((data, byte) => data + String.fromCharCode(byte), ''));
    setAttachment({ base64: pdfBase64, filename: 'constats_par_type.pdf', contentType: 'application/pdf' });
    setIsEmailModalOpen(true);
  };

  return (
    <div className="bg-white rounded-lg shadow p-6 flex flex-col items-center justify-center w-full">
      <h3 className="text-xl font-semibold mb-4">Répartition des Constats</h3>
      {constatsChart.loading ? (
        <div className="text-center text-gray-400">Chargement...</div>
      ) : constatsChart.error ? (
        <div className="text-center text-red-500">{constatsChart.error}</div>
      ) : constatsChart.data ? (
        <>
          <div className="w-full flex justify-center">
            <div style={{ maxWidth: 320, width: '100%' }}>
              <Doughnut ref={constatsChartRef} data={constatsChart.data} options={{ responsive: true, plugins: { legend: { position: 'bottom' } } }} height={250} width={250} />
            </div>
          </div>
          <div className="mt-4">
            <h4 className="text-lg font-semibold">Détails des Constats :</h4>
            <ul className="list-disc pl-5">
              {constatsChart.data.labels.map((label, idx) => (
                <li key={idx}>{label}: {constatsChart.data.datasets[0].data[idx]} constat(s)</li>
              ))}
            </ul>
          </div>
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-2 w-full">
            <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
              <p className="text-sm text-gray-500 mb-1">Nombre total de constats</p>
              <p className="text-xl font-bold text-[#1A2942]">{constatsChart.data.datasets[0].data.reduce((a, b) => a + b, 0)}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
              <p className="text-sm text-gray-500 mb-1">Type le plus fréquent</p>
              <p className="text-xl font-bold text-[#1A2942]">{(() => { const maxIdx = constatsChart.data.datasets[0].data.indexOf(Math.max(...constatsChart.data.datasets[0].data)); return constatsChart.data.labels[maxIdx]; })()}</p>
            </div>
          </div>
          <div className="flex gap-4 mt-4">
            <Button onClick={downloadConstatsPDF} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">Télécharger PDF</Button>
            <Button onClick={downloadConstatsExcel} className="bg-[#22c55e] hover:bg-[#16a34a] text-white">Télécharger Excel</Button>
            <Button onClick={handleEmailConstats} className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Envoyer par Email
            </Button>
          </div>
          <EmailReportModal
            isOpen={isEmailModalOpen}
            onClose={() => setIsEmailModalOpen(false)}
            defaultSubject="Rapport: Constats par Type"
            defaultMessage="Veuillez trouver ci-joint le rapport PDF des constats par type."
            defaultAttachment={attachment}
            reportTitle="Constats par Type"
          />
        </>
      ) : null}
    </div>
  );
}

export default RapportTab; 