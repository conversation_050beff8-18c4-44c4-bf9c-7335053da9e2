import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Loader2, Trash2, ArrowUpDown, AlertTriangle } from 'lucide-react';
import { useSelector } from 'react-redux';
import { hasPermission } from '@/store/auth-slice';
import PageHeader from '@/components/ui/page-header';
import axios from 'axios';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import TablePagination from "../../../components/ui/table-pagination";
import { getApiBaseUrl } from "@/utils/api-config";
function RiskTypes() {
  const navigate = useNavigate();
  const canCreate = useSelector(state => hasPermission(state, 'create'));
  const canDelete = useSelector(state => hasPermission(state, 'delete'));
  const [riskTypes, setRiskTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRiskTypes, setSelectedRiskTypes] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const API_BASE_URL = getApiBaseUrl();
  const [newRiskType, setNewRiskType] = useState({
    riskTypeID: '',
    name: '',
    code: '',
    comment: '',
    parentRiskTypeID: null
  });

  const fetchRiskTypes = async (signal) => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/riskTypes`, {
        signal,
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
      });
      if (response.data.success) {
        setRiskTypes(response.data.data);
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        toast.error("Failed to fetch risk types");
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const controller = new AbortController();
    fetchRiskTypes(controller.signal);
    return () => controller.abort();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Create a payload with the correct field name (parentRiskType instead of parentRiskTypeID)
    const riskTypeToCreate = {
      riskTypeID: newRiskType.riskTypeID || undefined,
      name: newRiskType.name,
      code: newRiskType.code,
      comment: newRiskType.comment,
      parentRiskType: newRiskType.parentRiskTypeID || null
    };

    console.log('Creating risk type with payload:', riskTypeToCreate);

    try {
      setSubmitting(true);
      const response = await axios.post(
        `${API_BASE_URL}/riskTypes`,
        riskTypeToCreate,
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
          timeout: 60000,
        }
      );

      if (response.data.success) {
        toast.success("Risk Type created successfully");
        setNewRiskType({
          riskTypeID: '',
          name: '',
          code: '',
          comment: '',
          parentRiskTypeID: null
        });
        setIsOpen(false);
        const controller = new AbortController();
        await fetchRiskTypes(controller.signal);
      }
    } catch (error) {
      console.error('Error creating risk type:', error);
      toast.error(error.response?.data?.message || "Failed to create risk type");
    } finally {
      setSubmitting(false);
    }
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      const allIds = currentRiskTypes.map(type => type.riskTypeID);
      setSelectedRiskTypes(allIds);
    } else {
      setSelectedRiskTypes([]);
    }
  };

  const handleSelectRiskType = (id) => {
    setSelectedRiskTypes(prev => {
      if (prev.includes(id)) {
        return prev.filter(item => item !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  const handleRowClick = (id) => {
    navigate(`/admin/risk-types/${id}`);
  };

  const handleDeleteSelected = async () => {
    if (selectedRiskTypes.length === 0) {
      toast.error("No risk types selected");
      return;
    }

    // Check for dependent risk types
    const dependentRiskTypes = riskTypes.filter(
      (type) => selectedRiskTypes.includes(type.parentRiskType)
    );

    if (dependentRiskTypes.length > 0) {
      const message = selectedRiskTypes
        .map((selectedId) => {
          const dependents = dependentRiskTypes.filter(
            (dep) => dep.parentRiskType === selectedId
          );
          if (dependents.length > 0) {
            const typeName =
              riskTypes.find((t) => t.riskTypeID === selectedId)?.name || selectedId;
            const dependentNames = dependents.map((dep) => dep.name).join(', ');
            return `Cannot delete "${typeName}" (ID: ${selectedId}) because it is a parent of: ${dependentNames}.`;
          }
          return null;
        })
        .filter(Boolean)
        .join(' ');

      toast.error(message, {
        position: "top-center",
        duration: 6000,
        style: {
          background: '#f8d7da',
          color: '#721c24',
          border: '1px solid #f5c6cb',
          padding: '16px',
          maxWidth: '80%',
          textAlign: 'center',
        },
      });
      return;
    }

    // Proceed with deletion if no dependencies
    if (window.confirm(`Are you sure you want to delete ${selectedRiskTypes.length} selected risk type(s)?`)) {
      try {
        setLoading(true);
        let failedDeletions = [];

        const attemptDelete = async (id, retries = 2) => {
          for (let attempt = 0; attempt <= retries; attempt++) {
            try {
              await axios.delete(`${API_BASE_URL}/riskTypes/${id}`, {
                withCredentials: true,
                timeout: 60000,
                headers: { "Content-Type": "application/json" },
              });
              return { success: true };
            } catch (error) {
              if (attempt === retries) {
                let userMessage = error.response?.data?.message || 'Unknown error';
                const riskTypeName = riskTypes.find(t => t.riskTypeID === id)?.name || id;

                if (userMessage.includes("used by one or more risks")) {
                  userMessage = `Cannot delete "${riskTypeName}" (ID: ${id}) because it is used by one or more risks.`;
                }
                return { success: false, error: userMessage };
              }
              await new Promise((resolve) => setTimeout(resolve, 1000 * (attempt + 1)));
            }
          }
        };

        const batchSize = 3;
        for (let i = 0; i < selectedRiskTypes.length; i += batchSize) {
          const batch = selectedRiskTypes.slice(i, i + batchSize);
          const results = await Promise.all(
            batch.map(async (id) => {
              const result = await attemptDelete(id);
              if (!result.success) {
                failedDeletions.push({ id, error: result.error });
              }
              await new Promise((resolve) => setTimeout(resolve, 500));
              return result;
            })
          );

          if (i + batchSize < selectedRiskTypes.length) {
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
        }

        await fetchRiskTypes();
        setSelectedRiskTypes([]);

        if (failedDeletions.length > 0) {
          const errorMessage = failedDeletions.map((f) => f.error).join('\n');
          toast.error(errorMessage, {
            position: "top-center",
            duration: 6000,
            style: {
              background: '#f8d7da',
              color: '#721c24',
              border: '1px solid #f5c6cb',
              padding: '16px',
              maxWidth: '80%',
              textAlign: 'center',
            },
          });
        } else {
          toast.success("All selected risk types deleted successfully");
        }
      } catch (error) {
        toast.error("An error occurred during the deletion process");
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSort = (key) => {
    setSortConfig((prevConfig) => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const filteredRiskTypes = riskTypes.filter((type) =>
    Object.values(type).some((value) =>
      value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const sortedRiskTypes = [...filteredRiskTypes].sort((a, b) => {
    if (!sortConfig.key) return 0;

    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];

    if (aValue === null || aValue === undefined) return 1;
    if (bValue === null || bValue === undefined) return -1;

    if (sortConfig.direction === 'asc') {
      return aValue.toString().localeCompare(bValue.toString(), undefined, { numeric: true });
    } else {
      return bValue.toString().localeCompare(aValue.toString(), undefined, { numeric: true });
    }
  });

  const totalPages = Math.ceil(sortedRiskTypes.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentRiskTypes = sortedRiskTypes.slice(indexOfFirstItem, indexOfLastItem);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const getParentRiskTypeName = (parentId) => {
    if (!parentId) return '-';
    const parent = riskTypes.find(t => t.riskTypeID === parentId);
    return parent ? parent.name : '-';
  };

  const columns = [
    { key: 'name', label: 'Name', sortable: true },
    { key: 'code', label: 'Code', sortable: true },
    { key: 'comment', label: 'Comment', sortable: true },
    { key: 'parentRiskType', label: 'Parent Risk Type', sortable: true }
  ];

  return (
    <div className="p-6">
      <PageHeader
        title="Risk Type Management"
        description="Define and manage different types of risks for better categorization and analysis of potential threats."
        section="Risk"
        currentPage="Types"
        icon={AlertTriangle}
        searchPlaceholder="Search risk types..."
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
      />

      {/* Action Buttons */}
      <div className="flex justify-end items-center gap-3 mb-4">
        {selectedRiskTypes.length > 0 && (
          <Button
            variant="outline"
            className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
              canDelete && !loading
                ? 'bg-[#F62D51]/90 text-white hover:bg-[#F62D51] hover:shadow-md'
                : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
            } flex items-center gap-2 px-6 py-2 font-semibold`}
            onClick={handleDeleteSelected}
            disabled={!canDelete || loading}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                Delete ({selectedRiskTypes.length})
              </>
            )}
          </Button>
        )}
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          {canCreate && (
            <DialogTrigger asChild>
              <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add Risk Type
              </Button>
            </DialogTrigger>
          )}
          <DialogContent className="max-w-3xl p-8">
            <DialogHeader>
              <DialogTitle>Create New Risk Type</DialogTitle>
              <DialogDescription>
                Fill in the details below to create a new risk type. Fields marked with * are required.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-8 mt-4">
              <div className="grid grid-cols-2 gap-6">
                <div className="flex flex-col">
                  <Label htmlFor="name" className="mb-3">
                    Name *
                  </Label>
                  <Input
                    id="name"
                    value={newRiskType.name}
                    onChange={(e) =>
                      setNewRiskType({ ...newRiskType, name: e.target.value })
                    }
                    placeholder="Enter risk type name"
                    required
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="code" className="mb-3">
                    Code
                  </Label>
                  <Input
                    id="code"
                    value={newRiskType.code}
                    onChange={(e) =>
                      setNewRiskType({ ...newRiskType, code: e.target.value })
                    }
                    placeholder="Enter code"
                  />
                </div>
              </div>
              <div className="flex flex-col">
                <Label htmlFor="parentRiskType" className="mb-3">
                  Parent Risk Type
                </Label>
                <Select
                  value={newRiskType.parentRiskTypeID || "none"}
                  onValueChange={(value) =>
                    setNewRiskType({ ...newRiskType, parentRiskTypeID: value === "none" ? null : value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select parent risk type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {riskTypes.map((type) => (
                      <SelectItem key={type.riskTypeID} value={type.riskTypeID}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex flex-col">
                <Label htmlFor="comment" className="mb-3">
                  Comment
                </Label>
                <Textarea
                  id="comment"
                  value={newRiskType.comment}
                  onChange={(e) =>
                    setNewRiskType({ ...newRiskType, comment: e.target.value })
                  }
                  placeholder="Enter comment"
                  rows={4}
                />
              </div>
              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-[#F62D51] hover:bg-red-700"
                  disabled={submitting}
                >
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Risk Type'
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {loading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                      <Checkbox
                        checked={selectedRiskTypes.length === currentRiskTypes.length && currentRiskTypes.length > 0}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all"
                      />
                    </th>
                    {columns.map((column) => (
                      <th
                        key={column.key}
                        className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100"
                        onClick={() => column.sortable && handleSort(column.key)}
                      >
                        <div className="flex items-center gap-1">
                          {column.label}
                          {sortConfig.key === column.key && (
                            <ArrowUpDown className="w-4 h-4" />
                          )}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {currentRiskTypes.map((type, index) => (
                    <tr
                      key={type.riskTypeID}
                      className={`hover:bg-gray-50 cursor-pointer ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                      onClick={() => handleRowClick(type.riskTypeID)}
                    >
                      <td className="px-6 py-4">
                        <Checkbox
                          checked={selectedRiskTypes.includes(type.riskTypeID)}
                          onCheckedChange={() => handleSelectRiskType(type.riskTypeID)}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </td>

                      <td className="px-6 py-4 text-sm whitespace-nowrap">
                        <span className="font-bold text-[#242A33]">{type.name}</span>
                      </td>
                      <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                        {type.code || '-'}
                      </td>
                      <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                        {type.comment || '-'}
                      </td>
                      <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                        {getParentRiskTypeName(type.parentRiskType)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={filteredRiskTypes.length}
            onPageChange={handlePageChange}
            onItemsPerPageChange={(value) => {
              setItemsPerPage(value);
              setCurrentPage(1);
            }}
            startIndex={indexOfFirstItem}
            endIndex={indexOfLastItem}
          />
        </>
      )}
    </div>
  );
}

export default RiskTypes;