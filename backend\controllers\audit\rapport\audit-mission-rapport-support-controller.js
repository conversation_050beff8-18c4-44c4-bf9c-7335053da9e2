const { v4: uuidv4 } = require('uuid');
const db = require('../../../models');
const { AuditMissionRapportSupport, AuditMission } = db;

// Get all rapport supports
const getAllRapportSupports = async (req, res) => {
  try {
    const rapportSupports = await AuditMissionRapportSupport.findAll({
      include: [
        {
          model: AuditMission,
          as: 'auditMission',
          attributes: ['id', 'name', 'principalAudite', 'datedebut', 'datefin']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    return res.status(200).json({
      success: true,
      data: rapportSupports
    });
  } catch (error) {
    console.error('Error fetching rapport supports:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch rapport supports',
      error: error.message
    });
  }
};

// Get rapport support by mission ID
const getRapportSupportByMissionId = async (req, res) => {
  try {
    const { missionId } = req.params;

    const rapportSupport = await AuditMissionRapportSupport.findOne({
      where: { auditMissionId: missionId },
      include: [
        {
          model: AuditMission,
          as: 'auditMission',
          attributes: ['id', 'name', 'principalAudite', 'datedebut', 'datefin']
        }
      ]
    });

    if (!rapportSupport) {
      return res.status(404).json({
        success: false,
        message: 'Rapport support not found for this mission'
      });
    }

    return res.status(200).json({
      success: true,
      data: rapportSupport
    });
  } catch (error) {
    console.error('Error fetching rapport support by mission ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch rapport support',
      error: error.message
    });
  }
};

// Get rapport support by ID
const getRapportSupportById = async (req, res) => {
  try {
    const { id } = req.params;

    const rapportSupport = await AuditMissionRapportSupport.findByPk(id, {
      include: [
        {
          model: AuditMission,
          as: 'auditMission',
          attributes: ['id', 'name', 'principalAudite', 'datedebut', 'datefin']
        }
      ]
    });

    if (!rapportSupport) {
      return res.status(404).json({
        success: false,
        message: 'Rapport support not found'
      });
    }

    return res.status(200).json({
      success: true,
      data: rapportSupport
    });
  } catch (error) {
    console.error('Error fetching rapport support by ID:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch rapport support',
      error: error.message
    });
  }
};

// Create a new rapport support
const createRapportSupport = async (req, res) => {
  try {
    const {
      logo,
      signatureElectrique,
      destinataire,
      auditMissionId
    } = req.body;

    // Validate required fields
    if (!auditMissionId) {
      return res.status(400).json({
        success: false,
        message: 'Audit mission ID is required'
      });
    }

    // Check if audit mission exists
    const auditMission = await AuditMission.findByPk(auditMissionId);
    if (!auditMission) {
      return res.status(404).json({
        success: false,
        message: 'Audit mission not found'
      });
    }

    // Check if rapport support already exists for this mission
    const existingRapportSupport = await AuditMissionRapportSupport.findOne({
      where: { auditMissionId }
    });

    if (existingRapportSupport) {
      return res.status(400).json({
        success: false,
        message: 'Rapport support already exists for this mission'
      });
    }

    // Create the rapport support
    const rapportSupport = await AuditMissionRapportSupport.create({
      id: `AMRS_${uuidv4().substring(0, 8)}`,
      logo,
      signatureElectrique,
      destinataire,
      auditMissionId
    });

    // Fetch the created rapport support with associations
    const createdRapportSupport = await AuditMissionRapportSupport.findByPk(rapportSupport.id, {
      include: [
        {
          model: AuditMission,
          as: 'auditMission',
          attributes: ['id', 'name', 'principalAudite', 'datedebut', 'datefin']
        }
      ]
    });

    return res.status(201).json({
      success: true,
      message: 'Rapport support created successfully',
      data: createdRapportSupport
    });
  } catch (error) {
    console.error('Error creating rapport support:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to create rapport support',
      error: error.message
    });
  }
};

// Update rapport support
const updateRapportSupport = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      logo,
      signatureElectrique,
      destinataire,
      auditMissionId
    } = req.body;

    const rapportSupport = await AuditMissionRapportSupport.findByPk(id);
    if (!rapportSupport) {
      return res.status(404).json({
        success: false,
        message: 'Rapport support not found'
      });
    }

    // If auditMissionId is being updated, check if the new mission exists
    if (auditMissionId && auditMissionId !== rapportSupport.auditMissionId) {
      const auditMission = await AuditMission.findByPk(auditMissionId);
      if (!auditMission) {
        return res.status(404).json({
          success: false,
          message: 'Audit mission not found'
        });
      }

      // Check if rapport support already exists for the new mission
      const existingRapportSupport = await AuditMissionRapportSupport.findOne({
        where: { auditMissionId }
      });

      if (existingRapportSupport && existingRapportSupport.id !== id) {
        return res.status(400).json({
          success: false,
          message: 'Rapport support already exists for this mission'
        });
      }
    }

    // Update the rapport support
    await rapportSupport.update({
      logo: logo !== undefined ? logo : rapportSupport.logo,
      signatureElectrique: signatureElectrique !== undefined ? signatureElectrique : rapportSupport.signatureElectrique,
      destinataire: destinataire !== undefined ? destinataire : rapportSupport.destinataire,
      auditMissionId: auditMissionId || rapportSupport.auditMissionId
    });

    // Fetch the updated rapport support with associations
    const updatedRapportSupport = await AuditMissionRapportSupport.findByPk(id, {
      include: [
        {
          model: AuditMission,
          as: 'auditMission',
          attributes: ['id', 'name', 'principalAudite', 'datedebut', 'datefin']
        }
      ]
    });

    return res.status(200).json({
      success: true,
      message: 'Rapport support updated successfully',
      data: updatedRapportSupport
    });
  } catch (error) {
    console.error('Error updating rapport support:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update rapport support',
      error: error.message
    });
  }
};

// Delete rapport support
const deleteRapportSupport = async (req, res) => {
  try {
    const { id } = req.params;

    const rapportSupport = await AuditMissionRapportSupport.findByPk(id);
    if (!rapportSupport) {
      return res.status(404).json({
        success: false,
        message: 'Rapport support not found'
      });
    }

    await rapportSupport.destroy();

    return res.status(200).json({
      success: true,
      message: 'Rapport support deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting rapport support:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete rapport support',
      error: error.message
    });
  }
};

module.exports = {
  getAllRapportSupports,
  getRapportSupportByMissionId,
  getRapportSupportById,
  createRapportSupport,
  updateRapportSupport,
  deleteRapportSupport
};
