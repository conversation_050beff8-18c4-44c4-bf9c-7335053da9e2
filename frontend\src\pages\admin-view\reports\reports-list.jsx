import React, { useState } from 'react';
import { Search, Filter, Trash2, UserRound, CalendarDays, FileText, Plus } from 'lucide-react';
import PageHeader from '@/components/ui/page-header';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Import chart images
import barChartImg from '../../../assets/barchart.png';
import pieChartImg from '../../../assets/piechart.png';

const reportsData = [
  {
    id: 1,
    title: 'Residual Risk by Risk Type-1 (EN)',
    description: 'Risque résiduel par type de risque',
    illustrations: 'Graphique à barres',
    chartType: 'bar',
    createdBy: 'Vitalisuser',
    date: '30/09/2024',
  },
  {
    id: 2,
    title: 'Risk and Incident Analysis-1 (EN)',
    description: 'Analyse des risques et des incidents',
    illustrations: 'Tableau, Matrice',
    chartType: 'pie',
    createdBy: 'fares',
    date: '20/09/2024',
  },
  {
    id: 3,
    title: 'comparaison',
    description: 'Rapport de comparaison de diagrammes',
    illustrations: 'Comparaison de ...',
    chartType: 'bar',
    createdBy: 'fares',
    date: '13/03/2025',
  },
  {
    id: 4,
    title: 'Internal Control - Test Coverage (EN)',
    description: 'Contrôle interne - Couverture des missions de te',
    illustrations: 'Tableau, Matrice',
    chartType: 'pie',
    createdBy: 'fares',
    date: '02/10/2024',
  },
  {
    id: 5,
    title: "Rapport d'agrégation-1",
    description: "Rapport d'agrégation",
    illustrations: 'Table hiérarchique',
    chartType: 'bar',
    createdBy: 'fares',
    date: '05/12/2024',
  },
  {
    id: 6,
    title: 'Bricksburg City - Control Direct Assessm',
    description: "Rapport d'agrégation",
    illustrations: 'Table hiérarchique',
    chartType: 'pie',
    createdBy: 'ihsen',
    date: '10/10/2024',
  },
  {
    id: 7,
    title: 'test matrice',
    description: 'Matrice de répartition des risques',
    illustrations: 'Tableau, Matrice',
    chartType: 'bar',
    createdBy: 'fares',
    date: '05/12/2024',
  },
];

export default function ReportTableView() {
  const [search, setSearch] = useState('');
  const [selectedReports, setSelectedReports] = useState([]);
  const navigate = useNavigate();

  const filteredReports = reportsData.filter(
    (r) =>
      r.title.toLowerCase().includes(search.toLowerCase()) ||
      r.description.toLowerCase().includes(search.toLowerCase())
  );

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedReports(filteredReports.map(report => report.id));
    } else {
      setSelectedReports([]);
    }
  };

  const handleSelectReport = (id) => {
    setSelectedReports(prev => {
      if (prev.includes(id)) {
        return prev.filter(item => item !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  const handleDeleteSelected = () => {
    if (selectedReports.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedReports.length} selected report(s)?`)) {
      // Here you would typically call an API to delete the reports
      // For now, we'll just show an alert
      alert(`Deleted ${selectedReports.length} reports`);
      setSelectedReports([]);
    }
  };

  const handleCreateReport = () => {
    navigate('/admin/reports/create');
  };

  const handleViewReport = (id) => {
    // In a real application, you would navigate to a specific report view
    // For now, we'll just navigate to the create page which will show our chart components
    navigate('/admin/reports/create');
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <PageHeader
        title="Reports"
        description="Gérez et analysez vos rapports d'activité"
        section="Reports"
        searchPlaceholder="Rechercher un rapport..."
        searchValue={search}
        onSearchChange={(e) => setSearch(e.target.value)}
        icon={FileText}
        actions={
          <div className="flex items-center gap-3">
            {selectedReports.length > 0 && (
              <Button
                variant="destructive"
                className="bg-[#F62D51]/90 hover:bg-[#F62D51] flex items-center gap-2 shadow-md"
                onClick={handleDeleteSelected}
              >
                <Trash2 className="h-4 w-4 fill-white stroke-[#F62D51]/0" />
                Supprimer ({selectedReports.length})
              </Button>
            )}
            <Button
              className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2"
              onClick={handleCreateReport}
            >
              <Plus className="h-4 w-4" />
              Créer un rapport
            </Button>
          </div>
        }
      />

      <div className="flex gap-6">
        {/* Sidebar Filters */}
        <aside className="w-64 p-4 bg-white rounded-lg shadow space-y-6">
          <h2 className="font-semibold text-lg">Filtres</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm mb-1">Recherche</label>
              <div className="relative">
                <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500 fill-gray-100 stroke-gray-500" />
                <Input
                  type="text"
                  placeholder="Chercher par nom"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm mb-1">Type de rapport</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Tous les types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les types</SelectItem>
                  <SelectItem value="risk">Risque</SelectItem>
                  <SelectItem value="incident">Incident</SelectItem>
                  <SelectItem value="control">Contrôle</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm mb-1">Créé par</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Tous les utilisateurs" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous les utilisateurs</SelectItem>
                  <SelectItem value="vitalisuser">Vitalisuser</SelectItem>
                  <SelectItem value="fares">Fares</SelectItem>
                  <SelectItem value="ihsen">Ihsen</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm mb-1">Date de création</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Toutes les dates" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Toutes les dates</SelectItem>
                  <SelectItem value="today">Aujourd'hui</SelectItem>
                  <SelectItem value="week">Cette semaine</SelectItem>
                  <SelectItem value="month">Ce mois</SelectItem>
                  <SelectItem value="year">Cette année</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </aside>

        {/* Main Table Section */}
        <section className="flex-1 bg-white p-4 rounded-lg shadow">
          <Table>
            <TableHeader className="bg-gray-50">
              <TableRow>
                <TableHead className="w-10">
                  <Checkbox
                    checked={selectedReports.length === filteredReports.length && filteredReports.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>Titre</TableHead>
                <TableHead>Illustration(s)</TableHead>
                <TableHead>Date de création</TableHead>
                <TableHead>Tag(s)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredReports.map((report) => (
                <TableRow
                  key={report.id}
                  className={`hover:bg-gray-50 ${selectedReports.includes(report.id) ? 'bg-gray-50' : ''} cursor-pointer`}
                  onClick={() => handleViewReport(report.id)}
                >
                  <TableCell className="p-2">
                    <Checkbox
                      checked={selectedReports.includes(report.id)}
                      onCheckedChange={(checked) => {
                        handleSelectReport(report.id);
                      }}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </TableCell>
                  <TableCell className="p-2">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 flex-shrink-0">
                        {report.chartType === 'bar' ? (
                          <img src={barChartImg} alt="Bar chart" className="w-full h-full object-contain" />
                        ) : (
                          <img src={pieChartImg} alt="Pie chart" className="w-full h-full object-contain" />
                        )}
                      </div>
                      <div>
                        <div className="font-semibold text-gray-800">{report.title}</div>
                        <div className="text-gray-500 text-xs">{report.description}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="p-2">{report.illustrations}</TableCell>
                  <TableCell className="p-2">
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-1 text-gray-600">
                        <UserRound className="h-3.5 w-3.5 fill-gray-600 stroke-white" />
                        <span className="text-sm">{report.createdBy}</span>
                      </div>
                      <div className="flex items-center gap-1 text-gray-600">
                        <CalendarDays className="h-3.5 w-3.5 fill-gray-600 stroke-white" />
                        <span className="text-sm">{report.date}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="p-2 text-gray-400 italic">—</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </section>
      </div>
    </div>
  );
}
