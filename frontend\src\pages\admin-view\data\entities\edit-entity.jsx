import { useEffect, useState, useCallback } from "react";
import { useParams, useNavigate, Outlet, useLocation } from "react-router-dom";
import { Building2, FileText, Edit, Loader2 } from "lucide-react";
import DetailHeader from "@/components/ui/detail-header";
import TabBar from "@/components/ui/tabs/tab-bar";
import TabContent from "@/components/ui/tabs/tab-content";
import axios from "axios";
import { toast } from "sonner";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from "react-i18next";
import entityIcon from "@/assets/entity.png";

function EditEntity() {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const API_BASE_URL = getApiBaseUrl();
  const [currentEntity, setCurrentEntity] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState(null);
  const { t } = useTranslation();

  // Get the current tab from the URL
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    if (path.includes('/features')) {
      return 'features';
    }
    return 'overview';
  }, [location.pathname]);

  const [activeTab, setActiveTab] = useState(getCurrentTab());

  // Update active tab when location changes
  useEffect(() => {
    setActiveTab(getCurrentTab());
  }, [location, getCurrentTab]);

  // Fetch entity on component mount
  useEffect(() => {
    const fetchEntity = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        const response = await axios.get(`${API_BASE_URL}/entities/${id}`, {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
        });

        if (response.data.success) {
          setCurrentEntity(response.data.data);
          setIsError(false);
          setError(null);
        } else {
          setIsError(true);
          setError(response.data.message || "Failed to fetch entity");
          toast.error(response.data.message || "Failed to fetch entity");
        }
      } catch (err) {
        setIsError(true);
        setError(err.message || "An error occurred while fetching the entity");
        toast.error(err.message || "An error occurred while fetching the entity");
      } finally {
        setIsLoading(false);
      }
    };

    fetchEntity();
  }, [id]);

  // Handle go back
  const handleGoBack = () => {
    navigate("/admin/data/entities");
  };

  const tabs = [
    { id: "overview", label: t('admin.entities.tabs.overview', 'Overview'), icon: <FileText className="h-4 w-4" /> },
    { id: "features", label: t('admin.entities.tabs.features', 'Features'), icon: <Edit className="h-4 w-4" /> },
  ];

  // Navigate to tab
  const navigateToTab = (tabId) => {
    switch (tabId) {
      case "overview":
        navigate(`/admin/data/entities/${id}`);
        break;
      case "features":
        navigate(`/admin/data/entities/${id}/features`);
        break;
      default:
        navigate(`/admin/data/entities/${id}`);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {t('admin.entities.error', 'Error')}: {error}
        </div>
      </div>
    );
  }

  if (!currentEntity) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          {t('admin.entities.not_found', 'Entity not found')}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      {/* Header */}
      <DetailHeader
        title={currentEntity?.name}
        icon={<img src={entityIcon} alt="Entité" className="h-6 w-6" />}
        badges={[
          {
            label: currentEntity.internalExternal === 'External' ? t('admin.entities.badges.external', 'External') : t('admin.entities.badges.internal', 'Internal'),
            color: `
              ${currentEntity.internalExternal === 'External' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}
            `
          }
        ]}
        metadata={[
          currentEntity.type ? `${t('admin.entities.metadata.type', 'Type')}: ${currentEntity.type}` : t('admin.entities.metadata.no_type', 'No type'),
          currentEntity.comment ?
            `${currentEntity.comment.substring(0, 100)}${currentEntity.comment.length > 100 ? '...' : ''}` :
            t('admin.entities.metadata.no_comment', 'No comment')
        ]}
        onBack={handleGoBack}
        backLabel={t('admin.entities.back', 'Back to Entities')}
      />

      {/* Tabs */}
      <TabBar
        activeTab={activeTab}
        tabs={tabs}
        onTabChange={navigateToTab}
        className="mb-6"
      />

      {/* Tab Content */}
      <TabContent>
        <Outlet context={{ entity: currentEntity, refreshEntity: () => {
          setIsLoading(true);
          axios.get(`${API_BASE_URL}/entities/${id}`, {
            withCredentials: true,
            headers: { "Content-Type": "application/json" },
          })
          .then(response => {
            if (response.data.success) {
              setCurrentEntity(response.data.data);
            }
          })
          .catch(err => {
            toast.error(err.message || t('admin.entities.errors.refresh_failed', 'Failed to refresh entity data'));
          })
          .finally(() => {
            setIsLoading(false);
          });
        } }} />
      </TabContent>
    </div>
  );
}

export default EditEntity;
