import React from 'react';
import { useTranslation } from 'react-i18next';
import { englishToKeyMap } from '../utils/translateText';

/**
 * A component that wraps its children and translates text nodes
 * This is useful for automatically translating components without modifying them
 */
const TranslateWrapper = ({ children }) => {
  const { t } = useTranslation();

  const translateNode = (node) => {
    // If the node is a string, try to translate it
    if (typeof node === 'string') {
      const key = englishToKeyMap[node];
      if (key) {
        return t(key);
      }
      return node;
    }

    // If the node is a valid React element, process its children
    if (React.isValidElement(node)) {
      // Skip translation for certain components or props
      const skipTranslation = node.props?.skipTranslation;
      
      if (skipTranslation) {
        return node;
      }
      
      // Clone the element with the same props but translated children
      return React.cloneElement(
        node,
        node.props,
        // Process all children recursively
        React.Children.map(node.props.children, translateNode)
      );
    }

    // For other node types (null, undefined, numbers, etc.), return as is
    return node;
  };

  return <>{React.Children.map(children, translateNode)}</>;
};

export default TranslateWrapper;
