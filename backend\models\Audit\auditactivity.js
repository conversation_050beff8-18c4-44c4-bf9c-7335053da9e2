'use strict';

module.exports = (sequelize, DataTypes) => {
  const AuditActivity = sequelize.define('AuditActivity', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    auditMissionID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'AuditMissions',
        key: 'id'
      }
    },
    status: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'Not Started'
    },
    datedebut: {
      type: DataTypes.DATE,
      allowNull: true
    },
    datefin: {
      type: DataTypes.DATE,
      allowNull: true
    },
    chargedetravailestimee: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    chargedetravaileffective: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    objectif: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    depense: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    }
  }, {
    tableName: 'AuditActivities',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['id']
      },
      {
        fields: ['auditMissionID'] // For foreign key lookups
      },
      {
        fields: ['status'] // For filtering by status
      },
      {
        fields: ['datedebut', 'datefin'] // For date range queries
      }
    ]
  });

  AuditActivity.associate = function(models) {
    // AuditActivity belongs to AuditMission
    AuditActivity.belongsTo(models.AuditMission, {
      foreignKey: 'auditMissionID',
      as: 'auditMission'
    });

    // AuditActivity has many FicheDeTravail
    AuditActivity.hasMany(models.FicheDeTravail, {
      foreignKey: 'auditActivityID',
      as: 'fichesDeTravail'
    });

    // AuditActivity has many AuditConstat
    AuditActivity.hasMany(models.AuditConstat, {
      foreignKey: 'auditActivityID',
      as: 'constats'
    });

    AuditActivity.hasMany(models.EquipeIntervenante, {
      foreignKey: 'auditActivityId',
      as: 'equipeIntervenantes'
    });
  };

  return AuditActivity;
};