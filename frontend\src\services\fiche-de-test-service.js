import axios from 'axios';
import { getApiEndpointUrl, getAuthHeaders, getApiBaseUrl } from '@/utils/api-config';

const API_BASE_URL = getApiBaseUrl();

/**
 * Get all fiches de test
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Array, message?: string}>}
 */
export const getAllFichesDeTest = async (signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl('audit/fiches-de-test'),
      {
        headers: getAuthHeaders(),
        signal
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Get fiche de test by ID
 * @param {string} id - The fiche de test ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Object, message?: string}>}
 */
export const getFicheDeTestById = async (id, signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl(`audit/fiches-de-test/${id}`),
      {
        headers: getAuthHeaders(),
        signal
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Get fiches de test by fiche de travail ID
 * @param {string} ficheDeTravailId - The fiche de travail ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Array, message?: string}>}
 */
export const getFichesDeTestByFicheDeTravailId = async (ficheDeTravailId, signal) => {
  try {
    const response = await axios.get(
      getApiEndpointUrl(`audit/fiches-de-test/ficheDeTravail/${ficheDeTravailId}`),
      {
        headers: getAuthHeaders(),
        signal
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Create a new fiche de test
 * @param {Object} ficheData - The fiche de test data
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Object, message?: string}>}
 */
export const createFicheDeTest = async (ficheData, signal) => {
  try {
    const response = await axios.post(
      getApiEndpointUrl('audit/fiches-de-test'),
      ficheData,
      {
        headers: getAuthHeaders(),
        signal
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Update a fiche de test
 * @param {string} id - The fiche de test ID
 * @param {Object} ficheData - The updated fiche de test data
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, data?: Object, message?: string}>}
 */
export const updateFicheDeTest = async (id, ficheData, signal) => {
  try {
    const response = await axios.put(
      getApiEndpointUrl(`audit/fiches-de-test/${id}`),
      ficheData,
      {
        headers: getAuthHeaders(),
        signal
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};

/**
 * Delete a fiche de test
 * @param {string} id - The fiche de test ID
 * @param {AbortSignal} [signal] - Optional AbortSignal for request cancellation
 * @returns {Promise<{success: boolean, message?: string}>}
 */
export const deleteFicheDeTest = async (id, signal) => {
  try {
    const response = await axios.delete(
      getApiEndpointUrl(`audit/fiches-de-test/${id}`),
      {
        headers: getAuthHeaders(),
        signal
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
}; 