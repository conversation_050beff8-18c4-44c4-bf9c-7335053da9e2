import React, { useState } from "react";
import { ChevronRight, ChevronDown, Trash2, Plus, Edit, ExternalLink } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

// Import correct asset icons
import activiteIcon from '@/assets/activite.png';
import ficheTravailIcon from '@/assets/fiche-travail.png';
import bpsIcon from '@/assets/BPS.png'; // Using BPS for note de synthèse
import constatIcon from '@/assets/constat.png';
import recommendationIcon from '@/assets/recommandation.png';
import riskIcon from '@/assets/risk.png';

const AuditTreeEnhanced = ({ data, onNodeDelete, onNodeAdd, onNodeEdit, missionAudit, planId }) => {
  const navigate = useNavigate();

  return (
    <div className="w-full bg-gray-50 rounded-lg border border-gray-200">
      <div className="p-6">
        <div className="space-y-2">
          {data.map((node) => (
            <TreeNode
              key={node.id}
              node={node}
              level={0}
              onDelete={onNodeDelete}
              onAdd={onNodeAdd}
              onEdit={onNodeEdit}
              missionAudit={missionAudit}
              planId={planId}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

const TreeNode = ({ node, level, onDelete, onAdd, onEdit, missionAudit, planId }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const hasChildren = node.children && node.children.length > 0;
  const navigate = useNavigate();

  const toggleExpand = (e) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  const handleDelete = (e) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(node.id, node.type);
    }
  };

  const handleAdd = (e) => {
    e.stopPropagation();
    if (onAdd) {
      onAdd(node.id, node.type);
    }
  };

  const handleRowClick = () => {
    // Navigate to specific pages/tabs based on node type
    if (!missionAudit?.id) return;

    // Get the activity ID for this node
    const activityId = getActivityIdForNode(node);

    switch (node.type) {
      case 'activity':
        // Always use the full plans-daudit path for activities using prop planId
        navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAudit.id}/activites/${node.id}`);
        break;
      case 'fiche-de-travail':
        // Navigate to fiche de travail detail page using prop planId and activityId
        navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAudit.id}/activites/${node.activityId}/fiches-travail/${node.id}`);
        break;
      case 'constat':
        // Navigate to constats tab within the activity, then to specific constat using full plans-daudit path
        navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAudit.id}/activites/${node.activityId}/constats/${node.id}`);
        break;
      case 'recommendation':
        // Navigate to recommendation detail page using full plans-daudit path
        navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAudit.id}/activites/${node.activityId}/constats/${node.parentId}/recommandations/${node.id}`);
        break;
      default:
        // For note-synthese and other types, just expand/collapse
        if (hasChildren) {
          setIsExpanded(!isExpanded);
        }
        break;
    }
  };

  // Helper function to get activity ID for any node in the tree
  const getActivityIdForNode = (currentNode) => {
    // If this is an activity, return its ID
    if (currentNode.type === 'activity') {
      return currentNode.id;
    }

    // For all other node types, use the activityId that's now included in each node
    return currentNode.activityId || null;
  };

  // Get color based on node type
  const getColor = () => {
    switch (node.type) {
      case "activity":
        return "#10B981"; // Green
      case "fiche-de-travail":
        return "#F97316"; // Orange
      case "note-synthese":
        return "#A855F7"; // Purple
      case "constat":
        return "#F62D51"; // Red
      case "recommendation":
        return "#3B82F6"; // Blue
      case "risk":
        return "#F62D51"; // Red
      default:
        return "#6B7280"; // Gray
    }
  };

  // Get the appropriate icon based on node type
  const getIcon = () => {
    switch (node.type) {
      case "activity":
        return <img src={activiteIcon} className="w-5 h-5" alt="activité" />;
      case "fiche-de-travail":
        return <img src={ficheTravailIcon} className="w-5 h-5" alt="fiche de travail" />;
      case "note-synthese":
        return <img src={bpsIcon} className="w-5 h-5" alt="note de synthèse" />;
      case "constat":
        return <img src={constatIcon} className="w-5 h-5" alt="constat" />;
      case "recommendation":
        return <img src={recommendationIcon} className="w-5 h-5" alt="recommandation" />;
      case "risk":
        return <img src={riskIcon} className="w-5 h-5" alt="risque" />;
      default:
        return <div className="w-5 h-5 bg-gray-300 rounded" />;
    }
  };

  // Get status badge for activities
  const getStatusBadge = () => {
    if (node.type === 'activity' && node.status) {
      const statusColors = {
        'Terminé': 'bg-green-100 text-green-800',
        'En cours': 'bg-blue-100 text-blue-800',
        'Planifié': 'bg-amber-100 text-amber-800',
        'Not Started': 'bg-gray-100 text-gray-800'
      };
      
      return (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[node.status] || 'bg-gray-100 text-gray-800'}`}>
          {node.status}
        </span>
      );
    }
    return null;
  };

  // Get impact badge for constats
  const getImpactBadge = () => {
    if (node.type === 'constat' && node.impact) {
      const impactColors = {
        'tres fort': 'bg-red-100 text-red-800',
        'fort': 'bg-orange-100 text-orange-800',
        'moyen': 'bg-yellow-100 text-yellow-800',
        'faible': 'bg-green-100 text-green-800',
        'tres faible': 'bg-green-100 text-green-800'
      };
      
      return (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${impactColors[node.impact] || 'bg-gray-100 text-gray-800'}`}>
          {node.impact}
        </span>
      );
    }
    return null;
  };

  // Check if node can be deleted (note-synthese cannot be deleted)
  const canDelete = node.type !== 'note-synthese';

  // Check if node can have children added
  const canAddChildren = ['activity', 'fiche-de-travail', 'note-synthese', 'constat'].includes(node.type);

  const color = getColor();
  const nodeIcon = getIcon();

  return (
    <div className="relative" style={{ marginLeft: `${level * 16}px` }}>
      <div className="flex items-center group hover:bg-white/50 rounded-lg transition-all duration-200">
        {/* Expand/Collapse Button */}
        {hasChildren ? (
          <button
            onClick={toggleExpand}
            className="mr-2 p-1 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-150"
            aria-label={isExpanded ? "Collapse" : "Expand"}
          >
            {isExpanded ? (
              <ChevronDown className="w-5 h-5 text-gray-600 hover:text-gray-800" />
            ) : (
              <ChevronRight className="w-5 h-5 text-gray-600 hover:text-gray-800" />
            )}
          </button>
        ) : (
          <div className="w-7 h-7 mr-2" />
        )}

        {/* Node Content */}
        <div
          className="flex items-center justify-between py-3 px-4 rounded-lg cursor-pointer w-full transition-all duration-200 hover:shadow-md border border-transparent hover:border-gray-200"
          style={{
            backgroundColor: 'white',
            borderLeft: `4px solid ${color}`,
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
          }}
          onClick={handleRowClick}
        >
          <div className="flex items-center flex-1 min-w-0">
            {/* Icon */}
            <div className="mr-3 flex-shrink-0">
              {nodeIcon}
            </div>

            {/* Name and badges */}
            <div className="flex items-center flex-1 min-w-0">
              {node.type === 'note-synthese' ? (
                <span style={{ fontSize: '11px', color: '#64748b', fontWeight: 500, marginRight: 4 }}>
                  Note de synthèse
                </span>
              ) : (
                <span className="font-semibold text-gray-800 text-sm mr-3 truncate">
                  <span style={{ fontSize: '11px', color: '#64748b', fontWeight: 500, marginRight: 4 }}>
                    {(() => {
                      switch (node.type) {
                        case 'activity': return 'Activité:';
                        case 'fiche-de-travail': return 'Fiche de travail:';
                        case 'constat': return 'Constat:';
                        case 'recommendation': return 'Recommandation:';
                        case 'risk': return 'Risque:';
                        default: return '';
                      }
                    })()}
                  </span>
                  {node.name}
                </span>
              )}

              {/* Status badge for activities */}
              {getStatusBadge()}

               {/* Type badge for constats */}
               {node.type === 'constat' && node.constatType && (
                 <span className={`px-2 py-1 rounded-full text-xs font-medium ml-2 ${
                   node.constatType === 'Point Fort' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                 }`}>
                   {node.constatType}
                 </span>
               )}

              {/* External link icon for clickable items */}
              {['activity', 'fiche-de-travail', 'constat', 'recommendation'].includes(node.type) && (
                <ExternalLink className="h-3.5 w-3.5 ml-2 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
              )}
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200" onClick={(e) => e.stopPropagation()}>
            {canAddChildren && (
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 rounded-md hover:bg-green-100"
                onClick={handleAdd}
                title="Ajouter un élément"
              >
                <Plus className="h-4 w-4 text-green-600" />
              </Button>
            )}

            {canDelete && (
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 rounded-md hover:bg-red-100"
                onClick={handleDelete}
                title="Supprimer"
              >
                <Trash2 className="h-4 w-4 text-red-600" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div className="mt-1 ml-3 border-l-2 border-gray-200 pl-2">
          {node.children.map((childNode) => (
            <TreeNode
              key={childNode.id}
              node={childNode}
              level={level + 1}
              onDelete={onDelete}
              onAdd={onAdd}
              onEdit={onEdit}
              missionAudit={missionAudit}
              planId={planId}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default AuditTreeEnhanced;
