module.exports = (sequelize, DataTypes) => {
  const FinancialEntry = sequelize.define('FinancialEntry', {
    entryID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    amount: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
    localAmount: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    currency: {
      type: DataTypes.STRING(10),
      allowNull: true,
      defaultValue: 'XOF',
    },
    category: {
      type: DataTypes.ENUM('losses', 'gains', 'recoveries', 'provisions'),
      allowNull: false,
    },
    incidentID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Incident',
        key: 'incidentID',
      },
    },
  }, {
    tableName: 'financial_entry',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['entryID'],
      },
      {
        fields: ['incidentID'],
      },
      {
        fields: ['category'],
      },
    ],
  });

  FinancialEntry.associate = (models) => {
    // FinancialEntry belongs to Incident
    FinancialEntry.belongsTo(models.Incident, {
      foreignKey: 'incidentID',
      as: 'incident',
      onDelete: 'CASCADE',
    });
  };

  return FinancialEntry;
}; 