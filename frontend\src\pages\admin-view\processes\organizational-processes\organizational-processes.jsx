import { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Loader2, Trash2, ArrowUpDown, Network, Filter, ChevronUp } from 'lucide-react';
import { useSelector } from 'react-redux';
import { hasPermission } from '@/store/auth-slice';
import PageHeader from '@/components/ui/page-header';
import axios from 'axios';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { getApiBaseUrl } from "@/utils/api-config";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import TablePagination from "../../../../components/ui/table-pagination";
import FilterPanel from "../../../../components/ui/filter-panel";
import orgProcIcon from '@/assets/org.png';

function OrganizationalProcessesManagement() {
  const navigate = useNavigate();
  const canCreate = useSelector(state => hasPermission(state, 'create'));
  const canDelete = useSelector(state => hasPermission(state, 'delete'));
  const [organizationalProcesses, setOrganizationalProcesses] = useState([]);
  const [businessProcesses, setBusinessProcesses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProcesses, setSelectedProcesses] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const API_BASE_URL = getApiBaseUrl();
  // Filter states
  const [filters, setFilters] = useState({
    parentOrganizationalProcessID: 'all',
    parentBusinessProcessID: 'all',
  });

  // Clear all filters
  const clearFilters = () => {
    console.log('Clearing all filters');
    const clearedFilters = {
      parentOrganizationalProcessID: 'all',
      parentBusinessProcessID: 'all',
    };
    setFilters(clearedFilters);
    console.log('Current filters before clearing:', filters);
    console.log('Filters cleared to:', clearedFilters);
  };

  const [newOrganizationalProcess, setNewOrganizationalProcess] = useState({
    organizationalProcessID: '',
    name: '',
    code: '',
    comment: '',
    parentOrganizationalProcess: null,
    parentBusinessProcess: null,
  });

  const orgProcessMap = useMemo(() => {
    return new Map(organizationalProcesses.map(p => [p.organizationalProcessID, p.name]));
  }, [organizationalProcesses]);

  const busProcessMap = useMemo(() => {
    return new Map(businessProcesses.map(p => [p.businessProcessID, p.name]));
  }, [businessProcesses]);

  const fetchOrganizationalProcesses = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/organizationalProcesses`, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
      });
      if (response.data.success) {
        setOrganizationalProcesses(response.data.data);
      }
    } catch (_error) {
      toast.error(_error.message || "Failed to fetch organizational processes");
    } finally {
      setLoading(false);
    }
  };

  const fetchBusinessProcesses = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/businessProcesses`, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
      });
      if (response.data.success) {
        setBusinessProcesses(response.data.data);
      }
    } catch (_error) {
      toast.error(_error.message || "Failed to fetch business processes");
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        await Promise.all([
          fetchOrganizationalProcesses(),
          fetchBusinessProcesses(),
        ]);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    const processToCreate = {
      ...newOrganizationalProcess,
      organizationalProcessID: newOrganizationalProcess.organizationalProcessID || `OPS_${Date.now()}`,
      code: newOrganizationalProcess.code || null,
      comment: newOrganizationalProcess.comment || null,
      parentOrganizationalProcess: newOrganizationalProcess.parentOrganizationalProcess || null,
      parentBusinessProcess: newOrganizationalProcess.parentBusinessProcess || null,
    };

    try {
      setSubmitting(true);
      const response = await axios.post(
        `${API_BASE_URL}/organizationalProcesses`,
        processToCreate,
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
          timeout: 60000,
        }
      );

      if (response.data.success) {
        toast.success("Organizational Process created successfully");
        setNewOrganizationalProcess({
          organizationalProcessID: '',
          name: '',
          code: '',
          comment: '',
          parentOrganizationalProcess: null,
          parentBusinessProcess: null,
        });
        setIsOpen(false);
        await fetchOrganizationalProcesses();
      }
    } catch (_error) {
      toast.error(_error.response?.data?.message || _error.message || "Failed to create organizational process");
    } finally {
      setSubmitting(false);
    }
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedProcesses(filteredOrganizationalProcesses.map(process => process.organizationalProcessID));
    } else {
      setSelectedProcesses([]);
    }
  };

  const handleSelectProcess = (id) => {
    setSelectedProcesses(prev => {
      if (prev.includes(id)) {
        return prev.filter(item => item !== id);
      }
      return [...prev, id];
    });
  };

  const handleRowClick = (id) => {
    // Get the current path to determine if we're in audit view
    const isAuditView = window.location.pathname.startsWith('/audit');
    const basePath = isAuditView ? '/audit' : '/admin';
    // Navigate to the organizational process edit page with overview tab
    navigate(`${basePath}/processes/organizational-processes/${id}/overview`);
  };

  const handleDeleteSelected = async () => {
    if (selectedProcesses.length === 0) {
      toast.error("No organizational processes selected");
      return;
    }

    const dependentProcesses = [];
    selectedProcesses.forEach(selectedId => {
      const dependents = organizationalProcesses.filter(
        process => process.parentOrganizationalProcess === selectedId
      );
      if (dependents.length > 0) {
        const processName = orgProcessMap.get(selectedId) || selectedId;
        dependentProcesses.push({
          id: selectedId,
          name: processName,
          dependents: dependents.map(dep => orgProcessMap.get(dep.organizationalProcessID) || dep.organizationalProcessID),
        });
      }
    });

    if (dependentProcesses.length > 0) {
      const message = dependentProcesses.map(dep =>
        `Cannot delete "${dep.name}" (ID: ${dep.id}) because it is a parent of: ${dep.dependents.join(', ')}.`
      ).join(' ');
      toast.error(message, {
        position: "top-center",
        duration: 6000,
        style: {
          background: '#f8d7da',
          color: '#721c24',
          border: '1px solid #f5c6cb',
          padding: '16px',
          maxWidth: '80%',
          textAlign: 'center',
        },
      });
      return;
    }

    if (window.confirm(`Are you sure you want to delete ${selectedProcesses.length} selected organizational process(es)?`)) {
      try {
        setLoading(true);
        let failedDeletions = [];

        const attemptDelete = async (id, retries = 2) => {
          for (let attempt = 0; attempt <= retries; attempt++) {
            try {
              await axios.delete(`${API_BASE_URL}/organizationalProcesses/${id}`, {
                withCredentials: true,
                timeout: 60000,
              });
              return { success: true };
            } catch (error) {
              if (attempt === retries) {
                let userMessage = error.response?.data?.message || 'Unknown error';
                if (userMessage.includes("It is referenced by other records")) {
                  userMessage = `Cannot delete process ${id}: It is referenced by other records. Please reassign or remove dependent records first.`;
                }
                return { success: false, error: userMessage };
              }
              await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
            }
          }
        };

        const batchSize = 3;
        for (let i = 0; i < selectedProcesses.length; i += batchSize) {
          const batch = selectedProcesses.slice(i, i + batchSize);
          await Promise.all(
            batch.map(async (id) => {
              const result = await attemptDelete(id);
              if (!result.success) {
                failedDeletions.push({ id, error: result.error });
              }
              await new Promise(resolve => setTimeout(resolve, 500));
              return result;
            })
          );

          if (i + batchSize < selectedProcesses.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        await fetchOrganizationalProcesses();
        setSelectedProcesses([]);

        if (failedDeletions.length > 0) {
          const errorMessage = failedDeletions.map(f => f.error).join('; ');
          toast.error(`Failed to delete ${failedDeletions.length} organizational process(es): ${errorMessage}`, {
            duration: 6000,
          });
        } else {
          toast.success("All selected organizational processes deleted successfully");
        }
      } catch (_error) {
        toast.error(_error.message || "An error occurred during the deletion process");
      } finally {
        setLoading(false);
      }
    }
  };

  // Filter organizational processes based on search query and filters
  const filteredOrganizationalProcesses = useMemo(() => {
    return organizationalProcesses.filter((process) => {
      // Apply search query filter
      if (searchQuery) {
        const matchesSearch = Object.values(process).some(value =>
          value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
        );
        if (!matchesSearch) return false;
      }

      // Apply parent organizational process filter
      if (filters.parentOrganizationalProcessID && filters.parentOrganizationalProcessID !== 'all') {
        console.log('Parent Organizational Process filter:', {
          filterValue: filters.parentOrganizationalProcessID,
          processValue: process.parentOrganizationalProcess,
          match: process.parentOrganizationalProcess === filters.parentOrganizationalProcessID
        });
        if (process.parentOrganizationalProcess !== filters.parentOrganizationalProcessID) return false;
      }

      // Apply parent business process filter
      if (filters.parentBusinessProcessID && filters.parentBusinessProcessID !== 'all') {
        console.log('Parent Business Process filter:', {
          filterValue: filters.parentBusinessProcessID,
          processValue: process.parentBusinessProcess,
          match: process.parentBusinessProcess === filters.parentBusinessProcessID
        });
        if (process.parentBusinessProcess !== filters.parentBusinessProcessID) return false;
      }

      return true;
    });
  }, [organizationalProcesses, searchQuery, filters]);

  const sortedOrganizationalProcesses = [...filteredOrganizationalProcesses].sort((a, b) => {
    if (!sortConfig.key) return 0;

    let aValue, bValue;
    if (sortConfig.key === 'parentOrganizationalProcess') {
      aValue = orgProcessMap.get(a.parentOrganizationalProcess) || '';
      bValue = orgProcessMap.get(b.parentOrganizationalProcess) || '';
    } else if (sortConfig.key === 'parentBusinessProcess') {
      aValue = busProcessMap.get(a.parentBusinessProcess) || '';
      bValue = busProcessMap.get(b.parentBusinessProcess) || '';
    } else {
      aValue = a[sortConfig.key];
      bValue = b[sortConfig.key];
    }

    if (aValue === null || aValue === undefined) aValue = '';
    if (bValue === null || bValue === undefined) bValue = '';

    return sortConfig.direction === 'asc'
      ? aValue.toString().localeCompare(bValue.toString(), undefined, { numeric: true })
      : bValue.toString().localeCompare(aValue.toString(), undefined, { numeric: true });
  });

  const handleSort = (key) => {
    setSortConfig((prevConfig) => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const totalPages = Math.ceil(sortedOrganizationalProcesses.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentOrganizationalProcesses = sortedOrganizationalProcesses.slice(indexOfFirstItem, indexOfLastItem);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const columns = [
    { key: 'name', label: 'Name', sortable: true },
    { key: 'code', label: 'Code', sortable: true },
    { key: 'parentOrganizationalProcess', label: 'Parent Organizational Process', sortable: true },
    { key: 'parentBusinessProcess', label: 'Parent Business Process', sortable: true },
    { key: 'comment', label: 'Comment', sortable: true },
  ];

  return (
    <div className="p-6">
      <PageHeader
        title="Organizational Processes Management"
        description="Define and manage organizational processes that structure your company's operations and governance."
        section="Processes"
        currentPage="Organizational"
        searchPlaceholder="Search organizational processes..."
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
        icon={Network}
      />

      {/* Filter Panel */}
      <div className="mb-6 filter-panel-container">
        <FilterPanel
          filters={[
            {
              id: "parentOrganizationalProcessID",
              label: "Parent Organizational Process",
              component: (
                <Select
                  value={filters.parentOrganizationalProcessID}
                  onValueChange={(value) => setFilters({ ...filters, parentOrganizationalProcessID: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select parent org. process" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    {organizationalProcesses && organizationalProcesses.map((process) => (
                      <SelectItem key={process.organizationalProcessID} value={process.organizationalProcessID}>
                        {process.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "parentBusinessProcessID",
              label: "Parent Business Process",
              component: (
                <Select
                  value={filters.parentBusinessProcessID}
                  onValueChange={(value) => setFilters({ ...filters, parentBusinessProcessID: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select parent business process" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    {businessProcesses && businessProcesses.map((process) => (
                      <SelectItem key={process.businessProcessID} value={process.businessProcessID}>
                        {process.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
          ]}
          activeFilters={filters}
          onClearFilters={clearFilters}
        />
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end items-center gap-3 mb-4">
        {selectedProcesses.length > 0 && (
          <Button
            variant="outline"
            className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
              canDelete && !loading
                ? 'bg-[#F62D51]/90 text-white hover:bg-[#F62D51] hover:shadow-md'
                : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
            } flex items-center gap-2 px-6 py-2 font-semibold`}
            onClick={handleDeleteSelected}
            disabled={!canDelete || loading}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                Delete ({selectedProcesses.length})
              </>
            )}
          </Button>
        )}
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          {canCreate && (
            <DialogTrigger asChild>
              <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add Process
              </Button>
            </DialogTrigger>
          )}
          <DialogContent className="max-w-3xl p-8">
            <DialogHeader>
              <DialogTitle>Add New Organizational Process</DialogTitle>
              <DialogDescription>
                Fill in the details to create a new organizational process.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-8">
              <div className="grid grid-cols-2 gap-6">
                <div className="flex flex-col">
                  <Label htmlFor="name" className="mb-2">Name *</Label>
                  <Input
                    id="name"
                    value={newOrganizationalProcess.name}
                    onChange={(e) => setNewOrganizationalProcess({
                      ...newOrganizationalProcess,
                      name: e.target.value
                    })}
                    placeholder="Enter organizational process name"
                    required
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="code" className="mb-2">Code</Label>
                  <Input
                    id="code"
                    value={newOrganizationalProcess.code}
                    onChange={(e) => setNewOrganizationalProcess({
                      ...newOrganizationalProcess,
                      code: e.target.value
                    })}
                    placeholder="Enter organizational process code"
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="parentOrganizationalProcess" className="mb-2">Parent Organizational Process</Label>
                  <Select
                    value={newOrganizationalProcess.parentOrganizationalProcess || "null"}
                    onValueChange={(value) => setNewOrganizationalProcess({
                      ...newOrganizationalProcess,
                      parentOrganizationalProcess: value === "null" ? null : value
                    })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select parent organizational process" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="null">None</SelectItem>
                      {organizationalProcesses.map((process) => (
                        <SelectItem
                          key={process.organizationalProcessID}
                          value={process.organizationalProcessID}
                        >
                          {process.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="parentBusinessProcess" className="mb-2">Parent Business Process</Label>
                  <Select
                    value={newOrganizationalProcess.parentBusinessProcess || "null"}
                    onValueChange={(value) => setNewOrganizationalProcess({
                      ...newOrganizationalProcess,
                      parentBusinessProcess: value === "null" ? null : value
                    })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select parent business process" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="null">None</SelectItem>
                      {businessProcesses.map((process) => (
                        <SelectItem
                          key={process.businessProcessID}
                          value={process.businessProcessID}
                        >
                          {process.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex flex-col">
                <Label htmlFor="comment" className="mb-2">Comment</Label>
                <Textarea
                  id="comment"
                  value={newOrganizationalProcess.comment}
                  onChange={(e) => setNewOrganizationalProcess({
                    ...newOrganizationalProcess,
                    comment: e.target.value
                  })}
                  placeholder="Enter comment"
                  className="w-full h-24 resize-y"
                />
              </div>
              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-[#F62D51] hover:bg-red-700"
                  disabled={submitting}
                >
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Organizational Process'
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {loading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                      <Checkbox
                        checked={selectedProcesses.length === currentOrganizationalProcesses.length && currentOrganizationalProcesses.length > 0}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all"
                      />
                    </th>
                    {columns.map((column) => (
                      <th
                        key={column.key}
                        className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100"
                        onClick={() => column.sortable && handleSort(column.key)}
                      >
                        <div className="flex items-center gap-1">
                          {column.label}
                          {sortConfig.key === column.key && (
                            <ArrowUpDown className="w-4 h-4" />
                          )}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {currentOrganizationalProcesses.map((process, index) => {
                    const parentOrgName = orgProcessMap.get(process.parentOrganizationalProcess);
                    const parentBusName = busProcessMap.get(process.parentBusinessProcess);
                    return (
                      <tr
                        key={process.organizationalProcessID}
                        className={`hover:bg-gray-50 cursor-pointer ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                        onClick={() => handleRowClick(process.organizationalProcessID)}
                      >
                        <td className="px-6 py-4">
                          <Checkbox
                            checked={selectedProcesses.includes(process.organizationalProcessID)}
                            onCheckedChange={() => handleSelectProcess(process.organizationalProcessID)}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </td>

                        <td className="px-6 py-4 text-sm font-bold text-[#242A33] whitespace-nowrap">
                          <div className="flex items-center gap-2">
                            <img
                              src={orgProcIcon}
                              alt="organizational process"
                              className="w-5 h-5 object-contain"
                            />
                            <span>{process.name}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                          {process.code || '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                          {parentOrgName || '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                          {parentBusName || '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                          {process.comment || '-'}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">
                {filteredOrganizationalProcesses.length} process{filteredOrganizationalProcesses.length !== 1 ? 'es' : ''} found
              </span>
              {/* Show filter badge if any filter is active */}
              {(filters.parentOrganizationalProcessID !== 'all' || filters.parentBusinessProcessID !== 'all') && (
                <Badge
                  variant="outline"
                  className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 cursor-pointer"
                  onClick={() => {
                    // Toggle filter panel visibility
                    const filterPanel = document.querySelector('.filter-panel-container');
                    if (filterPanel) {
                      filterPanel.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                >
                  Filtered
                  <ChevronUp className="ml-1 h-3 w-3" />
                </Badge>
              )}
            </div>
          </div>

          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={filteredOrganizationalProcesses.length}
            onPageChange={handlePageChange}
            onItemsPerPageChange={(value) => {
              setItemsPerPage(value);
              setCurrentPage(1);
            }}
            startIndex={indexOfFirstItem}
            endIndex={indexOfLastItem}
          />
        </>
      )}
    </div>
  );
}

export default OrganizationalProcessesManagement;
