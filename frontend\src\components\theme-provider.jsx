import { createContext, useContext, useEffect } from "react";

// Create a theme context
const ThemeProviderContext = createContext();

export function ThemeProvider({
  children,
  storageKey = "vitalis-theme",
}) {
  // Set a fixed theme based on your original UI design
  // Change this to "light" if your original UI was light-themed
  const theme = "light"; 

  // Apply the theme when the component mounts
  useEffect(() => {
    const root = window.document.documentElement;
    
    // Remove any theme classes first
    root.classList.remove("light", "dark");
    
    // Add our fixed theme class
    root.classList.add(theme);
    
    // Store the theme in localStorage to persist it
    localStorage.setItem(storageKey, theme);

    // Apply original UI variables
    applyOriginalUIVariables();

    // Block theme changes from OS
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    mediaQuery.addEventListener("change", () => {
      // Re-apply our theme if system preference changes
      root.classList.remove("dark");
      root.classList.add("light");
      localStorage.setItem(storageKey, "light");
      applyOriginalUIVariables();
    });
  }, [storageKey]);

  // Apply original UI CSS variables to ensure proper styling
  const applyOriginalUIVariables = () => {
    // This keeps your original UI colors regardless of system theme
    const root = document.documentElement;
    
    // Set your original UI colors here (these are example values)
    root.style.setProperty('--background', '#ffffff');
    root.style.setProperty('--foreground', '#1e293b');
    root.style.setProperty('--card', '#ffffff');
    root.style.setProperty('--card-foreground', '#1e293b');
    root.style.setProperty('--popover', '#ffffff');
    root.style.setProperty('--popover-foreground', '#1e293b');
    
    // UI elements with your original colors
    root.style.setProperty('--primary', '#F62D51');
    root.style.setProperty('--primary-foreground', '#ffffff');
    root.style.setProperty('--secondary', '#f8f9fa');
    root.style.setProperty('--secondary-foreground', '#1e293b');
    root.style.setProperty('--muted', '#f1f5f9');
    root.style.setProperty('--muted-foreground', '#64748b');
    root.style.setProperty('--accent', '#f1f5f9');
    root.style.setProperty('--accent-foreground', '#1e293b');
    
    // UI controls with your original colors
    root.style.setProperty('--destructive', '#ef4444');
    root.style.setProperty('--destructive-foreground', '#ffffff');
    root.style.setProperty('--border', '#e2e8f0');
    root.style.setProperty('--input', '#ffffff');
    root.style.setProperty('--ring', '#F62D51');
    
    // Sidebar with your original colors
    root.style.setProperty('--sidebar', '#ffffff');
    root.style.setProperty('--sidebar-foreground', '#1e293b');
    root.style.setProperty('--sidebar-primary', '#F62D51');
    root.style.setProperty('--sidebar-primary-foreground', '#ffffff');
    root.style.setProperty('--sidebar-accent', '#f1f5f9');
    root.style.setProperty('--sidebar-accent-foreground', '#1e293b');
    root.style.setProperty('--sidebar-border', '#e2e8f0');
    root.style.setProperty('--sidebar-ring', '#F62D51');
  };

  // Provide the theme context value
  const value = {
    theme,
    // These functions are now no-ops since we're forcing a consistent theme
    setTheme: () => {
      console.warn("Theme changes are disabled - using fixed theme");
    },
    toggleTheme: () => {
      console.warn("Theme changes are disabled - using fixed theme");
    }
  };

  return (
    <ThemeProviderContext.Provider value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

// Custom hook to use the theme context
export const useTheme = () => {
  const context = useContext(ThemeProviderContext);
  
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  
  return context;
};
