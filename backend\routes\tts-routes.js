const express = require('express');
const router = express.Router();
const axios = require('axios');
const realisticTTSController = require('../controllers/tts-realistic-controller');

const PLAYHT_USER_ID = process.env.PLAYHT_USER_ID || 'cM8QpTjsOQOZPrj6VNFK2kSd7sF2';
const PLAYHT_API_KEY = process.env.PLAYHT_API_KEY || 'ak-9998ef44ae044b86be9c65ba3d5b5a12';

// Default PlayHT voices (replace with your preferred voices if needed)
const VOICE_MAP = {
  Alice: 's3://voice-cloning-zero-shot/4e6e5e7e-2e7e-4e7e-8e7e-4e7e4e7e4e7e/en-US-Standard-C', // Example female
  Bob:   's3://voice-cloning-zero-shot/4e6e5e7e-2e7e-4e7e-8e7e-4e7e4e7e4e7e/en-US-Standard-D', // Example male
};

router.post('/playht', async (req, res) => {
  const { text, speaker } = req.body;
  const voiceId = VOICE_MAP[speaker] || VOICE_MAP.Alice;
  try {
    const response = await axios({
      method: 'post',
      url: 'https://api.play.ht/api/v2/tts/stream',
      headers: {
        'Authorization': `Bearer ${PLAYHT_API_KEY}`,
        'X-User-Id': PLAYHT_USER_ID,
        'Content-Type': 'application/json',
      },
      data: {
        text,
        voice: voiceId,
        output_format: 'mp3',
        voiceEngine: 'PlayDialog'
      },
      responseType: 'stream'
    });
    res.setHeader('Content-Type', 'audio/mpeg');
    response.data.pipe(res);
  } catch (err) {
    console.error('[PlayHT] Error:', err.response?.data || err.message);
    res.status(500).json({ error: 'Failed to generate audio' });
  }
});

router.post('/realistic', async (req, res) => {
  // Optionally add authentication middleware here if needed
  return realisticTTSController.generateRealisticTTS(req, res);
});

module.exports = router; 