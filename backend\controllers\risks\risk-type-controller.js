const { RiskType } = require('../../models');

// Get all risk types
const getAllRiskTypes = async (req, res) => {
  try {
    const riskTypes = await RiskType.findAll();
    res.json({
      success: true,
      data: riskTypes
    });
  } catch (error) {
    console.error('Error fetching risk types:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch risk types'
    });
  }
};

// Create new risk type
const createRiskType = async (req, res) => {
  try {
    const {
      riskTypeID,
      name,
      code,
      comment,
      parentRiskType
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name'].filter(field => !req.body[field])
      });
    }

    const riskType = await RiskType.create({
      riskTypeID: riskTypeID || `RT_${Date.now()}`,
      name,
      code: code || null,
      comment: comment || null,
      parentRiskType: parentRiskType || null
    });

    return res.status(201).json({
      success: true,
      message: 'Risk type created successfully',
      data: riskType
    });
  } catch (error) {
    console.error('Error creating risk type:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create risk type'
    });
  }
};

// Get risk type by ID
const getRiskTypeById = async (req, res) => {
  try {
    const { id } = req.params;
    const riskType = await RiskType.findByPk(id);
    
    if (!riskType) {
      return res.status(404).json({
        success: false,
        message: 'Risk type not found'
      });
    }
    
    res.json({
      success: true,
      data: riskType
    });
  } catch (error) {
    console.error('Error fetching risk type:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch risk type'
    });
  }
};

// Update risk type
const updateRiskType = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      code,
      comment,
      parentRiskType
    } = req.body;

    const riskType = await RiskType.findByPk(id);
    
    if (!riskType) {
      return res.status(404).json({
        success: false,
        message: 'Risk type not found'
      });
    }

    // Update fields
    await riskType.update({
      name: name || riskType.name,
      code: code !== undefined ? code : riskType.code,
      comment: comment !== undefined ? comment : riskType.comment,
      parentRiskType: parentRiskType !== undefined ? parentRiskType : riskType.parentRiskType
    });

    res.json({
      success: true,
      message: 'Risk type updated successfully',
      data: riskType
    });
  } catch (error) {
    console.error('Error updating risk type:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update risk type'
    });
  }
};

// Delete risk type
const deleteRiskType = async (req, res) => {
  try {
    const { id } = req.params;
    const riskType = await RiskType.findByPk(id);
    
    if (!riskType) {
      return res.status(404).json({
        success: false,
        message: 'Risk type not found'
      });
    }
    
    await riskType.destroy();
    
    res.json({
      success: true,
      message: 'Risk type deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting risk type:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete risk type'
    });
  }
};

module.exports = {
  getAllRiskTypes,
  createRiskType,
  getRiskTypeById,
  updateRiskType,
  deleteRiskType
};
