import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>board<PERSON>ist,
  ChevronUp,
  ChevronDown,
  Plus,
  AlertCircle,
  Edit,
  Trash2,
  User,
  Calendar,
  CheckCircle2,
  Loader2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { getApiBaseUrl } from "@/utils/api-config";
import { useCustomOutletContext } from "../edit-recommandation";
import axios from "axios";

function PlanActionTab() {
  // Get recommendation data from context
  const { recommandation } = useCustomOutletContext();
  const API_BASE_URL = getApiBaseUrl();

  // Section toggles
  const [isHistoriqueOpen, setIsHistoriqueOpen] = useState(true);
  const [isActionsOpen, setIsActionsOpen] = useState(true);

  // Actions state
  const [actions, setActions] = useState([]);
  const [isLoadingActions, setIsLoadingActions] = useState(false);
  const [selectedActions, setSelectedActions] = useState([]);
  const [users, setUsers] = useState([]);

  // Modal states
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentAction, setCurrentAction] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    priority: "Low",
    status: "Not Started",
    description: "",
    startDate: "",
    endDate: "",
    assigneeId: "none"
  });

  // Fetch actions when recommendation changes
  useEffect(() => {
    const fetchActions = async () => {
      if (!recommandation?.actionPlanID) return;

      setIsLoadingActions(true);
      try {
        const response = await axios.get(
          `${API_BASE_URL}/actions/action-plan/${recommandation.actionPlanID}`,
          { withCredentials: true }
        );

        if (response.data.success) {
          setActions(response.data.data || []);
        }
      } catch (error) {
        console.error("Error fetching actions:", error);
        toast.error("Erreur lors du chargement des actions");
      } finally {
        setIsLoadingActions(false);
      }
    };

    fetchActions();
  }, [recommandation?.actionPlanID, API_BASE_URL]);

  // Fetch users for assignee selection
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/users`, { withCredentials: true });
        if (response.data.success) {
          setUsers(response.data.data || []);
        }
      } catch (error) {
        console.error("Error fetching users:", error);
      }
    };

    fetchUsers();
  }, [API_BASE_URL]);

  // Helper functions for styling
  const getPriorityBadgeColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusBadgeColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in progress': return 'bg-blue-100 text-blue-800';
      case 'not started': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Le nom de l'action est requis");
      return;
    }

    try {
      const actionData = {
        ...formData,
        actionPlanID: recommandation.actionPlanID,
        assigneeId: formData.assigneeId === "none" ? null : parseInt(formData.assigneeId)
      };

      let response;
      if (isEditMode && currentAction) {
        response = await axios.put(
          `${API_BASE_URL}/actions/${currentAction.actionID}`,
          actionData,
          { withCredentials: true }
        );
      } else {
        response = await axios.post(
          `${API_BASE_URL}/actions`,
          actionData,
          { withCredentials: true }
        );
      }

      if (response.data.success) {
        toast.success(isEditMode ? "Action mise à jour avec succès" : "Action créée avec succès");
        setIsModalOpen(false);
        resetForm();

        // Refresh actions list
        const actionsResponse = await axios.get(
          `${API_BASE_URL}/actions/action-plan/${recommandation.actionPlanID}`,
          { withCredentials: true }
        );
        if (actionsResponse.data.success) {
          setActions(actionsResponse.data.data || []);
        }
      }
    } catch (error) {
      console.error("Error saving action:", error);
      toast.error("Erreur lors de la sauvegarde de l'action");
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: "",
      priority: "Low",
      status: "Not Started",
      description: "",
      startDate: "",
      endDate: "",
      assigneeId: "none"
    });
    setIsEditMode(false);
    setCurrentAction(null);
  };

  // Handle add action
  const handleAddAction = () => {
    resetForm();
    setIsModalOpen(true);
  };

  // Handle edit action
  const handleEditAction = (action) => {
    setFormData({
      name: action.name || "",
      priority: action.priority || "Low",
      status: action.status || "Not Started",
      description: action.description || "",
      startDate: action.startDate ? action.startDate.split('T')[0] : "",
      endDate: action.endDate ? action.endDate.split('T')[0] : "",
      assigneeId: action.assigneeId ? action.assigneeId.toString() : "none"
    });
    setCurrentAction(action);
    setIsEditMode(true);
    setIsModalOpen(true);
  };

  // Handle delete action
  const handleDeleteAction = async (actionID) => {
    if (!confirm("Êtes-vous sûr de vouloir supprimer cette action ?")) return;

    try {
      const response = await axios.delete(
        `${API_BASE_URL}/actions/${actionID}`,
        { withCredentials: true }
      );

      if (response.data.success) {
        toast.success("Action supprimée avec succès");
        setActions(prev => prev.filter(action => action.actionID !== actionID));
        setSelectedActions(prev => prev.filter(id => id !== actionID));
      }
    } catch (error) {
      console.error("Error deleting action:", error);
      toast.error("Erreur lors de la suppression de l'action");
    }
  };

  // Handle checkbox selection
  const handleSelectAction = (actionID) => {
    setSelectedActions(prev => {
      if (prev.includes(actionID)) {
        return prev.filter(id => id !== actionID);
      } else {
        return [...prev, actionID];
      }
    });
  };

  // Handle select all
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedActions(actions.map(action => action.actionID));
    } else {
      setSelectedActions([]);
    }
  };

  // Handle delete selected
  const handleDeleteSelected = async () => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer ${selectedActions.length} action(s) ?`)) return;

    try {
      const response = await axios.post(
        `${API_BASE_URL}/actions/delete-multiple`,
        { ids: selectedActions },
        { withCredentials: true }
      );

      if (response.data.success) {
        toast.success("Actions supprimées avec succès");
        setActions(prev => prev.filter(action => !selectedActions.includes(action.actionID)));
        setSelectedActions([]);
      }
    } catch (error) {
      console.error("Error deleting actions:", error);
      toast.error("Erreur lors de la suppression des actions");
    }
  };

  return (
    <div className="space-y-6 py-4">
      {/* Section 1: Historique de l'avancement des plans d'actions */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsHistoriqueOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isHistoriqueOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <ClipboardList className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">Historique de l'avancement des plans d'actions</span>
          </div>
        </button>
        {isHistoriqueOpen && (
          <div className="p-5 bg-white space-y-4">
            <div className="flex gap-2 mb-4 justify-end">
              <Button className="flex items-center bg-[#F62D51] hover:bg-[#F62D51]/90">
                <Plus className="h-4 w-4 mr-2" />Nouveau
              </Button>
            </div>
            <Card className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500">Cette liste n'est pas encore remplie</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Section 2: Actions */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-t-lg"
          onClick={() => setIsActionsOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isActionsOpen ? (
              <ChevronUp className="h-5 w-5 text-yellow-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-yellow-600" />
            )}
            <ClipboardList className="h-5 w-5 text-yellow-600 mr-1" />
            <span className="text-lg font-medium text-yellow-800">Actions</span>
          </div>
        </button>
        {isActionsOpen && (
          <div className="p-5 bg-white space-y-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Actions du plan d'action</h3>
              <div className="flex gap-2">
                {selectedActions.length > 0 && (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleDeleteSelected}
                    className="flex items-center gap-1"
                  >
                    <Trash2 className="h-4 w-4" />
                    Supprimer ({selectedActions.length})
                  </Button>
                )}
                <Button
                  onClick={handleAddAction}
                  className="flex items-center bg-[#F62D51] hover:bg-[#F62D51]/90"
                >
                  <Plus className="h-4 w-4 mr-2" />Nouvelle Action
                </Button>
              </div>
            </div>

            {isLoadingActions ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
              </div>
            ) : actions.length === 0 ? (
              <Card className="overflow-hidden">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
                    <p className="text-gray-500">Aucune action trouvée pour ce plan d'action</p>
                    <Button
                      onClick={handleAddAction}
                      variant="outline"
                      className="mt-4"
                    >
                      Créer votre première action
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white rounded-lg overflow-hidden">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="w-10 px-6 py-3 text-left">
                        <Checkbox
                          checked={selectedActions.length === actions.length && actions.length > 0}
                          onCheckedChange={handleSelectAll}
                        />
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Nom de l'action
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Priorité
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Assigné à
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date de début
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date de fin
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {actions.map((action) => (
                      <tr key={action.actionID} className="hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <Checkbox
                            checked={selectedActions.includes(action.actionID)}
                            onCheckedChange={() => handleSelectAction(action.actionID)}
                          />
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {action.name}
                        </td>
                        <td className="px-6 py-4 text-sm">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityBadgeColor(action.priority)}`}>
                            {action.priority || "Low"}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(action.status)}`}>
                            {action.status || "Not Started"}
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {action.assignee ? (
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-1 text-gray-500" />
                              {action.assignee.username}
                            </div>
                          ) : "-"}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {action.startDate ? new Date(action.startDate).toLocaleDateString() : "-"}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {action.endDate ? new Date(action.endDate).toLocaleDateString() : "-"}
                        </td>
                        <td className="px-6 py-4 text-sm text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditAction(action)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteAction(action.actionID)}
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Create/Edit Action Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>
              {isEditMode ? "Modifier l'action" : "Créer une nouvelle action"}
            </DialogTitle>
            <DialogDescription>
              {isEditMode
                ? "Modifiez les détails de cette action."
                : "Ajoutez une nouvelle action à ce plan d'action."}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4 py-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nom de l'action *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Entrez le nom de l'action"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="priority">Priorité</Label>
                  <Select
                    value={formData.priority}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner la priorité" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Low">Faible</SelectItem>
                      <SelectItem value="Medium">Moyenne</SelectItem>
                      <SelectItem value="High">Élevée</SelectItem>
                      <SelectItem value="Critical">Critique</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Statut</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner le statut" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Not Started">Non commencé</SelectItem>
                      <SelectItem value="In Progress">En cours</SelectItem>
                      <SelectItem value="Completed">Terminé</SelectItem>
                      <SelectItem value="Cancelled">Annulé</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="assigneeId">Assigné à</Label>
                <Select
                  value={formData.assigneeId}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, assigneeId: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un utilisateur" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Aucun</SelectItem>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.username}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Date de début</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endDate">Date de fin</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Entrez la description de l'action"
                  rows={3}
                />
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsModalOpen(false)}
              >
                Annuler
              </Button>
              <Button type="submit" className="bg-[#F62D51] hover:bg-[#F62D51]/90">
                {isEditMode ? "Mettre à jour" : "Créer"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default PlanActionTab;