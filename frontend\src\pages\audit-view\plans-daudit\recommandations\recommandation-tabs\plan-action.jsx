import React, { useState } from "react";
import {
  Clipboard<PERSON>ist,
  ChevronUp,
  ChevronDown,
  Plus,
  AlertCircle
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

function PlanActionTab() {
  // Section toggles
  const [isHistoriqueOpen, setIsHistoriqueOpen] = useState(true);
  const [isActionsOpen, setIsActionsOpen] = useState(true);

  return (
    <div className="space-y-6 py-4">
      {/* Section 1: Historique de l'avancement des plans d'actions */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsHistoriqueOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isHistoriqueOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <ClipboardList className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">Historique de l'avancement des plans d'actions</span>
          </div>
        </button>
        {isHistoriqueOpen && (
          <div className="p-5 bg-white space-y-4">
            <div className="flex gap-2 mb-4 justify-end">
              <Button className="flex items-center bg-[#F62D51] hover:bg-[#F62D51]/90">
                <Plus className="h-4 w-4 mr-2" />Nouveau
              </Button>
            </div>
            <Card className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500">Cette liste n'est pas encore remplie</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Section 2: Actions */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-t-lg"
          onClick={() => setIsActionsOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isActionsOpen ? (
              <ChevronUp className="h-5 w-5 text-yellow-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-yellow-600" />
            )}
            <ClipboardList className="h-5 w-5 text-yellow-600 mr-1" />
            <span className="text-lg font-medium text-yellow-800">Actions</span>
          </div>
        </button>
        {isActionsOpen && (
          <div className="p-5 bg-white space-y-4">
            <div className="flex gap-2 mb-4 justify-end">
              <Button className="flex items-center bg-[#F62D51] hover:bg-[#F62D51]/90">
                <Plus className="h-4 w-4 mr-2" />Nouveau
              </Button>
            </div>
            <Card className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500">Cette liste n'est pas encore remplie</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}

export default PlanActionTab;