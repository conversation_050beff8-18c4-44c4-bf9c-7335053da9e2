'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create AuditSkills table
    await queryInterface.createTable('AuditSkills', {
      id: {
        type: Sequelize.STRING,
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      updatedBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Create AuditorSkills table
    await queryInterface.createTable('AuditorSkills', {
      id: {
        type: Sequelize.STRING,
        primaryKey: true,
        allowNull: false
      },
      auditorId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      skillId: {
        type: Sequelize.STRING,
        allowNull: false,
        references: {
          model: 'AuditSkills',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      rating: {
        type: Sequelize.DECIMAL(3, 2),
        allowNull: false,
        validate: {
          min: 0.0,
          max: 5.0
        }
      },
      comments: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      ratedBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      },
      ratedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes for better performance
    await queryInterface.addIndex('AuditSkills', ['name'], { unique: true });
    await queryInterface.addIndex('AuditSkills', ['isActive']);
    await queryInterface.addIndex('AuditSkills', ['createdBy']);
    await queryInterface.addIndex('AuditSkills', ['createdAt']);

    await queryInterface.addIndex('AuditorSkills', ['auditorId']);
    await queryInterface.addIndex('AuditorSkills', ['skillId']);
    await queryInterface.addIndex('AuditorSkills', ['ratedBy']);
    await queryInterface.addIndex('AuditorSkills', ['ratedAt']);
    await queryInterface.addIndex('AuditorSkills', ['rating']);
    await queryInterface.addIndex('AuditorSkills', ['isActive']);
    await queryInterface.addIndex('AuditorSkills', ['auditorId', 'skillId'], {
      unique: true,
      name: 'unique_auditor_skill'
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Drop tables in reverse order due to foreign key constraints
    await queryInterface.dropTable('AuditorSkills');
    await queryInterface.dropTable('AuditSkills');
  }
};
