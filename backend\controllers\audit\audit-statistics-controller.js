const db = require('../../models');

// Get comprehensive audit statistics for dashboard/welcome page
const getAuditStatistics = async (req, res) => {
  try {
    console.log('[AuditStatistics] Starting statistics fetch...');
    const startTime = Date.now();

    // Use raw SQL queries for maximum performance
    const statisticsQuery = `
      WITH plan_stats AS (
        SELECT 
          COUNT(*) as total_plans,
          COUNT(CASE WHEN status = 'Completed' THEN 1 END) as completed_plans,
          COUNT(CASE WHEN status = 'In Progress' THEN 1 END) as in_progress_plans,
          COUNT(CASE WHEN status = 'Draft' THEN 1 END) as draft_plans
        FROM "AuditPlans"
      ),
      mission_stats AS (
        SELECT 
          COUNT(*) as total_missions,
          COUNT(CASE WHEN etat = 'Completed' THEN 1 END) as completed_missions,
          COUNT(CASE WHEN etat = 'In Progress' THEN 1 END) as in_progress_missions,
          COUNT(CASE WHEN etat = 'Draft' THEN 1 END) as draft_missions
        FROM "AuditMissions"
      ),
      activity_stats AS (
        SELECT COUNT(*) as total_activities
        FROM "AuditActivities"
      ),
      constat_stats AS (
        SELECT 
          COUNT(*) as total_constats,
          COUNT(CASE WHEN impact = 'tres fort' THEN 1 END) as tres_fort_constats,
          COUNT(CASE WHEN impact = 'fort' THEN 1 END) as fort_constats,
          COUNT(CASE WHEN impact = 'moyen' THEN 1 END) as moyen_constats,
          COUNT(CASE WHEN impact = 'faible' THEN 1 END) as faible_constats
        FROM "AuditConstats"
      )
      SELECT 
        ps.*,
        ms.*,
        as_.*,
        cs.*
      FROM plan_stats ps
      CROSS JOIN mission_stats ms
      CROSS JOIN activity_stats as_
      CROSS JOIN constat_stats cs
    `;

    const [statistics] = await db.sequelize.query(statisticsQuery, {
      type: db.sequelize.QueryTypes.SELECT
    });

    const endTime = Date.now();
    console.log(`[AuditStatistics] Statistics fetched in ${endTime - startTime}ms`);

    // Format the response to match the frontend expectations
    const formattedStats = {
      totalPlans: parseInt(statistics.total_plans) || 0,
      totalMissions: parseInt(statistics.total_missions) || 0,
      totalActivities: parseInt(statistics.total_activities) || 0,
      totalConstats: parseInt(statistics.total_constats) || 0,
      plansByStatus: {
        'Completed': parseInt(statistics.completed_plans) || 0,
        'In Progress': parseInt(statistics.in_progress_plans) || 0,
        'Draft': parseInt(statistics.draft_plans) || 0
      },
      missionsByStatus: {
        'Completed': parseInt(statistics.completed_missions) || 0,
        'In Progress': parseInt(statistics.in_progress_missions) || 0,
        'Draft': parseInt(statistics.draft_missions) || 0
      },
      constatsByRisk: {
        'tres fort': parseInt(statistics.tres_fort_constats) || 0,
        'fort': parseInt(statistics.fort_constats) || 0,
        'moyen': parseInt(statistics.moyen_constats) || 0,
        'faible': parseInt(statistics.faible_constats) || 0
      }
    };

    return res.status(200).json({
      success: true,
      data: formattedStats,
      fetchTime: endTime - startTime
    });

  } catch (error) {
    console.error('Error fetching audit statistics:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit statistics',
      error: error.message
    });
  }
};

// Get quick counts for specific entities (for performance monitoring)
const getQuickCounts = async (req, res) => {
  try {
    const { entities } = req.query; // comma-separated list: "plans,missions,activities"
    
    if (!entities) {
      return res.status(400).json({
        success: false,
        message: 'Please specify entities parameter (e.g., ?entities=plans,missions)'
      });
    }

    const entityList = entities.split(',');
    const counts = {};

    for (const entity of entityList) {
      switch (entity.trim()) {
        case 'plans':
          counts.plans = await db.AuditPlan.count();
          break;
        case 'missions':
          counts.missions = await db.AuditMission.count();
          break;
        case 'activities':
          counts.activities = await db.AuditActivity.count();
          break;
        case 'constats':
          counts.constats = await db.AuditConstat.count();
          break;
        case 'recommendations':
          counts.recommendations = await db.AuditRecommendation.count();
          break;
      }
    }

    return res.status(200).json({
      success: true,
      data: counts
    });

  } catch (error) {
    console.error('Error fetching quick counts:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch counts',
      error: error.message
    });
  }
};

module.exports = {
  getAuditStatistics,
  getQuickCounts
};
