import { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getAllControlTypes } from '@/store/slices/controlTypeSlice';
import { getAllRisks } from '@/store/slices/riskSlice';
import { getAllBusinessProcesses } from '@/store/slices/businessProcessSlice';
import { getAllOrganizationalProcesses } from '@/store/slices/organizationalProcessSlice';
import { getAllOperations } from '@/store/slices/operationSlice';
import { getAllApplications } from '@/store/slices/applicationSlice';
import { getAllEntities } from '@/store/slices/entitySlice';
import { getAllActionPlans } from '@/store/slices/actionPlanSlice';
import referenceDataCache, { CACHE_KEYS } from '@/utils/reference-data-cache';

// Optimized custom hook for efficiently loading reference data
export const useOptimizedReferenceData = () => {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [errors, setErrors] = useState([]);

  // Get data from Redux store
  const controlTypes = useSelector((state) => state.controlType?.controlTypes || []);
  const risks = useSelector((state) => state.risk?.risks || []);
  const businessProcesses = useSelector((state) => state.businessProcess?.businessProcesses || []);
  const organizationalProcesses = useSelector((state) => state.organizationalProcess?.organizationalProcesses || []);
  const operations = useSelector((state) => state.operation?.operations || []);
  const applications = useSelector((state) => state.application?.applications || []);
  const entities = useSelector((state) => state.entity?.entities || []);
  const actionPlans = useSelector((state) => state.actionPlan?.actionPlans || []);

  // Check if all data is loaded
  const hasAllData = controlTypes.length > 0 && risks.length > 0 && 
                    businessProcesses.length > 0 && organizationalProcesses.length > 0 &&
                    operations.length > 0 && applications.length > 0 && 
                    entities.length > 0 && actionPlans.length > 0;

  // Load reference data with caching and optimized parallel loading
  const loadReferenceData = useCallback(async () => {
    if (hasAllData) {
      setIsLoaded(true);
      return;
    }

    setIsLoading(true);
    setErrors([]);

    const loadTasks = [];

    // Only load data that we don't have
    if (controlTypes.length === 0) {
      loadTasks.push({
        key: CACHE_KEYS.CONTROL_TYPES,
        action: () => dispatch(getAllControlTypes()).unwrap(),
        name: 'Control Types'
      });
    }

    if (risks.length === 0) {
      loadTasks.push({
        key: CACHE_KEYS.RISKS,
        action: () => dispatch(getAllRisks()).unwrap(),
        name: 'Risks'
      });
    }

    if (businessProcesses.length === 0) {
      loadTasks.push({
        key: CACHE_KEYS.BUSINESS_PROCESSES,
        action: () => dispatch(getAllBusinessProcesses()).unwrap(),
        name: 'Business Processes'
      });
    }

    if (organizationalProcesses.length === 0) {
      loadTasks.push({
        key: CACHE_KEYS.ORGANIZATIONAL_PROCESSES,
        action: () => dispatch(getAllOrganizationalProcesses()).unwrap(),
        name: 'Organizational Processes'
      });
    }

    if (operations.length === 0) {
      loadTasks.push({
        key: CACHE_KEYS.OPERATIONS,
        action: () => dispatch(getAllOperations()).unwrap(),
        name: 'Operations'
      });
    }

    if (applications.length === 0) {
      loadTasks.push({
        key: CACHE_KEYS.APPLICATIONS,
        action: () => dispatch(getAllApplications()).unwrap(),
        name: 'Applications'
      });
    }

    if (entities.length === 0) {
      loadTasks.push({
        key: CACHE_KEYS.ENTITIES,
        action: () => dispatch(getAllEntities()).unwrap(),
        name: 'Entities'
      });
    }

    if (actionPlans.length === 0) {
      loadTasks.push({
        key: CACHE_KEYS.ACTION_PLANS,
        action: () => dispatch(getAllActionPlans()).unwrap(),
        name: 'Action Plans'
      });
    }

    // Execute all load tasks in parallel with caching
    const results = await Promise.allSettled(
      loadTasks.map(async (task) => {
        // Check cache first
        const cachedData = referenceDataCache.get(task.key);
        if (cachedData) {
          return cachedData;
        }

        // Load from API and cache
        try {
          const result = await task.action();
          referenceDataCache.set(task.key, result);
          return result;
        } catch (error) {
          throw new Error(`Failed to load ${task.name}: ${error.message}`);
        }
      })
    );

    // Collect errors but don't fail completely
    const loadErrors = results
      .filter(result => result.status === 'rejected')
      .map(result => result.reason.message);

    setErrors(loadErrors);
    setIsLoaded(true);
    setIsLoading(false);
  }, [
    dispatch,
    hasAllData,
    controlTypes.length,
    risks.length,
    businessProcesses.length,
    organizationalProcesses.length,
    operations.length,
    applications.length,
    entities.length,
    actionPlans.length
  ]);

  // Auto-load on mount
  useEffect(() => {
    if (!hasAllData && !isLoading) {
      loadReferenceData();
    }
  }, [hasAllData, isLoading, loadReferenceData]);

  // Return data and utilities
  return {
    // Data
    controlTypes,
    risks,
    businessProcesses,
    organizationalProcesses,
    operations,
    applications,
    entities,
    actionPlans,
    
    // Status
    isLoading,
    isLoaded,
    hasAllData,
    errors,
    
    // Actions
    loadReferenceData,
    clearCache: () => referenceDataCache.clearAll(),
    getCacheStatus: () => referenceDataCache.getStatus()
  };
};

export default useOptimizedReferenceData;
