import { getApiBaseUrl } from '../utils/api-config';

const API_BASE_URL = getApiBaseUrl();

// Get all users assigned to a specific control
export const getControlAssignedUsers = async (controlId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/control-assignments/control/${controlId}/users`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching control assigned users:', error);
    throw error;
  }
};

// Get all controls assigned to a specific user
export const getUserAssignedControls = async (userId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/control-assignments/user/${userId}/controls`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching user assigned controls:', error);
    throw error;
  }
};

// Assign multiple users to a control
export const assignUsersToControl = async (controlId, userIds, assignedBy = null) => {
  try {
    const response = await fetch(`${API_BASE_URL}/control-assignments/control/${controlId}/assign`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userIds,
        assignedBy
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error assigning users to control:', error);
    throw error;
  }
};

// Remove specific user assignment from control
export const removeUserFromControl = async (controlId, userId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/control-assignments/control/${controlId}/user/${userId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error removing user from control:', error);
    throw error;
  }
};

// Remove all user assignments from control
export const removeAllUsersFromControl = async (controlId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/control-assignments/control/${controlId}/users`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error removing all users from control:', error);
    throw error;
  }
};
