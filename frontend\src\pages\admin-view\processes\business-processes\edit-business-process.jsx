import { useEffect, useState, useCallback } from "react";
import { useParams, useNavigate, Outlet, useLocation } from "react-router-dom";
import { GitBranch, FileText, Edit, Loader2 } from "lucide-react";
import DetailHeader from "@/components/ui/detail-header";
import TabBar from "@/components/ui/tabs/tab-bar";
import TabContent from "@/components/ui/tabs/tab-content";
import axios from "axios";
import { toast } from "sonner";
import { getApiBaseUrl } from "@/utils/api-config";
import businessProcessIcon from "@/assets/BPS.png";

function EditBusinessProcess() {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const API_BASE_URL = getApiBaseUrl();
  const [currentProcess, setCurrentProcess] = useState(null);
  const [businessProcesses, setBusinessProcesses] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState(null);

  // Get the current tab from the URL
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    if (path.includes('/features')) {
      return 'features';
    }
    return 'overview';
  }, [location.pathname]);

  const [activeTab, setActiveTab] = useState(getCurrentTab());

  // Update active tab when location changes
  useEffect(() => {
    setActiveTab(getCurrentTab());
  }, [location, getCurrentTab]);

  // Fetch business processes
  const fetchBusinessProcesses = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/businessProcesses`, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
      });
      if (response.data.success) {
        setBusinessProcesses(response.data.data);
      }
    } catch (error) {
      console.error("Failed to fetch business processes:", error);
    }
  };

  // Fetch business process on component mount
  useEffect(() => {
    const fetchBusinessProcess = async () => {
      if (!id) return;
      
      try {
        setIsLoading(true);
        const response = await axios.get(`${API_BASE_URL}/businessProcesses/${id}`, {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
        });
        
        if (response.data.success) {
          setCurrentProcess(response.data.data);
          setIsError(false);
          setError(null);
        } else {
          setIsError(true);
          setError(response.data.message || "Failed to fetch business process");
          toast.error(response.data.message || "Failed to fetch business process");
        }
      } catch (err) {
        setIsError(true);
        setError(err.message || "An error occurred while fetching the business process");
        toast.error(err.message || "An error occurred while fetching the business process");
      } finally {
        setIsLoading(false);
      }
    };

    // Fetch data
    const fetchData = async () => {
      await Promise.all([
        fetchBusinessProcess(),
        fetchBusinessProcesses()
      ]);
    };

    fetchData();
  }, [id]);

  // Get parent business process name
  const getParentBusinessProcessName = useCallback(() => {
    if (!currentProcess?.parentBusinessProcessID || !businessProcesses.length) {
      return "None";
    }
    
    const parent = businessProcesses.find(
      process => process.businessProcessID === currentProcess.parentBusinessProcessID
    );
    
    return parent ? parent.name : "Unknown";
  }, [currentProcess, businessProcesses]);

  // Handle go back
  const handleGoBack = () => {
    navigate("/admin/processes/business-processes");
  };

  const tabs = [
    { id: "overview", label: "Overview", icon: <FileText className="h-4 w-4" /> },
    { id: "features", label: "Features", icon: <Edit className="h-4 w-4" /> },
  ];
  
  // Navigate to tab
  const navigateToTab = (tabId) => {
    switch (tabId) {
      case "overview":
        navigate(`/admin/processes/business-processes/${id}`);
        break;
      case "features":
        navigate(`/admin/processes/business-processes/${id}/features`);
        break;
      default:
        navigate(`/admin/processes/business-processes/${id}`);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          Error: {error}
        </div>
      </div>
    );
  }

  if (!currentProcess) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          Business Process not found
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      {/* Header */}
      <DetailHeader
        title={currentProcess?.name}
        icon={<img src={businessProcessIcon} alt="Processus Métier" className="h-6 w-6" />}
        metadata={[
          currentProcess.code ? `Code: ${currentProcess.code}` : 'No code',
          `Parent Process: ${getParentBusinessProcessName()}`,
          currentProcess.comment ? 
            `${currentProcess.comment.substring(0, 100)}${currentProcess.comment.length > 100 ? '...' : ''}` : 
            'No comment'
        ]}
        onBack={handleGoBack}
        backLabel="Back to Business Processes"
      />

      {/* Tabs */}
      <TabBar
        activeTab={activeTab}
        tabs={tabs}
        onTabChange={navigateToTab}
        className="mb-6"
      />

      {/* Tab Content */}
      <TabContent>
        <Outlet context={{ 
          process: currentProcess, 
          businessProcesses,
          refreshProcess: async () => {
            setIsLoading(true);
            try {
              const response = await axios.get(`${API_BASE_URL}/businessProcesses/${id}`, {
                withCredentials: true,
                headers: { "Content-Type": "application/json" },
              });
              
              if (response.data.success) {
                setCurrentProcess(response.data.data);
              }
            } catch (error) {
              toast.error("Failed to refresh business process data");
            } finally {
              setIsLoading(false);
            }
          }
        }} />
      </TabContent>
    </div>
  );
}

export default EditBusinessProcess;
