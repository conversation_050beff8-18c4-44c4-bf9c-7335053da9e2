module.exports = (sequelize, DataTypes) => {
  const IncidentContributor = sequelize.define('IncidentContributor', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    incident_id: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Incident',
        key: 'incidentID',
      },
      onDelete: 'CASCADE',
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    assigned_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id',
      },
    },
    assigned_date: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    }
  }, {
    tableName: 'IncidentContributor',
    freezeTableName: true,
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['incident_id', 'user_id'],
        name: 'unique_incident_contributor'
      }
    ]
  });

  IncidentContributor.associate = (models) => {
    // IncidentContributor belongs to Incident
    IncidentContributor.belongsTo(models.Incident, {
      foreignKey: 'incident_id',
      targetKey: 'incidentID',
      as: 'incident'
    });

    // IncidentContributor belongs to User
    IncidentContributor.belongsTo(models.User, {
      foreignKey: 'user_id',
      targetKey: 'id',
      as: 'contributor'
    });

    // IncidentContributor belongs to User (as assigner)
    IncidentContributor.belongsTo(models.User, {
      foreignKey: 'assigned_by',
      targetKey: 'id',
      as: 'assigner'
    });
  };

  return IncidentContributor;
}; 