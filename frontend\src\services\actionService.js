import axios from 'axios';
import { getApiBaseUrl } from "@/utils/api-config";
const API_BASE_URL = getApiBaseUrl();
const API_URL = `${API_BASE_URL}/actions`;

// Get all actions
const getAllActions = async () => {
  try {
    const response = await axios.get(API_URL, {
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Get all actions for an action plan
const getActionsByActionPlanId = async (actionPlanId) => {
  try {
    const response = await axios.get(`${API_URL}/action-plan/${actionPlanId}`, {
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Get action by ID
const getActionById = async (id) => {
  try {
    // Check if id is undefined or null
    if (!id) {
      console.warn('getActionById called with undefined or null id');
      return { data: null, success: false, message: 'Invalid action ID' };
    }
    
    const response = await axios.get(`${API_URL}/${id}`, {
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Create new action
const createAction = async (actionData) => {
  try {
    const response = await axios.post(API_URL, actionData, {
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Update action
const updateAction = async (id, actionData) => {
  try {
    const response = await axios.put(`${API_URL}/${id}`, actionData, {
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Delete action
const deleteAction = async (id) => {
  try {
    const response = await axios.delete(`${API_URL}/${id}`, {
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Delete multiple actions
const deleteMultipleActions = async (ids) => {
  try {
    const response = await axios.post(`${API_URL}/delete-multiple`, { ids }, {
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

const actionService = {
  getAllActions,
  getActionsByActionPlanId,
  getActionById,
  createAction,
  updateAction,
  deleteAction,
  deleteMultipleActions
};

export default actionService;
