const { sequelize } = require('../index');

const getLossDistributionRapport = async () => {
    const query = `
        SELECT 
            r."impact" AS "impact",
            COUNT(i."incidentID") AS "Probability",
            SUM(COALESCE(i."grossLoss", 0)) AS "TotalGrossLoss",
            SUM(COALESCE(i."recoveries", 0)) AS "TotalRecoveries",
            SUM(COALESCE(i."grossLoss", 0) - COALESCE(i."recoveries", 0)) AS "TotalNetLoss"
        FROM 
            "Risk" r
        LEFT JOIN 
            "Incident" i ON r."riskID" = i."riskID"
        GROUP BY 
            r."impact"
        ORDER BY 
            CASE 
                WHEN r."impact" = 'Very Low' THEN 1
                WHEN r."impact" = 'Low' THEN 2
                WHEN r."impact" = 'Medium' THEN 3
                WHEN r."impact" = 'High' THEN 4
                WHEN r."impact" = 'Very High' THEN 5
            END;
    `;
    try {
        const results = await sequelize.query(query, { 
            type: sequelize.QueryTypes.SELECT 
        });
        return results;
    } catch (error) {
        console.error('Error executing LossDistributionRapport query:', error);
        throw error;
    }
};

module.exports = { getLossDistributionRapport };