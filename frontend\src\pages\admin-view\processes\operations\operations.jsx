import { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Loader2, Trash2, ArrowUpDown, Workflow, Filter, ChevronUp } from 'lucide-react';
import { useSelector } from 'react-redux';
import { hasPermission } from '@/store/auth-slice';
import PageHeader from '@/components/ui/page-header';
import axios from 'axios';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { getApiBaseUrl } from "@/utils/api-config";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import TablePagination from "../../../../components/ui/table-pagination";
import FilterPanel from "../../../../components/ui/filter-panel";
import operationIcon from '@/assets/operation.png';

function OperationsManagement() {
  const navigate = useNavigate();
  const canCreate = useSelector(state => hasPermission(state, 'create'));
  const canDelete = useSelector(state => hasPermission(state, 'delete'));
  const [operations, setOperations] = useState([]);
  const [organizationalProcesses, setOrganizationalProcesses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedOperations, setSelectedOperations] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const API_BASE_URL = getApiBaseUrl();
  // Filter states
  const [filters, setFilters] = useState({
    parentOrganizationalProcessID: 'all',
  });

  // Clear all filters
  const clearFilters = () => {
    console.log('Clearing all filters');
    const clearedFilters = {
      parentOrganizationalProcessID: 'all',
    };
    setFilters(clearedFilters);
    console.log('Current filters before clearing:', filters);
    console.log('Filters cleared to:', clearedFilters);
  };

  const [newOperation, setNewOperation] = useState({
    operationID: '',
    name: '',
    code: '',
    comment: '',
    parentOrganizationalProcess: null,
  });

  const orgProcessMap = useMemo(() => {
    return new Map(organizationalProcesses.map(p => [p.organizationalProcessID, p.name]));
  }, [organizationalProcesses]);

  const fetchOperations = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/operations`, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
      });
      if (response.data.success) {
        setOperations(response.data.data);
      }
    } catch (_error) {
      toast.error(_error.message || "Failed to fetch operations");
    } finally {
      setLoading(false);
    }
  };

  const fetchOrganizationalProcesses = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/organizationalProcesses`, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
      });
      if (response.data.success) {
        setOrganizationalProcesses(response.data.data);
      }
    } catch (_error) {
      toast.error(_error.message || "Failed to fetch organizational processes");
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        await Promise.all([
          fetchOperations(),
          fetchOrganizationalProcesses(),
        ]);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    const operationToCreate = {
      ...newOperation,
      operationID: newOperation.operationID || `OP_${Date.now()}`,
      code: newOperation.code || null,
      comment: newOperation.comment || null,
      parentOrganizationalProcess: newOperation.parentOrganizationalProcess || null,
    };

    try {
      setSubmitting(true);
      const response = await axios.post(
        `${API_BASE_URL}/operations`,
        operationToCreate,
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
          timeout: 60000,
        }
      );

      if (response.data.success) {
        toast.success("Operation created successfully");
        setNewOperation({
          operationID: '',
          name: '',
          code: '',
          comment: '',
          parentOrganizationalProcess: null,
        });
        setIsOpen(false);
        await fetchOperations();
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "Failed to create operation");
    } finally {
      setSubmitting(false);
    }
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedOperations(currentOperations.map(operation => operation.operationID));
    } else {
      setSelectedOperations([]);
    }
  };

  const handleSelectOperation = (id) => {
    setSelectedOperations(prev => {
      if (prev.includes(id)) {
        return prev.filter(item => item !== id);
      }
      return [...prev, id];
    });
  };

  const handleRowClick = (id) => {
    // Get the current path to determine if we're in audit view
    const isAuditView = window.location.pathname.startsWith('/audit');
    const basePath = isAuditView ? '/audit' : '/admin';
    // Navigate to the operation edit page with overview tab
    navigate(`${basePath}/processes/operations/${id}/overview`);
  };

  const handleDeleteSelected = async () => {
    if (selectedOperations.length === 0) {
      toast.error("No operations selected");
      return;
    }

    if (window.confirm(`Are you sure you want to delete ${selectedOperations.length} selected operation(s)?`)) {
      try {
        setLoading(true);
        await Promise.all(
          selectedOperations.map(id =>
            axios.delete(`${API_BASE_URL}/operations/${id}`, {
              withCredentials: true,
            })
          )
        );
        toast.success("Selected operations deleted successfully");
        setSelectedOperations([]);
        await fetchOperations();
      } catch (_error) {
        toast.error(_error.message || "Failed to delete some operations");
      } finally {
        setLoading(false);
      }
    }
  };

  // Filter operations based on search query and filters
  const filteredOperations = useMemo(() => {
    return operations.filter((operation) => {
      // Apply search query filter
      if (searchQuery) {
        const matchesSearch = Object.values(operation).some(value =>
          value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
        );
        if (!matchesSearch) return false;
      }

      // Apply parent organizational process filter
      if (filters.parentOrganizationalProcessID && filters.parentOrganizationalProcessID !== 'all') {
        console.log('Parent Organizational Process filter:', {
          filterValue: filters.parentOrganizationalProcessID,
          operationValue: operation.parentOrganizationalProcess,
          match: operation.parentOrganizationalProcess === filters.parentOrganizationalProcessID
        });
        if (operation.parentOrganizationalProcess !== filters.parentOrganizationalProcessID) return false;
      }

      return true;
    });
  }, [operations, searchQuery, filters]);

  const sortedOperations = [...filteredOperations].sort((a, b) => {
    if (!sortConfig.key) return 0;

    let aValue, bValue;
    if (sortConfig.key === 'parentOrganizationalProcess') {
      aValue = orgProcessMap.get(a.parentOrganizationalProcess) || '';
      bValue = orgProcessMap.get(b.parentOrganizationalProcess) || '';
    } else {
      aValue = a[sortConfig.key];
      bValue = b[sortConfig.key];
    }

    if (aValue === null || aValue === undefined) aValue = '';
    if (bValue === null || bValue === undefined) bValue = '';

    return sortConfig.direction === 'asc'
      ? aValue.toString().localeCompare(bValue.toString(), undefined, { numeric: true })
      : bValue.toString().localeCompare(aValue.toString(), undefined, { numeric: true });
  });

  const handleSort = (key) => {
    setSortConfig((prevConfig) => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const totalPages = Math.ceil(sortedOperations.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentOperations = sortedOperations.slice(indexOfFirstItem, indexOfLastItem);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const columns = [
    { key: 'name', label: 'Name', sortable: true },
    { key: 'code', label: 'Code', sortable: true },
    { key: 'parentOrganizationalProcess', label: 'Parent Organizational Process', sortable: true },
    { key: 'comment', label: 'Comment', sortable: true },
  ];

  return (
    <div className="p-6">
      <PageHeader
        title="Operations Management"
        description="Define and manage operations linked to your organizational processes."
        section="Processes"
        currentPage="Operations"
        searchPlaceholder="Search operations..."
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
        icon={Workflow}
      />

      {/* Filter Panel */}
      <div className="mb-6 filter-panel-container">
        <FilterPanel
          filters={[
            {
              id: "parentOrganizationalProcessID",
              label: "Parent Organizational Process",
              component: (
                <Select
                  value={filters.parentOrganizationalProcessID}
                  onValueChange={(value) => setFilters({ ...filters, parentOrganizationalProcessID: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select parent org. process" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    {organizationalProcesses && organizationalProcesses.map((process) => (
                      <SelectItem key={process.organizationalProcessID} value={process.organizationalProcessID}>
                        {process.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
          ]}
          activeFilters={filters}
          onClearFilters={clearFilters}
        />
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end items-center gap-3 mb-4">
        {selectedOperations.length > 0 && (
          <Button
            variant="outline"
            className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
              canDelete && !loading
                ? 'bg-[#F62D51]/90 text-white hover:bg-[#F62D51] hover:shadow-md'
                : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
            } flex items-center gap-2 px-6 py-2 font-semibold`}
            onClick={handleDeleteSelected}
            disabled={!canDelete || loading}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                Delete ({selectedOperations.length})
              </>
            )}
          </Button>
        )}
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          {canCreate && (
            <DialogTrigger asChild>
              <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add Operation
              </Button>
            </DialogTrigger>
          )}
          <DialogContent className="max-w-3xl p-8">
            <DialogHeader>
              <DialogTitle>Add New Operation</DialogTitle>
              <DialogDescription>
                Fill in the details to create a new operation.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-8">
              <div className="grid grid-cols-2 gap-6">
                <div className="flex flex-col">
                  <Label htmlFor="name" className="mb-2">Name *</Label>
                  <Input
                    id="name"
                    value={newOperation.name}
                    onChange={(e) => setNewOperation({
                      ...newOperation,
                      name: e.target.value
                    })}
                    placeholder="Enter operation name"
                    required
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="code" className="mb-2">Code</Label>
                  <Input
                    id="code"
                    value={newOperation.code}
                    onChange={(e) => setNewOperation({
                      ...newOperation,
                      code: e.target.value
                    })}
                    placeholder="Enter operation code"
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="parentOrganizationalProcess" className="mb-2">Parent Organizational Process</Label>
                  <Select
                    value={newOperation.parentOrganizationalProcess || "null"}
                    onValueChange={(value) => setNewOperation({
                      ...newOperation,
                      parentOrganizationalProcess: value === "null" ? null : value
                    })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select parent organizational process" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="null">None</SelectItem>
                      {organizationalProcesses.map((process) => (
                        <SelectItem
                          key={process.organizationalProcessID}
                          value={process.organizationalProcessID}
                        >
                          {process.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex flex-col">
                <Label htmlFor="comment" className="mb-2">Comment</Label>
                <Textarea
                  id="comment"
                  value={newOperation.comment}
                  onChange={(e) => setNewOperation({
                    ...newOperation,
                    comment: e.target.value
                  })}
                  placeholder="Enter comment"
                  className="w-full h-24 resize-y"
                />
              </div>
              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-[#F62D51] hover:bg-red-700"
                  disabled={submitting}
                >
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Operation'
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {loading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                      <Checkbox
                        checked={selectedOperations.length === currentOperations.length && currentOperations.length > 0}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all"
                      />
                    </th>
                    {columns.map((column) => (
                      <th
                        key={column.key}
                        className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100"
                        onClick={() => column.sortable && handleSort(column.key)}
                      >
                        <div className="flex items-center gap-1">
                          {column.label}
                          {sortConfig.key === column.key && (
                            <ArrowUpDown className="w-4 h-4" />
                          )}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {currentOperations.map((operation, index) => {
                    const parentOrgName = orgProcessMap.get(operation.parentOrganizationalProcess);
                    return (
                      <tr
                        key={operation.operationID}
                        className={`hover:bg-gray-50 cursor-pointer ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                        onClick={() => handleRowClick(operation.operationID)}
                      >
                        <td className="px-6 py-4">
                          <Checkbox
                            checked={selectedOperations.includes(operation.operationID)}
                            onCheckedChange={() => handleSelectOperation(operation.operationID)}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </td>

                        <td className="px-6 py-4 text-sm font-bold text-[#242A33] whitespace-nowrap">
                          <div className="flex items-center gap-2">
                            <img
                              src={operationIcon}
                              alt="operation"
                              className="w-5 h-5 object-contain"
                            />
                            <span>{operation.name}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                          {operation.code || '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                          {parentOrgName || '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                          {operation.comment || '-'}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">
                {filteredOperations.length} operation{filteredOperations.length !== 1 ? 's' : ''} found
              </span>
              {/* Show filter badge if any filter is active */}
              {filters.parentOrganizationalProcessID !== 'all' && (
                <Badge
                  variant="outline"
                  className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 cursor-pointer"
                  onClick={() => {
                    // Toggle filter panel visibility
                    const filterPanel = document.querySelector('.filter-panel-container');
                    if (filterPanel) {
                      filterPanel.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                >
                  Filtered
                  <ChevronUp className="ml-1 h-3 w-3" />
                </Badge>
              )}
            </div>
          </div>

          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={filteredOperations.length}
            onPageChange={handlePageChange}
            onItemsPerPageChange={(value) => {
              setItemsPerPage(value);
              setCurrentPage(1);
            }}
            startIndex={indexOfFirstItem}
            endIndex={indexOfLastItem}
          />
        </>
      )}
    </div>
  );
}

export default OperationsManagement;
