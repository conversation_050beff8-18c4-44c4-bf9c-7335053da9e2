'use strict';

module.exports = (sequelize, DataTypes) => {
  const AuditPlan = sequelize.define('AuditPlan', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    status: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'Planned'
    },
    datedebut: {
      type: DataTypes.DATE,
      allowNull: true
    },
    datefin: {
      type: DataTypes.DATE,
      allowNull: true,
      validate: {
        isAfterDateDebut(value) {
          if (value && this.datedebut && new Date(value) <= new Date(this.datedebut)) {
            throw new Error('Date de fin doit être après la date de début');
          }
        }
      }
    },
    avancement: {
      type: DataTypes.STRING,
      allowNull: true
    },
    calendrier: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 2000,
        max: 2100
      }
    },
    directeuraudit: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  }, {
    tableName: 'AuditPlans',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['id']
      },
      {
        fields: ['directeuraudit'] // For user lookups
      },
      {
        fields: ['status'] // For filtering by status
      },
      {
        fields: ['datedebut', 'datefin'] // For date range queries
      },
      {
        fields: ['calendrier'] // For filtering by year
      }
    ]
  });

  AuditPlan.associate = function(models) {
    AuditPlan.belongsTo(models.User, {
      foreignKey: 'directeuraudit',
      as: 'director'
    });
  };

  return AuditPlan;
};

