const express = require('express');
const router = express.Router();
const contributorController = require('../../controllers/incidents/contributor-controller');
const { authenticateToken, authorizeRoles } = require('../../middleware/auth');

/**
 * @route   GET /api/incidents/:incidentId/contributors
 * @desc    Get all contributors for an incident
 * @access  Private
 */
router.get('/:incidentId/contributors', 
  authenticateToken, 
  contributorController.getIncidentContributors
);

/**
 * @route   POST /api/incidents/:incidentId/contributors
 * @desc    Assign a contributor to an incident
 * @access  Private - Higher roles only
 */
router.post('/:incidentId/contributors', 
  authenticateToken, 
  authorizeRoles(['grc_admin', 'grc_manager', 'incident_manager']), 
  contributorController.assignContributor
);

/**
 * @route   DELETE /api/incidents/:incidentId/contributors/:contributorId
 * @desc    Remove a contributor from an incident
 * @access  Private - Higher roles only
 */
router.delete('/:incidentId/contributors/:contributorId', 
  authenticateToken, 
  authorizeRoles(['grc_admin', 'grc_manager', 'incident_manager']), 
  contributorController.removeContributor
);

/**
 * @route   GET /api/incidents/:incidentId/contributors/check
 * @desc    Check if a user is a contributor to an incident
 * @access  Private
 */
router.get('/:incidentId/contributors/check', 
  authenticateToken, 
  contributorController.checkContributor
);

module.exports = router; 