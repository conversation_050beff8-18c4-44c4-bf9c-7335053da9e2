const express = require('express');
const router = express.Router();
const notificationController = require('../../controllers/notifications/notification-controller');
const authMiddleware = require('../../middleware/auth');

// All routes are protected with auth middleware
router.use(authMiddleware.verifyToken);

// Get all notifications for the authenticated user
router.get('/', notificationController.getUserNotifications);

// Mark a notification as read
router.put('/:notificationId/mark-read', notificationController.markAsRead);

// Mark all notifications as read
router.put('/mark-all-read', notificationController.markAllAsRead);

// Delete a notification
router.delete('/:notificationId', notificationController.deleteNotification);

module.exports = router; 