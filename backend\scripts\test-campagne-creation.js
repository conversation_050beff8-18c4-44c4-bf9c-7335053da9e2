const db = require('../models');

async function testCampagneCreation() {
  try {
    console.log('🧪 Testing campagne creation functionality...');
    
    // Test database connection
    await db.sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Check if models are loaded
    console.log('📋 Available models:', Object.keys(db).filter(key => !['sequelize', 'Sequelize'].includes(key)));
    
    // Sync tables if needed
    if (db.Campagne) {
      await db.Campagne.sync({ alter: true });
      console.log('✅ Campagne table synced');
    }
    
    if (db.UserCampagne) {
      await db.UserCampagne.sync({ alter: true });
      console.log('✅ UserCampagne table synced');
    }
    
    // Test creating a campagne
    console.log('🧪 Testing campagne creation...');
    const testCampagne = await db.Campagne.create({
      name: 'Test Campagne Auto',
      code: 'TEST-AUTO-001',
      description: 'Campagne de test automatique',
      statut: 'planifié'
    });
    
    console.log('✅ Test campagne created:', {
      id: testCampagne.campagneID,
      name: testCampagne.name,
      code: testCampagne.code,
      statut: testCampagne.statut
    });
    
    // Test finding users (if any exist)
    const users = await db.User.findAll({ limit: 3 });
    console.log(`📋 Found ${users.length} users in database`);
    
    if (users.length > 0) {
      // Test user assignment
      console.log('🧪 Testing user assignment...');
      const userAssignments = users.map(user => ({
        userID: user.id,
        campagneID: testCampagne.campagneID,
        assignedBy: users[0].id, // Use first user as assigner
        assignedAt: new Date()
      }));
      
      await db.UserCampagne.bulkCreate(userAssignments);
      console.log(`✅ Assigned ${users.length} users to campagne`);
      
      // Test fetching campagne with users
      const campagneWithUsers = await db.Campagne.findByPk(testCampagne.campagneID, {
        include: [
          {
            model: db.User,
            as: 'assignedUsers',
            attributes: ['id', 'username', 'email'],
            through: {
              attributes: ['assignedAt', 'assignedBy']
            }
          }
        ]
      });
      
      console.log('✅ Campagne with users:', {
        id: campagneWithUsers.campagneID,
        name: campagneWithUsers.name,
        assignedUsers: campagneWithUsers.assignedUsers.length
      });
    }
    
    // Clean up test data
    console.log('🧹 Cleaning up test data...');
    await db.UserCampagne.destroy({
      where: { campagneID: testCampagne.campagneID }
    });
    await testCampagne.destroy();
    console.log('✅ Test data cleaned up');
    
    console.log('✅ All tests passed! Campagne functionality is working correctly.');
    
  } catch (error) {
    console.error('❌ Error testing campagne creation:', error);
  } finally {
    // Close the database connection
    await db.sequelize.close();
    console.log('Database connection closed');
  }
}

// Run the test
testCampagneCreation();
