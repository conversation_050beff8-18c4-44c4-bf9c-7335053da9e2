const express = require('express');
const router = express.Router();
const {
  getResponsesByFicheId,
  getResponseById,
  upsertResponse,
  bulkUpsertResponses,
  deleteResponse,
  deleteResponsesByFicheId
} = require('../../controllers/audit/audit-fiche-test-response-controller');

// Get all responses for a fiche de travail
router.get('/fiche/:ficheDeTravailID', getResponsesByFicheId);

// Get response by ID
router.get('/:id', getResponseById);

// Create or update a response (upsert)
router.post('/upsert', upsertResponse);

// Bulk upsert responses
router.post('/bulk-upsert', bulkUpsertResponses);

// Delete a response
router.delete('/:id', deleteResponse);

// Delete all responses for a fiche de travail
router.delete('/fiche/:ficheDeTravailID', deleteResponsesByFicheId);

module.exports = router; 