module.exports = (sequelize, DataTypes) => {
  const IncidentGain = sequelize.define('IncidentGain', {
    gainID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    amount: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
    localAmount: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    currency: {
      type: DataTypes.STRING(10),
      allowNull: true,
      defaultValue: 'XOF',
    },
    incidentID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Incident',
        key: 'incidentID',
      },
    },
  }, {
    tableName: 'incident_gain',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['gainID'],
      },
      {
        fields: ['incidentID'],
      },
    ],
  });

  IncidentGain.associate = (models) => {
    // IncidentGain belongs to Incident
    IncidentGain.belongsTo(models.Incident, {
      foreignKey: 'incidentID',
      as: 'incident',
      onDelete: 'CASCADE',
    });
  };

  return IncidentGain;
};
