import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

const API_BASE_URL = getApiBaseUrl();

// Get all responses for a specific campagne
export const getResponsesByCampagneId = async (campagneId) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/campagnes/${campagneId}/responses`, {
      withCredentials: true
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching campagne responses:', error);
    throw error;
  }
};

// Get questions for a campagne (from the associated control)
export const getQuestionsByCampagneId = async (campagneId) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/campagnes/${campagneId}/questions`, {
      withCredentials: true
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching campagne questions:', error);
    throw error;
  }
};

// Save or update a response
export const upsertCampagneResponse = async (campagneId, responseData) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/campagnes/${campagneId}/responses`, responseData, {
      withCredentials: true
    });

    return response.data;
  } catch (error) {
    console.error('Error saving campagne response:', error);
    throw error;
  }
};

// Delete a response
export const deleteCampagneResponse = async (campagneId, responseId) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/campagnes/${campagneId}/responses/${responseId}`, {
      withCredentials: true
    });

    return response.data;
  } catch (error) {
    console.error('Error deleting campagne response:', error);
    throw error;
  }
};
