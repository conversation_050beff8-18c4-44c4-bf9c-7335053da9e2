'use strict';

module.exports = (sequelize, DataTypes) => {
  const AuditRecommendation = sequelize.define('AuditRecommendation', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    priorite: {
      type: DataTypes.ENUM('trés faible', 'faible', 'moyen', 'fort', 'très fort'),
      allowNull: true
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true
    },
    planification: {
      type: DataTypes.STRING,
      allowNull: true
    },
    code: {
      type: DataTypes.STRING,
      allowNull: true
    },
    details: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    dateLimite: {
      type: DataTypes.DATE,
      allowNull: true
    },
    actionPlanID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'action_plan',
        key: 'actionPlanID'
      }
    }
  }, {
    tableName: 'AuditRecommendations',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['id']
      },
      {
        fields: ['actionPlanID'] // For foreign key lookups
      },
      {
        fields: ['priorite'] // For filtering by priority
      },
      {
        fields: ['code'] // For filtering by code
      }
    ]
  });

  AuditRecommendation.associate = function(models) {
    // Many-to-many with AuditConstat
    AuditRecommendation.belongsToMany(models.AuditConstat, {
      through: 'ConstatRecommendation',
      foreignKey: 'recommendationId',
      otherKey: 'constatId',
      as: 'constats'
    });

    // One-to-one with ActionPlan
    AuditRecommendation.belongsTo(models.ActionPlan, {
      foreignKey: 'actionPlanID',
      as: 'actionPlan'
    });

    AuditRecommendation.hasMany(models.EquipeIntervenante, {
      foreignKey: 'auditRecommendationId',
      as: 'equipeIntervenantes'
    });
  };

  return AuditRecommendation;
};