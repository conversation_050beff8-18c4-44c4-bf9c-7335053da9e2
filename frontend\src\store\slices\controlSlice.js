import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import controlService from '../../services/controlService';
import { toast } from 'sonner';

// Initial state
const initialState = {
  controls: [],
  currentControl: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: ''
};

// Get all controls
export const getAllControls = createAsyncThunk(
  'controls/getAll',
  async (_, thunkAPI) => {
    try {
      return await controlService.getAllControls();
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch controls';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get control by ID
export const getControlById = createAsyncThunk(
  'controls/getById',
  async (id, thunkAPI) => {
    try {
      console.log('Fetching control with ID:', id);
      const result = await controlService.getControlById(id);
      console.log('Control fetched successfully:', result);
      return result;
    } catch (error) {
      console.error('Error fetching control:', error);
      const message = error.response?.data?.message || error.message || 'Failed to fetch control';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create new control
export const createControl = createAsyncThunk(
  'controls/create',
  async (controlData, thunkAPI) => {
    try {
      return await controlService.createControl(controlData);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to create control';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update control
export const updateControl = createAsyncThunk(
  'controls/update',
  async ({ id, controlData }, thunkAPI) => {
    try {
      // The controlService.updateControl now handles foreign key constraint errors internally
      // by retrying with the problematic field set to null
      return await controlService.updateControl(id, controlData);
    } catch (error) {
      console.error('Error in updateControl thunk:', error);
      const message = error.response?.data?.message || error.message || 'Failed to update control';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete control
export const deleteControl = createAsyncThunk(
  'controls/delete',
  async (id, thunkAPI) => {
    try {
      return await controlService.deleteControl(id);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete control';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete multiple controls
export const deleteMultipleControls = createAsyncThunk(
  'controls/deleteMultiple',
  async (ids, thunkAPI) => {
    try {
      return await controlService.deleteMultipleControls(ids);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete controls';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Control slice
const controlSlice = createSlice({
  name: 'control',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all controls
      .addCase(getAllControls.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllControls.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.controls = action.payload.data;
      })
      .addCase(getAllControls.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Get control by ID
      .addCase(getControlById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getControlById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentControl = action.payload.data;
      })
      .addCase(getControlById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Create control
      .addCase(createControl.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createControl.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.controls.push(action.payload.data);
        toast.success('Control created successfully');
      })
      .addCase(createControl.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Update control
      .addCase(updateControl.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateControl.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.controls = state.controls.map(control =>
          control.controlID === action.payload.data.controlID ? action.payload.data : control
        );

        state.currentControl = action.payload.data;
        // Removed toast to prevent spam during auto-save
      })
      .addCase(updateControl.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;

        // Check if the error is related to a foreign key constraint
        if (typeof action.payload === 'string' && action.payload.includes('foreign key constraint')) {
          // Extract the field name from the error message
          const match = action.payload.match(/Key \(([^)]+)\)=\(([^)]+)\)/);
          if (match && match.length >= 3) {
            const fieldName = match[1];
            const fieldValue = match[2];
            toast.error(`The selected ${fieldName} (${fieldValue}) does not exist. It will be set to none.`);
          } else {
            toast.error('A reference field is invalid. It will be set to none.');
          }
        } else {
          toast.error(action.payload);
        }
      })

      // Delete control
      .addCase(deleteControl.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteControl.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.controls = state.controls.filter(control => control.controlID !== action.meta.arg);
        toast.success('Control deleted successfully');
      })
      .addCase(deleteControl.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Delete multiple controls
      .addCase(deleteMultipleControls.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteMultipleControls.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.controls = state.controls.filter(control => !action.meta.arg.includes(control.controlID));
        toast.success('Controls deleted successfully');
      })
      .addCase(deleteMultipleControls.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      });
  }
});

export const { reset } = controlSlice.actions;
export default controlSlice.reducer;
