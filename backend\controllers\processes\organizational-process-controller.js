const { OrganizationalProcess } = require('../../models');

// Get all organizational processes
const getAllOrganizationalProcesses = async (req, res) => {
  try {
    const organizationalProcesses = await OrganizationalProcess.findAll();
    res.json({
      success: true,
      data: organizationalProcesses
    });
  } catch (error) {
    console.error('Error fetching organizational processes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch organizational processes',
      error: error.message
    });
  }
};

// Create new organizational process
const createOrganizationalProcess = async (req, res) => {
  try {
    const {
      organizationalProcessID,
      name,
      code,
      comment,
      parentOrganizationalProcess,
      parentBusinessProcess
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name'].filter(field => !req.body[field])
      });
    }

    const organizationalProcess = await OrganizationalProcess.create({
      organizationalProcessID: organizationalProcessID || `OP_${Date.now()}`,
      name,
      code: code || null,
      comment: comment || null,
      parentOrganizationalProcess: parentOrganizationalProcess || null,
      parentBusinessProcess: parentBusinessProcess || null
    });

    return res.status(201).json({
      success: true,
      message: 'Organizational process created successfully',
      data: organizationalProcess
    });
  } catch (error) {
    console.error('Error creating organizational process:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Failed to create organizational process'
    });
  }
};

// Get organizational process by ID
const getOrganizationalProcessById = async (req, res) => {
  try {
    const { id } = req.params;
    const organizationalProcess = await OrganizationalProcess.findByPk(id);

    if (!organizationalProcess) {
      return res.status(404).json({
        success: false,
        message: 'Organizational process not found'
      });
    }

    res.json({
      success: true,
      data: organizationalProcess
    });
  } catch (error) {
    console.error('Error fetching organizational process:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch organizational process'
    });
  }
};

// Update organizational process
const updateOrganizationalProcess = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      code,
      comment,
      parentOrganizationalProcess,
      parentBusinessProcess
    } = req.body;

    const organizationalProcess = await OrganizationalProcess.findByPk(id);

    if (!organizationalProcess) {
      return res.status(404).json({
        success: false,
        message: 'Organizational process not found'
      });
    }

    // Update fields
    await organizationalProcess.update({
      name: name || organizationalProcess.name,
      code: code !== undefined ? code : organizationalProcess.code,
      comment: comment !== undefined ? comment : organizationalProcess.comment,
      parentOrganizationalProcess: parentOrganizationalProcess !== undefined ? parentOrganizationalProcess : organizationalProcess.parentOrganizationalProcess,
      parentBusinessProcess: parentBusinessProcess !== undefined ? parentBusinessProcess : organizationalProcess.parentBusinessProcess
    });

    res.json({
      success: true,
      message: 'Organizational process updated successfully',
      data: organizationalProcess
    });
  } catch (error) {
    console.error('Error updating organizational process:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update organizational process'
    });
  }
};

// Delete organizational process
const deleteOrganizationalProcess = async (req, res) => {
  try {
    const { id } = req.params;
    const organizationalProcess = await OrganizationalProcess.findByPk(id);

    if (!organizationalProcess) {
      return res.status(404).json({
        success: false,
        message: 'Organizational process not found'
      });
    }

    await organizationalProcess.destroy();

    res.json({
      success: true,
      message: 'Organizational process deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting organizational process:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete organizational process'
    });
  }
};

module.exports = {
  getAllOrganizationalProcesses,
  createOrganizationalProcess,
  getOrganizationalProcessById,
  updateOrganizationalProcess,
  deleteOrganizationalProcess
};
