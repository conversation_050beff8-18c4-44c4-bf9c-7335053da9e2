'use strict';

module.exports = (sequelize, DataTypes) => {
  const AuditConstat = sequelize.define('AuditConstat', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('Point Fort', 'Point Faible'),
      allowNull: false,
      defaultValue: 'Point Faible'
    },
    impact: {
      type: DataTypes.ENUM('tres faible', 'faible', 'moyen', 'fort', 'tres fort'),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    causes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    auditActivityID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'AuditActivities',
        key: 'id'
      }
    }
  }, {
    tableName: 'AuditConstats',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['id']
      },
      {
        fields: ['auditActivityID'] // For foreign key lookups
      },
      {
        fields: ['type'] // For filtering by type
      },
      {
        fields: ['impact'] // For filtering by impact
      }
    ]
  });

  AuditConstat.associate = function(models) {
    // AuditConstat belongs to AuditActivity
    AuditConstat.belongsTo(models.AuditActivity, {
      foreignKey: 'auditActivityID',
      as: 'auditActivity'
    });

    // New association: AuditConstat belongs to many AuditRecommendations
    AuditConstat.belongsToMany(models.AuditRecommendation, {
      through: 'ConstatRecommendation',
      foreignKey: 'constatId',
      otherKey: 'recommendationId',
      as: 'recommendations'
    });

    // Many-to-many with Risk
    AuditConstat.belongsToMany(models.Risk, {
      through: 'ConstatRisk',
      foreignKey: 'constatId',
      otherKey: 'riskID',
      as: 'risks'
    });

    AuditConstat.hasMany(models.EquipeIntervenante, {
      foreignKey: 'auditConstatId',
      as: 'equipeIntervenantes'
    });
  };

  return AuditConstat;
};