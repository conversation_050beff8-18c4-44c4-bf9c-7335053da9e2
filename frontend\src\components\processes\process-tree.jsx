import { useState, useRef, useEffect } from "react";
import { ChevronRight, ChevronDown, FileText } from "lucide-react";
import bpsIcon from '@/assets/BPS.png';
import orgIcon from '@/assets/org.png';
import operationIcon from '@/assets/operation.png';
import riskIcon from '@/assets/risk.png';
import incidentIcon from '@/assets/incident.png';

// Inline styles
const styles = {
  processTreeContainer: {
    width: '100%',
    overflowX: 'auto',
    padding: '20px'
  },
  processTree: {
    display: 'flex',
    flexDirection: 'column',
    minWidth: '800px'
  },
  treeNode: {
    marginBottom: '5px',
    position: 'relative'
  },
  treeChildren: {
    position: 'relative',
    paddingLeft: '25px',
    marginLeft: '5px',
    borderLeft: '1px dashed #a0aec0',
    marginTop: '5px',
    marginBottom: '5px'
  },
  mindMapContainer: {
    width: '100%',
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden'
  },
  mindMapSvg: {
    width: '100%',
    height: '100%',
    overflow: 'visible'
  }
}

const ProcessTree = ({ data, onNodeSelect }) => {
  const [viewMode, setViewMode] = useState("tree"); // "tree" or "mindmap"

  return (
    <div style={styles.processTreeContainer}>
      <div className="flex justify-between items-center mb-8">
        <div className="bg-white rounded-lg shadow-md p-4 inline-block">
          <h3 className="text-lg font-medium">Process Hierarchy</h3>
        </div>
        <div className="flex space-x-2">
          <button
            className={`px-4 py-2 rounded-md ${viewMode === 'tree' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
            onClick={() => setViewMode("tree")}
          >
            Tree View
          </button>
          <button
            className={`px-4 py-2 rounded-md ${viewMode === 'mindmap' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
            onClick={() => setViewMode("mindmap")}
          >
            Mind Map
          </button>
        </div>
      </div>

      {viewMode === "tree" ? (
        <div style={styles.processTree}>
          {data.map((node) => (
            <TreeNode key={node.id} node={node} level={0} onSelect={onNodeSelect} />
          ))}
        </div>
      ) : (
        <div style={{ height: '600px' }}>
          <MindMapView data={data} onNodeSelect={onNodeSelect} />
        </div>
      )}
    </div>
  );
};

const TreeNode = ({ node, level, onSelect }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const hasChildren = node.children && node.children.length > 0;

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  // Determine the color based on the node type
  const getColor = () => {
    switch (node.type) {
      case "business":
        return "#10B981"; // Green
      case "organizational":
        return "#F97316"; // Orange
      case "operation":
        return "#A855F7"; // Purple
      case "risk":
        return "#F62D51"; // Red
      case "incident":
        return "#3B82F6"; // Blue
      default:
        return "#6B7280"; // Gray
    }
  };

  // Get the appropriate icon based on node type
  const getIcon = () => {
    switch (node.type) {
      case "business":
        return <img src={bpsIcon} className="w-4 h-4" alt="business process" />;
      case "organizational":
        return <img src={orgIcon} className="w-4 h-4" alt="organizational process" />;
      case "operation":
        return <img src={operationIcon} className="w-4 h-4" alt="operation" />;
      case "risk":
        return <img src={riskIcon} className="w-4 h-4" alt="risk" />;
      case "incident":
        return <img src={incidentIcon} className="w-4 h-4" alt="incident" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const color = getColor();
  const nodeIcon = getIcon();

  return (
    <div style={{ ...styles.treeNode }}>
      <div className="flex items-center mb-1">
        {hasChildren ? (
          <button
            onClick={toggleExpand}
            className="mr-0.5 focus:outline-none"
            aria-label={isExpanded ? "Collapse" : "Expand"}
          >
            {isExpanded ? (
              <ChevronDown className="w-4 h-4 text-gray-500" />
            ) : (
              <ChevronRight className="w-4 h-4 text-gray-500" />
            )}
          </button>
        ) : (
          <div className="w-4 h-4 mr-0.5" /> // Empty space for alignment
        )}

        <div
          className="flex items-center py-1 px-2 rounded-md cursor-pointer hover:bg-gray-50"
          style={{
            backgroundColor: `${color}10`, // Very light background
            borderLeft: `3px solid ${color}`,
            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
            width: `calc(100% - ${level * 10}px)`,
            transition: 'all 0.2s ease'
          }}
          onClick={() => onSelect && onSelect(node.id)}
        >
          <span className="mr-1">{nodeIcon}</span>
          <div className="flex items-center">
            <span className="font-medium" style={{ color }}>
              {node.name}
            </span>
          </div>
        </div>
      </div>

      {hasChildren && isExpanded && (
        <div style={styles.treeChildren}>
          {node.children.map((childNode) => (
            <TreeNode key={childNode.id} node={childNode} level={level + 1} onSelect={onSelect} />
          ))}
        </div>
      )}
    </div>
  );
};

const MindMapView = ({ data, onNodeSelect }) => {
  const svgRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 1000, height: 800 });

  useEffect(() => {
    if (svgRef.current) {
      const updateDimensions = () => {
        const container = svgRef.current.parentElement;
        if (container) {
          setDimensions({
            width: Math.max(container.clientWidth, 1000),
            height: Math.max(container.clientHeight, 800)
          });
        }
      };

      updateDimensions();
      window.addEventListener('resize', updateDimensions);

      return () => window.removeEventListener('resize', updateDimensions);
    }
  }, []);

  // Calculate positions for the mind map nodes
  const centerX = dimensions.width / 2;
  const centerY = dimensions.height / 2;

  // Root node is centered
  const rootNode = {
    x: centerX,
    y: centerY,
    label: "Process Hierarchy",
    type: "root"
  };

  // Position business processes horizontally from the root
  const businessProcessNodes = data.map((bp, index) => {
    const angle = (index / data.length) * Math.PI * 2;
    const distance = 200;

    return {
      x: centerX + Math.cos(angle) * distance,
      y: centerY + Math.sin(angle) * distance,
      label: bp.name,
      type: "business",
      id: bp.id,
      children: bp.children,
      code: bp.code
    };
  });

  // For each business process, position its organizational processes
  const allNodes = [rootNode, ...businessProcessNodes];
  const connections = [];

  // Add connections from root to business processes
  businessProcessNodes.forEach(bpNode => {
    connections.push({
      from: rootNode,
      to: bpNode,
      type: "business"
    });

    // Position organizational processes, risks, and incidents for this business process
    if (bpNode.children && bpNode.children.length > 0) {
      // Separate children by type
      const orgProcesses = bpNode.children.filter(child => child.type === "organizational");
      const directChildren = bpNode.children.filter(child => child.type === "risk" || child.type === "incident");

      // Position direct risks and incidents
      if (directChildren.length > 0) {
        directChildren.forEach((child, dcIndex) => {
          const dcAngle = ((dcIndex / directChildren.length) * Math.PI * 0.5) +
                        (Math.atan2(bpNode.y - centerY, bpNode.x - centerX) + Math.PI * 0.75);
          const dcDistance = 120;

          const dcNode = {
            x: bpNode.x + Math.cos(dcAngle) * dcDistance,
            y: bpNode.y + Math.sin(dcAngle) * dcDistance,
            label: child.name,
            type: child.type,
            id: child.id,
            code: child.code
          };

          allNodes.push(dcNode);
          connections.push({
            from: bpNode,
            to: dcNode,
            type: child.type
          });
        });
      }

      // Position organizational processes
      const orgProcessNodes = orgProcesses.map((orgProcess, index) => {
        const angle = ((index / orgProcesses.length) * Math.PI) +
                      (Math.atan2(bpNode.y - centerY, bpNode.x - centerX));
        const distance = 150;

        const node = {
          x: bpNode.x + Math.cos(angle) * distance,
          y: bpNode.y + Math.sin(angle) * distance,
          label: orgProcess.name,
          type: "organizational",
          id: orgProcess.id,
          children: orgProcess.children,
          code: orgProcess.code
        };

        allNodes.push(node);
        connections.push({
          from: bpNode,
          to: node,
          type: "organizational"
        });

        // Position operations, risks, and incidents for this organizational process
        if (orgProcess.children && orgProcess.children.length > 0) {
          const childNodes = orgProcess.children.map((child, childIndex) => {
            const childAngle = ((childIndex / orgProcess.children.length) * Math.PI) +
                          (Math.atan2(node.y - bpNode.y, node.x - bpNode.x));
            const childDistance = 100;

            const childNode = {
              x: node.x + Math.cos(childAngle) * childDistance,
              y: node.y + Math.sin(childAngle) * childDistance,
              label: child.name,
              type: child.type,
              id: child.id,
              code: child.code,
              children: child.children || []
            };

            allNodes.push(childNode);
            connections.push({
              from: node,
              to: childNode,
              type: child.type
            });

            // If this is an operation with children (risks or incidents), position them
            if (child.type === "operation" && child.children && child.children.length > 0) {
              child.children.forEach((grandchild, gcIndex) => {
                const gcAngle = ((gcIndex / child.children.length) * Math.PI) +
                              (Math.atan2(childNode.y - node.y, childNode.x - node.x));
                const gcDistance = 70;

                const gcNode = {
                  x: childNode.x + Math.cos(gcAngle) * gcDistance,
                  y: childNode.y + Math.sin(gcAngle) * gcDistance,
                  label: grandchild.name,
                  type: grandchild.type,
                  id: grandchild.id,
                  code: grandchild.code
                };

                allNodes.push(gcNode);
                connections.push({
                  from: childNode,
                  to: gcNode,
                  type: grandchild.type
                });
              });
            }

            return childNode;
          });
        }

        return node;
      });
    }
  });

  // Get color based on node type
  const getColor = (type) => {
    switch (type) {
      case "business":
        return "#10B981"; // Green
      case "organizational":
        return "#F97316"; // Orange
      case "operation":
        return "#A855F7"; // Purple
      case "risk":
        return "#F62D51"; // Red
      case "incident":
        return "#3B82F6"; // Blue
      case "root":
        return "#3B82F6"; // Blue
      default:
        return "#6B7280"; // Gray
    }
  };

  // Get the appropriate icon based on node type
  const getIcon = (type) => {
    switch (type) {
      case "business":
        return bpsIcon;
      case "organizational":
        return orgIcon;
      case "operation":
        return operationIcon;
      case "risk":
        return riskIcon;
      case "incident":
        return incidentIcon;
      default:
        return null;
    }
  };

  return (
    <div style={{ ...styles.mindMapContainer, height: "800px" }}>
      <svg
        ref={svgRef}
        width={dimensions.width}
        height={dimensions.height}
        style={styles.mindMapSvg}
      >
        {/* Draw connections first so they appear behind nodes */}
        {connections.map((connection, index) => {
          const color = getColor(connection.type);
          return (
            <path
              key={`connection-${index}`}
              d={`M${connection.from.x},${connection.from.y} C${(connection.from.x + connection.to.x) / 2},${connection.from.y} ${(connection.from.x + connection.to.x) / 2},${connection.to.y} ${connection.to.x},${connection.to.y}`}
              stroke={color}
              strokeWidth="2"
              fill="none"
              strokeDasharray={connection.type === "root" ? "0" : "0"}
            />
          );
        })}

        {/* Draw nodes on top of connections */}
        {allNodes.map((node, index) => {
          const color = getColor(node.type);
          const iconSrc = getIcon(node.type);

          return (
            <g
              key={`node-${index}`}
              transform={`translate(${node.x},${node.y})`}
              className="cursor-pointer"
              onClick={() => onNodeSelect && node.id && onNodeSelect(node.id)}
            >
              {/* Node background */}
              <rect
                x="-60"
                y="-15"
                width="120"
                height="30"
                rx="15"
                ry="15"
                fill={node.type === "root" ? color : "white"}
                stroke={color}
                strokeWidth="1.5"
              />

              {/* Icon if available */}
              {iconSrc && node.type !== "root" && (
                <image
                  href={iconSrc}
                  x="-50"
                  y="-10"
                  height="20"
                  width="20"
                />
              )}

              {/* Node label */}
              <text
                textAnchor="middle"
                dominantBaseline="middle"
                fill={node.type === "root" ? "white" : color}
                fontWeight="500"
                fontSize={node.type === "root" ? "12px" : "10px"}
                x={iconSrc && node.type !== "root" ? "5" : "0"}
              >
                {node.label}
              </text>

              {/* Code display removed */}
            </g>
          );
        })}
      </svg>
    </div>
  );
};

export default ProcessTree;
