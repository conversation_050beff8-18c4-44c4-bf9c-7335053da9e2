/**
 * <PERSON>ript to toggle debug logs in the .env file
 * Run with: node scripts/toggle-debug-logs.js [on|off]
 */

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const envFilePath = path.join(__dirname, '../.env');

// Read the .env file
const envContent = fs.readFileSync(envFilePath, 'utf8');

// Get the command line argument
const arg = process.argv[2]?.toLowerCase();

if (arg !== 'on' && arg !== 'off') {
  console.error('Please specify either "on" or "off" as an argument.');
  console.error('Example: node scripts/toggle-debug-logs.js on');
  process.exit(1);
}

// Set the debug logs value
const debugValue = arg === 'on' ? 'true' : 'false';
const nodeEnv = arg === 'on' ? 'development' : 'production';

// Update the .env file
let updatedContent;

if (envContent.includes('DEBUG_LOGS=')) {
  // Replace existing DEBUG_LOGS value
  updatedContent = envContent.replace(/DEBUG_LOGS=(true|false)/, `DEBUG_LOGS=${debugValue}`);
} else {
  // Add DEBUG_LOGS if it doesn't exist
  updatedContent = `${envContent}\nDEBUG_LOGS=${debugValue}`;
}

if (envContent.includes('NODE_ENV=')) {
  // Replace existing NODE_ENV value
  updatedContent = updatedContent.replace(/NODE_ENV=(development|production)/, `NODE_ENV=${nodeEnv}`);
} else {
  // Add NODE_ENV if it doesn't exist
  updatedContent = `${updatedContent}\nNODE_ENV=${nodeEnv}`;
}

// Write the updated content back to the .env file
fs.writeFileSync(envFilePath, updatedContent);

console.log(`Debug logs turned ${arg.toUpperCase()}`);
console.log(`NODE_ENV set to ${nodeEnv}`);
console.log('Restart the server for changes to take effect.');
