import { useEffect, useState, useCallback } from "react";
import { useParams, useNavigate, Outlet, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
// MODIFY: Import your specific slice actions
import { getItemById, reset } from "@/store/slices/itemSlice";
// MODIFY: Import your specific icons
import { FileText, Edit, Loader2, Package } from "lucide-react";
import DetailHeader from "@/components/ui/detail-header";
import TabBar from "@/components/ui/tabs/tab-bar";
import TabContent from "@/components/ui/tabs/tab-content";

function EditItemTemplate() {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  
  // MODIFY: Update with your specific state slice
  const { currentItem, isLoading, isError, error } = useSelector(
    (state) => state.item
  );

  // Get the current tab from the URL
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    // MODIFY: Update with your specific tab paths
    if (path.includes('/features')) {
      return 'features';
    }
    // Add more tab conditions as needed
    // if (path.includes('/actions')) return "actions";
    return 'overview';
  }, [location.pathname]);

  const [activeTab, setActiveTab] = useState(getCurrentTab());

  // Update active tab when location changes
  useEffect(() => {
    setActiveTab(getCurrentTab());
  }, [location, getCurrentTab]);

  // Fetch item on component mount
  useEffect(() => {
    if (id) {
      // MODIFY: Update with your specific action
      dispatch(getItemById(id));
    }

    // Reset state when component unmounts
    return () => {
      dispatch(reset());
    };
  }, [dispatch, id]);

  // Handle go back
  const handleGoBack = () => {
    // MODIFY: Update with your specific route
    navigate("/admin/data/items");
  };

  // MODIFY: Update with your specific tabs
  const tabs = [
    { id: "overview", label: "Overview", icon: <FileText className="h-4 w-4" /> },
    { id: "features", label: "Features", icon: <Edit className="h-4 w-4" /> },
    // Add more tabs as needed
    // { id: "actions", label: "Actions", icon: <ListChecks className="h-4 w-4" /> },
  ];
  
  // Navigate to tab
  const navigateToTab = (tabId) => {
    // MODIFY: Update with your specific routes
    switch (tabId) {
      case "overview":
        navigate(`/admin/data/items/${id}`);
        break;
      case "features":
        navigate(`/admin/data/items/${id}/features`);
        break;
      // Add more cases as needed
      // case "actions":
      //   navigate(`/admin/data/items/${id}/actions`);
      //   break;
      default:
        navigate(`/admin/data/items/${id}`);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          Error: {error}
        </div>
      </div>
    );
  }

  if (!currentItem) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          Item not found
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      {/* Header */}
      <DetailHeader
        // MODIFY: Update with your specific item properties
        title={currentItem.name}
        icon={<Package className="h-6 w-6 text-[#F62D51]" />}
        // MODIFY: Update with your specific badges if needed
        badges={[
          {
            label: currentItem.status || 'No Status',
            color: `
              ${currentItem.status === 'Active' ? 'bg-green-100 text-green-800' :
              currentItem.status === 'Inactive' ? 'bg-gray-100 text-gray-800' :
              'bg-blue-100 text-blue-800'}
            `
          }
        ]}
        // MODIFY: Update with your specific metadata
        metadata={[
          currentItem.description ? 
            `${currentItem.description.substring(0, 100)}${currentItem.description.length > 100 ? '...' : ''}` : 
            'No description'
        ]}
        onBack={handleGoBack}
        // MODIFY: Update with your specific back label
        backLabel="Back to Items"
      />

      {/* Tabs */}
      <TabBar
        activeTab={activeTab}
        tabs={tabs}
        onTabChange={navigateToTab}
        className="mb-6"
      />

      {/* Tab Content */}
      <TabContent>
        {/* MODIFY: Update with your specific context property name */}
        <Outlet context={{ item: currentItem }} />
      </TabContent>
    </div>
  );
}

export default EditItemTemplate;
