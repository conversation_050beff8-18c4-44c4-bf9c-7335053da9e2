const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllAuditScopes,
  getAuditScopesByMission,
  getAuditScope,
  createAuditScope,
  updateAuditScope,
  deleteAuditScope
} = require('../../controllers/audit/audit-scope-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all audit scopes
router.get('/', authorizeRoles(['audit_director', 'auditor']), getAllAuditScopes);

// Get audit scopes by mission ID
router.get('/mission/:missionId', authorizeRoles(['audit_director', 'auditor']), getAuditScopesByMission);

// Create new audit scope
router.post('/', authorizeRoles(['audit_director', 'auditor']), createAuditScope);

// Get audit scope by ID
router.get('/:id', authorizeRoles(['audit_director', 'auditor']), getAuditScope);

// Update audit scope
router.put('/:id', authorizeRoles(['audit_director', 'auditor']), updateAuditScope);

// Delete audit scope
router.delete('/:id', authorizeRoles(['audit_director', 'auditor']), deleteAuditScope);

module.exports = router;
