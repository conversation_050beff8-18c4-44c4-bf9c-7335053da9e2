const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middleware/auth');
const geminiController = require('../controllers/gemini/gemini-controller');

// CORS headers for all Gemini routes
router.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:5173');
  res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, x-user-id');
  res.header('Access-Control-Allow-Credentials', 'true');
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  next();
});

// Apply authentication middleware
router.use(verifyToken);

// Gemini conversation endpoint
router.get('/conversation/:missionId', geminiController.generateGeminiConversation);

module.exports = router; 