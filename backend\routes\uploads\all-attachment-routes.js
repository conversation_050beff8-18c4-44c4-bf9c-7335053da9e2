const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  uploadFile,
  addReference,
  getAllAttachments,
  getAttachment,
  deleteAttachment,
  downloadAttachment,
  syncAttachments,
  getAttachmentsByAuditActivityID,
  getAttachmentsByAuditConstatID,
  getAttachmentsByAuditFicheDeTravailID,
  getAttachmentsByAuditFicheDeTestID,
  getAttachmentsByAuditMissionID,
  getAttachmentsByAuditScopeID,
  getAttachmentsByActionPlanID,
  getAttachmentsByBusinessProcessID,
  getAttachmentsByControlID,
  getAttachmentsByEntityID,
  getAttachmentsByIncidentID,
  getAttachmentsByOrganizationalProcessID,
  getAttachmentsByRiskID,
  getAttachmentsByCampagneID
} = require('../../controllers/uploads/all-attachment-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Upload a file
router.post('/upload', uploadFile);

// Add an external reference
router.post('/reference', addReference);

// Get all attachments with optional filtering
router.get('/', getAllAttachments);

// Get a specific attachment
router.get('/:id', getAttachment);

// Delete an attachment
router.delete('/:id', deleteAttachment);

// Download an attachment
router.get('/download/:id', downloadAttachment);

// Sync attachments from all tables (admin only)
router.post('/sync', authorizeRoles(['admin', 'grc_admin']), syncAttachments);

// Get attachments by different entity IDs
router.get('/by-audit-activity/:auditActivityID', getAttachmentsByAuditActivityID);
router.get('/by-audit-constat/:auditConstatID', getAttachmentsByAuditConstatID);
router.get('/by-audit-fiche-de-travail/:auditFicheDeTravailID', getAttachmentsByAuditFicheDeTravailID);
router.get('/by-audit-fiche-de-test/:auditFicheDeTestID', getAttachmentsByAuditFicheDeTestID);
router.get('/by-audit-mission/:auditMissionID', getAttachmentsByAuditMissionID);
router.get('/by-audit-scope/:auditScopeID', getAttachmentsByAuditScopeID);
router.get('/by-action-plan/:actionPlanID', getAttachmentsByActionPlanID);
router.get('/by-business-process/:businessProcessID', getAttachmentsByBusinessProcessID);
router.get('/by-control/:controlID', getAttachmentsByControlID);
router.get('/by-entity/:entityID', getAttachmentsByEntityID);
router.get('/by-incident/:incidentID', getAttachmentsByIncidentID);
router.get('/by-organizational-process/:organizationalProcessID', getAttachmentsByOrganizationalProcessID);
router.get('/by-risk/:riskID', getAttachmentsByRiskID);
router.get('/campagne/:campagneID', getAttachmentsByCampagneID);

module.exports = router;