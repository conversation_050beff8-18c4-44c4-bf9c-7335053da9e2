const db = require('../models');
const { ActionPlan } = db;

async function seedActionPlans() {
  try {
    console.log('Starting to seed action plans...');
    
    // Check if action plans already exist
    const existingActionPlans = await ActionPlan.findAll();
    if (existingActionPlans.length > 0) {
      console.log(`${existingActionPlans.length} action plans already exist. Skipping seed.`);
      return;
    }

    // Sample action plans
    const actionPlans = [
      {
        actionPlanID: 'AP_001',
        name: 'Implement New Security Protocol',
        category: 'Security',
        nature: 'Preventive',
        origin: 'Risk Assessment',
        priority: 'High',
        organizationalLevel: 'Local',
        means: 'Internal Resources',
        comment: 'Enhance security measures across all systems',
        plannedBeginDate: new Date('2024-06-01'),
        plannedEndDate: new Date('2024-08-31')
      },
      {
        actionPlanID: 'AP_002',
        name: 'Staff Training Program',
        category: 'Training',
        nature: 'Corrective',
        origin: 'Incident Analysis',
        priority: 'Medium',
        organizationalLevel: 'Global',
        means: 'External Consultant',
        comment: 'Comprehensive training for all staff on new procedures',
        plannedBeginDate: new Date('2024-07-15'),
        plannedEndDate: new Date('2024-09-30')
      },
      {
        actionPlanID: 'AP_003',
        name: 'System Upgrade',
        category: 'IT',
        nature: 'Preventive',
        origin: 'Audit',
        priority: 'High',
        organizationalLevel: 'Local',
        means: 'Mixed',
        comment: 'Upgrade all systems to latest version',
        plannedBeginDate: new Date('2024-05-01'),
        plannedEndDate: new Date('2024-07-31')
      },
      {
        actionPlanID: 'AP_004',
        name: 'Process Documentation',
        category: 'Documentation',
        nature: 'Preventive',
        origin: 'Internal Review',
        priority: 'Low',
        organizationalLevel: 'Global',
        means: 'Internal Resources',
        comment: 'Document all critical processes',
        plannedBeginDate: new Date('2024-06-15'),
        plannedEndDate: new Date('2024-08-15')
      },
      {
        actionPlanID: 'AP_005',
        name: 'Compliance Review',
        category: 'Compliance',
        nature: 'Preventive',
        origin: 'Regulatory Change',
        priority: 'High',
        organizationalLevel: 'Global',
        means: 'External Consultant',
        comment: 'Ensure compliance with new regulations',
        plannedBeginDate: new Date('2024-04-01'),
        plannedEndDate: new Date('2024-05-31')
      }
    ];

    // Create action plans
    await ActionPlan.bulkCreate(actionPlans);
    
    console.log(`Successfully seeded ${actionPlans.length} action plans.`);
  } catch (error) {
    console.error('Error seeding action plans:', error);
  }
}

// If this file is run directly, execute the seed function
if (require.main === module) {
  seedActionPlans()
    .then(() => {
      console.log('Action plan seeding completed.');
      process.exit(0);
    })
    .catch(error => {
      console.error('Error during action plan seeding:', error);
      process.exit(1);
    });
}

module.exports = seedActionPlans;
