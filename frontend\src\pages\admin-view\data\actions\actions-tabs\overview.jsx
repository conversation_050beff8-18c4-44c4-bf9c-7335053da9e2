import { useOutletContext } from "react-router-dom";
import { 
  Calendar, 
  Clock, 
  Tag, 
  User, 
  AlertCircle, 
  CheckCircle2 
} from "lucide-react";
import { format } from "date-fns";

function ActionOverview() {
  const { action } = useOutletContext();

  // Format date if it exists
  const formatDate = (dateString) => {
    if (!dateString) return "Not set";
    try {
      return format(new Date(dateString), "yyyy-MM-dd");
    } catch (error) {
      return dateString;
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get priority badge color
  const getPriorityBadgeColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800";
      case "High":
        return "bg-orange-100 text-orange-800";
      case "Medium":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-green-100 text-green-800";
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">Action Overview</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h3 className="text-lg font-medium mb-4">Details</h3>
          <div className="space-y-4">
            <div className="flex items-start">
              <Tag className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">Name</p>
                <p className="text-base">{action.name}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Tag className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">Priority</p>
                <p className="text-base">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityBadgeColor(action.priority)}`}>
                    {action.priority || "Low"}
                  </span>
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <Tag className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">Status</p>
                <p className="text-base">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(action.status)}`}>
                    {action.status || "Not Started"}
                  </span>
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <User className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">Assignee</p>
                <p className="text-base">
                  {action.assignee ? 
                    `${action.assignee.username} (${action.assignee.role})` : 
                    "Not assigned"}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-medium mb-4">Dates & Description</h3>
          <div className="space-y-4">
            <div className="flex items-start">
              <Calendar className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">Start Date</p>
                <p className="text-base">{formatDate(action.startDate)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <Calendar className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">End Date</p>
                <p className="text-base">{formatDate(action.endDate)}</p>
              </div>
            </div>

            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 mr-3 text-gray-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-500">Description</p>
                <p className="text-base whitespace-pre-wrap">{action.description || "No description provided"}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ActionOverview;
