const { Sequelize } = require('sequelize');

module.exports = (sequelize) => {
  const AuditMissionRapport = {
    async getMissionReport(missionId) {
      const query = `
        SELECT 
            chef_user."username" AS chefmission,
            director."username" AS directeuraudit,
            activity_resp_user."username" AS activity_responsable,
            constat_resp_user."username" AS constat_responsable,
            recommendation_resp_user."username" AS recommendation_responsable,
            am."name" AS mission_name,
            am."categorie",
            am."evaluation",
            am."objectif",
            am."pointfort",
            am."pointfaible",
            STRING_AGG(DISTINCT r."name", ', ') AS risk_names,
            STRING_AGG(DISTINCT e."name", ', ') AS entity_names,
            STRING_AGG(DISTINCT c."name", ', ') AS control_names,
            STRING_AGG(DISTINCT i."name", ', ') AS incident_names,
            STRING_AGG(DISTINCT op."name", ', ') AS organizational_process_names,
            SUM(aa."chargedetravailestimee") AS chargedetravailestimee,
            SUM(aa."chargedetravaileffective") AS chargedetravaileffective,
            SUM(aa."depense") AS depenses,
            am."principalAudite",
            ac."name" AS constat_name,
            ac."impact" AS constat_impact,
            risks.constat_risks,
            ar."name" AS recommendation_name,
            ar."details" AS recommendation_details,
            am."datefin",
            am."datedebut"
        FROM 
            "AuditMissions" am
            LEFT JOIN "AuditPlans" ap ON am."auditplanID" = ap."id"
            LEFT JOIN "Users" director ON director."id" = ap."directeuraudit"
            LEFT JOIN "AuditActivities" aa ON aa."auditMissionID" = am."id"
            LEFT JOIN LATERAL (
              SELECT chef_equipe."userId", u."username"
              FROM "EquipeIntervenantes" chef_equipe
              JOIN "Users" u ON u."id" = chef_equipe."userId"
              WHERE chef_equipe."auditMissionId" = am."id" AND chef_equipe."chefdemission" = 'oui'
              LIMIT 1
            ) chef_user ON TRUE
            LEFT JOIN LATERAL (
              SELECT equipe_act."userId", u."username"
              FROM "EquipeIntervenantes" equipe_act
              JOIN "Users" u ON u."id" = equipe_act."userId"
              WHERE equipe_act."auditMissionId" = am."id" AND equipe_act."auditActivityId" = aa."id"
              LIMIT 1
            ) activity_resp_user ON TRUE
            LEFT JOIN "AuditConstats" ac ON ac."auditActivityID" = aa."id"
            LEFT JOIN LATERAL (
              SELECT equipe_constat."userId", u."username"
              FROM "EquipeIntervenantes" equipe_constat
              JOIN "Users" u ON u."id" = equipe_constat."userId"
              WHERE equipe_constat."auditMissionId" = am."id" AND equipe_constat."auditConstatId" = ac."id"
              LIMIT 1
            ) constat_resp_user ON TRUE
            LEFT JOIN LATERAL (
              SELECT STRING_AGG(risk."name", ', ') AS constat_risks
              FROM "ConstatRisk" crk
              JOIN "Risk" risk ON risk."riskID" = crk."riskID"
              WHERE crk."constatId" = ac."id"
            ) risks ON TRUE
            LEFT JOIN "AuditScopes" ascop ON ascop."auditMissionID" = am."id"
            LEFT JOIN "AuditScopeRisks" asr ON asr."auditScopeID" = ascop."id"
            LEFT JOIN "Risk" r ON r."riskID" = asr."riskID"
            LEFT JOIN "AuditScopeEntities" ase ON ase."auditScopeID" = ascop."id"
            LEFT JOIN "Entity" e ON e."entityID" = ase."entityID"
            LEFT JOIN "AuditScopeControls" asctrl ON asctrl."auditScopeID" = ascop."id"
            LEFT JOIN "Control" c ON c."controlID" = asctrl."controlID"
            LEFT JOIN "AuditScopeIncidents" asi ON asi."auditScopeID" = ascop."id"
            LEFT JOIN "Incident" i ON i."incidentID" = asi."incidentID"
            LEFT JOIN "AuditScopeProcesses" asp ON asp."auditScopeID" = ascop."id"
            LEFT JOIN "OrganizationalProcess" op ON op."organizationalProcessID" = asp."organizationalProcessID"
            LEFT JOIN "ConstatRecommendation" cr ON cr."constatId" = ac."id"
            LEFT JOIN "AuditRecommendations" ar ON ar."id" = cr."recommendationId"
            LEFT JOIN LATERAL (
              SELECT equipe_rec."userId", u."username"
              FROM "EquipeIntervenantes" equipe_rec
              JOIN "Users" u ON u."id" = equipe_rec."userId"
              WHERE equipe_rec."auditMissionId" = am."id" AND equipe_rec."auditRecommendationId" = ar."id"
              LIMIT 1
            ) recommendation_resp_user ON TRUE
        WHERE 
            am."id" = :missionId
        GROUP BY
            chef_user."username",
            director."username",
            activity_resp_user."username",
            constat_resp_user."username",
            recommendation_resp_user."username",
            am."name",
            am."categorie",
            am."evaluation",
            am."objectif",
            am."pointfort",
            am."pointfaible",
            am."principalAudite",
            ac."name",
            ac."impact",
            risks.constat_risks,
            ar."name",
            ar."details",
            am."datefin",
            am."datedebut"
      `;
      try {
        const results = await sequelize.query(query, {
          replacements: { missionId },
          type: Sequelize.QueryTypes.SELECT,
        });
        return results;
      } catch (error) {
        throw new Error(`Error fetching mission report: ${error.message}`);
      }
    },
  };

  return AuditMissionRapport;
};