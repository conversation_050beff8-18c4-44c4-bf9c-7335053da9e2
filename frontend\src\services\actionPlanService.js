import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

// Determine the API base URL dynamically
const API_URL = `${getApiBaseUrl()}/actionPlans`;

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('authToken');
  return token ? { 'Authorization': `Bearer ${token}` } : {};
};

// Get all action plans
const getAllActionPlans = async () => {
  try {
    console.log('Fetching action plans from:', API_URL);
    const headers = getAuthHeaders();
    const response = await axios.get(API_URL, {
      withCredentials: true,
      headers
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching action plans:', error.response?.data || error.message);
    if (error.code === 'ERR_CONNECTION_REFUSED') {
      console.error('Connection refused. Please ensure the backend server is running.');
    }
    throw error;
  }
};

// Get action plan by ID
const getActionPlanById = async (id) => {
  try {
    const headers = getAuthHeaders();
    const response = await axios.get(`${API_URL}/${id}`, {
      withCredentials: true,
      headers
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching action plan:', error.response?.data || error.message);
    throw error;
  }
};

// Create new action plan
const createActionPlan = async (actionPlanData) => {
  try {
    const headers = getAuthHeaders();
    const response = await axios.post(API_URL, actionPlanData, {
      withCredentials: true,
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error creating action plan:', error.response?.data || error.message);
    throw error;
  }
};

// Update action plan
const updateActionPlan = async (id, actionPlanData) => {
  try {
    const headers = getAuthHeaders();
    const response = await axios.put(`${API_URL}/${id}`, actionPlanData, {
      withCredentials: true,
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error updating action plan:', error.response?.data || error.message);
    throw error;
  }
};

// Delete action plan
const deleteActionPlan = async (id) => {
  try {
    const headers = getAuthHeaders();
    const response = await axios.delete(`${API_URL}/${id}`, {
      withCredentials: true,
      headers
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting action plan:', error.response?.data || error.message);
    throw error;
  }
};

// Delete multiple action plans
const deleteMultipleActionPlans = async (ids) => {
  try {
    const deletePromises = ids.map(id => deleteActionPlan(id));
    const results = await Promise.all(deletePromises);
    return results;
  } catch (error) {
    throw error;
  }
};

const actionPlanService = {
  getAllActionPlans,
  getActionPlanById,
  createActionPlan,
  updateActionPlan,
  deleteActionPlan,
  deleteMultipleActionPlans
};

export default actionPlanService;
