import { useState, useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { Plus, ArrowUpDown, Trash2, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>p, User } from "lucide-react";
import { getAllActionPlans, createActionPlan, deleteMultipleActionPlans, reset } from "@/store/slices/actionPlanSlice";
import { getAllUsers } from "@/store/slices/userSlice";
import { hasPermission } from '@/store/auth-slice';
import PageHeader from "@/components/ui/page-header";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { useTranslation } from "react-i18next";
// Date formatting is handled in the DateRangePickerFilter component
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
// Toast notifications are handled in the Redux slice
import TablePagination from "@/components/ui/table-pagination";
import FilterPanel from "@/components/ui/filter-panel";
import { DateRangePickerShadcn } from "@/components/ui/date-range-picker-shadcn";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import actionPlanIcon from '@/assets/action-plan.png';

function ActionPlansManagement() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { actionPlans, isLoading, isError, message } = useSelector((state) => state.actionPlan);
  const { users } = useSelector((state) => state.user);
  const canCreate = useSelector(state => hasPermission(state, 'create'));
  const canDelete = useSelector(state => hasPermission(state, 'delete'));

  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [selectedActionPlans, setSelectedActionPlans] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "asc" });
  const [isOpen, setIsOpen] = useState(false);

  const [submitting, setSubmitting] = useState(false);

  // Filter states
  const [filters, setFilters] = useState({
    category: "all",
    nature: "all",
    origin: "all",
    priority: "all",
    organizationalLevel: "all",
    means: "all",
    approverId: "all",
    assigneeId: "all",
    plannedBeginDateRange: { start: null, end: null },
    plannedEndDateRange: { start: null, end: null },
  });

  // Clear all filters
  const clearFilters = () => {
    console.log('Clearing all filters');
    const clearedFilters = {
      category: "all",
      nature: "all",
      origin: "all",
      priority: "all",
      organizationalLevel: "all",
      means: "all",
      approverId: "all",
      assigneeId: "all",
      plannedBeginDateRange: { start: null, end: null },
      plannedEndDateRange: { start: null, end: null },
    };
    setFilters(clearedFilters);
    console.log('Current filters before clearing:', filters);
    console.log('Filters cleared to:', clearedFilters);
  };

  // Fetch action plans and users on component mount
  useEffect(() => {
    dispatch(getAllActionPlans());
    dispatch(getAllUsers());

    // Reset state when component unmounts
    return () => {
      dispatch(reset());
    };
  }, [dispatch]);

  // New action plan state
  const [newActionPlan, setNewActionPlan] = useState({
    name: "",
    nature: "",
    comment: ""
  });

  // Columns configuration
  const columns = [
    { key: "name", label: t('admin.action_plans.columns.name', 'Name'), sortable: true },
    { key: "approver", label: t('admin.action_plans.columns.approver', 'Approver'), sortable: false },
    { key: "assignee", label: t('admin.action_plans.columns.assignee', 'Assignee'), sortable: false },
    { key: "category", label: t('admin.action_plans.columns.category', 'Category'), sortable: true },
    { key: "nature", label: t('admin.action_plans.columns.nature', 'Nature'), sortable: true },
    { key: "origin", label: t('admin.action_plans.columns.origin', 'Origin'), sortable: true },
    { key: "priority", label: t('admin.action_plans.columns.priority', 'Priority'), sortable: true },
    { key: "organizationalLevel", label: t('admin.action_plans.columns.organizational_level', 'Organizational Level'), sortable: true },
    { key: "means", label: t('admin.action_plans.columns.means', 'Means'), sortable: true },
    { key: "comment", label: t('admin.action_plans.columns.comment', 'Comment'), sortable: true },
    { key: "plannedBeginDate", label: t('admin.action_plans.columns.planned_begin_date', 'Planned Begin Date'), sortable: true },
    { key: "plannedEndDate", label: t('admin.action_plans.columns.planned_end_date', 'Planned End Date'), sortable: true }
  ];

  // Handle sort
  const handleSort = (key) => {
    let direction = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  // Filter action plans based on search query and filters
  const filteredActionPlans = useMemo(() => {
    return actionPlans ? actionPlans.filter((actionPlan) => {
      // Apply search query filter
      if (searchQuery) {
        const matchesSearch =
          actionPlan.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          actionPlan.category?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          actionPlan.comment?.toLowerCase().includes(searchQuery.toLowerCase());
        if (!matchesSearch) return false;
      }

      // Apply category filter
      if (filters.category && filters.category !== 'all') {
        console.log('Category filter:', {
          filterValue: filters.category,
          planValue: actionPlan.category,
          match: actionPlan.category === filters.category
        });
        if (actionPlan.category !== filters.category) return false;
      }

      // Apply nature filter
      if (filters.nature && filters.nature !== 'all') {
        console.log('Nature filter:', {
          filterValue: filters.nature,
          planValue: actionPlan.nature,
          match: actionPlan.nature === filters.nature
        });
        if (actionPlan.nature !== filters.nature) return false;
      }

      // Apply origin filter
      if (filters.origin && filters.origin !== 'all') {
        console.log('Origin filter:', {
          filterValue: filters.origin,
          planValue: actionPlan.origin,
          match: actionPlan.origin === filters.origin
        });
        if (actionPlan.origin !== filters.origin) return false;
      }

      // Apply priority filter
      if (filters.priority && filters.priority !== 'all') {
        console.log('Priority filter:', {
          filterValue: filters.priority,
          planValue: actionPlan.priority,
          match: actionPlan.priority === filters.priority
        });
        if (actionPlan.priority !== filters.priority) return false;
      }

      // Apply organizational level filter
      if (filters.organizationalLevel && filters.organizationalLevel !== 'all') {
        console.log('Organizational Level filter:', {
          filterValue: filters.organizationalLevel,
          planValue: actionPlan.organizationalLevel,
          match: actionPlan.organizationalLevel === filters.organizationalLevel
        });
        if (actionPlan.organizationalLevel !== filters.organizationalLevel) return false;
      }

      // Apply means filter
      if (filters.means && filters.means !== 'all') {
        console.log('Means filter:', {
          filterValue: filters.means,
          planValue: actionPlan.means,
          match: actionPlan.means === filters.means
        });
        if (actionPlan.means !== filters.means) return false;
      }

      // Apply approver filter
      if (filters.approverId && filters.approverId !== 'all') {
        console.log('Approver filter:', {
          filterValue: filters.approverId,
          planValue: actionPlan.approverId,
          approver: actionPlan.approver,
          match: actionPlan.approver && actionPlan.approver.id.toString() === filters.approverId
        });
        if (!actionPlan.approver || actionPlan.approver.id.toString() !== filters.approverId) return false;
      }

      // Apply assignee filter
      if (filters.assigneeId && filters.assigneeId !== 'all') {
        console.log('Assignee filter:', {
          filterValue: filters.assigneeId,
          planValue: actionPlan.assigneeId,
          assignee: actionPlan.assignee,
          match: actionPlan.assignee && actionPlan.assignee.id.toString() === filters.assigneeId
        });
        if (!actionPlan.assignee || actionPlan.assignee.id.toString() !== filters.assigneeId) return false;
      }

      // Apply planned begin date range filter
      if (filters.plannedBeginDateRange.start || filters.plannedBeginDateRange.end) {
        const planDate = actionPlan.plannedBeginDate ? new Date(actionPlan.plannedBeginDate) : null;

        if (!planDate) return false;

        if (filters.plannedBeginDateRange.start) {
          const startDate = new Date(filters.plannedBeginDateRange.start);
          // Reset time to start of day for accurate comparison
          startDate.setHours(0, 0, 0, 0);

          // Reset time to start of day for plan date
          const planDateStart = new Date(planDate);
          planDateStart.setHours(0, 0, 0, 0);

          console.log('Begin Date Start filter:', {
            filterValue: startDate,
            planValue: planDateStart,
            match: planDateStart >= startDate
          });

          if (planDateStart < startDate) return false;
        }

        if (filters.plannedBeginDateRange.end) {
          const endDate = new Date(filters.plannedBeginDateRange.end);
          // Set time to end of day for accurate comparison
          endDate.setHours(23, 59, 59, 999);

          // Reset time to start of day for plan date
          const planDateEnd = new Date(planDate);
          planDateEnd.setHours(0, 0, 0, 0);

          console.log('Begin Date End filter:', {
            filterValue: endDate,
            planValue: planDateEnd,
            match: planDateEnd <= endDate
          });

          if (planDateEnd > endDate) return false;
        }
      }

      // Apply planned end date range filter
      if (filters.plannedEndDateRange.start || filters.plannedEndDateRange.end) {
        const planDate = actionPlan.plannedEndDate ? new Date(actionPlan.plannedEndDate) : null;

        if (!planDate) return false;

        if (filters.plannedEndDateRange.start) {
          const startDate = new Date(filters.plannedEndDateRange.start);
          // Reset time to start of day for accurate comparison
          startDate.setHours(0, 0, 0, 0);

          // Reset time to start of day for plan date
          const planDateStart = new Date(planDate);
          planDateStart.setHours(0, 0, 0, 0);

          console.log('End Date Start filter:', {
            filterValue: startDate,
            planValue: planDateStart,
            match: planDateStart >= startDate
          });

          if (planDateStart < startDate) return false;
        }

        if (filters.plannedEndDateRange.end) {
          const endDate = new Date(filters.plannedEndDateRange.end);
          // Set time to end of day for accurate comparison
          endDate.setHours(23, 59, 59, 999);

          // Reset time to start of day for plan date
          const planDateEnd = new Date(planDate);
          planDateEnd.setHours(0, 0, 0, 0);

          console.log('End Date End filter:', {
            filterValue: endDate,
            planValue: planDateEnd,
            match: planDateEnd <= endDate
          });

          if (planDateEnd > endDate) return false;
        }
      }

      return true;
    }) : [];
  }, [actionPlans, searchQuery, filters]);

  // Sort action plans based on sort config
  const sortedActionPlans = useMemo(() => {
    const sortableActionPlans = [...filteredActionPlans];
    if (sortConfig.key) {
      sortableActionPlans.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === "asc" ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === "asc" ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableActionPlans;
  }, [filteredActionPlans, sortConfig]);

  // Pagination
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentActionPlans = sortedActionPlans.slice(startIndex, endIndex);
  const totalPages = Math.ceil(sortedActionPlans.length / itemsPerPage);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedActionPlans.length === currentActionPlans.length) {
      setSelectedActionPlans([]);
    } else {
      setSelectedActionPlans(currentActionPlans.map((actionPlan) => actionPlan.actionPlanID));
    }
  };

  // Handle select action plan
  const handleSelectActionPlan = (id) => {
    if (selectedActionPlans.includes(id)) {
      setSelectedActionPlans(selectedActionPlans.filter((item) => item !== id));
    } else {
      setSelectedActionPlans([...selectedActionPlans, id]);
    }
  };

  // Handle row click
  const handleRowClick = (id) => {
    // Navigate to edit action plan page
    navigate(`/admin/data/action-plans/${id}`);
  };

  // Handle delete selected
  const handleDeleteSelected = async () => {
    if (selectedActionPlans.length === 0) return;

    if (window.confirm(t('admin.action_plans.delete_confirm', 'Are you sure you want to delete {{count}} action plan(s)?', { count: selectedActionPlans.length }))) {
      dispatch(deleteMultipleActionPlans(selectedActionPlans));
      setSelectedActionPlans([]);
    }
  };

  // Handle submit new action plan
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setSubmitting(true);
      dispatch(createActionPlan(newActionPlan));
      setNewActionPlan({
        name: "",
        nature: "",
        comment: ""
      });
      setIsOpen(false);
    } catch (error) {
      console.error(t('admin.action_plans.error_creating', 'Error creating action plan: {{error}}', { error }));
    } finally {
      setSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center h-screen">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
          <span className="mt-4 text-gray-600">{t('admin.action_plans.loading', 'Loading action plans...')}</span>
        </div>
      </div>
    );
  }

  if (isError) {
    return <div className="p-6 text-red-500">{t('admin.action_plans.error', 'Error: {{message}}', { message })}</div>;
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      <PageHeader
        title={t('admin.action_plans.title', 'Action Plans Management')}
        description={t('admin.action_plans.description', 'Define and manage action plans within your organization.')}
        section="Data"
        currentPage={t('admin.sidebar.action_plans', 'Action Plans')}
        searchPlaceholder={t('admin.action_plans.search_placeholder', 'Search action plans...')}
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
        icon={ClipboardCheck}
      />

      {/* Filter Panel */}
      <div className="mb-6 filter-panel-container">
        <FilterPanel
          filters={[
            {
              id: "approverId",
              label: "Approver",
              component: (
                <Select
                  value={filters.approverId}
                  onValueChange={(value) => setFilters({ ...filters, approverId: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select approver" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    {(users || []).map(user => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.username} ({user.role})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "assigneeId",
              label: "Assignee",
              component: (
                <Select
                  value={filters.assigneeId}
                  onValueChange={(value) => setFilters({ ...filters, assigneeId: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select assignee" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    {(users || []).map(user => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.username} ({user.role})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "category",
              label: "Category",
              component: (
                <Select
                  value={filters.category}
                  onValueChange={(value) => setFilters({ ...filters, category: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    {/* Get unique categories from action plans */}
                    {Array.from(new Set((actionPlans || []).map(plan => plan.category).filter(Boolean)))
                      .sort()
                      .map(category => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "nature",
              label: "Nature",
              component: (
                <Select
                  value={filters.nature}
                  onValueChange={(value) => setFilters({ ...filters, nature: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select nature" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="Preventive">Preventive</SelectItem>
                    <SelectItem value="Corrective">Corrective</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "origin",
              label: "Origin",
              component: (
                <Select
                  value={filters.origin}
                  onValueChange={(value) => setFilters({ ...filters, origin: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select origin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="Audit">Audit</SelectItem>
                    <SelectItem value="Compliance">Compliance</SelectItem>
                    <SelectItem value="Event">Event</SelectItem>
                    <SelectItem value="Risk">Risk</SelectItem>
                    <SelectItem value="RFC">RFC</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "priority",
              label: "Priority",
              component: (
                <Select
                  value={filters.priority}
                  onValueChange={(value) => setFilters({ ...filters, priority: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="Low">Low</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="High">High</SelectItem>
                    <SelectItem value="Critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "organizationalLevel",
              label: "Organizational Level",
              component: (
                <Select
                  value={filters.organizationalLevel}
                  onValueChange={(value) => setFilters({ ...filters, organizationalLevel: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="Local">Local</SelectItem>
                    <SelectItem value="Global">Global</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "means",
              label: "Means",
              component: (
                <Select
                  value={filters.means}
                  onValueChange={(value) => setFilters({ ...filters, means: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select means" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    {/* Get unique means from action plans */}
                    {Array.from(new Set((actionPlans || []).map(plan => plan.means).filter(Boolean)))
                      .sort()
                      .map(means => (
                        <SelectItem key={means} value={means}>
                          {means}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "plannedBeginDateRange",
              label: "Planned Begin Date",
              component: (
                <DateRangePickerShadcn
                  value={filters.plannedBeginDateRange}
                  onChange={(value) => setFilters({ ...filters, plannedBeginDateRange: value })}
                  placeholder="Select begin date range"
                />
              ),
            },
            {
              id: "plannedEndDateRange",
              label: "Planned End Date",
              component: (
                <DateRangePickerShadcn
                  value={filters.plannedEndDateRange}
                  onChange={(value) => setFilters({ ...filters, plannedEndDateRange: value })}
                  placeholder="Select end date range"
                />
              ),
            },
          ]}
          activeFilters={filters}
          onClearFilters={clearFilters}
        />
      </div>

      <div className="flex justify-between items-center gap-3 mb-4">
        <div></div>
        <div className="flex items-center gap-3">
          {selectedActionPlans.length > 0 && (
            <Button
              variant="outline"
              className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
                canDelete && !isLoading
                  ? 'bg-[#F62D51]/90 text-white hover:bg-[#F62D51] hover:shadow-md'
                  : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
              } flex items-center gap-2 px-6 py-2 font-semibold`}
              onClick={handleDeleteSelected}
              disabled={!canDelete || isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  Delete ({selectedActionPlans.length})
                </>
              )}
            </Button>
          )}

          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            {canCreate && (
              <DialogTrigger asChild>
                <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Action Plan
                </Button>
              </DialogTrigger>
            )}
            <DialogContent className="max-w-md p-6">
              <DialogHeader>
                <DialogTitle>Create New Action Plan</DialogTitle>
                <DialogDescription>
                  Fill in the details below to create a new action plan. Fields marked with * are required.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-8">
                <div className="space-y-6">
                  <div className="flex flex-col">
                    <Label htmlFor="name" className="mb-2">Name *</Label>
                    <Input
                      id="name"
                      value={newActionPlan.name}
                      onChange={(e) => setNewActionPlan({ ...newActionPlan, name: e.target.value })}
                      placeholder="Enter action plan name"
                      required
                      className="w-full"
                    />
                  </div>
                  <div className="flex flex-col">
                    <Label htmlFor="nature" className="mb-2">Nature</Label>
                    <Select
                      value={newActionPlan.nature}
                      onValueChange={(value) => setNewActionPlan({ ...newActionPlan, nature: value })}
                    >
                      <SelectTrigger id="nature" className="w-full">
                        <SelectValue placeholder="Select nature" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Preventive">Preventive</SelectItem>
                        <SelectItem value="Corrective">Corrective</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex flex-col">
                    <Label htmlFor="comment" className="mb-2">Comment</Label>
                    <textarea
                      id="comment"
                      value={newActionPlan.comment}
                      onChange={(e) => setNewActionPlan({ ...newActionPlan, comment: e.target.value })}
                      placeholder="Enter comment"
                      className="w-full h-24 p-2 border rounded-md resize-y focus:outline-none focus:ring-2 focus:ring-[#F62D51] focus:border-transparent"
                    />
                  </div>
                </div>
                <div className="flex justify-end gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className="bg-[#F62D51] hover:bg-red-700"
                    disabled={submitting}
                  >
                    {submitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      "Create Action Plan"
                    )}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                  <Checkbox
                    checked={selectedActionPlans.length === currentActionPlans.length && currentActionPlans.length > 0}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all"
                  />
                </th>
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100"
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      {sortConfig.key === column.key && (
                        <ArrowUpDown className="w-4 h-4" />
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {currentActionPlans.map((actionPlan, index) => (
                <tr
                  key={actionPlan.actionPlanID}
                  className={`hover:bg-gray-50 cursor-pointer ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                  onClick={() => handleRowClick(actionPlan.actionPlanID)}
                >
                  <td className="px-6 py-4">
                    <Checkbox
                      checked={selectedActionPlans.includes(actionPlan.actionPlanID)}
                      onCheckedChange={() => handleSelectActionPlan(actionPlan.actionPlanID)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </td>
                  <td className="px-6 py-4 text-sm font-bold text-[#242A33] whitespace-nowrap">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <img src={actionPlanIcon} alt="Action Plan" className="w-5 h-5" />
                        {actionPlan.name}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                    {actionPlan.approver ? (
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1 text-gray-500" />
                        {actionPlan.approver.username}
                      </div>
                    ) : '-'}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                    {actionPlan.assignee ? (
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1 text-gray-500" />
                        {actionPlan.assignee.username}
                      </div>
                    ) : '-'}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                    {actionPlan.category || '-'}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                    {actionPlan.nature || '-'}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                    {actionPlan.origin || '-'}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      actionPlan.priority === 'High' ? 'bg-red-100 text-red-800' :
                      actionPlan.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {actionPlan.priority || 'Low'}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                    {actionPlan.organizationalLevel || '-'}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                    {actionPlan.means || '-'}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                    {actionPlan.comment || "-"}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                    {actionPlan.plannedBeginDate || '-'}
                  </td>
                  <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                    {actionPlan.plannedEndDate || '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">
            {filteredActionPlans.length} action plan{filteredActionPlans.length !== 1 ? 's' : ''} found
          </span>
          {/* Show filter badge if any filter is active */}
          {(filters.category !== 'all' ||
            filters.nature !== 'all' ||
            filters.origin !== 'all' ||
            filters.priority !== 'all' ||
            filters.organizationalLevel !== 'all' ||
            filters.means !== 'all' ||
            filters.plannedBeginDateRange.start ||
            filters.plannedBeginDateRange.end ||
            filters.plannedEndDateRange.start ||
            filters.plannedEndDateRange.end) && (
            <Badge
              variant="outline"
              className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 cursor-pointer"
              onClick={() => {
                // Toggle filter panel visibility
                const filterPanel = document.querySelector('.filter-panel-container');
                if (filterPanel) {
                  filterPanel.scrollIntoView({ behavior: 'smooth' });
                }
              }}
            >
              Filtered
              <ChevronUp className="ml-1 h-3 w-3" />
            </Badge>
          )}
        </div>
      </div>

      <TablePagination
        currentPage={currentPage}
        totalPages={totalPages}
        itemsPerPage={itemsPerPage}
        totalItems={filteredActionPlans.length}
        onPageChange={handlePageChange}
        onItemsPerPageChange={(value) => {
          setItemsPerPage(value);
          setCurrentPage(1);
        }}
        startIndex={startIndex}
        endIndex={endIndex}
      />

    </div>
  );
}

export default ActionPlansManagement;
