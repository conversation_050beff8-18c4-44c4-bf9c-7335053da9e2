const fs = require('fs');
const path = require('path');
const { Sequelize } = require('sequelize');
require('dotenv').config();

async function runMigration() {
  try {
    console.log('Starting Attachment table migration...');
    
    // Create a new Sequelize instance
    const sequelize = new Sequelize(
      process.env.DB_NAME,
      process.env.DB_USER,
      process.env.DB_PASSWORD,
      {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        dialect: 'postgres',
        logging: console.log
      }
    );

    // Test the connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Read the SQL migration file
    const migrationPath = path.join(__dirname, '../migrations/create-attachment-table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the SQL
    console.log('Executing migration SQL...');
    await sequelize.query(migrationSQL);
    
    console.log('Migration completed successfully!');
    
    // Create uploads directories if they don't exist
    const uploadsDir = path.join(__dirname, '../uploads');
    const businessDocsDir = path.join(uploadsDir, 'business-documents');
    const externalRefsDir = path.join(uploadsDir, 'external-references');

    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir);
      console.log('Created uploads directory');
    }
    
    if (!fs.existsSync(businessDocsDir)) {
      fs.mkdirSync(businessDocsDir);
      console.log('Created business-documents directory');
    }
    
    if (!fs.existsSync(externalRefsDir)) {
      fs.mkdirSync(externalRefsDir);
      console.log('Created external-references directory');
    }

    console.log('Setup complete!');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
