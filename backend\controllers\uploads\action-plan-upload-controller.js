const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { ActionPlanAttachment } = require('../../models');

// Create directories if they don't exist
const uploadsDir = path.join(__dirname, '../../uploads');
const actionPlanDocsDir = path.join(uploadsDir, 'action-plan-documents');
const actionPlanRefsDir = path.join(uploadsDir, 'action-plan-references');

if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir);
}
if (!fs.existsSync(actionPlanDocsDir)) {
  fs.mkdirSync(actionPlanDocsDir);
}
if (!fs.existsSync(actionPlanRefsDir)) {
  fs.mkdirSync(actionPlanRefsDir);
}

// Upload file attachment
const uploadFile = async (req, res) => {
  try {
    if (!req.files || Object.keys(req.files).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files were uploaded'
      });
    }

    const { actionPlanID, type } = req.body;

    if (!actionPlanID) {
      return res.status(400).json({
        success: false,
        message: 'Action plan ID is required'
      });
    }

    if (!type || !['business-document', 'external-reference'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Valid attachment type is required'
      });
    }

    const file = req.files.file;
    const fileExtension = path.extname(file.name);
    const fileName = `${uuidv4()}${fileExtension}`;
    const uploadDir = type === 'business-document' ? actionPlanDocsDir : actionPlanRefsDir;
    const filePath = path.join(uploadDir, fileName);

    // Move the file to the uploads directory
    await file.mv(filePath);

    // Generate a unique ID for the attachment
    const attachmentID = `AP_ATT_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

    // Save file metadata to database
    const attachment = await ActionPlanAttachment.create({
      attachmentID,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.mimetype,
      filePath: fileName,
      uploadDate: new Date(),
      type,
      actionPlanID
    });

    res.status(201).json({
      success: true,
      message: 'File uploaded successfully',
      data: attachment
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload file'
    });
  }
};

// Add web reference
const addReference = async (req, res) => {
  try {
    const { actionPlanID, url, description } = req.body;

    if (!actionPlanID) {
      return res.status(400).json({
        success: false,
        message: 'Action plan ID is required'
      });
    }

    if (!url) {
      return res.status(400).json({
        success: false,
        message: 'URL is required'
      });
    }

    // Ensure URL has protocol prefix
    let formattedUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      formattedUrl = `https://${url}`;
    }

    // Extract hostname for name if no description is provided
    let fileName;
    try {
      const urlObj = new URL(formattedUrl);
      fileName = urlObj.hostname;
    } catch (e) {
      fileName = 'External link';
    }

    // Generate a unique ID for the attachment
    const attachmentID = `AP_REF_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

    // Save reference to database
    const reference = await ActionPlanAttachment.create({
      attachmentID,
      fileName,
      url: formattedUrl,
      description,
      uploadDate: new Date(),
      type: 'external-reference',
      actionPlanID
    });

    res.status(201).json({
      success: true,
      message: 'Reference added successfully',
      data: reference
    });
  } catch (error) {
    console.error('Error adding reference:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add reference'
    });
  }
};

// Get all attachments for an action plan
const getAttachments = async (req, res) => {
  try {
    const { actionPlanID } = req.params;

    if (!actionPlanID) {
      return res.status(400).json({
        success: false,
        message: 'Action plan ID is required'
      });
    }

    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    const attachments = await ActionPlanAttachment.findAll({
      where: { actionPlanID },
      order: [['uploadDate', 'DESC']],
      raw: true // Get plain objects instead of Sequelize instances for better performance
    });

    res.status(200).json({
      success: true,
      data: attachments.map(attachment => ({
        id: attachment.attachmentID,
        name: attachment.fileName,
        size: attachment.fileSize,
        type: attachment.fileType,
        url: attachment.url,
        description: attachment.description,
        uploadDate: attachment.uploadDate,
        attachmentType: attachment.type,
        actionPlanID: attachment.actionPlanID
      }))
    });
  } catch (error) {
    console.error('Error fetching attachments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attachments'
    });
  }
};

// Get all attachments across all action plans
const getAllAttachments = async (req, res) => {
  try {
    // Optional query parameters for filtering
    const { type, limit = 100, offset = 0 } = req.query;
    
    // Build query conditions
    const where = {};
    if (type && ['business-document', 'external-reference'].includes(type)) {
      where.type = type;
    }

    // Set cache control headers to prevent caching
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Query with pagination
    const attachments = await ActionPlanAttachment.findAll({
      where,
      order: [['uploadDate', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset),
      raw: true
    });

    // Get total count for pagination
    const totalCount = await ActionPlanAttachment.count({ where });

    res.status(200).json({
      success: true,
      data: attachments.map(attachment => ({
        id: attachment.attachmentID,
        name: attachment.fileName,
        size: attachment.fileSize,
        type: attachment.fileType,
        url: attachment.url,
        description: attachment.description,
        uploadDate: attachment.uploadDate,
        attachmentType: attachment.type,
        actionPlanID: attachment.actionPlanID
      })),
      pagination: {
        total: totalCount,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
  } catch (error) {
    console.error('Error fetching all attachments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attachments'
    });
  }
};

// Delete an attachment
const deleteAttachment = async (req, res) => {
  try {
    const { attachmentID } = req.params;

    const attachment = await ActionPlanAttachment.findByPk(attachmentID);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // If it's a file attachment, delete the file
    if (attachment.filePath) {
      const uploadDir = attachment.type === 'business-document' ? actionPlanDocsDir : actionPlanRefsDir;
      const filePath = path.join(uploadDir, attachment.filePath);
      
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }

    // Delete from database
    await attachment.destroy();

    res.status(200).json({
      success: true,
      message: 'Attachment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete attachment'
    });
  }
};

module.exports = {
  uploadFile,
  addReference,
  getAttachments,
  getAllAttachments,
  deleteAttachment
};


