const MissionCharts = require('../../../models/rapport/MissionCharts');

exports.getActivitiesProgression = async (req, res) => {
  try {
    const { missionId } = req.params;
    const data = await MissionCharts.getActivitiesProgressionByMission(missionId);
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error in getActivitiesProgression:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch activities progression', error: error.message });
  }
};

exports.getRecommendationsProgression = async (req, res) => {
  try {
    const { missionId } = req.params;
    const data = await MissionCharts.getRecommendationsProgressionByMission(missionId);
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error in getRecommendationsProgression:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch recommendations progression', error: error.message });
  }
};

exports.getConstatsBreakdown = async (req, res) => {
  try {
    const { missionId } = req.params;
    const data = await MissionCharts.getConstatsBreakdownByMission(missionId);
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error in getConstatsBreakdown:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch constats breakdown', error: error.message });
  }
};

// DEBUG: Get all recommendations, their action plans, and actions for a mission
exports.getRecommendationsActionPlansActions = async (req, res) => {
  try {
    const { missionId } = req.params;
    const data = await MissionCharts.getRecommendationsActionPlansActionsByMission(missionId);
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error in getRecommendationsActionPlansActions:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch recommendations-actionplans-actions', error: error.message });
  }
}; 