/* src/styles/global.css */

/* Webkit Browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
    width: 8px;  /* Vertical scrollbar width */
    height: 8px; /* Horizontal scrollbar height */
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1; /* Light gray track */
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #F62D51, #ff7589); /* Gradient thumb */
    border-radius: 4px;
    border: 1px solid #f1f1f1; /* Subtle border */
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #e02847, #f56c7f); /* Darker gradient on hover */
  }

  /* Firefox */
  * {
    scrollbar-width: thin; /* Slim scrollbar */
    scrollbar-color: #F62D51 #f1f1f1; /* Thumb color, track color */
  }

  /* Optional: Hide scrollbar buttons (arrows) in Webkit */
  ::-webkit-scrollbar-button {
    display: none;
  }

  /* Custom Animations for Auth Layout */
  @keyframes pulse-slow {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
  }

  @keyframes spin-slow {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .animate-pulse-slow {
    animation: pulse-slow 8s infinite ease-in-out;
  }

  .animate-spin-slow {
    animation: spin-slow 20s linear infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  /* Date Input Calendar Icon Styling */
  .date-input-with-icon::-webkit-calendar-picker-indicator {
    color: #374151 !important; /* Gray-700 color */
    background-color: transparent !important;
    cursor: pointer;
    opacity: 1 !important;
    filter: invert(0.4) !important; /* Makes the icon darker/more visible */
  }

  .date-input-with-icon::-webkit-calendar-picker-indicator:hover {
    filter: invert(0.2) !important; /* Even darker on hover */
  }

  /* For Firefox - ensure proper color scheme */
  .date-input-with-icon {
    color-scheme: light !important;
  }

  /* Additional styling for better visibility across browsers */
  input[type="date"].date-input-with-icon {
    color: #374151; /* Ensure text color is dark */
  }

  /* Fallback styling for all date inputs (in case the class is missing) */
  input[type="date"]::-webkit-calendar-picker-indicator {
    opacity: 1 !important;
    filter: invert(0.4) !important;
    cursor: pointer;
  }

  input[type="date"]::-webkit-calendar-picker-indicator:hover {
    filter: invert(0.2) !important;
  }

  input[type="date"] {
    color-scheme: light !important;
    color: #374151;
  }