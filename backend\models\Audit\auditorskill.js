'use strict';

module.exports = (sequelize, DataTypes) => {
  const AuditorSkill = sequelize.define('AuditorSkill', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
      defaultValue: () => `AS_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    },
    auditorId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    skillId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'AuditSkills',
        key: 'id'
      }
    },
    rating: {
      type: DataTypes.DECIMAL(3, 2),
      allowNull: false,
      validate: {
        min: 0.0,
        max: 5.0
      }
    },
    comments: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    ratedBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    ratedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    }
  }, {
    tableName: 'AuditorSkills',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['id']
      },
      {
        fields: ['auditorId']
      },
      {
        fields: ['skillId']
      },
      {
        fields: ['ratedBy']
      },
      {
        fields: ['ratedAt']
      },
      {
        fields: ['rating']
      },
      {
        fields: ['isActive']
      },
      {
        unique: true,
        fields: ['auditorId', 'skillId'],
        name: 'unique_auditor_skill'
      }
    ]
  });

  AuditorSkill.associate = function(models) {
    // AuditorSkill belongs to User (auditor)
    AuditorSkill.belongsTo(models.User, {
      foreignKey: 'auditorId',
      as: 'auditor'
    });

    // AuditorSkill belongs to AuditSkill
    AuditorSkill.belongsTo(models.AuditSkill, {
      foreignKey: 'skillId',
      as: 'skill'
    });

    // AuditorSkill belongs to User (rater)
    AuditorSkill.belongsTo(models.User, {
      foreignKey: 'ratedBy',
      as: 'rater'
    });
  };

  return AuditorSkill;
};
