import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate, Outlet } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  ArrowLeft,
  Loader2,
  Edit,
  User,
  FileText,
  Activity,
  GitBranch
} from "lucide-react";
import { getActionById, reset } from "@/store/slices/actionSlice";
import { Button } from "@/components/ui/button";
import TabBar from "@/components/ui/tabs/tab-bar";
import TabContent from "@/components/ui/tabs/tab-content";

function EditAction() {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { currentAction, isLoading, isError } = useSelector((state) => state.action);
  const [activeTab, setActiveTab] = useState("features");

  // Fetch action data on component mount
  useEffect(() => {
    if (id) {
      dispatch(getActionById(id));
    }

    return () => {
      dispatch(reset());
    };
  }, [dispatch, id]);

  // Handle back button click
  const handleBack = () => {
    navigate("/admin/data/actions-management");
  };

  // Handle tab change
  const handleTabChange = (value) => {
    setActiveTab(value);
    navigate(`/admin/data/actions/edit/${id}/${value}`);
  };

  // Define tabs
  const tabs = [
    { id: "overview", label: "Overview", icon: <FileText className="h-4 w-4" /> },
    { id: "features", label: "Features", icon: <Edit className="h-4 w-4" /> },
    { id: "activity", label: "Activity", icon: <Activity className="h-4 w-4" /> },
    { id: "workflows", label: "Workflows", icon: <GitBranch className="h-4 w-4" /> }
  ];

  // Get status badge variant and color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get priority badge variant and color
  const getPriorityBadgeColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800";
      case "High":
        return "bg-orange-100 text-orange-800";
      case "Medium":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-green-100 text-green-800";
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-6">
        <div className="flex items-center mb-6">
          <Button variant="ghost" onClick={handleBack} className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Actions
          </Button>
        </div>
        <div className="bg-red-50 text-red-800 p-4 rounded-lg">
          <h2 className="text-lg font-semibold">Error</h2>
          <p>Failed to load action details. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={handleBack} className="mr-2">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Actions
        </Button>
      </div>

      {currentAction ? (
        <>
          <div className="bg-white p-6 rounded-lg shadow mb-6">
            <div className="flex items-center mb-2">
              <Edit className="h-5 w-5 mr-2 text-gray-600" />
              <h1 className="text-2xl font-semibold">{currentAction.name}</h1>
            </div>

            <div className="flex flex-wrap gap-2 mb-4">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityBadgeColor(currentAction.priority)}`}>
                {currentAction.priority || "Low"}
              </span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(currentAction.status)}`}>
                {currentAction.status || "Not Started"}
              </span>
              {currentAction.assignee && (
                <span className="flex items-center text-sm text-gray-600">
                  <User className="h-4 w-4 mr-1" />
                  {currentAction.assignee.username}
                </span>
              )}
            </div>

            {currentAction.description && (
              <p className="text-gray-600 mb-2">{currentAction.description}</p>
            )}
          </div>

          <div className="mt-6">
            <TabBar
              activeTab={activeTab}
              onTabChange={handleTabChange}
              tabs={tabs}
              className="mb-6"
            />

            <TabContent>
              <Outlet context={{ action: currentAction }} />
            </TabContent>
          </div>
        </>
      ) : (
        <div className="bg-white p-6 rounded-lg shadow">
          <p className="text-gray-500">No action found with ID: {id}</p>
        </div>
      )}
    </div>
  );
}

export default EditAction;
