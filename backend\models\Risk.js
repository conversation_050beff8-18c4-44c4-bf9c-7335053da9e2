const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const Risk = sequelize.define('Risk', {
    riskID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    current_state: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'Risk Created',
    },
    code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    methodOfIdentification: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isIn: [['survey', 'incident_database', 'audit_mission', 'workshop', null]]
      }
    },
    impact: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 5
      }
    },
    DMR: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 25
      }
    },
    probability: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 5
      }
    },
    appetite: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 5
      }
    },
    acceptance: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    avoidance: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    insurance: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    reduction: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    // targetRisk field removed
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    mitigatingActionPlan: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    inherentRisk: {
      type: DataTypes.VIRTUAL,
      get() {
        const impact = this.getDataValue('impact');
        const probability = this.getDataValue('probability');

        if (impact !== null && probability !== null) {
          return impact * probability;
        }
        return null;
      }
    },
    residualRisk: {
      type: DataTypes.VIRTUAL,
      get() {
        const dmr = this.getDataValue('DMR');
        const inherentRisk = this.get('inherentRisk');

        if (dmr !== null && inherentRisk !== null) {
          return dmr * inherentRisk;
        }
        return null;
      }
    },
    businessProcessID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'BusinessProcess',
        key: 'businessProcessID',
      },
    },
    organizationalProcessID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'OrganizationalProcess',
        key: 'organizationalProcessID',
      },
    },
    operationID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Operation',
        key: 'operationID',
      },
    },
    applicationID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Application',
        key: 'applicationID',
      },
    },
    entityID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Entity',
        key: 'entityID',
      },
    },
    riskTypeID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'RiskType',
        key: 'riskTypeID',
      },
    },
    controlID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Control',
        key: 'controlID',
      },
    },
    impactLabel: {
      type: DataTypes.VIRTUAL,
      get() {
        const impact = this.getDataValue('impact');
        switch (impact) {
          case 1: return 'Très faible';
          case 2: return 'Faible';
          case 3: return 'Moyen';
          case 4: return 'Élevé';
          case 5: return 'Très élevé';
          default: return null;
        }
      }
    },
    DMRLabel: {
      type: DataTypes.VIRTUAL,
      get() {
        const dmr = this.getDataValue('DMR');
        switch (dmr) {
          case 1: return 'Very Strong';
          case 4: return 'Strong';
          case 9: return 'Medium';
          case 16: return 'Weak';
          case 25: return 'Very Weak';
          default: return null;
        }
      }
    },
    probabilityLabel: {
      type: DataTypes.VIRTUAL,
      get() {
        const probability = this.getDataValue('probability');
        switch (probability) {
          case 1: return 'Rare';
          case 2: return 'Possible';
          case 3: return 'Vraisemblable';
          case 4: return 'Probable';
          case 5: return 'Certain';
          default: return null;
        }
      }
    },
    appetiteLabel: {
      type: DataTypes.VIRTUAL,
      get() {
        const appetite = this.getDataValue('appetite');
        switch (appetite) {
          case 1: return 'Very Low';
          case 2: return 'Low';
          case 3: return 'Medium';
          case 4: return 'High';
          case 5: return 'Very High';
          default: return null;
        }
      }
    },
    residualRiskLabel: {
      type: DataTypes.VIRTUAL,
      get() {
        const residualRisk = this.get('residualRisk');

        if (residualRisk === null) return null;

        if (residualRisk <= 20) return 'Faible';
        if (residualRisk <= 45) return 'Moyen';
        if (residualRisk <= 75) return 'Élevé';
        return 'Très élevé';
      }
    },
    inherentRiskLabel: {
      type: DataTypes.VIRTUAL,
      get() {
        const inherentRisk = this.get('inherentRisk');

        if (inherentRisk === null) return null;

        if (inherentRisk <= 4) return 'Faible';
        if (inherentRisk <= 9) return 'Moyen';
        if (inherentRisk <= 15) return 'Élevé';
        return 'Très élevé';
      }
    },
    major: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    evaluations: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
  }, {
    tableName: 'Risk',
    freezeTableName: true,
    timestamps: false,
  });

  Risk.associate = (models) => {
    Risk.hasMany(models.Incident, {
      foreignKey: 'riskID',
      sourceKey: 'riskID',
      as: 'riskIncidents'
    });
    Risk.hasMany(models.Event, {
      foreignKey: 'risk_id',
      sourceKey: 'riskID',
      as: 'events'
    });
    Risk.belongsToMany(models.User, {
      through: models.RiskContributor,
      foreignKey: 'risk_id',
      otherKey: 'user_id',
      as: 'contributors'
    });
    Risk.hasMany(models.RiskContributor, {
      foreignKey: 'risk_id',
      sourceKey: 'riskID',
      as: 'contributorAssignments'
    });
    Risk.belongsToMany(models.ActionPlan, {
      through: {
        model: 'ActionPlanRisk',
        timestamps: true
      },
      foreignKey: 'riskID',
      otherKey: 'actionPlanID',
      as: 'actionPlans'
    });
    // Many-to-many with AuditConstat
    Risk.belongsToMany(models.AuditConstat, {
      through: 'ConstatRisk',
      foreignKey: 'riskID',
      otherKey: 'constatId',
      as: 'constats'
    });
  };

  return Risk;
};
