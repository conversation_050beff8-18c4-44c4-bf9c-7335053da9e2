const db = require('../../models');
const { Op } = require('sequelize');
const User = db.User;
const Incident = db.Incident;
const IncidentContributor = db.IncidentContributor;
const activityController = require('./activity-controller');
const { sequelize } = require('../../models');
const socketUtils = require('../../utils/socket-io'); // Import Socket.IO utility instead of server.js
const notificationController = require('../notifications/notification-controller'); // Import notification controller
const { sendEmailDirect } = require('../email-controller');

/**
 * Get all contributors for an incident
 * 
 * @param {Object} req - Express request object with incidentId parameter
 * @param {Object} res - Express response object
 */
exports.getIncidentContributors = async (req, res) => {
  try {
    const { incidentId } = req.params;
    console.log('Getting contributors for incident:', incidentId);
    
    // Get all users who are contributors for this incident
    const contributors = await IncidentContributor.findAll({
      where: { incident_id: incidentId },
      include: [
        {
          model: User,
          as: 'contributor',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'assigner',
          attributes: ['id', 'username', 'email']
        }
      ],
      order: [['assigned_date', 'DESC']]
    });
    
    console.log(`Found ${contributors.length} contributors`);
    
    return res.status(200).json({
      success: true,
      data: contributors
    });
  } catch (error) {
    console.error('Error in getIncidentContributors:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve contributors',
      error: error.message
    });
  }
};

/**
 * Assign a contributor to an incident
 * 
 * @param {Object} req - Express request object with incidentId parameter and userId in body
 * @param {Object} res - Express response object
 */
exports.assignContributor = async (req, res) => {
  const { userId } = req.body;
  const { incidentId } = req.params;
  const assignerId = req.user.userId || req.user.id; // ID of the user performing the assignment
  const transaction = await sequelize.transaction();

  try {
    console.log(`[IncidentContributor] Assigning user ${userId} to incident ${incidentId} by assigner ${assignerId}`);
    
    if (!incidentId || !userId) {
      await transaction.rollback();
      return res.status(400).json({ success: false, message: 'Incident ID and User ID for assignment are required' });
    }

    const incident = await Incident.findByPk(incidentId, { transaction });
    if (!incident) {
      await transaction.rollback();
      return res.status(404).json({ success: false, message: 'Incident not found' });
    }

    const userToAssign = await User.findByPk(userId, { transaction });
    if (!userToAssign) {
      await transaction.rollback();
      return res.status(404).json({ success: false, message: 'User to be assigned not found' });
    }
    
    const assignerUser = await User.findByPk(assignerId, { transaction });
    if (!assignerUser) {
        await transaction.rollback();
        return res.status(404).json({ success: false, message: 'Assigner user performing the action not found' });
    }

    const existingContributor = await IncidentContributor.findOne({
      where: { incident_id: incidentId, user_id: userId },
      transaction
    });

    if (existingContributor) {
      await transaction.rollback();
      return res.status(400).json({ success: false, message: 'User is already a contributor to this incident' });
    }

    const contributor = await IncidentContributor.create({
      incident_id: incidentId,
      user_id: userId,
      assigned_by: assignerId,
      status: 'pending' 
    }, { transaction });
    console.log('[IncidentContributor] Contributor record created:', contributor.id);

    // Log activity (optional, but good practice)
    // Consider creating a generic activity logger or using an existing one if available
    // await activityController.logActivity({ ... }, { transaction });

    // Create notification for the assigned user (INSIDE TRANSACTION)
    const notificationData = {
      userId: userId, // The user being assigned
      type: 'incident_assignment',
      entityId: incidentId,
      entityName: incident.name || 'Untitled Incident',
      message: `You have been assigned to incident: ${incident.name || 'Untitled Incident'}`,
      assignedBy: assignerId 
    };

    console.log('[IncidentContributor] Attempting to create notification (within transaction):', JSON.stringify(notificationData));
    const newNotification = await notificationController.createNotification(notificationData, { transaction });

    if (!newNotification) {
      console.error('[IncidentContributor] Failed to create notification. Rolling back transaction.');
      await transaction.rollback();
      return res.status(500).json({ success: false, message: 'Failed to create notification for contributor assignment.' });
    }
    console.log('[IncidentContributor] Notification created successfully (within transaction), ID:', newNotification.id);

    // Send email to the assigned user (after commit)
    if (userToAssign && userToAssign.email) {
      await sendEmailDirect({
        to: userToAssign.email,
        subject: "Nouvel incident assigné",
        text: `Bonjour ${userToAssign.username},\n\nVous avez été assigné(e) comme contributeur à l'incident : ${incident.name}. Veuillez consulter les détails de l'incident dans l'application.\n\nMerci.`,
        html: `<p>Bonjour ${userToAssign.username},</p><p>Vous avez été assigné(e) comme contributeur à l'incident : <b>${incident.name}</b>. Veuillez consulter les détails de l'incident dans l'application.</p><p>Merci.</p>`
      });
    }

    // All database operations successful, commit the transaction
    await transaction.commit();
    console.log('[IncidentContributor] Transaction committed successfully.');

    // Emit Socket.IO notification (AFTER successful commit)
    try {
      const io = socketUtils.getIo(); // Corrected from getIo() to socketUtils.getIo()
      if (io) {
        const targetRoom = `user-${userId.toString()}`;
        const socketData = {
            ...notificationData,
            id: newNotification.id,
            notificationId: newNotification.id.toString(),
            createdAt: newNotification.createdAt,
            is_read: false
        };
        io.to(targetRoom).emit('notification', socketData);
        console.log(`[IncidentContributor] Socket notification sent to room ${targetRoom}:`, socketData);
      } else {
        console.warn('[IncidentContributor] Socket.IO instance not available. Notification not sent via socket.');
      }
    } catch (socketError) {
      console.error('[IncidentContributor] Error emitting socket notification:', socketError);
    }

    return res.status(201).json({ 
      success: true,
      message: 'Contributor assigned and notification sent successfully', 
      contributor 
    });

  } catch (error) {
    if (transaction && !transaction.finished) {
        try {
            await transaction.rollback();
            console.log('[IncidentContributor] Transaction rolled back due to error:', error.message);
        } catch (rollbackError) {
            console.error('[IncidentContributor] Error rolling back transaction:', rollbackError);
        }
    }
    console.error('[IncidentContributor] Error in assignContributor:', error);
    // Send a success:false for client-side error handling
    return res.status(500).json({ 
        success: false, 
        message: error.message || 'An error occurred while assigning the contributor' 
    });
  }
};

/**
 * Remove a contributor from an incident
 * 
 * @param {Object} req - Express request object with incidentId and contributorId parameters
 * @param {Object} res - Express response object
 */
exports.removeContributor = async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const { incidentId, contributorId } = req.params;
    
    // Use userId from auth token - check multiple possible field names
    const userId = req.user.id || req.user.userId || req.user._id;
    
    console.log('Remove contributor params:', { incidentId, contributorId, userId });
    
    // Validate incident exists
    const incident = await Incident.findByPk(incidentId, { transaction });
    if (!incident) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Incident not found'
      });
    }
    
    // Log raw record from database to debug
    console.log('Looking for contributor record with ID:', contributorId);
    const rawContributor = await sequelize.query(
      `SELECT * FROM "IncidentContributor" WHERE id = ?`,
      {
        replacements: [contributorId],
        type: sequelize.QueryTypes.SELECT,
        transaction
      }
    );
    console.log('Raw contributor record:', rawContributor);
    
    // Try to find the contributor record by its primary key ID
    const contributor = await IncidentContributor.findByPk(contributorId, { transaction });
    
    if (!contributor) {
      console.log('Contributor not found by primary key, trying by user_id');
      // Look for a contributor record where the user_id matches the contributorId parameter
      const contributorByUserId = await IncidentContributor.findOne({
        where: {
          incident_id: incidentId,
          user_id: contributorId
        },
        transaction
      });
      
      if (!contributorByUserId) {
        console.log('Contributor not found by user_id either');
        await transaction.rollback();
        return res.status(404).json({
          success: false,
          message: 'Contributor not found'
        });
      }
      
      console.log('Found contributor by user_id:', contributorByUserId.toJSON());
      
      // Delete the found contributor
      await contributorByUserId.destroy({ transaction });
      
      // Log activity
      await activityController.logActivity({
        userId,
        action: 'REMOVE_CONTRIBUTOR',
        entityType: 'Incident',
        entityId: incidentId,
        details: `Removed user with ID ${contributorId} as contributor from incident ${incident.name} (ID: ${incidentId})`
      }, { transaction });
      
      await transaction.commit();
      console.log('Successfully removed contributor by user_id');
      
      return res.status(200).json({
        success: true,
        message: 'Contributor removed successfully'
      });
    }
    
    console.log('Found contributor by primary key:', contributor.toJSON());
    
    // If found by primary key, continue with deletion
    await contributor.destroy({ transaction });
    
    // Log activity
    await activityController.logActivity({
      userId,
      action: 'REMOVE_CONTRIBUTOR',
      entityType: 'Incident',
      entityId: incidentId,
      details: `Removed user with ID ${contributor.user_id} as contributor from incident ${incident.name} (ID: ${incidentId})`
    }, { transaction });
    
    await transaction.commit();
    console.log('Successfully removed contributor by primary key');
    
    return res.status(200).json({
      success: true,
      message: 'Contributor removed successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error in removeContributor:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to remove contributor',
      error: error.message
    });
  }
};

/**
 * Check if a user is a contributor to an incident
 * 
 * @param {Object} req - Express request object with incidentId parameter and userId in query
 * @param {Object} res - Express response object
 */
exports.checkContributor = async (req, res) => {
  try {
    const { incidentId } = req.params;
    const { userId } = req.query;
    
    // If no userId provided, use the authenticated user's ID
    const userIdToCheck = userId || req.user.id;
    
    // Check if user is a contributor
    const contributor = await IncidentContributor.findOne({
      where: {
        incident_id: incidentId,
        user_id: userIdToCheck
      }
    });
    
    return res.status(200).json({
      success: true,
      isContributor: !!contributor
    });
  } catch (error) {
    console.error('Error in checkContributor:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to check contributor status',
      error: error.message
    });
  }
}; 