import { Outlet } from "react-router-dom";
import redLogo from "../../assets/redlogovitalis.png";

function AuthLayout() {
  return (
    <div className="min-h-screen w-full bg-background flex items-center justify-center p-4 relative overflow-hidden">
      {/* Large background shapes */}
      <div className="absolute -top-20 -left-20 w-96 h-96 bg-primary/15 rounded-full blur-lg" />
      <div className="absolute -bottom-20 -right-20 w-80 h-80 bg-secondary/30 rounded-full blur-lg" />
      
      {/* Left side shapes - Repositioned and spaced out */}
      <div className="absolute top-10 left-10 w-32 h-32 bg-primary/20 rounded-full" />
      <div className="absolute top-32 left-40 w-16 h-16 bg-secondary/25 transform rotate-45" />
      <div className="absolute top-60 left-20 w-24 h-24 border-[12px] border-primary/20 rounded-lg transform rotate-12" />
      <div className="absolute bottom-40 left-32 w-20 h-20 bg-primary/20"
           style={{ clipPath: "polygon(50% 0%, 0% 100%, 100% 100%)" }} />
      <div className="absolute bottom-20 left-16 w-28 h-28 border-[12px] border-secondary/25 rounded-full" />
      <div className="absolute bottom-80 left-24 w-5 h-5 bg-primary/40 rounded-full" />
      
      {/* Right side shapes - Kept as is */}
      <div className="absolute top-1/4 right-1/4 w-48 h-48 border-[20px] border-primary/25 rounded-lg transform rotate-12" />
      <div className="absolute bottom-1/4 right-32 w-36 h-36 bg-secondary/20 rounded-full" />
      <div className="absolute top-20 right-40 w-20 h-20 bg-primary/25 transform rotate-45" />
      <div className="absolute top-1/2 right-20 w-24 h-24 border-[12px] border-primary/20 rounded-full" />
      <div className="absolute bottom-40 right-1/2 w-24 h-24 bg-secondary/25 transform rotate-45"
           style={{ clipPath: "polygon(50% 0%, 0% 100%, 100% 100%)" }} />
      <div className="absolute bottom-1/4 right-40 w-20 h-20 bg-secondary/25 transform rotate-45" />
      <div className="absolute top-1/3 right-60 w-4 h-4 bg-primary/40 rounded-full" />
      <div className="absolute top-2/3 right-32 w-3 h-3 bg-primary/40 rounded-full" />

      {/* Centered Card container */}
      <div className="w-full max-w-md bg-card p-6 rounded-lg shadow-lg border border-border z-10 backdrop-blur-sm">
        <div className="flex justify-center mb-6">
           <img 
             src={redLogo}
             alt="Vitalis Logo" 
             className="w-40 h-20 object-contain"
           />
        </div>
        <Outlet />
      </div>
    </div>
  );
}

export default AuthLayout;
