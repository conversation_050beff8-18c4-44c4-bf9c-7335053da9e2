const db = require('../../models');
const { Op } = require('sequelize');

/**
 * Get all activities for a specific incident
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getIncidentActivities = async (req, res) => {
  try {
    const { incidentId } = req.params;

    // Validate incident exists
    const incident = await db.Incident.findByPk(incidentId);
    if (!incident) {
      return res.status(404).json({
        success: false,
        message: 'Incident not found'
      });
    }

    // Get workflow events from IncidentEvent table
    const workflowEvents = await db.IncidentEvent.findAll({
      where: { incident_id: incidentId },
      attributes: ['id', 'timestamp', 'user', 'step', 'transition', 'message'],
      order: [['timestamp', 'DESC']]
    });

    // Get activity logs from IncidentActivityLog table
    const activityLogs = await db.IncidentActivityLog.findAll({
      where: { incident_id: incidentId },
      attributes: ['id', 'timestamp', 'user', 'type', 'field', 'old_value', 'new_value', 'details'],
      order: [['timestamp', 'DESC']]
    });

    // Format workflow events as activity items
    const workflowActivities = workflowEvents.map(event => ({
      id: `event-${event.id}`,
      type: 'transition',
      timestamp: event.timestamp,
      user: event.user,
      details: event.transition === 'Create' 
        ? 'Incident created' 
        : `${event.transition} to ${event.step}`
    }));

    // Format activity logs as activity items
    const logActivities = activityLogs.map(log => ({
      id: `log-${log.id}`,
      type: log.type,
      timestamp: log.timestamp,
      user: log.user,
      field: log.field,
      oldValue: log.old_value,
      newValue: log.new_value,
      details: log.details
    }));

    // Combine and sort all activities by timestamp (newest first)
    const allActivities = [...workflowActivities, ...logActivities]
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    return res.status(200).json({
      success: true,
      data: allActivities
    });
  } catch (error) {
    console.error('Error in getIncidentActivities:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve incident activities',
      error: error.message
    });
  }
};

/**
 * Log a creation activity
 * @param {Object} incident - The created incident object
 * @param {String} username - Username who created the incident
 */
exports.logCreationActivity = async (incident, username) => {
  try {
    await db.IncidentActivityLog.create({
      incident_id: incident.incidentID,
      user: username,
      type: 'creation',
      details: 'Incident created'
    });
    console.log(`Creation activity logged for incident ${incident.incidentID}`);
    return true;
  } catch (error) {
    console.error('Error logging creation activity:', error);
    return false;
  }
};

/**
 * Log update activities by comparing old and new incident values
 * @param {Object} oldIncident - Incident object before update
 * @param {Object} newData - New data being applied to the incident
 * @param {String} username - Username who updated the incident
 */
exports.logUpdateActivities = async (oldIncident, newData, username) => {
  try {
    const changes = [];
    
    // Define which fields to track and their display names
    const trackableFields = {
      name: 'Name',
      description: 'Description',
      detectionDate: 'Detection date',
      occurrenceDate: 'Occurrence date',
      declarantEntity: 'Declarant entity',
      declaredBy: 'Declared by',
      declarationDate: 'Declaration date',
      nearMiss: 'Near miss',
      nature: 'Nature',
      impact: 'Impact',
      priority: 'Priority',
      currency: 'Currency',
      grossLoss: 'Gross loss',
      recoveries: 'Recoveries',
      provisions: 'Provisions'
    };

    // Check each trackable field for changes
    for (const [field, displayName] of Object.entries(trackableFields)) {
      if (newData[field] !== undefined && oldIncident[field] !== newData[field]) {
        // Format boolean values for better readability
        let oldValue = oldIncident[field];
        let newValue = newData[field];
        
        if (typeof oldValue === 'boolean') {
          oldValue = oldValue ? 'Yes' : 'No';
        }
        
        if (typeof newValue === 'boolean') {
          newValue = newValue ? 'Yes' : 'No';
        }
        
        // Format dates for better readability
        if (field.includes('Date') && oldValue) {
          oldValue = new Date(oldValue).toISOString().split('T')[0];
        }
        
        if (field.includes('Date') && newValue) {
          newValue = new Date(newValue).toISOString().split('T')[0];
        }
        
        // Only log if there's an actual change after normalization
        if (String(oldValue || '') !== String(newValue || '')) {
          // Create activity log entry
          await db.IncidentActivityLog.create({
            incident_id: oldIncident.incidentID,
            user: username,
            type: 'update',
            field: field,
            old_value: String(oldValue || ''),
            new_value: String(newValue || ''),
            details: `${displayName} changed from '${oldValue || ''}' to '${newValue || ''}'`
          });
          
          changes.push(field);
        }
      }
    }
    
    // Special handling for reference fields that use IDs
    const referenceFields = {
      entityID: 'Entity',
      riskID: 'Risk',
      businessLineID: 'Business Line',
      incidentTypeID: 'Incident Type',
      businessProcessID: 'Business Process',
      organizationalProcessID: 'Organizational Process',
      productID: 'Product',
      applicationID: 'Application',
      controlID: 'Control',
      actionPlanID: 'Action Plan'
    };
    
    for (const [field, displayName] of Object.entries(referenceFields)) {
      if (newData[field] !== undefined && oldIncident[field] !== newData[field]) {
        await db.IncidentActivityLog.create({
          incident_id: oldIncident.incidentID,
          user: username,
          type: 'update',
          field: field,
          old_value: oldIncident[field] || '',
          new_value: newData[field] || '',
          details: `${displayName} reference updated`
        });
        
        changes.push(field);
      }
    }
    
    console.log(`Logged ${changes.length} changes for incident ${oldIncident.incidentID}`);
    return changes.length > 0;
  } catch (error) {
    console.error('Error logging update activities:', error);
    return false;
  }
};

/**
 * Log a general activity for an incident
 * @param {Object} activityData - Activity data including userId, action, entityType, entityId, details
 * @param {Object} options - Additional options like transaction
 */
exports.logActivity = async (activityData, options = {}) => {
  try {
    const { userId, action, entityType, entityId, details } = activityData;
    
    // Find username if userId is provided
    let username = 'System';
    if (userId) {
      const user = await db.User.findByPk(userId, { transaction: options.transaction });
      if (user) {
        username = user.username || user.email || `User ID: ${userId}`;
      }
    }
    
    // Map custom actions to existing enum values
    let type = action.toLowerCase();
    if (type === 'assign_contributor' || type === 'remove_contributor') {
      type = 'update'; // Map to an existing enum value
    }
    
    // Create activity log entry
    await db.IncidentActivityLog.create({
      incident_id: entityId,
      user: username,
      type: type,
      details: details || `${action} performed`,
      timestamp: new Date()
    }, { transaction: options.transaction });
    
    return true;
  } catch (error) {
    console.error('Error logging activity:', error);
    return false;
  }
}; 