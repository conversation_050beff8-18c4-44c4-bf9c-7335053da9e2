import { useParams } from "react-router-dom";
import { useState, useEffect, useRef } from "react";
import { getAuditPlanKpiCounts } from "@/services/audit-plan-kpi-service";
import {
  FileText,
  Calendar,
  Users,
  ClipboardList,
  Info,
  Clock,
  ShieldCheck,
  CalendarRange,
  CalendarDays,
  Hash,
  Tag,
  GitBranch,
  Activity,
  Loader2
} from "lucide-react";

function VueEnsembleTab({ auditPlan }) {
  const { id: planId } = useParams();
  const abortControllerRef = useRef(null);

  // State for KPI counts
  const [kpiCounts, setKpiCounts] = useState({
    missionsCount: 0,
    recommendationsCount: 0,
    loading: true,
    error: null
  });

  // Fetch KPI counts when component mounts or planId changes
  useEffect(() => {
    const fetchKpiCounts = async () => {
      if (!planId) return;

      // Cancel any existing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new AbortController for this request
      abortControllerRef.current = new AbortController();

      try {
        setKpiCounts(prev => ({ ...prev, loading: true, error: null }));

        // Use the optimized service function
        const counts = await getAuditPlanKpiCounts(planId, abortControllerRef.current.signal);

        if (counts) {
          setKpiCounts({
            missionsCount: counts.missionsCount,
            recommendationsCount: counts.recommendationsCount,
            loading: false,
            error: null
          });
        }

      } catch (error) {
        if (error.name !== 'CanceledError') {
          console.error('Error fetching KPI counts:', error);
          setKpiCounts(prev => ({
            ...prev,
            loading: false,
            error: 'Erreur lors du chargement des statistiques'
          }));
        }
      }
    };

    fetchKpiCounts();

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [planId]);

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString("fr-FR", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch {
      return "Date invalide";
    }
  };

  const renderValue = (value, fallback = "N/A") => {
    if (value === null || value === undefined || value === "") {
      return <span className="text-gray-400">{fallback}</span>;
    }
    return value;
  };

  const getStatusColor = (status) => {
    if (!status) return 'bg-gray-100 text-gray-800';

    switch(status) {
      case 'Terminé': return 'bg-green-100 text-green-800';
      case 'En cours': return 'bg-blue-100 text-blue-800';
      case 'Planifié': return 'bg-purple-100 text-purple-800';
      case 'Retardé': return 'bg-red-100 text-red-800';
      case 'Annulé': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Helper function to get KPI box color based on status and progression
  const getKpiColor = (kpiType, status, progression = 0) => {
    // Normalize status to lowercase and remove accents for comparison
    const normalizedStatus = (status || '').toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g, "");

    // Check for all possible variations of "Planifié" status
    const isPlanned = normalizedStatus === 'planifie' ||
                     normalizedStatus === 'planifiee' ||
                     normalizedStatus === 'planned';

    if (kpiType === 'etat' && isPlanned) {
      return "bg-gradient-to-br from-gray-500 to-gray-600";
    }

    if (kpiType === 'progression') {
      if (isPlanned) {
        return "bg-gradient-to-br from-gray-500 to-gray-600";
      }
      // Use progression-based color logic
      if (progression < 30) return "bg-gradient-to-br from-red-500 to-red-600";
      if (progression < 70) return "bg-gradient-to-br from-yellow-500 to-yellow-600";
      return "bg-gradient-to-br from-blue-500 to-blue-600";
    }

    // Default colors for other KPIs
    switch(kpiType) {
      case 'missions': return "bg-gradient-to-br from-blue-500 to-blue-600";
      case 'recommandations': return "bg-gradient-to-br from-green-500 to-green-600";
      default: return "bg-gradient-to-br from-indigo-500 to-indigo-600";
    }
  };

  if (!auditPlan) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
          <p className="text-gray-500">Chargement des caractéristiques du plan d'audit...</p>
        </div>
      </div>
    );
  }

  // KPI data with real counts and dynamic colors
  const kpis = [
    {
      label: "Missions d'audit",
      value: kpiCounts.loading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        kpiCounts.missionsCount
      ),
      icon: <ClipboardList className="h-4 w-4 text-white" />,
      color: getKpiColor('missions', auditPlan?.status)
    },
    {
      label: "État",
      value: auditPlan?.status || "N/A",
      icon: <GitBranch className="h-4 w-4 text-white" />,
      color: getKpiColor('etat', auditPlan?.status)
    },
    {
      label: "Recommandations",
      value: kpiCounts.loading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        kpiCounts.recommendationsCount
      ),
      icon: <ShieldCheck className="h-4 w-4 text-white" />,
      color: getKpiColor('recommandations', auditPlan?.status)
    },
    {
      label: "Progression",
      value: `${auditPlan?.avancement || 0}%`,
      icon: <Activity className="h-4 w-4 text-white" />,
      color: getKpiColor('progression', auditPlan?.status, auditPlan?.avancement || 0)
    }
  ];

  return (
    <div className="space-y-8">
      {/* Prominent KPI Cards */}
      <div className="flex flex-wrap gap-3">
        {kpiCounts.error && (
          <div className="w-full mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600 text-sm">{kpiCounts.error}</p>
          </div>
        )}
        {kpis.map((kpi, index) => (
          <div key={index} className={`rounded-lg shadow-md overflow-hidden w-fit ${kpi.color}`}>
            <div className="p-3">
              <div className="flex items-center gap-2">
                <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-white/20 flex items-center justify-center">
                  {kpi.icon}
                </div>
                <div>
                  <h3 className="text-xs font-medium text-white/80">{kpi.label}</h3>
                  <div className="text-lg font-bold text-white">
                    {kpi.value}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Key Information Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <Info className="h-5 w-5 mr-2 text-blue-500" />
            Informations Clés
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-4">
          <div className="flex items-start space-x-3">
            <div className="bg-purple-50 p-2 rounded-full">
              <Tag className="h-5 w-5 text-purple-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Nom du Plan</p>
              <p className="text-base font-semibold">{renderValue(auditPlan.name)}</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="bg-emerald-50 p-2 rounded-full">
              <Hash className="h-5 w-5 text-emerald-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Année</p>
              <p className="text-base font-semibold">{renderValue(auditPlan.calendrier)}</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="bg-blue-50 p-2 rounded-full">
              <GitBranch className="h-5 w-5 text-blue-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Statut</p>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(auditPlan.status)}`}>
                {auditPlan.status || 'N/A'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Description Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-indigo-500" />
            Description
          </h3>
        </div>

        <div className="p-4">
          <p className="text-gray-700 whitespace-pre-line">{renderValue(auditPlan.description)}</p>
        </div>
      </div>

      {/* Planning Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-teal-500" />
            Planification
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4">
          <div className="flex items-start space-x-3">
            <div className="bg-blue-50 p-2 rounded-full">
              <CalendarDays className="h-5 w-5 text-blue-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Date de Début</p>
              <p className="text-base">{renderValue(formatDate(auditPlan.datedebut))}</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="bg-purple-50 p-2 rounded-full">
              <CalendarRange className="h-5 w-5 text-purple-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Date de Fin</p>
              <p className="text-base">{renderValue(formatDate(auditPlan.datefin))}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Resources Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
            <Users className="h-5 w-5 mr-2 text-orange-500" />
            Ressources
          </h3>
        </div>

        <div className="p-4">
          <div className="flex items-start space-x-3">
            <div className="bg-orange-50 p-2 rounded-full">
              <Users className="h-5 w-5 text-orange-500" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Ressources Prévues</p>
              <p className="text-base">{renderValue(auditPlan.resources)}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default VueEnsembleTab;