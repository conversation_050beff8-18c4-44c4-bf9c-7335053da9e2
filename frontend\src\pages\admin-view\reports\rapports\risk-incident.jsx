import React, { useState, useEffect, useRef } from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";
import * as XLSX from "https://cdn.jsdelivr.net/npm/xlsx@0.18.5/xlsx.mjs";
import { Button } from "@/components/ui/button";
import { getApiBaseUrl } from "@/utils/api-config";
import EmailReportModal from '@/components/reports/EmailReportModal';

const RapportRiskIncident = (props) => {
  // Add state for email modal
  const [isEmailModalOpen, setIsEmailModalOpen] = useState(false);
  const [emailAttachment, setEmailAttachment] = useState(null);
  
  // Add function to handle email button click
  const handleEmailReport = async () => {
    if (!data || !data.length) {
      alert("No data available to generate the PDF.");
      return;
    }
    const pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'in',
      format: 'letter'
    });
    const headers = [
      'Risk Code', 'Risk', 'Net Risk Level', 'Number of Incidents',
      'Gross Loss', 'Recoveries', 'Net Loss'
    ];
    const rows = data.map(row => [
      row["Code de Risque"] || '',
      row.Risque || '',
      row["Niveau de Risque net"] || '',
      row["Nb des incidents"] || 0,
      `€${parseFloat(row["Perte brute"] || 0).toLocaleString()}`,
      `€${parseFloat(row["Récupérations"] || 0).toLocaleString()}`,
      `€${parseFloat(row["Perte nette"] || 0).toLocaleString()}`
    ]);
    autoTable(pdf, {
      head: [headers],
      body: rows,
      startY: 0.5,
      theme: 'striped',
      headStyles: { 
        fillColor: [26, 41, 66],
        textColor: [255, 255, 255], 
        fontSize: 10, 
        fontStyle: 'bold',
        halign: 'center'
      },
      styles: { 
        fontSize: 9, 
        cellPadding: 0.1, 
        font: 'helvetica',
        textColor: [31, 41, 55]
      },
      columnStyles: {
        0: { cellWidth: 1.3 },
        1: { cellWidth: 2.2 },
        2: { cellWidth: 1.55 },
        3: { cellWidth: 1.0, halign: 'center' },
        4: { cellWidth: 1.3, textColor: [220, 38, 38] },
        5: { cellWidth: 1.3, textColor: [22, 163, 74] },
        6: { cellWidth: 1.3, fontStyle: 'bold' },
      },
      alternateRowStyles: {
        fillColor: [247, 250, 252]
      },
      margin: { left: 0.5, right: 0.5 }
    });
    // Add Loss Summary
    let yPosition = pdf.lastAutoTable.finalY + 0.5;
    pdf.setFontSize(16);
    pdf.setTextColor(45, 55, 72);
    pdf.text('Loss Summary', 0.5, yPosition);
    yPosition += 0.5;
    pdf.setFontSize(12);
    pdf.setTextColor(113, 128, 150);
    pdf.text('Total Gross Losses:', 0.5, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    pdf.text(`€${totalGrossLoss.toLocaleString()}`, 3.5, yPosition);
    yPosition += 0.5;
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(113, 128, 150);
    pdf.text('Total Recoveries:', 0.5, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    pdf.text(`€${totalRecoveries.toLocaleString()}`, 3.5, yPosition);
    yPosition += 0.5;
    pdf.setTextColor(113, 128, 150);
    pdf.text('Total Net Losses:', 0.5, yPosition);
    pdf.setTextColor(26, 41, 66);
    pdf.setFont('helvetica', 'bold');
    pdf.text(`€${totalNetLoss.toLocaleString()}`, 3.5, yPosition);

    // Get PDF as base64
    const pdfBase64 = pdf.output('datauristring').split(',')[1];
    setEmailAttachment({
      base64: pdfBase64,
      filename: 'risk_incident_report.pdf',
      contentType: 'application/pdf'
    });
    setIsEmailModalOpen(true);
  };
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const tableRef = useRef(null);
  const API_BASE_URL = getApiBaseUrl();
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_BASE_URL}/risk-incident`, {
          headers: {
            'Content-Type': 'application/json',
            'x-user-id': '1',
          },
          credentials: 'include',
        });
        if (!response.ok) {
          throw new Error(`Failed to fetch risk incident data: ${response.statusText}`);
        }
        const result = await response.json();
        if (!result.success) {
          throw new Error(result.message || 'Failed to fetch data');
        }
        setData(result.data || []);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const downloadPDF = async () => {
    try {
      if (!data || !data.length) {
        alert("No data available to generate the PDF.");
        return;
      }

      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'in',
        format: 'letter'
      });

      // Define table columns and rows
      const headers = [
        'Risk Code', 'Risk', 'Net Risk Level', 'Number of Incidents',
        'Gross Loss', 'Recoveries', 'Net Loss'
      ];
      const rows = data.map(row => [
        row["Code de Risque"] || '',
        row.Risque || '',
        row["Niveau de Risque net"] || '',
        row["Nb des incidents"] || 0,
        `€${parseFloat(row["Perte brute"] || 0).toLocaleString()}`,
        `€${parseFloat(row["Récupérations"] || 0).toLocaleString()}`,
        `€${parseFloat(row["Perte nette"] || 0).toLocaleString()}`
      ]);

      // Use the imported autoTable function
      autoTable(pdf, {
        head: [headers],
        body: rows,
        startY: 0.5,
        theme: 'striped',
        headStyles: { 
          fillColor: [26, 41, 66],
          textColor: [255, 255, 255], 
          fontSize: 10, 
          fontStyle: 'bold',
          halign: 'center'
        },
        styles: { 
          fontSize: 9, 
          cellPadding: 0.1, 
          font: 'helvetica',
          textColor: [31, 41, 55]
        },
        columnStyles: {
          0: { cellWidth: 1.3 }, // Risk Code
          1: { cellWidth: 2.2 }, // Risk
          2: { cellWidth: 1.55 }, // Net Risk Level
          3: { cellWidth: 1.0, halign: 'center' }, // Number of Incidents
          4: { cellWidth: 1.3, textColor: [220, 38, 38] }, // Gross Loss
          5: { cellWidth: 1.3, textColor: [22, 163, 74] }, // Recoveries
          6: { cellWidth: 1.3, fontStyle: 'bold' }, // Net Loss
        },
        alternateRowStyles: {
          fillColor: [247, 250, 252]
        },
        margin: { left: 0.5, right: 0.5 }
      });

      // Add Loss Summary
      let yPosition = pdf.lastAutoTable.finalY + 0.5;
      pdf.setFontSize(16);
      pdf.setTextColor(45, 55, 72);
      pdf.text('Loss Summary', 0.5, yPosition);
      yPosition += 0.5;

      pdf.setFontSize(12);
      pdf.setTextColor(113, 128, 150);
      pdf.text('Total Gross Losses:', 0.5, yPosition);
      pdf.setTextColor(26, 41, 66);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`€${totalGrossLoss.toLocaleString()}`, 3.5, yPosition);
      yPosition += 0.5;

      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(113, 128, 150);
      pdf.text('Total Recoveries:', 0.5, yPosition);
      pdf.setTextColor(26, 41, 66);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`€${totalRecoveries.toLocaleString()}`, 3.5, yPosition);
      yPosition += 0.5;

      pdf.setTextColor(113, 128, 150);
      pdf.text('Total Net Losses:', 0.5, yPosition);
      pdf.setTextColor(26, 41, 66);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`€${totalNetLoss.toLocaleString()}`, 3.5, yPosition);

      // Save the PDF
      pdf.save('risk_incident_report.pdf');
      console.log("PDF generated and downloaded successfully.");
    } catch (err) {
      console.error("Error generating PDF:", err);
      alert("Error generating PDF. Please check the console for details.");
    }
  };

  const downloadExcel = () => {
    if (!data) return;
    const excelData = data.map(row => ({
      'Risk Code': row["Code de Risque"],
      Risk: row.Risque,
      'Net Risk Level': row["Niveau de Risque net"],
      'Number of Incidents': row["Nb des incidents"],
      'Gross Loss': row["Perte brute"],
      Recoveries: row["Récupérations"],
      'Net Loss': row["Perte nette"],
    }));
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "RiskIncident");
    XLSX.writeFile(workbook, "risk_incident_report.xlsx");
  };

  // Calculate totals for the summary section
  const totalGrossLoss = data.reduce((sum, row) => sum + (parseFloat(row["Perte brute"]) || 0), 0);
  const totalRecoveries = data.reduce((sum, row) => sum + (parseFloat(row["Récupérations"]) || 0), 0);
  const totalNetLoss = totalGrossLoss - totalRecoveries;

  // Define color mappings using HEX to avoid oklch
  const colorMap = {
    'Very High': { bg: '#FF4C4C', text: '#FFFFFF', badgeBg: '#FF9999', badgeText: '#3D0000' },
    'High': { bg: '#FFE5CC', text: '#4A2C00', badgeBg: '#FFCC99', badgeText: '#4A2C00' },
    'Medium': { bg: '#FFFFCC', text: '#4A4A00', badgeBg: '#FFFF99', badgeText: '#4A4A00' },
    'Low': { bg: '#CCFFD9', text: '#004A1E', badgeBg: '#99FFBB', badgeText: '#004A1E' },
    'Very Low': { bg: '#99FFCC', text: '#004A2C', badgeBg: '#66FF99', badgeText: '#004A2C' },
  };

  if (loading) return (
    <div style={{ padding: '24px', display: 'flex', justifyContent: 'center', alignItems: 'center', height: '256px' }}>
      <div style={{ animation: 'spin 1s linear infinite', borderRadius: '50%', width: '48px', height: '48px', border: '4px solid rgb(34, 197, 94)', borderTopColor: 'transparent' }}></div>
    </div>
  );

  if (error) return (
    <div style={{ padding: '24px', backgroundColor: 'rgb(249, 250, 251)', borderRadius: '8px', border: '1px solid rgb(229, 231, 235)', color: 'rgb(107, 114, 128)', textAlign: 'center' }}>
      <svg style={{ width: '48px', height: '48px', margin: '0 auto 16px', color: 'rgb(156, 163, 175)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <p style={{ fontSize: '18px' }}>Error: {error}</p>
    </div>
  );

  if (!data.length) return (
    <div style={{ padding: '24px', backgroundColor: 'rgb(249, 250, 251)', borderRadius: '8px', border: '1px solid rgb(229, 231, 235)', color: 'rgb(107, 114, 128)', textAlign: 'center' }}>
      <svg style={{ width: '48px', height: '48px', margin: '0 auto 16px', color: 'rgb(156, 163, 175)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <p style={{ fontSize: '18px' }}>No data available</p>
    </div>
  );

  return (
    <div style={{ padding: '24px', background: 'linear-gradient(to bottom, rgb(243, 244, 246), rgb(209, 213, 219))' }}>
      <div style={{ backgroundColor: 'rgb(255, 255, 255)', padding: '24px', borderRadius: '12px', boxShadow: '0 4px 6px rgba(0,0,0,0.1)', border: '1px solid rgb(229, 231, 235)' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <h2 style={{ fontSize: '24px', fontWeight: '700', color: 'rgb(26, 41, 66)' }}>Back Testing Rapport</h2>
        </div>

        <div id="risk-incident-table" style={{ overflow: 'hidden', borderRadius: '12px', border: '1px solid rgb(229, 231, 235)', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
          <div ref={tableRef}>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead style={{ backgroundColor: 'rgb(26, 41, 66)', color: 'rgb(255, 255, 255)', fontWeight: '600', padding: '12px' }}>
                    Risk Code
                  </TableHead>
                  <TableHead style={{ backgroundColor: 'rgb(26, 41, 66)', color: 'rgb(255, 255, 255)', fontWeight: '600', padding: '12px' }}>
                    Risk
                  </TableHead>
                  <TableHead style={{ backgroundColor: 'rgb(26, 41, 66)', color: 'rgb(255, 255, 255)', fontWeight: '600', padding: '12px' }}>
                    Net Risk Level
                  </TableHead>
                  <TableHead style={{ backgroundColor: 'rgb(26, 41, 66)', color: 'rgb(255, 255, 255)', fontWeight: '600', padding: '12px' }}>
                    Number of Incidents
                  </TableHead>
                  <TableHead style={{ backgroundColor: 'rgb(26, 41, 66)', color: 'rgb(255, 255, 255)', fontWeight: '600', padding: '12px' }}>
                    Gross Loss
                  </TableHead>
                  <TableHead style={{ backgroundColor: 'rgb(26, 41, 66)', color: 'rgb(255, 255, 255)', fontWeight: '600', padding: '12px' }}>
                    Recoveries
                  </TableHead>
                  <TableHead style={{ backgroundColor: 'rgb(26, 41, 66)', color: 'rgb(255, 255, 255)', fontWeight: '600', padding: '12px' }}>
                    Net Loss
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((row, index) => {
                  const riskLevel = row["Niveau de Risque net"];
                  const colors = colorMap[riskLevel] || { 
                    bg: index % 2 === 0 ? 'rgb(255, 255, 255)' : 'rgb(247, 250, 252)', 
                    text: 'rgb(0, 0, 0)', 
                    badgeBg: 'rgb(229, 231, 235)', 
                    badgeText: 'rgb(31, 41, 55)' 
                  };

                  return (
                    <TableRow 
                      key={index} 
                      style={{ backgroundColor: colors.bg, color: colors.text }}
                    >
                      <TableCell style={{ fontWeight: '500' }}>{row["Code de Risque"]}</TableCell>
                      <TableCell>{row.Risque}</TableCell>
                      <TableCell>
                        <span 
                          style={{ 
                            backgroundColor: colors.badgeBg, 
                            color: colors.badgeText, 
                            padding: '4px 10px', 
                            borderRadius: '9999px', 
                            fontSize: '12px', 
                            fontWeight: '500',
                            display: 'inline-flex',
                            alignItems: 'center',
                          }}
                        >
                          {riskLevel}
                        </span>
                      </TableCell>
                      <TableCell style={{ textAlign: 'center', fontWeight: '600' }}>{row["Nb des incidents"]}</TableCell>
                      <TableCell style={{ color: 'rgb(220, 38, 38)' }}>{`€${parseFloat(row["Perte brute"]).toLocaleString()}`}</TableCell>
                      <TableCell style={{ color: 'rgb(22, 163, 74)' }}>{`€${parseFloat(row["Récupérations"]).toLocaleString()}`}</TableCell>
                      <TableCell style={{ fontWeight: '700' }}>{`€${parseFloat(row["Perte nette"]).toLocaleString()}`}</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </div>

        <div style={{ display: 'flex', gap: '16px', marginTop: '16px' }}>
          <Button 
            onClick={downloadPDF} 
            style={{ backgroundColor: 'rgb(34, 197, 94)', color: 'rgb(255, 255, 255)' }}
            onMouseOver={e => e.currentTarget.style.backgroundColor = 'rgb(22, 163, 74)'}
            onMouseOut={e => e.currentTarget.style.backgroundColor = 'rgb(34, 197, 94)'}
          >
            Download PDF
          </Button>
          <Button 
            onClick={downloadExcel} 
            style={{ backgroundColor: 'rgb(34, 197, 94)', color: 'rgb(255, 255, 255)' }}
            onMouseOver={e => e.currentTarget.style.backgroundColor = 'rgb(22, 163, 74)'}
            onMouseOut={e => e.currentTarget.style.backgroundColor = 'rgb(34, 197, 94)'}
          >
            Download Excel
          </Button>
          <Button 
            onClick={handleEmailReport} 
            style={{ backgroundColor: 'rgb(59, 130, 246)', color: 'rgb(255, 255, 255)' }}
            onMouseOver={e => e.currentTarget.style.backgroundColor = 'rgb(37, 99, 235)'}
            onMouseOut={e => e.currentTarget.style.backgroundColor = 'rgb(59, 130, 246)'}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
            </svg>
            Send via Email
          </Button>
        </div>

        <div style={{ marginTop: '24px', backgroundColor: 'rgb(249, 250, 251)', borderRadius: '12px', padding: '16px', border: '1px solid rgb(229, 231, 235)' }}>
          <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '12px', color: 'rgb(31, 41, 55)' }}>Loss Summary</h3>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px' }}>
            <div style={{ backgroundColor: 'rgb(255, 255, 255)', padding: '16px', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid rgb(229, 231, 235)' }}>
              <p style={{ fontSize: '14px', color: 'rgb(107, 114, 128)', marginBottom: '4px' }}>Total Gross Losses</p>
              <p style={{ fontSize: '20px', fontWeight: '700', color: 'rgb(220, 38, 38)' }}>€${totalGrossLoss.toLocaleString()}</p>
            </div>
            <div style={{ backgroundColor: 'rgb(255, 255, 255)', padding: '16px', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid rgb(229, 231, 235)' }}>
              <p style={{ fontSize: '14px', color: 'rgb(107, 114, 128)', marginBottom: '4px' }}>Total Recoveries</p>
              <p style={{ fontSize: '20px', fontWeight: '700', color: 'rgb(22, 163, 74)' }}>€${totalRecoveries.toLocaleString()}</p>
            </div>
            <div style={{ backgroundColor: 'rgb(255, 255, 255)', padding: '16px', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid rgb(229, 231, 235)' }}>
              <p style={{ fontSize: '14px', color: 'rgb(107, 114, 128)', marginBottom: '4px' }}>Total Net Losses</p>
              <p style={{ fontSize: '20px', fontWeight: '700', color: 'rgb(26, 41, 66)' }}>€${totalNetLoss.toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
      <EmailReportModal
        isOpen={isEmailModalOpen}
        onClose={() => setIsEmailModalOpen(false)}
        reportType="back-testing"
        reportTitle="Back Testing"
        reportData={data}
        defaultAttachment={emailAttachment}
      />
    </div>
  );
};

export default RapportRiskIncident;
