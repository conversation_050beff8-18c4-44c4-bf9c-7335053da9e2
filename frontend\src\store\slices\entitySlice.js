import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { toast } from 'sonner';
import { getApiBaseUrl } from '../../utils/api-config';

const API_URL = `${getApiBaseUrl()}/entities`;

// Initial state
const initialState = {
  entities: [],
  currentEntity: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: ''
};

// Get all entities
export const getAllEntities = createAsyncThunk(
  'entities/getAll',
  async (_, thunkAPI) => {
    try {
      const response = await axios.get(API_URL, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch entities';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get entity by ID
export const getEntityById = createAsyncThunk(
  'entities/getById',
  async (id, thunkAPI) => {
    try {
      const response = await axios.get(`${API_URL}/${id}`, {
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch entity';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create new entity
export const createEntity = createAsyncThunk(
  'entities/create',
  async (entityData, thunkAPI) => {
    try {
      const response = await axios.post(API_URL, entityData, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to create entity';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update entity
export const updateEntity = createAsyncThunk(
  'entities/update',
  async ({ id, entityData }, thunkAPI) => {
    try {
      const response = await axios.put(`${API_URL}/${id}`, entityData, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      });
      return response.data;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to update entity';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete entity
export const deleteEntity = createAsyncThunk(
  'entities/delete',
  async (id, thunkAPI) => {
    try {
      await axios.delete(`${API_URL}/${id}`, {
        withCredentials: true
      });
      return id;
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete entity';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Entity slice
const entitySlice = createSlice({
  name: 'entity',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all entities
      .addCase(getAllEntities.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllEntities.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.entities = action.payload.data || [];
      })
      .addCase(getAllEntities.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Get entity by ID
      .addCase(getEntityById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getEntityById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentEntity = action.payload.data;
      })
      .addCase(getEntityById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Create entity
      .addCase(createEntity.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createEntity.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.entities.push(action.payload.data);
        toast.success('Entity created successfully');
      })
      .addCase(createEntity.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Update entity
      .addCase(updateEntity.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateEntity.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.entities = state.entities.map(entity => 
          entity.entityID === action.payload.data.entityID ? action.payload.data : entity
        );
        state.currentEntity = action.payload.data;
        toast.success('Entity updated successfully');
      })
      .addCase(updateEntity.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Delete entity
      .addCase(deleteEntity.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteEntity.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.entities = state.entities.filter(entity => entity.entityID !== action.meta.arg);
        toast.success('Entity deleted successfully');
      })
      .addCase(deleteEntity.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
  }
});

export const { reset } = entitySlice.actions;
export default entitySlice.reducer;
