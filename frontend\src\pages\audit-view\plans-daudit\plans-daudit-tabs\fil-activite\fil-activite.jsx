import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Clock, Settings, FileText, Activity, ArrowLeft, ArrowRight, Loader, Trash2, Link, Database } from 'lucide-react';

// MOCK DATA for now
const mockActivities = [
  {
    id: 1,
    type: 'creation',
    user: '<PERSON>',
    timestamp: new Date().toISOString(),
    details: 'Plan d’audit créé',
  },
  {
    id: 2,
    type: 'update',
    user: '<PERSON>',
    timestamp: new Date(Date.now() - 3600 * 1000).toISOString(),
    field: 'name',
    oldValue: 'Plan 2023',
    newValue: 'Plan Annuel 2023',
  },
  {
    id: 3,
    type: 'update',
    user: '<PERSON>',
    timestamp: new Date(Date.now() - 7200 * 1000).toISOString(),
    field: 'calendrier',
    oldValue: '2022',
    newValue: '2023',
  },
  {
    id: 4,
    type: 'deletion',
    user: 'Admin',
    timestamp: new Date(Date.now() - 10800 * 1000).toISOString(),
    details: 'Suppression d’un commentaire',
  },
];

export default function PlanAuditFilActivite() {
  const [isOpen, setIsOpen] = useState(true);
  const [activities] = useState(mockActivities);
  const [loading] = useState(false);
  const [error] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = activities.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(activities.length / itemsPerPage);

  const goToNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  };
  const goToPreviousPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  const getActivityIcon = (type) => {
    switch(type) {
      case 'creation': return <FileText className="h-5 w-5 text-green-500" />;
      case 'update': return <Settings className="h-5 w-5 text-blue-500" />;
      case 'deletion': return <Trash2 className="h-5 w-5 text-red-500" />;
      case 'link': return <Link className="h-5 w-5 text-purple-500" />;
      case 'unlink': return <Database className="h-5 w-5 text-orange-500" />;
      default: return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit'
    });
  };

  const getFieldDisplayName = (fieldName) => {
    const fieldNames = {
      'name': 'Nom',
      'calendrier': 'Calendrier',
      'status': 'Statut',
      'directeuraudit': 'Directeur d’audit',
      'datedebut': 'Date de début',
      'datefin': 'Date de fin',
      'avancement': 'Avancement',
      'description': 'Description',
      'comment': 'Commentaire',
    };
    return fieldNames[fieldName] || fieldName;
  };

  const renderActivityItem = (activity) => (
    <div key={activity.id} className="mb-4 border-b pb-4 last:border-b-0">
      <div className="flex items-start">
        <div className="mr-3 mt-1">{getActivityIcon(activity.type)}</div>
        <div className="flex-1">
          <div className="text-sm text-gray-500 mb-1">{formatDate(activity.timestamp)}</div>
          <div className="font-medium mb-1">
            {activity.user} {getActivityText(activity)}
          </div>
          {activity.type === 'update' && activity.field && (
            <div className="text-sm bg-gray-50 p-2 rounded mt-1">
              <span className="font-medium">{getFieldDisplayName(activity.field)}:</span>{' '}
              {activity.oldValue !== undefined && (
                <span className="line-through mr-2 text-red-600">{activity.oldValue || 'Vide'}</span>
              )}
              {activity.newValue !== undefined && (
                <span className="text-green-600 font-medium">{activity.newValue || 'Vide'}</span>
              )}
            </div>
          )}
          {activity.details && (
            <div className="text-sm text-gray-600 mt-1">{activity.details}</div>
          )}
        </div>
      </div>
      </div>
    );

  const getActivityText = (activity) => {
    switch(activity.type) {
      case 'creation': return 'a créé ce plan d’audit';
      case 'update': return 'a modifié le plan d’audit';
      case 'deletion': return 'a supprimé le plan d’audit';
      case 'link': return 'a lié une donnée de référence';
      case 'unlink': return 'a délié une donnée de référence';
      default: return 'a effectué une action';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <Activity className="h-6 w-6 mr-3 text-[#F62D51]" />
          Fil d'activité du plan d'audit
        </h2>
      </div>
      <div className="border rounded-lg">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gray-50 rounded-t-lg"
          onClick={() => setIsOpen(!isOpen)}
        >
          <div className="flex items-center gap-2">
            {isOpen ? (
              <ChevronUp className="h-5 w-5" />
            ) : (
              <ChevronDown className="h-5 w-5" />
            )}
            <span className="text-lg font-medium">Fil d'Activité</span>
          </div>
        </button>
        {isOpen && (
          <div className="p-4">
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader className="h-6 w-6 animate-spin mr-2" />
                <span>Chargement des activités...</span>
              </div>
            ) : error ? (
              <div className="text-red-500 py-4">{error}</div>
            ) : activities.length === 0 ? (
              <div className="text-gray-500 py-4">Aucune activité enregistrée pour le moment.</div>
            ) : (
              <>
                <div className="space-y-2">
                  {currentItems.map(renderActivityItem)}
                </div>
                {totalPages > 1 && (
                  <div className="flex justify-between items-center mt-6 pt-4 border-t">
                    <button
                      onClick={goToPreviousPage}
                      disabled={currentPage === 1}
                      className={`flex items-center ${
                        currentPage === 1
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-blue-500 hover:text-blue-700'
                      }`}
                    >
                      <ArrowLeft className="h-4 w-4 mr-1" />
                      Précédent
                    </button>
                    <span className="text-sm text-gray-600">
                      Page {currentPage} sur {totalPages}
                    </span>
                    <button
                      onClick={goToNextPage}
                      disabled={currentPage === totalPages}
                      className={`flex items-center ${
                        currentPage === totalPages
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-blue-500 hover:text-blue-700'
                      }`}
                    >
                      Suivant
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
