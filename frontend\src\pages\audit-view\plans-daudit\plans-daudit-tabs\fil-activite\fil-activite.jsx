import { useParams } from "react-router-dom";
import { Activity } from "lucide-react"; // Icon for Fil d'Activité

function FilActiviteTab({ auditPlan }) {
  const { id } = useParams();

  if (!auditPlan) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">Chargement des informations du plan d'audit...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 py-4">
      <div className="bg-white p-8 rounded-lg shadow text-center">
        <Activity className="h-12 w-12 mx-auto text-gray-300 mb-4" />
        <h2 className="text-xl font-semibold text-[#1A202C] mb-2">
          Fil d'Activité
        </h2>
        <p className="text-md text-gray-600">
          Contenu pour le fil d'activité du plan d'audit: <span className="font-medium">{auditPlan.name}</span>.
        </p>
        <p className="text-sm text-gray-400 mt-4">
          (Ce composant est un placeholder. Le contenu spécifique sera ajouté ultérieurement.)
        </p>
      </div>
    </div>
  );
}

export default FilActiviteTab;
