require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });
const { Sequelize } = require('sequelize');

// Create Sequelize instance
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: false
  }
);

async function testControlAssignmentWithEmail() {
  try {
    console.log('🧪 Testing Control Assignment with Email Notifications...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Get test users
    const [users] = await sequelize.query(`
      SELECT id, username, email FROM "Users" WHERE email IS NOT NULL LIMIT 2;
    `);
    
    if (users.length === 0) {
      console.error('❌ No users with email addresses found in database.');
      return;
    }
    
    console.log(`✅ Found ${users.length} test users with email addresses:`);
    users.forEach(user => {
      console.log(`   • ${user.username} (${user.email})`);
    });

    // Get a test control
    const [controls] = await sequelize.query(`
      SELECT "controlID", name FROM "Control" LIMIT 1;
    `);
    
    if (controls.length === 0) {
      console.error('❌ No controls found in database. Please create a control first.');
      return;
    }
    
    const control = controls[0];
    console.log(`✅ Found test control: ${control.name} (ID: ${control.controlID})`);

    // Simulate the assignment API call
    const userIds = users.map(u => u.id);
    const assignmentData = {
      userIds: userIds,
      assignedBy: null
    };

    console.log('\n📝 Assignment data:', assignmentData);

    // Make the actual API call
    const fetch = require('node-fetch');
    const API_BASE_URL = 'http://localhost:5001/api';
    const url = `${API_BASE_URL}/control-assignments/control/${control.controlID}/assign`;
    
    console.log(`\n📡 Making API call: POST ${url}`);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(assignmentData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API call failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ API call successful:', result);

    // Check if notifications were created
    const [notifications] = await sequelize.query(`
      SELECT id, type, entity_id, entity_name, message, user_id, is_read, created_at
      FROM "Notifications" 
      WHERE type = 'control_assignment' AND entity_id = '${control.controlID}'
      ORDER BY created_at DESC
      LIMIT 10;
    `);
    
    console.log(`\n🔔 Created notifications: ${notifications.length}`);
    notifications.forEach(notif => {
      const user = users.find(u => u.id === notif.user_id);
      console.log(`   • User: ${user?.username || notif.user_id} - ${notif.message}`);
    });

    // Check assignments in database
    const [assignments] = await sequelize.query(`
      SELECT "userId", "controlId", "assignedAt"
      FROM control_question_assignments 
      WHERE "controlId" = '${control.controlID}';
    `);
    
    console.log(`\n📋 Database assignments: ${assignments.length}`);
    assignments.forEach(assignment => {
      const user = users.find(u => u.id === assignment.userId);
      console.log(`   • User: ${user?.username || assignment.userId} - Assigned at: ${assignment.assignedAt}`);
    });

    console.log('\n🎉 Control Assignment with Email test completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   • Control: ${control.name} (${control.controlID})`);
    console.log(`   • Users assigned: ${result.data?.assignedUsers?.length || 0}`);
    console.log(`   • Notifications created: ${notifications.length}`);
    console.log(`   • Database assignments: ${assignments.length}`);
    console.log(`   • New assignments: ${result.data?.newAssignments || 0}`);
    console.log(`   • Notifications sent: ${result.data?.notificationsSent || 0}`);
    
    console.log('\n✉️ Check the email inboxes of assigned users for notification emails!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n🔧 Server connection issue:');
      console.log('   • Make sure the backend server is running on port 5001');
      console.log('   • Run: npm start in the backend directory');
    }
    
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Run the test
if (require.main === module) {
  testControlAssignmentWithEmail()
    .then(() => {
      console.log('\n✅ Test script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test script failed');
      process.exit(1);
    });
}

module.exports = testControlAssignmentWithEmail;
