import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { toast } from "react-hot-toast";
import { Loader2 } from "lucide-react";
import useReferenceData from "../../hooks/useReferenceData";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";
import { Checkbox } from "../ui/checkbox";
import { useDispatch, useSelector } from "react-redux";
import { getAllActionPlans } from "../../store/slices/actionPlanSlice";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { getApiBaseUrl } from '../../utils/api-config';
export function CreateRiskForm() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const API_BASE_URL = getApiBaseUrl();
  // Helper functions for impact, DMR, and probability labels
  const getImpactLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const impactLabels = {
      '1': { label: 'Very Low', color: 'bg-blue-100 text-blue-800' },
      '2': { label: 'Low', color: 'bg-green-100 text-green-800' },
      '3': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
      '4': { label: 'High', color: 'bg-orange-100 text-orange-800' },
      '5': { label: 'Very High', color: 'bg-red-100 text-red-800' }
    };
    return impactLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const getDMRLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const dmrLabels = {
      '1': { label: 'Very Strong', color: 'bg-green-100 text-green-800' },
      '4': { label: 'Strong', color: 'bg-blue-100 text-blue-800' },
      '9': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
      '16': { label: 'Weak', color: 'bg-orange-100 text-orange-800' },
      '25': { label: 'Very Weak', color: 'bg-red-100 text-red-800' }
    };
    return dmrLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const getProbabilityLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const probabilityLabels = {
      '1': { label: 'Very Low', color: 'bg-blue-100 text-blue-800' },
      '2': { label: 'Low', color: 'bg-green-100 text-green-800' },
      '3': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
      '4': { label: 'High', color: 'bg-orange-100 text-orange-800' },
      '5': { label: 'Very High', color: 'bg-red-100 text-red-800' }
    };
    return probabilityLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const getAppetiteLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const appetiteLabels = {
      '1': { label: 'Very Low', color: 'bg-blue-100 text-blue-800' },
      '2': { label: 'Low', color: 'bg-green-100 text-green-800' },
      '3': { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
      '4': { label: 'High', color: 'bg-orange-100 text-orange-800' },
      '5': { label: 'Very High', color: 'bg-red-100 text-red-800' }
    };
    return appetiteLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const [formData, setFormData] = useState({
    riskID: "",
    name: "",
    code: "",
    impact: "",
    DMR: "",
    probability: "",
    appetite: "",
    acceptance: false,
    avoidance: false,
    insurance: false,
    reduction: false,
    comment: "",
    mitigatingActionPlan: "",
    businessProcessID: "",
    organizationalProcessID: "",
    operationID: "",
    applicationID: "",
    entityID: "",
    riskTypeID: "",
    controlID: "",
  });

  // Get reference data from Redux
  const {
    businessProcesses,
    organizationalProcesses,
    operations,
    applications,
    entities,
    riskTypes,
    controls,
    isLoading: referenceDataLoading
  } = useReferenceData();

  // Get action plans from Redux
  const { actionPlans, isLoading: actionPlansLoading } = useSelector((state) => state.actionPlan);

  // Fetch action plans when component mounts
  useEffect(() => {
    dispatch(getAllActionPlans());
  }, [dispatch]);

  const riskLevels = ["Very Low", "Low", "Medium", "High", "Very High"];

  // Reference data is automatically loaded by the useReferenceData hook

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCheckboxChange = (name) => {
    setFormData((prev) => ({
      ...prev,
      [name]: !prev[name],
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Prepare the data for submission
      const riskData = {
        ...formData,
        riskID: formData.riskID || `RISK_${Date.now()}`, // Generate ID if not provided
        acceptance: Boolean(formData.acceptance),
        avoidance: Boolean(formData.avoidance),
        insurance: Boolean(formData.insurance),
        reduction: Boolean(formData.reduction),
        // Ensure mitigatingActionPlan is included
        mitigatingActionPlan: formData.mitigatingActionPlan || null,
        // Convert empty strings to null for reference fields
        businessProcessID: formData.businessProcessID || null,
        organizationalProcessID: formData.organizationalProcessID || null,
        operationID: formData.operationID || null,
        applicationID: formData.applicationID || null,
        entityID: formData.entityID || null,
        riskTypeID: formData.riskTypeID || null,
        controlID: formData.controlID || null
      };

      const response = await axios.post(
        `${API_BASE_URL}/risk`,
        riskData,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data.success) {
        toast.success("Risk created successfully", {
          style: {
            background: '#10B981',
            color: 'white',
          },
        });
        navigate('/admin/risks');
      }
    } catch (error) {
      console.error('API Error Details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        serverError: error.response?.data?.error
      });

      toast.error(
        error.response?.data?.message ||
        error.message ||
        "Failed to create risk",
        {
          style: {
            background: '#EF4444',
            color: 'white',
          },
        }
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (referenceDataLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
          <span className="mt-4 text-gray-600">Loading reference data...</span>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="p-6 space-y-6 max-w-3xl mx-auto">
      <div className="space-y-4">
        {/* Name and Code fields */}
        <div className="grid grid-cols-4 gap-6">
          <div className="space-y-2 col-span-3">
            <label className="text-sm font-medium">
              Name <span className="text-red-500">*</span>
            </label>
            <Input
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
              placeholder="Enter risk name"
            />
          </div>

          <div className="space-y-2 col-span-1">
            <label className="text-sm font-medium">Code</label>
            <Input
              name="code"
              value={formData.code}
              onChange={handleInputChange}
              placeholder="Enter code"
            />
          </div>
        </div>

        {/* Impact, DMR, and Probability on the same line */}
        <div className="grid grid-cols-3 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">Impact</label>
            <Select
              value={formData.impact}
              onValueChange={(value) => handleSelectChange("impact", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select impact level">
                  {formData.impact ? getImpactLabel(formData.impact).label : "Select impact level"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {[1, 2, 3, 4, 5].map((level) => (
                  <SelectItem key={level} value={level.toString()}>
                    {getImpactLabel(level).label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">DMR</label>
            <Select
              value={formData.DMR}
              onValueChange={(value) => handleSelectChange("DMR", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select DMR level">
                  {formData.DMR ? getDMRLabel(formData.DMR).label : "Select DMR level"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {[1, 2, 3, 4, 5].map((level) => (
                  <SelectItem key={level} value={level.toString()}>
                    {getDMRLabel(level).label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Probability</label>
            <Select
              value={formData.probability}
              onValueChange={(value) => handleSelectChange("probability", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select probability level">
                  {formData.probability ? getProbabilityLabel(formData.probability).label : "Select probability level"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {[1, 2, 3, 4, 5].map((level) => (
                  <SelectItem key={level} value={level.toString()}>
                    {getProbabilityLabel(level).label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Risk ID field removed */}

        {/* Risk Treatment Options */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Risk Treatment Options</label>
          <div className="flex gap-6 flex-wrap">
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={formData.acceptance}
                onCheckedChange={() => handleCheckboxChange("acceptance")}
              />
              <label className="text-sm">Acceptance</label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={formData.avoidance}
                onCheckedChange={() => handleCheckboxChange("avoidance")}
              />
              <label className="text-sm">Avoidance</label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={formData.insurance}
                onCheckedChange={() => handleCheckboxChange("insurance")}
              />
              <label className="text-sm">Insurance</label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={formData.reduction}
                onCheckedChange={() => handleCheckboxChange("reduction")}
              />
              <label className="text-sm">Reduction</label>
            </div>
          </div>
        </div>

        {/* Comment */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Comment</label>
          <Textarea
            name="comment"
            value={formData.comment}
            onChange={handleInputChange}
            placeholder="Add any additional comments"
            className="min-h-[100px]"
          />
        </div>

        {/* Mitigating Action Plan */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Mitigating Action Plan</label>
          <Select
            value={formData.mitigatingActionPlan || "none"}
            onValueChange={(value) => handleSelectChange("mitigatingActionPlan", value === "none" ? "" : value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select an action plan" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">None</SelectItem>
              {actionPlans && actionPlans.map((plan) => (
                <SelectItem key={plan.actionPlanID} value={plan.actionPlanID}>
                  {plan.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {actionPlansLoading && (
            <div className="flex items-center justify-center py-2">
              <Loader2 className="h-4 w-4 animate-spin text-gray-500 mr-2" />
              <span className="text-sm text-gray-500">Loading action plans...</span>
            </div>
          )}
        </div>

        {/* Reference Fields */}
        <div className="grid grid-cols-2 gap-6">
          {/* Business Process */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Business Process</label>
            <Select
              value={formData.businessProcessID || "none"}
              onValueChange={(value) => handleSelectChange("businessProcessID", value === "none" ? null : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select business process" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {businessProcesses.map((bp) => (
                  <SelectItem key={bp.businessProcessID} value={bp.businessProcessID}>
                    {bp.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Organizational Process */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Organizational Process</label>
            <Select
              value={formData.organizationalProcessID || "none"}
              onValueChange={(value) => handleSelectChange("organizationalProcessID", value === "none" ? null : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select organizational process" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {organizationalProcesses.map((op) => (
                  <SelectItem key={op.organizationalProcessID} value={op.organizationalProcessID}>
                    {op.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Operation */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Operation</label>
            <Select
              value={formData.operationID || "none"}
              onValueChange={(value) => handleSelectChange("operationID", value === "none" ? null : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select operation" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {operations.map((op) => (
                  <SelectItem key={op.operationID} value={op.operationID}>
                    {op.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Application */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Application</label>
            <Select
              value={formData.applicationID || "none"}
              onValueChange={(value) => handleSelectChange("applicationID", value === "none" ? null : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select application" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {applications.map((app) => (
                  <SelectItem key={app.applicationID} value={app.applicationID}>
                    {app.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Entity */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Entity</label>
            <Select
              value={formData.entityID || "none"}
              onValueChange={(value) => handleSelectChange("entityID", value === "none" ? null : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select entity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {entities.map((entity) => (
                  <SelectItem key={entity.entityID} value={entity.entityID}>
                    {entity.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Risk Type */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Risk Type</label>
            <Select
              value={formData.riskTypeID || "none"}
              onValueChange={(value) => handleSelectChange("riskTypeID", value === "none" ? null : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select risk type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {riskTypes.map((rt) => (
                  <SelectItem key={rt.riskTypeID} value={rt.riskTypeID}>
                    {rt.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Control */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Control</label>
            <Select
              value={formData.controlID || "none"}
              onValueChange={(value) => handleSelectChange("controlID", value === "none" ? null : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select control" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {controls.map((control) => (
                  <SelectItem key={control.controlID} value={control.controlID}>
                    {control.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Appetite */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Risk Appetite</label>
          <div className="flex space-x-2">
            {[1, 2, 3, 4, 5].map((value) => {
              const { label, color } = getAppetiteLabel(value);
              return (
                <button
                  key={value}
                  type="button"
                  onClick={() => handleInputChange('appetite', value)}
                  className={`flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ${
                    formData.appetite === value
                      ? color
                      : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
                  }`}
                >
                  {label}
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Buttons */}
      <div className="flex justify-end space-x-4 pt-6 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={() => navigate('/admin/risks')}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-[#F62D51] hover:bg-red-700"
        >
          {isLoading ? 'Creating...' : 'Create Risk'}
        </Button>
      </div>
    </form>
  );
}





