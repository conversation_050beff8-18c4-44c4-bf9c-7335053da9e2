import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  CheckCircle,
  ChevronDown,
  ChevronUp,
  List,
  Plus,
  Search
} from "lucide-react";


import { toast } from "sonner";
import { useMissionAuditContext } from '@/utils/context-helpers';
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';
import AuditTreeEnhanced from '@/components/audit/audit-tree-enhanced';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { createAuditActivity } from '@/services/audit-activity-service';
import { useParams } from "react-router-dom";

function RealisationTab(props) {
  const contextData = useMissionAuditContext();
  const missionAudit = props.missionAudit || contextData.missionAudit;
  const API_BASE_URL = getApiBaseUrl();
  const { planId } = useParams();

  const [treeData, setTreeData] = useState([]);
  const [isLoadingTree, setIsLoadingTree] = useState(false);

  const [isProgrammeOpen, setIsProgrammeOpen] = useState(true);
  const [programmeSearch, setProgrammeSearch] = useState("");

  const [isAddActivityOpen, setIsAddActivityOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newActivity, setNewActivity] = useState({
    name: '',
    datedebut: '',
    datefin: '',
    description: ''
  });

  useEffect(() => {
    if (missionAudit?.id) {
      fetchTreeData();
    }
  }, [missionAudit?.id]);

  const fetchTreeData = async () => {
    if (!missionAudit?.id) return;

    setIsLoadingTree(true);
    try {
      const response = await axios.get(`${API_BASE_URL}/audit-tree/mission/${missionAudit.id}`, { withCredentials: true });
      if (response.data.success) {
        // Flatten fiches de travail and note-synthese to be siblings under activity, but only one note-synthese per activity
        const flattenTree = (tree) => {
          return tree.map(activity => {
            if (!activity.children) return activity;
            const newChildren = [];
            let noteSyntheseNode = null;
            activity.children.forEach(fiche => {
              // Remove note-synthese from under fiche
              if (fiche.children && fiche.children.length > 0) {
                fiche.children.forEach(child => {
                  if (child.type === 'note-synthese') {
                    noteSyntheseNode = { ...child, parentId: activity.id };
                  }
                });
              }
              newChildren.push({ ...fiche, children: [] });
            });
            // Add a single note-synthese as sibling if it exists
            if (noteSyntheseNode) {
              newChildren.push(noteSyntheseNode);
            }
            return { ...activity, children: newChildren };
          });
        };
        setTreeData(flattenTree(response.data.data));
      }
    } catch (error) {
      console.error('Error fetching tree data:', error);
      toast.error('Erreur lors du chargement des données');
    } finally {
      setIsLoadingTree(false);
    }
  };

  // Tree node handlers
  const handleNodeDelete = async (nodeId, nodeType) => {
    try {
      const response = await axios.delete(`${API_BASE_URL}/audit-tree/${nodeType}/${nodeId}`, { withCredentials: true });
      if (response.data.success) {
        toast.success('Élément supprimé avec succès');
        fetchTreeData(); // Refresh tree data
      }
    } catch (error) {
      console.error('Error deleting node:', error);
      toast.error('Erreur lors de la suppression');
    }
  };

  const handleNodeAdd = (parentId, parentType) => {
    // This will be implemented based on the parent type
    console.log('Add node to parent:', parentId, parentType);
    // TODO: Open appropriate dialog based on parent type
  };

  const handleNodeEdit = (nodeId, nodeType) => {
    // This will be implemented based on the node type
    console.log('Edit node:', nodeId, nodeType);
    // TODO: Open appropriate edit dialog based on node type
  };

  // Add activity handler
  const handleAddActivity = async () => {
    if (!newActivity.name.trim()) {
      toast.error("Le nom de l'activité est requis");
      return;
    }
    if (!missionAudit?.id) {
      toast.error("Mission d'audit manquante");
      return;
    }
    if (newActivity.datefin && newActivity.datedebut && new Date(newActivity.datefin) < new Date(newActivity.datedebut)) {
      toast.error("La date de fin doit être après la date de début");
      return;
    }
    setIsSubmitting(true);
    try {
      const payload = {
        name: newActivity.name.trim(),
        datedebut: newActivity.datedebut || null,
        datefin: newActivity.datefin || null,
        description: newActivity.description?.trim() || '',
        auditMissionID: missionAudit.id
      };
      const res = await createAuditActivity(payload);
      if (res && res.success) {
        toast.success("Activité ajoutée");
        setIsAddActivityOpen(false);
        setNewActivity({ name: '', datedebut: '', datefin: '', description: '' });
        fetchTreeData();
      } else {
        throw new Error(res?.message || 'Erreur lors de la création');
      }
    } catch (err) {
      toast.error(err.message || "Erreur lors de l'ajout de l'activité");
    } finally {
      setIsSubmitting(false);
    }
  };


  if (!missionAudit || !missionAudit.id) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement de la réalisation...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-[#1A202C] flex items-center">
          <CheckCircle className="h-6 w-6 mr-3 text-[#F62D51]" />
          Réalisation
        </h2>
      </div>

      {/* Programme de Travail Section */}
      <div className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
        <button
          type="button"
          className="w-full flex items-center justify-between p-6 bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 hover:from-blue-100 hover:via-indigo-100 hover:to-purple-100 transition-all duration-200"
          onClick={() => setIsProgrammeOpen(!isProgrammeOpen)}
        >
          <div className="flex items-center gap-3">
            <div className="p-2 bg-white rounded-lg shadow-sm">
              <List className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-800">Programme de Travail</h3>
              <p className="text-sm text-gray-600 mt-1">Structure hiérarchique des activités d'audit</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500 hidden sm:block">
              {isProgrammeOpen ? 'Réduire' : 'Développer'}
            </span>
            {isProgrammeOpen ?
              <ChevronUp className="h-6 w-6 text-gray-600" /> :
              <ChevronDown className="h-6 w-6 text-gray-600" />
            }
          </div>
        </button>

        {isProgrammeOpen && (
          <div className="border-t border-gray-100">
            {/* Header with search and add button */}
            <div className="p-6 bg-gray-50 border-b border-gray-100">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Rechercher dans le programme..."
                    className="pl-10 bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                    value={programmeSearch}
                    onChange={e => setProgrammeSearch(e.target.value)}
                  />
                </div>
                <Button
                  className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white"
                  onClick={() => setIsAddActivityOpen(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Ajouter une activité
                </Button>
              </div>
            </div>
            {/* Tree Content */}
            <div className="p-6">
              {isLoadingTree ? (
                <div className="flex items-center justify-center h-64">
                  <div className="flex flex-col items-center space-y-4">
                    <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600"></div>
                    <div className="text-center">
                      <p className="text-gray-600 font-medium">Chargement des données...</p>
                      <p className="text-gray-400 text-sm mt-1">Récupération de la structure du programme</p>
                    </div>
                  </div>
                </div>
              ) : treeData.length > 0 ? (
                <AuditTreeEnhanced
                  data={treeData}
                  onNodeDelete={handleNodeDelete}
                  onNodeAdd={handleNodeAdd}
                  onNodeEdit={handleNodeEdit}
                  missionAudit={missionAudit}
                  planId={planId}
                />
              ) : (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center max-w-md">
                    <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <List className="h-10 w-10 text-gray-400" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">Aucune activité</h3>
                    <p className="text-gray-500 mb-4">Votre programme de travail est vide. Commencez par ajouter des activités pour structurer votre audit.</p>
                    <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white" onClick={() => setIsAddActivityOpen(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Ajouter une activité
                    </Button>
                  </div>
                </div>
              )}
            </div>
            {/* Add Activity Modal */}
            <Dialog open={isAddActivityOpen} onOpenChange={setIsAddActivityOpen}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Ajouter une activité</DialogTitle>
                  <DialogDescription>Remplissez les informations de la nouvelle activité.</DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-2">
                  <div className="space-y-2">
                    <Label htmlFor="activity-name">Nom de l'activité *</Label>
                    <Input
                      id="activity-name"
                      value={newActivity.name}
                      onChange={e => setNewActivity(a => ({ ...a, name: e.target.value }))}
                      placeholder="Nom de l'activité"
                      required
                      disabled={isSubmitting}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="activity-datedebut">Date début</Label>
                      <Input
                        id="activity-datedebut"
                        type="date"
                        value={newActivity.datedebut}
                        onChange={e => setNewActivity(a => ({ ...a, datedebut: e.target.value }))}
                        disabled={isSubmitting}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="activity-datefin">Date fin</Label>
                      <Input
                        id="activity-datefin"
                        type="date"
                        value={newActivity.datefin}
                        onChange={e => setNewActivity(a => ({ ...a, datefin: e.target.value }))}
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="activity-description">Description</Label>
                    <Textarea
                      id="activity-description"
                      value={newActivity.description}
                      onChange={e => setNewActivity(a => ({ ...a, description: e.target.value }))}
                      placeholder="Description de l'activité"
                      rows={3}
                      disabled={isSubmitting}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddActivityOpen(false)} disabled={isSubmitting}>Annuler</Button>
                  <Button className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white" onClick={handleAddActivity} disabled={isSubmitting}>
                    {isSubmitting ? 'Ajout en cours...' : 'Ajouter'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </div>



    </div>
  );
}

export default RealisationTab;
