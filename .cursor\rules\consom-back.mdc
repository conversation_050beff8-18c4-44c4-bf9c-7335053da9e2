---
description: 
globs: 
alwaysApply: true
---
# Backend Integration Rules

## State Management

### Redux Store Structure
```typescript
interface RootState {
  auditPlans: {
    plans: AuditPlan[];        // List of all audit plans
    currentPlan: AuditPlan | null;  // Currently selected plan
    loading: boolean;          // Loading state
    error: string | null;      // Error state
    lastFetched: number | null; // Timestamp of last fetch
  };
  // ... other slices
}
```

### Redux Actions
- `fetchAuditPlans`: Fetch all audit plans
- `fetchAuditPlanById`: Fetch single audit plan
- `addAuditPlan`: Create new audit plan
- `updateAuditPlanById`: Update existing audit plan
- `deleteAuditPlanById`: Delete audit plan
- `clearCurrentPlan`: Clear current plan from state
- `clearError`: Clear error state

### State Management Guidelines
1. **Data Caching**
   - Use Redux for caching API responses
   - Store fetched data in Redux store
   - Reuse cached data when navigating between tabs
   - Clear cache when explicitly needed (e.g., after updates)

2. **Loading States**
   - Track loading state in Redux
   - Show loading indicators during API calls
   - Handle loading states per action type

3. **Error Handling**
   - Store error messages in Redux
   - Display errors using toast notifications
   - Clear errors when starting new actions
   - Handle API errors consistently

4. **Optimistic Updates**
   - Update UI immediately for better UX
   - Revert changes if API call fails
   - Show loading states during updates

5. **State Persistence**
   - Consider using Redux Persist for critical data
   - Clear sensitive data on logout
   - Handle state rehydration properly

## API Endpoints

### Audit Plans
- Base URL: `/api/audit-plans`
- Endpoints:
  - GET `/api/audit-plans` - Get all audit plans
  - GET `/api/audit-plans/:id` - Get audit plan by ID
  - POST `/api/audit-plans` - Create new audit plan
  - PUT `/api/audit-plans/:id` - Update audit plan
  - DELETE `/api/audit-plans/:id` - Delete audit plan

### Audit Missions
- Base URL: `/api/audit-missions`
- Endpoints:
  - GET `/api/audit-missions` - Get all missions
  - GET `/api/audit-missions/:id` - Get mission by ID
  - GET `/api/audit-missions/plan/:planId` - Get missions by plan ID
  - POST `/api/audit-missions` - Create new mission
  - PUT `/api/audit-missions/:id` - Update mission
  - DELETE `/api/audit-missions/:id` - Delete mission

### Audit Activities
- Base URL: `/api/audit-activities`
- Endpoints:
  - GET `/api/audit-activities` - Get all activities
  - GET `/api/audit-activities/:id` - Get activity by ID
  - GET `/api/audit-activities/mission/:missionId` - Get activities by mission ID
  - POST `/api/audit-activities` - Create new activity
  - PUT `/api/audit-activities/:id` - Update activity
  - DELETE `/api/audit-activities/:id` - Delete activity

## Data Structures

### Audit Plan
```typescript
interface AuditPlan {
  id: string;              // Format: "AP_" + uuid
  name: string;            // Required
  status: string;          // Enum: "Planned", "In Progress", "Completed"
  datedebut: Date;         // Optional
  datefin: Date;          // Optional
  avancement: number;      // Optional, percentage
  calendrier: number;      // Required, year (1900-2100)
  directeuraudit: string;  // User ID of the director
  director?: {            // Populated when including User model
    id: string;
    username: string;
    email: string;
  };
}
```

### Audit Mission
```typescript
interface AuditMission {
  id: string;              // Format: "AM_" + uuid
  name: string;            // Required
  categorie: string;       // Optional
  code: string;           // Optional
  etat: string;           // Enum: "Planned", "In Progress", "Completed"
  chefmission: string;    // User ID of the mission chief
  principalAudite: string; // Optional
  objectif: string;       // Optional
  avancement: number;     // Optional, percentage
  planifieInitialement: boolean;
  evaluation: string;     // Optional
  datedebut: Date;        // Optional
  datefin: Date;         // Optional
  pointfort: string;     // Optional
  pointfaible: string;   // Optional
  auditplanID: string;   // Required, references AuditPlan
  missionChief?: {       // Populated when including User model
    id: string;
    username: string;
    email: string;
  };
  auditPlan?: {          // Populated when including AuditPlan model
    id: string;
    name: string;
  };
}
```

## Implementation Considerations

1. **Authentication**
   - All API endpoints require authentication
   - Use JWT token in Authorization header
   - Token should be stored in localStorage as 'authToken'
   - Include token in all API requests

2. **Error Handling**
   - All API responses follow the format:
     ```typescript
     interface ApiResponse<T> {
       success: boolean;
       message?: string;
       data?: T;
       error?: string;
     }
     ```
   - Handle 401 (Unauthorized) by redirecting to login
   - Handle 404 (Not Found) with appropriate user feedback
   - Handle 500 (Server Error) with error logging and user notification

3. **Data Validation**
   - Validate required fields on both frontend and backend
   - Use appropriate data types (e.g., Date for dates, number for percentages)
   - Implement proper error messages for validation failures

4. **State Management**
   - Use Redux Toolkit for state management
   - Implement proper loading states
   - Handle optimistic updates for better UX
   - Implement proper error states
   - Cache API responses in Redux store

5. **File Structure**
   - Services: `frontend/src/services/`
   - Store: `frontend/src/store/`
   - Slices: `frontend/src/store/slices/`
   - Models: `backend/models/`
   - Controllers: `backend/controllers/`
   - Routes: `backend/routes/`

6. **API Configuration**
   - Base URL is configured in `frontend/src/utils/api-config.js`
   - Use environment variables for different environments
   - Handle CORS properly in backend

7. **Testing**
   - Write unit tests for services and Redux slices
   - Write integration tests for API endpoints
   - Test error scenarios
   - Test authentication flows
   - Test Redux state management

8. **Performance**
   - Implement pagination for list endpoints
   - Use proper indexing in database
   - Cache frequently accessed data in Redux
   - Optimize database queries
   - Use Redux for client-side caching

9. **Security**
   - Never expose sensitive data in API responses
   - Implement proper input sanitization
   - Use HTTPS in production
   - Implement rate limiting
   - Validate user permissions for each operation

10. **Documentation**
    - Keep API documentation up to date
    - Document all data structures
    - Document error codes and messages
    - Document authentication requirements
    - Document Redux state structure and actions

# Redux State Management Patterns

## Request Cancellation and Data Persistence Pattern

### Overview
This pattern provides a standardized approach for handling Redux state management with request cancellation and data persistence across page navigation. It's particularly useful for detail/edit views where data needs to persist while navigating between tabs.

### Implementation Structure

#### 1. Redux Slice Pattern
```typescript
// Example slice structure
interface SliceState<T> {
  items: T[];              // List of items
  currentItem: T | null;   // Currently selected item
  loading: boolean;        // Loading state
  error: string | null;    // Error state
  lastFetched: number | null; // Timestamp of last successful fetch
}

// Async thunk pattern
export const fetchItemById = createAsyncThunk(
  'sliceName/fetchById',
  async ({ id, signal }, { rejectWithValue }) => {
    try {
      const response = await apiService.getItemById(id, signal);
      if (!response) return null; // Request cancelled
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.message);
    } catch (error) {
      if (error.name === 'CanceledError') return null;
      return rejectWithValue(error.message);
    }
  }
);

// Slice reducers
const slice = createSlice({
  name: 'sliceName',
  initialState,
  reducers: {
    clearCurrentItem: (state) => {
      state.currentItem = null;
      state.lastFetched = null;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchItemById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchItemById.fulfilled, (state, action) => {
        if (action.payload === null) return; // Request cancelled
        state.loading = false;
        state.currentItem = action.payload;
        state.lastFetched = Date.now();
        // Update item in items array if it exists
        const index = state.items.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.items[index] = action.payload;
        }
      })
      .addCase(fetchItemById.rejected, (state, action) => {
        if (action.payload === null) return; // Request cancelled
        state.loading = false;
        state.error = action.payload;
      });
  }
});
```

#### 2. API Service Pattern
```typescript
// API service pattern with request cancellation
const apiInstance = axios.create({
  timeout: 10000, // 10 second timeout
  withCredentials: true
});

export const getItemById = async (id, signal) => {
  try {
    const response = await apiInstance.get(`${API_URL}/${id}`, { signal });
    return response.data;
  } catch (error) {
    if (axios.isCancel(error)) {
      console.log('Request cancelled:', error.message);
      return null;
    }
    throw error;
  }
};
```

#### 3. Component Pattern
```typescript
function DetailView() {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const abortControllerRef = useRef(null);
  
  // Redux state selector with proper null checks
  const { currentItem, loading, error, lastFetched } = useSelector((state) => ({
    currentItem: state.sliceName?.currentItem,
    loading: state.sliceName?.loading,
    error: state.sliceName?.error,
    lastFetched: state.sliceName?.lastFetched
  }));

  // Data fetching with cancellation
  useEffect(() => {
    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new AbortController
    abortControllerRef.current = new AbortController();

    if (id && (!currentItem || currentItem.id !== id || !lastFetched)) {
      const itemId = id.toString();
      dispatch(fetchItemById({ 
        id: itemId,
        signal: abortControllerRef.current.signal 
      }))
        .unwrap()
        .catch((error) => {
          if (error.name !== 'CanceledError') {
            toast.error(error || 'Failed to fetch item');
            navigate('/list-view');
          }
        });
    }

    // Cleanup
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      // Only clear if navigating away from detail view
      if (!location.pathname.includes('/detail-view/')) {
        dispatch(clearCurrentItem());
      }
    };
  }, [id, dispatch, navigate, currentItem, lastFetched, location.pathname]);
}
```

### Key Features

1. **Request Cancellation**
   - Uses AbortController for request cancellation
   - Properly handles cancelled requests in Redux
   - Prevents unnecessary state updates for cancelled requests
   - Cleans up requests on component unmount

2. **Data Persistence**
   - Maintains data in Redux store while navigating between tabs
   - Only clears data when leaving the detail view
   - Uses lastFetched timestamp to prevent unnecessary refetches
   - Updates both currentItem and items array for consistency

3. **Error Handling**
   - Proper error handling for API requests
   - Distinguishes between cancelled requests and actual errors
   - Provides user feedback for errors
   - Clears errors appropriately

4. **Loading States**
   - Manages loading state in Redux
   - Shows loading indicator only when necessary
   - Prevents loading flicker during tab navigation

### Best Practices

1. **State Management**
   - Always use proper null checks in selectors
   - Keep state structure consistent across slices
   - Use TypeScript interfaces for better type safety
   - Implement proper cleanup in useEffect

2. **Request Handling**
   - Always include signal parameter in API calls
   - Handle request cancellation gracefully
   - Use appropriate timeout values
   - Implement proper error handling

3. **Component Structure**
   - Use refs for AbortController
   - Implement proper cleanup
   - Handle navigation appropriately
   - Provide proper loading and error states

4. **Performance**
   - Prevent unnecessary refetches
   - Cache data appropriately
   - Clear data only when necessary
   - Use proper memoization where needed

### Usage Guidelines

1. **When to Use**
   - Detail/edit views with multiple tabs
   - Pages requiring data persistence during navigation
   - Components making API calls that might need cancellation
   - Views where data needs to be shared between components

2. **Implementation Steps**
   - Create Redux slice following the pattern
   - Implement API service with cancellation support
   - Set up component with proper state management
   - Implement proper cleanup and error handling

3. **Common Pitfalls to Avoid**
   - Not handling request cancellation
   - Clearing data too early
   - Not using proper null checks
   - Missing cleanup in useEffect
   - Not handling errors properly

4. **Testing Considerations**
   - Test request cancellation
   - Verify data persistence
   - Check error handling
   - Validate cleanup behavior
   - Test navigation scenarios

# Your rule content

- You can @ files here
- You can use markdown but dont have to

# Your rule content

- You can @ files here
- You can use markdown but dont have to
