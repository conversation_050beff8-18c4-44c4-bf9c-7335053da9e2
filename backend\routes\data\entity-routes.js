const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllEntities,
  createEntity,
  getEntityById,
  updateEntity,
  deleteEntity
} = require('../../controllers/data/entity-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all entities
router.get('/', getAllEntities);

// Create new entity
router.post('/', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), createEntity);

// Get entity by ID
router.get('/:id', getEntityById);

// Update entity
router.put('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), updateEntity);

// Delete entity
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteEntity);

module.exports = router;
