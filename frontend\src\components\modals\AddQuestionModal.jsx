import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { Loader2, Plus, Trash2, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

function AddQuestionModal({ isOpen, onClose, onAddQuestion, isLoading = false }) {
  const { t } = useTranslation();
  
  const [newQuestion, setNewQuestion] = useState({
    question_text: "",
    input_type: "text",
    options: []
  });
  const [option, setOption] = useState("");

  // Reset form when modal opens/closes
  React.useEffect(() => {
    if (!isOpen) {
      setNewQuestion({
        question_text: "",
        input_type: "text",
        options: []
      });
      setOption("");
    }
  }, [isOpen]);

  // Handle input changes
  const handleQuestionInputChange = (e) => {
    const { name, value } = e.target;
    setNewQuestion((prev) => ({ ...prev, [name]: value }));
  };

  // Handle input type change
  const handleInputTypeChange = (value) => {
    setNewQuestion((prev) => ({ 
      ...prev, 
      input_type: value, 
      options: [] // Reset options when changing type
    }));
  };

  // Add option for select/radio/multi-select types
  const handleAddOption = () => {
    if (option.trim()) {
      setNewQuestion((prev) => ({
        ...prev,
        options: [...prev.options, option.trim()]
      }));
      setOption("");
    }
  };

  // Remove option
  const handleRemoveOption = (index) => {
    setNewQuestion((prev) => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index)
    }));
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!newQuestion.question_text.trim()) {
      toast.error(t('admin.controls.execution.question_modal.question_required', "Veuillez entrer une question"));
      return;
    }
    
    if (["radio", "select", "checkbox"].includes(newQuestion.input_type) && newQuestion.options.length === 0) {
      toast.error(t('admin.controls.execution.question_modal.options_required', "Veuillez ajouter au moins une option pour ce type de question"));
      return;
    }

    try {
      await onAddQuestion({
        question_text: newQuestion.question_text,
        input_type: newQuestion.input_type,
        options: ["radio", "select", "checkbox"].includes(newQuestion.input_type) ? [...newQuestion.options] : null
      });
      
      // Reset form on success
      setNewQuestion({
        question_text: "",
        input_type: "text",
        options: []
      });
      setOption("");
      onClose();
    } catch (error) {
      console.error('Error adding question:', error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            {t('admin.controls.execution.question_modal.title', 'Ajouter une nouvelle question')}
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-6 w-6"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
          <DialogDescription>
            {t('admin.controls.execution.question_modal.description', 'Créez une nouvelle question pour cette étape de contrôle')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Question Text */}
          <div className="space-y-2">
            <Label htmlFor="question-text">
              {t('admin.controls.execution.question_modal.question_text', 'Texte de la question')}
            </Label>
            <Textarea
              id="question-text"
              name="question_text"
              value={newQuestion.question_text}
              onChange={handleQuestionInputChange}
              placeholder={t('admin.controls.execution.question_modal.question_placeholder', 'Ex: Quel est le risque associé ?')}
              rows={3}
              className="resize-none"
            />
          </div>

          {/* Input Type */}
          <div className="space-y-2">
            <Label htmlFor="input-type">
              {t('admin.controls.execution.question_modal.response_type', 'Type de réponse')}
            </Label>
            <Select
              value={newQuestion.input_type}
              onValueChange={handleInputTypeChange}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('admin.controls.execution.question_modal.select_type', 'Sélectionnez un type')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="text">{t('admin.controls.execution.question_modal.text_type', 'Texte libre')}</SelectItem>
                <SelectItem value="number">{t('admin.controls.execution.question_modal.number_type', 'Nombre')}</SelectItem>
                <SelectItem value="date">{t('admin.controls.execution.question_modal.date_type', 'Date')}</SelectItem>
                <SelectItem value="radio">{t('admin.controls.execution.question_modal.radio_type', 'Choix unique (radio)')}</SelectItem>
                <SelectItem value="checkbox">{t('admin.controls.execution.question_modal.multiselect_type', 'Choix multiple (checkbox)')}</SelectItem>
                <SelectItem value="file">{t('admin.controls.execution.question_modal.document_type', 'Document métier')}</SelectItem>
                <SelectItem value="url">{t('admin.controls.execution.question_modal.reference_type', 'Référence')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Options for select/radio/checkbox */}
          {["radio", "select", "checkbox"].includes(newQuestion.input_type) && (
            <div className="space-y-2">
              <Label htmlFor="option-input">
                {t('admin.controls.execution.question_modal.response_options', 'Options de réponse')}
              </Label>
              <div className="flex gap-2">
                <Input
                  id="option-input"
                  value={option}
                  onChange={(e) => setOption(e.target.value)}
                  placeholder={t('admin.controls.execution.question_modal.add_option', 'Ajouter une option')}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddOption();
                    }
                  }}
                />
                <Button type="button" onClick={handleAddOption} variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  {t('admin.controls.execution.question_modal.add', 'Ajouter')}
                </Button>
              </div>
              
              {/* Display added options */}
              {newQuestion.options.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {newQuestion.options.map((opt, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10"
                    >
                      {opt}
                      <button
                        type="button"
                        onClick={() => handleRemoveOption(index)}
                        className="ml-1 -mr-0.5 h-4 w-4 flex-shrink-0 rounded-full hover:bg-blue-200 inline-flex items-center justify-center text-blue-400"
                      >
                        <Trash2 className="h-3 w-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            {t('common.buttons.cancel', 'Annuler')}
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading} className="bg-[#F62D51] hover:bg-red-700 text-white">
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {t('admin.controls.execution.question_modal.adding', 'Ajout...')}
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                {t('admin.controls.execution.question_modal.add_question', 'Ajouter la question')}
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default AddQuestionModal;
