const { AuditSkill, User } = require('../models');

async function seedAuditSkills() {
  try {
    console.log('🌱 Seeding audit skills...');

    // Find an admin or audit director user to be the creator
    const adminUser = await User.findOne({
      where: { id: 4 } // Assuming user ID 4 is an audit director
    });

    if (!adminUser) {
      console.log('❌ No admin user found. Please ensure user ID 4 exists.');
      return;
    }

    // Sample skills to create
    const skillsToCreate = [
      {
        name: 'Audit Financier',
        description: 'Capacité à effectuer des audits financiers complets et à analyser les états financiers',
        createdBy: adminUser.id
      },
      {
        name: 'Gestion des Risques',
        description: 'Compétence en identification, évaluation et mitigation des risques opérationnels',
        createdBy: adminUser.id
      },
      {
        name: 'Conformité Réglementaire',
        description: 'Connaissance des réglementations et capacité à assurer la conformité',
        createdBy: adminUser.id
      },
      {
        name: 'Communication',
        description: 'Capacité à communiquer efficacement avec les parties prenantes',
        createdBy: adminUser.id
      },
      {
        name: '<PERSON><PERSON><PERSON>',
        description: 'Compétence en analyse et interprétation de données complexes',
        createdBy: adminUser.id
      },
      {
        name: 'Rédaction de Rapports',
        description: 'Capacité à rédiger des rapports d\'audit clairs et détaillés',
        createdBy: adminUser.id
      }
    ];

    // Create skills
    for (const skillData of skillsToCreate) {
      // Check if skill already exists
      const existingSkill = await AuditSkill.findOne({
        where: { name: skillData.name }
      });

      if (!existingSkill) {
        // The model will auto-generate the ID using the defaultValue function
        await AuditSkill.create(skillData);
        
        console.log(`✅ Created skill: ${skillData.name}`);
      } else {
        console.log(`⚠️  Skill already exists: ${skillData.name}`);
      }
    }

    console.log('🎉 Audit skills seeding completed!');

  } catch (error) {
    console.error('❌ Error seeding audit skills:', error);
  } finally {
    process.exit(0);
  }
}

seedAuditSkills();
