const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllOrganizationalProcesses,
  createOrganizationalProcess,
  getOrganizationalProcessById,
  updateOrganizationalProcess,
  deleteOrganizationalProcess
} = require('../../controllers/processes/organizational-process-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all organizational processes
router.get('/', getAllOrganizationalProcesses);

// Create new organizational process
router.post('/', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), createOrganizationalProcess);

// Get organizational process by ID
router.get('/:id', getOrganizationalProcessById);

// Update organizational process
router.put('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), updateOrganizationalProcess);

// Delete organizational process
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteOrganizationalProcess);

module.exports = router;
