import React, { useState, useEffect, useCallback, useRef } from "react";
import { Upload, File, Trash2, FileText, ExternalLink, Download, Loader2, Link, Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";

export function AuditConstatAttachmentsSection({ constatId }) {
  const [businessDocuments, setBusinessDocuments] = useState([]);
  const [externalReferences, setExternalReferences] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [, setActiveTab] = useState("business-documents");
  const [isDraggingBusiness, setIsDraggingBusiness] = useState(false);
  const [isReferenceModalOpen, setIsReferenceModalOpen] = useState(false);
  const [newReference, setNewReference] = useState({ url: "", description: "" });

  const businessDocInputRef = useRef(null);

  const API_BASE_URL = getApiBaseUrl();

  // Fetch attachments from API
  const fetchAttachments = useCallback(async () => {
    if (!constatId) {
      console.warn('No constat ID provided.');
      return;
    }

    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      // Fetch business documents
      const businessDocsResponse = await axios.get(
        `${API_BASE_URL}/all-attachments/by-audit-constat/${constatId}`,
        { headers }
      );

      // Fetch external references
      if (businessDocsResponse.data.success) {
        setBusinessDocuments((businessDocsResponse.data.data || []).filter(att => att.type === 'business-document'));
        setExternalReferences((businessDocsResponse.data.data || []).filter(att => att.type === 'external-reference'));
      }
    } catch (error) {
      console.error('Error fetching attachments:', error);
      toast.error('Erreur lors du chargement des pièces jointes');
    } finally {
      setIsLoading(false);
    }
  }, [constatId]);

  useEffect(() => {
    fetchAttachments();
  }, [fetchAttachments]);

  const handleBusinessDocumentUpload = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0 || !constatId) return;

    const allowedExtensions = [
      ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
      ".txt", ".csv", ".rtf", ".odt", ".ods", ".odp",
      ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg",
      ".zip", ".rar", ".7z", ".tar", ".gz",
    ];

    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    const oversizedFiles = files.filter((file) => file.size > MAX_FILE_SIZE);
    if (oversizedFiles.length > 0) {
      const fileNames = oversizedFiles.map((file) => file.name).join(", ");
      toast.error(`Fichiers trop volumineux : ${fileNames}`, {
        description: `Les fichiers suivants dépassent la limite de 50MB : ${fileNames}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }

    const invalidFiles = files.filter((file) => {
      const extension = "." + file.name.split(".").pop().toLowerCase();
      return !allowedExtensions.includes(extension);
    });
    if (invalidFiles.length > 0) {
      const fileNames = invalidFiles.map((file) => file.name).join(", ");
      toast.error(`Types de fichiers non supportés : ${fileNames}`, {
        description: `Les fichiers suivants ont des formats non supportés : ${fileNames}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }

    setIsLoading(true);
    setUploadProgress(0);

    try {
      const token = localStorage.getItem('token');
      const uploadPromises = files.map(async (file, index) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', 'business-document');
        formData.append('auditConstatID', constatId);

        const response = await axios.post(
          `${API_BASE_URL}/all-attachments/upload`,
          formData,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              setUploadProgress(Math.round(((index + progress / 100) / files.length) * 100));
            }
          }
        );

        return response.data;
      });

      const results = await Promise.all(uploadPromises);
      const successfulUploads = results.filter(result => result.success);

      if (successfulUploads.length > 0) {
        toast.success(`${successfulUploads.length} document${successfulUploads.length !== 1 ? "s" : ""} téléchargé${successfulUploads.length !== 1 ? "s" : ""} avec succès`);
        await fetchAttachments(); // Refresh the list
      }

      if (results.length !== successfulUploads.length) {
        toast.error(`${results.length - successfulUploads.length} fichier(s) n'ont pas pu être téléchargés`);
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      toast.error('Erreur lors du téléchargement des fichiers');
    } finally {
      setIsLoading(false);
      setUploadProgress(0);
      e.target.value = "";
    }
  };

  const handleAddExternalReference = async () => {
    if (!newReference.url || !newReference.url.trim() || !constatId) {
      toast.error("L'URL est requise");
      return;
    }

    setIsLoading(true);

    try {
      const token = localStorage.getItem('token');
      const formattedUrl = newReference.url.startsWith("http://") || newReference.url.startsWith("https://")
        ? newReference.url
        : `https://${newReference.url}`;

      const referenceData = {
        url: formattedUrl,
        fileName: newReference.description || new URL(formattedUrl).hostname,
        description: newReference.description,
        type: 'external-reference',
        auditConstatID: constatId
      };

      const response = await axios.post(
        `${API_BASE_URL}/all-attachments/reference`,
        referenceData,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        toast.success('Référence externe ajoutée avec succès');
        setIsReferenceModalOpen(false);
        setNewReference({ url: "", description: "" });
        await fetchAttachments();
      } else {
        toast.error(response.data.message || 'Erreur lors de l\'ajout de la référence externe');
      }
    } catch (error) {
      console.error('Error adding external reference:', error);
      toast.error('Erreur lors de l\'ajout de la référence externe');
    } finally {
      setIsLoading(false);
    }
  };

  const downloadAttachment = async (id, fileName) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(
        `${API_BASE_URL}/all-attachments/download/${id}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          },
          responseType: 'blob'
        }
      );
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success(`${fileName} téléchargé avec succès`);
    } catch (error) {
      console.error('Error downloading attachment:', error);
      toast.error('Erreur lors du téléchargement du fichier');
    }
  };

  const deleteBusinessDocument = async (id) => {
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer ce document ?")) return;
    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.delete(
        `${API_BASE_URL}/all-attachments/${id}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      if (response.data.success) {
        toast.success("Document supprimé");
        await fetchAttachments();
      } else {
        toast.error("Erreur lors de la suppression du document");
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      toast.error('Erreur lors de la suppression du document');
    } finally {
      setIsLoading(false);
    }
  };

  const deleteExternalReference = async (id) => {
    if (!window.confirm("Êtes-vous sûr de vouloir supprimer cette référence ?")) return;
    setIsLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.delete(
        `${API_BASE_URL}/all-attachments/${id}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      if (response.data.success) {
        toast.success("Référence supprimée");
        await fetchAttachments();
      } else {
        toast.error("Erreur lors de la suppression de la référence");
      }
    } catch (error) {
      console.error('Error deleting external reference:', error);
      toast.error('Erreur lors de la suppression de la référence externe');
    } finally {
      setIsLoading(false);
    }
  };

  const openExternalReference = (url) => {
    if (!url) {
      toast.error('URL invalide');
      return;
    }
    try {
      const formattedUrl = url.startsWith("http://") || url.startsWith("https://") ? url : `https://${url}`;
      window.open(formattedUrl, "_blank", "noopener,noreferrer");
    } catch (error) {
      console.error('Error opening URL:', error);
      toast.error('Impossible d\'ouvrir le lien');
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes == null || isNaN(bytes)) return "N/A";
    if (bytes === 0) return "0 Octets";
    const k = 1024;
    const sizes = ["Octets", "Ko", "Mo", "Go"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("fr-FR", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return "Date invalide";
    }
  };

  const handleBusinessDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleBusinessDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(true);
  };

  const handleBusinessDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(false);
  };

  const handleBusinessDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingBusiness(false);
    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;
    const event = { target: { files, value: "" } };
    handleBusinessDocumentUpload(event);
  };

  const handleReferenceInputChange = (e) => {
    const { name, value } = e.target;
    setNewReference((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <div>
      <Tabs defaultValue="business-documents" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="business-documents" className="text-center">
            <FileText className="h-4 w-4 mr-2" />
            Documents métier
          </TabsTrigger>
          <TabsTrigger value="external-references" className="text-center">
            <ExternalLink className="h-4 w-4 mr-2" />
            Références externes
          </TabsTrigger>
        </TabsList>

        <TabsContent value="business-documents" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-md font-medium">Documents métier</h3>
            <div>
              <input
                type="file"
                id="business-document-upload"
                multiple
                className="hidden"
                onChange={handleBusinessDocumentUpload}
                disabled={isLoading}
                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.rtf,.odt,.ods,.odp,.jpg,.jpeg,.png,.gif,.bmp,.svg,.zip,.rar,.7z,.tar,.gz"
                ref={businessDocInputRef}
              />
              <label htmlFor="business-document-upload">
                <Button
                  type="button"
                  variant="outline"
                  className="flex items-center gap-2 cursor-pointer"
                  disabled={isLoading}
                  asChild
                >
                  <span>
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Téléchargement... {uploadProgress > 0 ? `${uploadProgress}%` : ""}
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4" />
                        Télécharger un document
                      </>
                    )}
                  </span>
                </Button>
              </label>
            </div>
          </div>

          {businessDocuments.length === 0 ? (
            <div
              className={`text-center py-8 border-2 border-dashed rounded-lg transition-colors ${
                isDraggingBusiness ? "border-blue-500 bg-blue-50" : "border-gray-300"
              }`}
              onDragOver={handleBusinessDragOver}
              onDragEnter={handleBusinessDragEnter}
              onDragLeave={handleBusinessDragLeave}
              onDrop={handleBusinessDrop}
            >
              <File className="h-12 w-12 mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">
                {isDraggingBusiness ? "Déposez les fichiers ici" : "Aucun document métier téléchargé pour le moment"}
              </p>
              <p className="text-sm text-gray-400">Glissez-déposez les fichiers ici ou cliquez sur le bouton de téléchargement</p>
              <p className="text-xs text-gray-400 mt-2">Taille maximale : 50MB. Types de fichiers autorisés : PDF, documents Office, images, archives.</p>
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <table className="w-full relative">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Nom</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Taille</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Date</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {businessDocuments.map((doc, index) => (
                    <tr key={`business-doc-${doc.allAttachmentID || index}`} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-blue-500" />
                          {doc.fileName}
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm">{formatFileSize(doc.fileSize)}</td>
                      <td className="px-4 py-3 text-sm">{formatDate(doc.uploadDate)}</td>
                      <td className="px-4 py-3 text-sm">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => downloadAttachment(doc.allAttachmentID, doc.fileName)}
                            title="Télécharger"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                            onClick={() => deleteBusinessDocument(doc.allAttachmentID)}
                            title="Supprimer"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>

        <TabsContent value="external-references" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-md font-medium">Références externes</h3>
            <Button
              type="button"
              variant="outline"
              className="flex items-center gap-2"
              disabled={isLoading}
              onClick={() => setIsReferenceModalOpen(true)}
            >
              <Plus className="h-4 w-4" />
              Ajouter une référence
            </Button>
          </div>

          {externalReferences.length === 0 ? (
            <div className="text-center py-8 border-2 border-dashed rounded-lg border-gray-300">
              <ExternalLink className="h-12 w-12 mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">Aucune référence externe ajoutée pour le moment</p>
              <p className="text-sm text-gray-400">Cliquez sur le bouton 'Ajouter une référence' pour ajouter un lien</p>
            </div>
          ) : (
            <div className="border rounded-lg overflow-hidden">
              <table className="w-full relative">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Nom</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Description</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Date</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {externalReferences.map((ref, index) => {
                    const getDisplayName = (ref) => {
                      if (ref.fileName) return ref.fileName;
                      try {
                        return new URL(ref.filePath).hostname;
                      } catch {
                        return ref.filePath || 'Lien externe';
                      }
                    };
                    return (
                      <tr key={`external-ref-${ref.allAttachmentID || index}`} className="hover:bg-gray-50">
                        <td className="px-4 py-3 text-sm">
                          <div className="flex items-center">
                            <Link className="h-4 w-4 mr-2 text-green-500" />
                            <span
                              className="text-blue-600 hover:underline cursor-pointer"
                              onClick={() => openExternalReference(ref.filePath)}
                            >
                              {getDisplayName(ref)}
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm">{ref.description || '-'}</td>
                        <td className="px-4 py-3 text-sm">{formatDate(ref.uploadDate)}</td>
                        <td className="px-4 py-3 text-sm">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => openExternalReference(ref.filePath)}
                              title="Ouvrir le lien"
                            >
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                              onClick={() => deleteExternalReference(ref.allAttachmentID)}
                              title="Supprimer"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* External Reference Modal */}
      <Dialog open={isReferenceModalOpen} onOpenChange={setIsReferenceModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Ajouter une référence externe</DialogTitle>
            <DialogDescription>Ajoutez un lien externe avec une description facultative.</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Label htmlFor="reference-url">URL</Label>
            <Input
              id="reference-url"
              name="url"
              type="url"
              placeholder="https://exemple.com/politique"
              value={newReference.url}
              onChange={handleReferenceInputChange}
              required
            />
            <Label htmlFor="reference-description">Description</Label>
            <Textarea
              id="reference-description"
              name="description"
              placeholder="Description de la référence (facultatif)"
              value={newReference.description}
              onChange={handleReferenceInputChange}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReferenceModalOpen(false)}>
              Annuler
            </Button>
            <Button onClick={handleAddExternalReference}>
              Ajouter
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 