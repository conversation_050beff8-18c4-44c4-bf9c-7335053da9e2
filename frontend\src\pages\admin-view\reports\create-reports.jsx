import React, { useState, useEffect } from "react";
import { Filter, FileText, ArrowLeft, X, Link as LinkIcon, Calendar } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import PageHeader from '@/components/ui/page-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogTitle,
} from '@/components/ui/dialog';
import html2pdf from "html2pdf.js";
import ReactSelect from 'react-select';
import axios from 'axios';
import { useReactTable, getCoreRowModel, flexRender } from '@tanstack/react-table';
import { useDMROptions, useRiskLevels, useIncidentTypes, useIncidents, useEntities, useBusinessProcesses, useOrganizationalProcesses } from '@/hooks/use-filter-queries';

// Import chart images
import pieChartImg from '../../../assets/piechart.png';
import barChartImg from '../../../assets/barchart.png';
import lineChartImg from '../../../assets/linechart.png';
import { getApiBaseUrl } from "@/utils/api-config";

// Import chart components
import InherentRiskRapport from './rapports/inherent-risk';
import ResidualRiskRapport from './rapports/residual-risk';
import LossDistributionRapport from './rapports/loss-distribution';
import RapportRiskIncident from './rapports/risk-incident';
import IncidentsOverTime from './rapports/IncidentsOverTime';
import IncidentsByType from './rapports/IncidentsByType';
import LossesOverTime from './rapports/LossesOverTime';
import LossesByIncidentType from './rapports/LossesByIncidentType';
import EvolutionOfLossesAndIncidents from './rapports/EvolutionOfLossesAndIncidents';
const initialReports = [
  {
    id: 1,
    title: "Inherent Risk",
    description: "Inherent risk matrix showing the distribution of risks based on their probability and impact.",
    img: pieChartImg,
    component: InherentRiskRapport
  },
  {
    id: 2,
    title: "Residual Risk",
    description: "Residual risk matrix after applying controls and mitigation measures.",
    img: pieChartImg,
    component: ResidualRiskRapport
  },
  {
    id: 3,
    title: "Loss Distribution",
    description: "Chart showing the distribution of losses by target risk level.",
    img: barChartImg,
    component: LossDistributionRapport
  },
  {
    id: 4,
    title: "Back Testing",
    description: "Detailed table of risks, associated incidents, and financial losses.",
    img: barChartImg,
    component: RapportRiskIncident
  },
  {
    id: 5,
    title: "Number of Incidents Over Time",
    description: "Chart displaying the number of incidents per month over time.",
    img: barChartImg,
    component: IncidentsOverTime
  },
  {
    id: 6,
    title: "Incidents by Incident Type",
    description: "Donut chart showing the number of incidents by incident type.",
    img: pieChartImg,
    component: IncidentsByType
  },
  {
    id: 7,
    title: "Losses Over Time",
    description: "Bar chart displaying gross and net losses over time.",
    img: barChartImg,
    component: LossesOverTime
  },
  {
    id: 8,
    title: "Losses by Incident Type",
    description: "Bar chart displaying gross and net losses by incident type.",
    img: barChartImg,
    component: LossesByIncidentType
  },
  {
    id: 9,
    title: "Evolution of Losses and Incidents Over Time",
    description: "Multi-axis line chart showing net losses and number of incidents over time.",
    img: lineChartImg,
    component: EvolutionOfLossesAndIncidents
  }
];

// Report Creation Modal Component
const ReportModal = ({ isOpen, onClose, report, onViewReport }) => {
  const navigate = useNavigate();
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [riskNet, setRiskNet] = useState('');
  const [incidentType, setIncidentType] = useState('');
  const [filterLoading, setFilterLoading] = useState(false);
  const [filterError, setFilterError] = useState('');
  const [filterOptions, setFilterOptions] = useState({
    incidentTypes: [],
    incidents: [],
    entities: [],
    businessProcesses: [],
    organizationalProcesses: []
  });
  const [showRelierOptions, setShowRelierOptions] = useState({
    riskName: false,
    riskTypes: false,
    incidentName: false,
    entityName: false,
    businessProcess: false,
    organizationalProcess: false,
    incidentType: false,
    riskControlLevel: false
  });
  const relierRefs = {
    riskName: React.useRef(null),
    riskTypes: React.useRef(null),
    incidentName: React.useRef(null),
    entityName: React.useRef(null),
    businessProcess: React.useRef(null),
    organizationalProcess: React.useRef(null),
    incidentType: React.useRef(null),
    riskControlLevel: React.useRef(null)
  };
  const API_BASE_URL = getApiBaseUrl();
  // State for report ID 9 filters
  const [selectedRiskLevels, setSelectedRiskLevels] = useState([]);
  const [selectedIncidentTypes, setSelectedIncidentTypes] = useState([]);
  const [selectedIncidents, setSelectedIncidents] = useState([]);
  const [selectedEntities, setSelectedEntities] = useState([]);
  const [selectedBusinessProcesses, setSelectedBusinessProcesses] = useState([]);
  const [selectedOrganizationalProcesses, setSelectedOrganizationalProcesses] = useState([]);
  const [selectedDmr, setSelectedDmr] = useState([]);

  const { data: dmrOptions, isLoading: dmrLoading } = useDMROptions();
  const { data: riskLevelOptions, isLoading: riskLevelsLoading } = useRiskLevels();
  const { data: incidentTypeOptions, isLoading: incidentTypesLoading } = useIncidentTypes();
  const { data: incidentOptions, isLoading: incidentsLoading } = useIncidents();
  const { data: entityOptions, isLoading: entitiesLoading } = useEntities();
  const { data: businessProcessOptions, isLoading: businessProcessesLoading } = useBusinessProcesses();
  const { data: organizationalProcessOptions, isLoading: organizationalProcessesLoading } = useOrganizationalProcesses();

  const [filteredIncidents, setFilteredIncidents] = useState([]);

  useEffect(() => {
    if (isOpen && report?.id === 9) {
      setFilterLoading(
        dmrLoading ||
        incidentTypesLoading ||
        incidentsLoading ||
        entitiesLoading ||
        businessProcessesLoading ||
        organizationalProcessesLoading
      );
    }
  }, [
    isOpen,
    report,
    dmrLoading,
    incidentTypesLoading,
    incidentsLoading,
    entitiesLoading,
    businessProcessesLoading,
    organizationalProcessesLoading
  ]);

  useEffect(() => {
    if (selectedDmr) {
      setFilterLoading(true);
      axios.get(`${API_BASE_URL}/incidents`, {
        params: { dmr: selectedDmr },
        withCredentials: true
      })
      .then(res => {
        const incidentsData = res.data.data || [];
        console.log("Filtered incidents data:", incidentsData);

        // Transform the data to the format needed for React-Select
        const formattedIncidents = incidentsData.map(incident => ({
          value: incident.incidentID.toString(),
          label: incident.name || `Incident ${incident.incidentID}`
        }));

        console.log("Formatted incidents for dropdown:", formattedIncidents);
        setFilteredIncidents(formattedIncidents);
        setFilterLoading(false);
      })
      .catch(err => {
        console.error("Error fetching filtered incidents:", err);
        setFilteredIncidents([]);
        setFilterLoading(false);
      });
    } else {
      setFilteredIncidents([]);
    }
  }, [selectedDmr, API_BASE_URL]);

  const columns = React.useMemo(() => [
    { header: 'Incident ID', accessorKey: 'incidentID' },
    { header: 'Name', accessorKey: 'name' },
    { header: 'Date', accessorKey: 'date' },
    { header: 'riskID', accessorKey: 'riskID' },
    // Add more columns as needed
  ], []);
  const table = useReactTable({
    data: filteredIncidents,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  React.useEffect(() => {
    if (!isOpen) {
      setShowRelierOptions({
        riskName: false,
        riskTypes: false,
        incidentName: false,
        entityName: false,
        businessProcess: false,
        organizationalProcess: false,
        incidentType: false,
        riskControlLevel: false
      });
      setRiskNet('');
      setIncidentType('');
      // Reset report ID 9 filters
      setSelectedRiskLevels([]);
      setSelectedIncidentTypes([]);
      setSelectedIncidents([]);
      setSelectedEntities([]);
      setSelectedBusinessProcesses([]);
      setSelectedOrganizationalProcesses([]);
      setSelectedDmr([]);
    }
  }, [isOpen]);

  React.useEffect(() => {
    function handleClickOutside(event) {
      Object.keys(relierRefs).forEach((key) => {
        if (relierRefs[key].current && !relierRefs[key].current.contains(event.target)) {
          setShowRelierOptions((prev) => ({ ...prev, [key]: false }));
        }
      });
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Define chart-specific filters
  const renderChartSpecificFilters = () => {
    if (!report) return null;

    switch (report.id) {
      case 1: // Inherent Risk
      case 2: // Residual Risk
        return (
          <div className="space-y-6">
            {/* Risk Name */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Risk Name</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Risk Name list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.riskName}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, riskName: !prev.riskName }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.riskName && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, riskName: false }));
                          window.location.href = '/admin/risk/risk-list';
                        }}
                      >
                        Risk List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Risk Types */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Risk Types</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Risk Types list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.riskTypes}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, riskTypes: !prev.riskTypes }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.riskTypes && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, riskTypes: false }));
                          window.location.href = '/admin/risk/risk-list';
                        }}
                      >
                        Risk List
                      </button>
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, riskTypes: false }));
                          window.location.href = '/admin/incident/incident-list';
                        }}
                      >
                        Incident List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Entity Name */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Entity Name</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Entity Name list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.entityName}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, entityName: !prev.entityName }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.entityName && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, entityName: false }));
                          window.location.href = '/admin/entity/entity-list';
                        }}
                      >
                        Entity List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Business Process */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Business Process</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Business Process list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.businessProcess}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, businessProcess: !prev.businessProcess }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.businessProcess && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, businessProcess: false }));
                          window.location.href = '/admin/process/business-process-list';
                        }}
                      >
                        Business Process List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Organizational Process */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Organizational Process</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Organizational Process list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.organizationalProcess}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, organizationalProcess: !prev.organizationalProcess }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.organizationalProcess && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, organizationalProcess: false }));
                          window.location.href = '/admin/process/organizational-process-list';
                        }}
                      >
                        Organizational Process List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      case 3: // Loss Distribution
        return (
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">Loss Type</label>
            <Select value={incidentType} onValueChange={setIncidentType}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Losses</SelectItem>
                <SelectItem value="gross">Gross Losses</SelectItem>
                <SelectItem value="net">Net Losses</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      case 4: // Back Testing
        return (
          <div className="space-y-6">
            {/* Risk Control Level */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Risk Control Level</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Risk Control Level list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.riskControlLevel}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, riskControlLevel: !prev.riskControlLevel }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.riskControlLevel && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, riskControlLevel: false }));
                          window.location.href = '/admin/risk/risk-list';
                        }}
                      >
                        Risk List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Incident Type */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Incident Type</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Incident Type list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.incidentType}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, incidentType: !prev.incidentType }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.incidentType && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, incidentType: false }));
                          window.location.href = '/admin/incident/incident-list';
                        }}
                      >
                        Incident List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Incident Name */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Incident Name</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Incident Name list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.incidentName}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, incidentName: !prev.incidentName }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.incidentName && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, incidentName: false }));
                          window.location.href = '/admin/incident/incident-list';
                        }}
                      >
                        Incident List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Entity Name */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Entity Name</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Entity Name list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.entityName}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, entityName: !prev.entityName }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.entityName && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, entityName: false }));
                          window.location.href = '/admin/entity/entity-list';
                        }}
                      >
                        Entity List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Business Process */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Business Process</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Business Process list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.businessProcess}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, businessProcess: !prev.businessProcess }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.businessProcess && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, businessProcess: false }));
                          window.location.href = '/admin/process/business-process-list';
                        }}
                      >
                        Business Process List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Organizational Process */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Organizational Process</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Organizational Process list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.organizationalProcess}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, organizationalProcess: !prev.organizationalProcess }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.organizationalProcess && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, organizationalProcess: false }));
                          window.location.href = '/admin/process/organizational-process-list';
                        }}
                      >
                        Organizational Process List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      case 5: // Number of Incidents Over Time
      case 7: // Losses Over Time
        return (
          <div className="space-y-6">
            {/* Risk Control Level */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Risk Control Level</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Risk Control Level list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.riskControlLevel}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, riskControlLevel: !prev.riskControlLevel }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.riskControlLevel && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, riskControlLevel: false }));
                          window.location.href = '/admin/risk/risk-list';
                        }}
                      >
                        Risk List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Incident Type */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Incident Type</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Incident Type list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.incidentType}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, incidentType: !prev.incidentType }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.incidentType && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, incidentType: false }));
                          window.location.href = '/admin/incident/incident-list';
                        }}
                      >
                        Incident List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Incident Name */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Incident Name</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Incident Name list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.incidentName}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, incidentName: !prev.incidentName }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.incidentName && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, incidentName: false }));
                          window.location.href = '/admin/incident/incident-list';
                        }}
                      >
                        Incident List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Entity Name */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Entity Name</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Entity Name list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.entityName}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, entityName: !prev.entityName }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.entityName && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, entityName: false }));
                          window.location.href = '/admin/entity/entity-list';
                        }}
                      >
                        Entity List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Business Process */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Business Process</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Business Process list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.businessProcess}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, businessProcess: !prev.businessProcess }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.businessProcess && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, businessProcess: false }));
                          window.location.href = '/admin/process/business-process-list';
                        }}
                      >
                        Business Process List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Organizational Process */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Organizational Process</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Organizational Process list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.organizationalProcess}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, organizationalProcess: !prev.organizationalProcess }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.organizationalProcess && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, organizationalProcess: false }));
                          window.location.href = '/admin/process/organizational-process-list';
                        }}
                      >
                        Organizational Process List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      case 9: // Evolution of Losses and Incidents Over Time
        return (
          <div className="space-y-6">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Risk Control Level (DMR)</label>
              <ReactSelect
                isMulti
                options={dmrOptions || []}
                value={selectedDmr}
                onChange={(selected) => setSelectedDmr(selected)}
                placeholder="Select DMR levels"
                isLoading={dmrLoading}
                className="w-full"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Incident Types</label>
              <ReactSelect
                isMulti
                options={incidentTypeOptions || []}
                value={selectedIncidentTypes}
                onChange={(selected) => setSelectedIncidentTypes(selected)}
                placeholder="Select incident types"
                isLoading={incidentTypesLoading}
                className="w-full"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Incidents</label>
              <ReactSelect
                isMulti
                options={filteredIncidents.length > 0 ? filteredIncidents : incidentOptions || []}
                value={selectedIncidents}
                onChange={(selected) => setSelectedIncidents(selected)}
                placeholder={filterLoading ? "Loading incidents..." : "Select incidents"}
                isLoading={incidentsLoading || filterLoading}
                className="w-full"
                noOptionsMessage={() =>
                  filterLoading ? "Loading incidents..." :
                  filteredIncidents.length === 0 && selectedDmr ?
                    "No incidents found for selected DMR" :
                    "No incidents available"
                }
              />
              {filteredIncidents.length === 0 && selectedDmr && !filterLoading && (
                <p className="text-xs text-amber-600 mt-1">
                  No incidents found for the selected DMR level(s). Try selecting different DMR levels.
                </p>
              )}
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Entities</label>
              <ReactSelect
                isMulti
                options={entityOptions || []}
                value={selectedEntities}
                onChange={(selected) => setSelectedEntities(selected)}
                placeholder="Select entities"
                isLoading={entitiesLoading}
                className="w-full"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Business Processes</label>
              <ReactSelect
                isMulti
                options={businessProcessOptions || []}
                value={selectedBusinessProcesses}
                onChange={(selected) => setSelectedBusinessProcesses(selected)}
                placeholder="Select business processes"
                isLoading={businessProcessesLoading}
                className="w-full"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Organizational Processes</label>
              <ReactSelect
                isMulti
                options={organizationalProcessOptions || []}
                value={selectedOrganizationalProcesses}
                onChange={(selected) => setSelectedOrganizationalProcesses(selected)}
                placeholder="Select organizational processes"
                isLoading={organizationalProcessesLoading}
                className="w-full"
              />
            </div>
          </div>
        );
      case 6: // Incidents by Incident Type
      case 8: // Losses by Incident Type
        return (
          <div className="space-y-6">
            {/* Risk Control Level */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Risk Control Level</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Risk Control Level list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.riskControlLevel}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, riskControlLevel: !prev.riskControlLevel }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.riskControlLevel && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, riskControlLevel: false }));
                          window.location.href = '/admin/risk/risk-list';
                        }}
                      >
                        Risk List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Incident Type */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Incident Type</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Incident Type list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.incidentType}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, incidentType: !prev.incidentType }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.incidentType && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, incidentType: false }));
                          window.location.href = '/admin/incident/incident-list';
                        }}
                      >
                        Incident List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Incident Name */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Incident Name</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Incident Name list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.incidentName}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, incidentName: !prev.incidentName }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.incidentName && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, incidentName: false }));
                          window.location.href = '/admin/incident/incident-list';
                        }}
                      >
                        Incident List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Entity Name */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Entity Name</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Entity Name list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.entityName}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, entityName: !prev.entityName }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.entityName && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, entityName: false }));
                          window.location.href = '/admin/entity/entity-list';
                        }}
                      >
                        Entity List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Business Process */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Business Process</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Business Process list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.businessProcess}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, businessProcess: !prev.businessProcess }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.businessProcess && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, businessProcess: false }));
                          window.location.href = '/admin/process/business-process-list';
                        }}
                      >
                        Business Process List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Organizational Process */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Organizational Process</label>
              <div className="bg-gray-50 p-6 rounded-md flex flex-col items-center justify-center text-center space-y-4">
                <div>
                  <p className="text-gray-600">This Organizational Process list is not yet filled.</p>
                  <p className="text-gray-600">To fill it, click on:</p>
                </div>
                <div className="relative" ref={relierRefs.organizationalProcess}>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2 px-6 py-2 rounded-md"
                    onClick={() => setShowRelierOptions((prev) => ({ ...prev, organizationalProcess: !prev.organizationalProcess }))}
                  >
                    <LinkIcon className="h-4 w-4" />
                    Link
                  </Button>
                  {showRelierOptions.organizationalProcess && (
                    <div className="absolute z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1 text-left border border-gray-200">
                      <button
                        className="block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => {
                          setShowRelierOptions((prev) => ({ ...prev, organizationalProcess: false }));
                          window.location.href = '/admin/process/organizational-process-list';
                        }}
                      >
                        Organizational Process List
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose(false)}>
      <DialogContent className="max-w-3xl" aria-describedby="report-modal-description">
        <DialogTitle className="text-xl font-semibold">{report?.title || 'Create Report'}</DialogTitle>
        <div id="report-modal-description" className="text-gray-600 mb-4">
          {report?.description || 'Configure your report parameters below.'}
        </div>
        <div className="p-6 max-h-[70vh] overflow-y-auto">
          <div className="space-y-6">
            {/* General Filters */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Start Date</label>
                <div className="relative">
                  <Input
                    type="date"
                    value={startDate || "2024-03-26"}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="w-full text-sm cursor-pointer"
                    onClick={(e) => {
                      // This ensures the native date picker opens when clicking anywhere in the input
                      e.currentTarget.showPicker();
                    }}
                  />
                  <div
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                    onClick={(e) => {
                      // Find the input element and open its date picker
                      const input = e.currentTarget.parentElement.querySelector('input');
                      input.showPicker();
                    }}
                  >
                    <Calendar className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">End Date</label>
                <div className="relative">
                  <Input
                    type="date"
                    value={endDate || "2025-03-26"}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="w-full text-sm cursor-pointer"
                    onClick={(e) => {
                      // This ensures the native date picker opens when clicking anywhere in the input
                      e.currentTarget.showPicker();
                    }}
                  />
                  <div
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                    onClick={(e) => {
                      // Find the input element and open its date picker
                      const input = e.currentTarget.parentElement.querySelector('input');
                      input.showPicker();
                    }}
                  >
                    <Calendar className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>

            {/* Chart-Specific Filters */}
            {renderChartSpecificFilters()}
          </div>
        </div>

        <DialogFooter className="p-4 border-t bg-gray-50 flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={() => onClose(false)}
            className="border-gray-300 text-gray-700 hover:bg-gray-100"
          >
            Cancel
          </Button>
          <Button
            disabled={!startDate || !endDate}
            onClick={() => {
              // Create filters object based on report type
              let filters = {
                startDate,
                endDate
              };

              // Add specific filters for report ID 9
              if (report?.id === 9) {
                filters = {
                  ...filters,
                  DMR: selectedDmr?.length > 0 ? selectedDmr.map(item => item.value) : [],
                  incidentTypes: selectedIncidentTypes?.length > 0 ? selectedIncidentTypes.map(item => item.value) : [],
                  incidents: selectedIncidents?.length > 0 ? selectedIncidents.map(item => item.value) : [],
                  entities: selectedEntities?.length > 0 ? selectedEntities.map(item => item.value) : [],
                  businessProcesses: selectedBusinessProcesses?.length > 0 ? selectedBusinessProcesses.map(item => item.value) : [],
                  organizationalProcesses: selectedOrganizationalProcesses?.length > 0 ? selectedOrganizationalProcesses.map(item => item.value) : []
                };
              }

              console.log("Sending filters:", filters);

              // Option 1: Use URL parameters
              if (report?.id === 9) {
                // Convert filters to URL parameters
                const queryParams = new URLSearchParams();

                // Add each filter to the URL
                if (filters.startDate) queryParams.append('startDate', filters.startDate);
                if (filters.endDate) queryParams.append('endDate', filters.endDate);

                // Add array filters
                if (filters.DMR && filters.DMR.length > 0) {
                  filters.DMR.forEach(dmr => queryParams.append('DMR', dmr));
                }

                if (filters.incidentTypes && filters.incidentTypes.length > 0) {
                  filters.incidentTypes.forEach(type => queryParams.append('incidentTypes', type));
                }

                if (filters.incidents && filters.incidents.length > 0) {
                  filters.incidents.forEach(incident => queryParams.append('incidents', incident));
                }

                if (filters.entities && filters.entities.length > 0) {
                  filters.entities.forEach(entity => queryParams.append('entities', entity));
                }

                if (filters.businessProcesses && filters.businessProcesses.length > 0) {
                  filters.businessProcesses.forEach(process => queryParams.append('businessProcesses', process));
                }

                if (filters.organizationalProcesses && filters.organizationalProcesses.length > 0) {
                  filters.organizationalProcesses.forEach(process => queryParams.append('organizationalProcesses', process));
                }

                // Store filters in sessionStorage for retrieval
                sessionStorage.setItem('reportFilters', JSON.stringify(filters));

                // Call the onViewReport function with the report and filters
                onViewReport(report, filters);
                onClose();
              } else {
                // For other reports, use the existing approach
                onViewReport(report, filters);
                onClose();
              }
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
          >
            Preview
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default function CreateReports() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedReport, setSelectedReport] = useState(null);
  const [viewingReport, setViewingReport] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [reportForModal, setReportForModal] = useState(null);
  const [filters, setFilters] = useState({});
  const [showReport, setShowReport] = useState(false);

  const filteredReports = initialReports.filter(
    (report) =>
      report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleBackToList = () => {
    setViewingReport(false);
    setSelectedReport(null);
  };

  const handleSaveReport = () => {
    window.location.href = '/admin/reports';
  };

  const handleCancelReport = () => {
    setViewingReport(false);
    setSelectedReport(null);
  };

  const handleCreateReport = (report) => {
    // Directly navigate to chart for specified reports
    if ([3].includes(report.id)) {
      setSelectedReport(report);
      setViewingReport(true);
    } else {
      // Open modal for other reports
      setReportForModal(report);
      setIsModalOpen(true);
    }
  };

  const handleViewReport = (report, filters) => {
    console.log("handleViewReport called with filters:", filters);

    // Store filters in component state
    setFilters(filters);

    // Also store in sessionStorage as a backup
    sessionStorage.setItem('reportFilters', JSON.stringify(filters));

    setSelectedReport({
      ...report,
      filters
    });
    setViewingReport(true);
  };

  return (
    <div className="p-6 space-y-6 bg-gray-100">
      <ReportModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        report={reportForModal}
        onViewReport={handleViewReport}
      />

      {viewingReport && selectedReport ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                onClick={handleBackToList}
                className="bg-white text-gray-700 hover:bg-gray-100 border border-gray-300 shadow-sm"
              >
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to List
              </Button>
              <h2 className="text-xl font-bold">{selectedReport.title}</h2>
            </div>
          </div>

          <div className="mb-4 flex justify-end gap-2">
            <Button
              onClick={() => {
                const element = document.getElementById("inherent-risk-matrix") ||
                               document.getElementById("residual-risk-matrix") ||
                               document.getElementById("loss-distribution-chart") ||
                               document.getElementById("risk-incident-table") ||
                               document.getElementById("incidents-over-time-chart") ||
                               document.getElementById("losses-over-time-chart") ||
                               document.getElementById("losses-by-incident-type-chart") ||
                               document.getElementById("losses-and-incidents-chart");
                if (element) {
                  const opt = {
                    margin: 1,
                    filename: `${selectedReport.title.toLowerCase().replace(/ /g, '_')}.pdf`,
                    image: { type: 'jpeg', quality: 0.98 },
                    html2canvas: { scale: 2 },
                    jsPDF: { unit: 'in', format: 'letter', orientation: 'landscape' }
                  };
                  html2pdf().set(opt).from(element).save();
                }
              }}
              className="bg-red-600 hover:bg-red-700 text-white transition-all duration-200 flex items-center gap-2 rounded-lg px-4 shadow-md"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
              </svg>
              Export to PDF
            </Button>
            <Button
              onClick={handleCancelReport}
              className="bg-white text-gray-700 hover:bg-gray-100 border border-gray-300 shadow-sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveReport}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Save
            </Button>
          </div>

          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            {selectedReport.component && (() => {
              const Component = selectedReport.component;
              // Pass filters directly to the component
              return <Component filters={selectedReport.filters || filters} />;
            })()}
          </div>
        </div>
      ) : (
        <>
          <PageHeader
            title="Create a Report"
            description="Select a report template and customize it to your needs. You can choose from various visualization types and data formats."
            section="Reports"
            currentPage="Create"
            searchPlaceholder="Search for a template..."
            searchValue={searchTerm}
            onSearchChange={(e) => setSearchTerm(e.target.value)}
            icon={FileText}
          />

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="flex gap-6">
              <div className="w-64 p-5 bg-gray-50 rounded-lg mr-4">
                <h3 className="text-sm font-semibold mb-4 flex items-center gap-2">
                  <Filter className="h-4 w-4" /> Filters
                </h3>

                <div className="space-y-5">
                  <div className="mb-4">
                    <label className="block text-sm mb-2">Categories</label>
                    <Select>
                      <SelectTrigger className="bg-white w-full">
                        <SelectValue placeholder="Measure & Cost" />
                      </SelectTrigger>
                      <SelectContent className="w-[var(--radix-select-trigger-width)]" position="popper">
                        <SelectItem value="measure-cost">Measure & Cost</SelectItem>
                        <SelectItem value="measure">Measure</SelectItem>
                        <SelectItem value="cost">Cost</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm mb-2">Objects</label>
                    <Select>
                      <SelectTrigger className="bg-white w-full">
                        <SelectValue placeholder="Incident, Risk" />
                      </SelectTrigger>
                      <SelectContent className="w-[var(--radix-select-trigger-width)]" position="popper">
                        <SelectItem value="incident-risk">Incident, Risk</SelectItem>
                        <SelectItem value="incident">Incident</SelectItem>
                        <SelectItem value="risk">Risk</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm mb-2">Illustrations</label>
                    <Select>
                      <SelectTrigger className="bg-white w-full">
                        <SelectValue placeholder="Chart Types" />
                      </SelectTrigger>
                      <SelectContent className="w-[var(--radix-select-trigger-width)]" position="popper">
                        <SelectItem value="line-bar">Line and Bar</SelectItem>
                        <SelectItem value="line">Line Chart</SelectItem>
                        <SelectItem value="bar">Bar Chart</SelectItem>
                        <SelectItem value="pie">Pie Chart</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="flex-1">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredReports.map((report) => (
                    <div
                      key={report.id}
                      className="relative group bg-white rounded-lg shadow-md overflow-hidden border hover:shadow-lg transition-all duration-300 cursor-pointer"
                      onClick={() => setSelectedReport(report)}
                    >
                      <div className="relative w-full h-56 bg-gray-100 flex items-center justify-center">
                        <img
                          src={report.img}
                          alt="chart preview"
                          className="w-[70%] h-[70%] object-contain"
                        />
                      </div>

                      <div className="py-4 px-3 border-t text-center font-medium text-sm text-gray-800">
                        {report.title}
                      </div>

                      <div className="absolute inset-0 bg-white/95 p-4 flex flex-col justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
                        <div className="flex-1">
                          <h3 className="text-md font-semibold">{report.title}</h3>
                          <p className="text-sm text-gray-600 mt-2">
                            {report.description}
                          </p>
                        </div>

                        <div className="mt-4 space-y-2">
                          <button className="w-full bg-gray-100 text-gray-700 font-medium py-2 px-4 rounded hover:bg-gray-200 transition">
                            View Existing Reports
                          </button>
                          <button
                            className="w-full bg-[#F62D51] text-white font-medium py-2 px-4 rounded hover:bg-red-700 transition"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCreateReport(report);
                            }}
                          >
                            Create a Report
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {selectedReport && !viewingReport && (
                  <div className="mt-6 p-6 border rounded-lg">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-semibold">
                        {selectedReport.title}
                      </h3>
                      <button
                        className="text-gray-500 hover:text-gray-700"
                        onClick={() => setSelectedReport(null)}
                      >
                        ✕
                      </button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium">Start Date</label>
                        <input type="date" className="w-full border rounded p-2 date-input-with-icon" />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium">End Date</label>
                        <input type="date" className="w-full border rounded p-2 date-input-with-icon" />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium">Chart Type</label>
                        <Select>
                          <SelectTrigger className="bg-white">
                            <SelectValue placeholder="Select a type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="line">Line Chart</SelectItem>
                            <SelectItem value="bar">Bar Chart</SelectItem>
                            <SelectItem value="pie">Pie Chart</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="flex justify-end gap-3">
                      <button
                        className="px-4 py-2 border rounded hover:bg-gray-50"
                        onClick={() => setSelectedReport(null)}
                      >
                        Cancel
                      </button>
                      <button
                        className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                        onClick={() => handleCreateReport(selectedReport)}
                      >
                        Generate Report
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
