import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ChevronDown, ChevronUp, Plus, Link, Loader2 } from "lucide-react";
import { getAllActionPlans } from "@/store/slices/actionPlanSlice";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import axios from "axios";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { LinkIcon } from "lucide-react";
import { getApiBaseUrl } from '../../utils/api-config';
import { useTranslation } from "react-i18next";

export function ActionPlanSection({ incident, handleInputChange }) {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(true);
  const dispatch = useDispatch();
  const { actionPlans, isLoading } = useSelector((state) => state.actionPlan || { actionPlans: [] });
  const [selectedActionPlan, setSelectedActionPlan] = useState(incident.actionPlanID || "none");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [linkedActionPlan, setLinkedActionPlan] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [newActionPlan, setNewActionPlan] = useState({
    name: "",
    nature: "",
    comment: ""
  });
  // API Base URL - use dynamic URL from utility function
  const API_BASE_URL = getApiBaseUrl();
  // Fetch action plans on component mount
  useEffect(() => {
    dispatch(getAllActionPlans());
  }, [dispatch]);

  // Find the linked action plan if any
  useEffect(() => {
    if (incident?.actionPlanID && actionPlans?.length > 0) {
      const plan = actionPlans.find(plan => plan.actionPlanID === incident.actionPlanID);
      setLinkedActionPlan(plan || null);
    } else {
      setLinkedActionPlan(null);
    }
  }, [incident, actionPlans]);

  // Handle action plan selection
  const handleActionPlanChange = (value) => {
    setSelectedActionPlan(value);
    handleInputChange('actionPlanID', value === "none" ? null : value);
  };

  // Get action plan name by ID
  const getActionPlanName = () => {
    if (!selectedActionPlan || selectedActionPlan === "none" || !actionPlans) return "";
    const plan = actionPlans.find(plan => plan.actionPlanID === selectedActionPlan);
    return plan ? plan.name : "";
  };

  // Handle create action plan form input changes
  const handleCreateInputChange = (e) => {
    const { name, value } = e.target;
    setNewActionPlan(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle select input changes
  const handleSelectChange = (name, value) => {
    setNewActionPlan(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle create action plan form submission
  const handleCreateActionPlan = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Prepare the data for submission
      const actionPlanData = {
        ...newActionPlan,
        actionPlanID: `AP_${Date.now()}`, // Generate ID
      };

      // Create the action plan
      const response = await axios.post(
        `${API_BASE_URL}/actionPlans`,
        actionPlanData,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data.success) {
        // Link the action plan to the incident
        await linkActionPlanToIncident(response.data.data.actionPlanID);

        // Show success message
        toast.success(t('admin.incidents.action_plan.created_and_linked_successfully'));

        // Reset form and close modal
        setNewActionPlan({
          name: "",
          nature: "",
          comment: ""
        });
        setIsCreateModalOpen(false);

        // Refresh action plans list
        dispatch(getAllActionPlans());
      }
    } catch (error) {
      console.error('Error creating action plan:', error);
      toast.error(error?.response?.data?.message || error?.message || t('admin.incidents.action_plan.failed_to_create_action_plan', 'Default text'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle link action plan to incident
  const handleLinkActionPlan = async (actionPlanID) => {
    setIsSubmitting(true);
    try {
      await linkActionPlanToIncident(actionPlanID);
      setIsLinkModalOpen(false);
      toast.success(t('admin.incidents.action_plan.linked_successfully'));
    } catch (error) {
      console.error('Error linking action plan:', error);
      toast.error(error?.response?.data?.message || error?.message || t('admin.incidents.action_plan.failed_to_link_action_plan', 'Default text'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to link action plan to incident
  const linkActionPlanToIncident = async (actionPlanID) => {
    handleInputChange('actionPlanID', actionPlanID);
    
    // Refresh linked action plan
    const plan = actionPlans.find(plan => plan.actionPlanID === actionPlanID);
    setLinkedActionPlan(plan || null);
    
    // Only send the actionPlanID in the update request
    try {
      const response = await axios.put(
        `${API_BASE_URL}/incidents/${incident.incidentID}`,
        { actionPlanID }, // Only send the actionPlanID
        {
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' },
        }
      );
      
      if (!response.data.success) {
        throw new Error(response.data.message || t('admin.incidents.action_plan.failed_to_update_incident', 'Default text'));
      }
      
      return true;
    } catch (error) {
      console.error('Error updating incident with action plan:', error);
      toast.error(t('admin.incidents.action_plan.failed_to_save_action_plan_link_please_try_again', 'Default text'));
      throw error;
    }
  };

  // Handle unlink action plan from incident
  const handleUnlinkActionPlan = async () => {
    if (!window.confirm(t('admin.incidents.action_plan.are_you_sure_you_want_to_unlink_this_action_plan', 'Default text'))) {
      return;
    }

    try {
      // Update incident state by removing the action plan
      handleInputChange('actionPlanID', null);
      setLinkedActionPlan(null);
      
      // Only send the actionPlanID in the update request
      const response = await axios.put(
        `${API_BASE_URL}/incidents/${incident.incidentID}`,
        { actionPlanID: null }, // Only send the actionPlanID as null
        {
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' },
        }
      );
      
      if (response.data.success) {
        toast.success(t('admin.incidents.action_plan.unlinked_successfully'));
      } else {
        throw new Error(response.data.message || t('admin.incidents.action_plan.failed_to_unlink_action_plan', 'Default text'));
      }
    } catch (error) {
      console.error('Error unlinking action plan:', error);
      toast.error(error?.response?.data?.message || error?.message || t('admin.incidents.action_plan.failed_to_unlink_action_plan', 'Default text'));
    }
  };

  // Filter action plans based on search query
  const filteredActionPlans = actionPlans?.filter(plan =>
    plan.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (plan.comment && plan.comment.toLowerCase().includes(searchQuery.toLowerCase()))
  ) || [];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
          <span className="mt-4 text-gray-600">{t('admin.incidents.action_plan.loading_action_plans', 'Default text')}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">{t('admin.incidents.action_plan.action_plan', 'Default text')}</h2>

        {/* Show action buttons when there's a linked plan */}
        {linkedActionPlan && (
          <div className="flex gap-2">
            <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
              <DialogTrigger asChild>
                <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white">
                  <Plus className="h-4 w-4 mr-2" />
                  {t('admin.incidents.action_plan.create_action_plan', 'Default text')}
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>{t('admin.incidents.action_plan.create_new_action_plan', 'Default text')}</DialogTitle>
                  <DialogDescription>
                    {t('admin.incidents.action_plan.fill_in_the_details_below_to_create_a_new_action_plan', 'Default text')}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleCreateActionPlan} className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">{t('admin.incidents.action_plan.name', 'Default text')}</Label>
                    <Input
                      id="name"
                      name="name"
                      value={newActionPlan.name}
                      onChange={handleCreateInputChange}
                      placeholder={t('admin.incidents.action_plan.enter_action_plan_name', 'Default text')}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nature">{t('admin.incidents.action_plan.nature', 'Default text')}</Label>
                    <Select
                      value={newActionPlan.nature}
                      onValueChange={(value) => handleSelectChange("nature", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('admin.incidents.action_plan.select_nature', 'Default text')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="preventive">{t('admin.incidents.action_plan.preventive', 'Default text')}</SelectItem>
                        <SelectItem value="corrective">{t('admin.incidents.action_plan.corrective', 'Default text')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="comment">{t('admin.incidents.action_plan.comment', 'Default text')}</Label>
                    <Textarea
                      id="comment"
                      name="comment"
                      value={newActionPlan.comment}
                      onChange={handleCreateInputChange}
                      placeholder={t('admin.incidents.action_plan.enter_comment', 'Default text')}
                      rows={3}
                    />
                  </div>
                  <div className="flex justify-end gap-3 mt-6">
                    <Button
        type="button"
                      variant="outline"
                      onClick={() => setIsCreateModalOpen(false)}
                    >
                      {t('admin.incidents.action_plan.cancel', 'Default text')}
                    </Button>
                    <Button
                      type="submit"
                      className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {t('admin.incidents.action_plan.creating', 'Default text')}
                        </>
                      ) : (
                        t('admin.incidents.action_plan.create_action_plan', 'Default text')
                      )}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>

            <Dialog open={isLinkModalOpen} onOpenChange={setIsLinkModalOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="w-9 h-9 p-0 flex items-center justify-center">
                  <LinkIcon className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>{t('admin.incidents.action_plan.link_existing_action_plan', 'Default text')}</DialogTitle>
                  <DialogDescription>
                    {t('admin.incidents.action_plan.select_an_action_plan_to_link_to_this_incident', 'Default text')}
                  </DialogDescription>
                </DialogHeader>
                <div className="mt-4">
                  <div className="mb-4">
                    <Input
                      placeholder={t('admin.incidents.action_plan.search_action_plans', 'Default text')}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full"
                    />
        </div>

                  {isSubmitting ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-[#F62D51] mr-2" />
                      <span className="text-gray-500">{t('admin.incidents.action_plan.linking_action_plan', 'Default text')}</span>
            </div>
          ) : (
                    <div className="max-h-[400px] overflow-y-auto">
                      {filteredActionPlans.length > 0 ? (
                        <div className="grid gap-3">
                          {filteredActionPlans.map((plan) => (
                            <div
                              key={plan.actionPlanID}
                              className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                              onClick={() => handleLinkActionPlan(plan.actionPlanID)}
                            >
                              <div className="flex justify-between items-start">
                                <div>
                                  <h3 className="font-medium text-[#242A33]">{plan.name}</h3>
                                  <p className="text-sm text-gray-500 mt-1">
                                    {plan.nature ? `${t('admin.incidents.action_plan.nature', 'Default text')}: ${plan.nature}` : t('admin.incidents.action_plan.no_nature_specified', 'Default text')}
                                  </p>
                                </div>
                                <span className="text-xs text-gray-400">{plan.actionPlanID}</span>
                              </div>
                              {plan.comment && (
                                <p className="text-sm mt-2 text-gray-600 line-clamp-2">{plan.comment}</p>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          {searchQuery ? t('admin.incidents.action_plan.no_action_plans_match_your_search', 'Default text') : t('admin.incidents.action_plan.no_action_plans_available', 'Default text')}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        {linkedActionPlan ? (
          <div className="p-5 border border-gray-200 rounded-lg">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="font-medium text-lg text-[#242A33]">{linkedActionPlan.name}</h3>
                <div className="flex items-center mt-1">
                  {linkedActionPlan.nature && (
                    <span className="text-xs text-gray-500 mr-3">
                      {t('admin.incidents.action_plan.nature', 'Default text')}: {linkedActionPlan.nature}
                    </span>
                  )}
                  <span className="text-xs text-gray-500">{t('admin.incidents.action_plan.id', 'Default text')}: {linkedActionPlan.actionPlanID}</span>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
                onClick={handleUnlinkActionPlan}
              >
                {t('admin.incidents.action_plan.unlink', 'Default text')}
              </Button>
              </div>

            {linkedActionPlan.comment && (
              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-1">{t('admin.incidents.action_plan.comment', 'Default text')}</h4>
                <p className="text-[#242A33]">{linkedActionPlan.comment}</p>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8 px-6">
            <h3 className="text-lg font-medium text-[#242A33] mb-2">{t('admin.incidents.action_plan.no_action_plan_linked', 'Default text')}</h3>
            <p className="text-gray-500 mb-6 max-w-md mx-auto">
              {t('admin.incidents.action_plan.link_an_existing_action_plan_or_create_a_new_one_to_help_manage_this_incident', 'Default text')}
            </p>
            <div className="flex justify-center gap-4">
              <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white">
                    <Plus className="h-4 w-4 mr-2" />
                    {t('admin.incidents.action_plan.create_action_plan', 'Default text')}
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>{t('admin.incidents.action_plan.create_new_action_plan', 'Default text')}</DialogTitle>
                    <DialogDescription>
                      {t('admin.incidents.action_plan.fill_in_the_details_below_to_create_a_new_action_plan', 'Default text')}
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleCreateActionPlan} className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">{t('admin.incidents.action_plan.name', 'Default text')}</Label>
                      <Input
                        id="name"
                        name="name"
                        value={newActionPlan.name}
                        onChange={handleCreateInputChange}
                        placeholder={t('admin.incidents.action_plan.enter_action_plan_name', 'Default text')}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="nature">{t('admin.incidents.action_plan.nature', 'Default text')}</Label>
                      <Select
                        value={newActionPlan.nature}
                        onValueChange={(value) => handleSelectChange("nature", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={t('admin.incidents.action_plan.select_nature', 'Default text')} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="preventive">{t('admin.incidents.action_plan.preventive', 'Default text')}</SelectItem>
                          <SelectItem value="corrective">{t('admin.incidents.action_plan.corrective', 'Default text')}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="comment">{t('admin.incidents.action_plan.comment', 'Default text')}</Label>
                      <Textarea
                        id="comment"
                        name="comment"
                        value={newActionPlan.comment}
                        onChange={handleCreateInputChange}
                        placeholder={t('admin.incidents.action_plan.enter_comment', 'Default text')}
                        rows={3}
                      />
                    </div>
                    <div className="flex justify-end gap-3 mt-6">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsCreateModalOpen(false)}
                      >
                        {t('admin.incidents.action_plan.cancel', 'Default text')}
                      </Button>
                      <Button
                        type="submit"
                        className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            {t('admin.incidents.action_plan.creating', 'Default text')}
                          </>
                        ) : (
                          t('admin.incidents.action_plan.create_action_plan', 'Default text')
                        )}
                      </Button>
                              </div>
                  </form>
                </DialogContent>
              </Dialog>

              <Dialog open={isLinkModalOpen} onOpenChange={setIsLinkModalOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <LinkIcon className="h-4 w-4 mr-2" />
                    {t('admin.incidents.action_plan.link_existing', 'Default text')}
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>{t('admin.incidents.action_plan.link_existing_action_plan', 'Default text')}</DialogTitle>
                    <DialogDescription>
                      {t('admin.incidents.action_plan.select_an_action_plan_to_link_to_this_incident', 'Default text')}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="mt-4">
                    <div className="mb-4">
                      <Input
                        placeholder={t('admin.incidents.action_plan.search_action_plans', 'Default text')}
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full"
                      />
                              </div>

                    {isSubmitting ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51] mr-2" />
                        <span className="text-gray-500">{t('admin.incidents.action_plan.linking_action_plan', 'Default text')}</span>
                              </div>
                    ) : (
                      <div className="max-h-[400px] overflow-y-auto">
                        {filteredActionPlans.length > 0 ? (
                          <div className="grid gap-3">
                            {filteredActionPlans.map((plan) => (
                              <div
                                key={plan.actionPlanID}
                                className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                                onClick={() => handleLinkActionPlan(plan.actionPlanID)}
                              >
                                <div className="flex justify-between items-start">
                              <div>
                                    <h3 className="font-medium text-[#242A33]">{plan.name}</h3>
                                    <p className="text-sm text-gray-500 mt-1">
                                      {plan.nature ? `${t('admin.incidents.action_plan.nature', 'Default text')}: ${plan.nature}` : t('admin.incidents.action_plan.no_nature_specified', 'Default text')}
                                    </p>
                              </div>
                                  <span className="text-xs text-gray-400">{plan.actionPlanID}</span>
                            </div>
                            {plan.comment && (
                                  <p className="text-sm mt-2 text-gray-600 line-clamp-2">{plan.comment}</p>
                                )}
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            {searchQuery ? t('admin.incidents.action_plan.no_action_plans_match_your_search', 'Default text') : t('admin.incidents.action_plan.no_action_plans_available', 'Default text')}
                    </div>
                  )}
                </div>
              )}
                  </div>
                </DialogContent>
              </Dialog>
            </div>
            </div>
          )}
        </div>
    </div>
  );
}