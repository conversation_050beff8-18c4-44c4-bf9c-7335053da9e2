const express = require('express');
const router = express.Router();
const db = require('../models');
const CampagneResponse = db.CampagneResponse;
const ControlQuestion = db.ControlQuestion;
const Campagne = db.Campagne;
const User = db.User;
const { authenticateToken } = require('../middleware/auth');



// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get all responses for a specific campagne
router.get('/campagnes/:campagneId/responses', async (req, res) => {
  try {
    const { campagneId } = req.params;

    const responses = await CampagneResponse.findAll({
      where: { campagneID: campagneId },
      include: [
        {
          model: ControlQuestion,
          as: 'question',
          attributes: ['id', 'question_text', 'input_type', 'options', 'order']
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        }
      ],
      order: [['sampleNumber', 'ASC'], [{ model: ControlQuestion, as: 'question' }, 'order', 'ASC']]
    });

    res.json({
      success: true,
      data: responses
    });
  } catch (error) {
    console.error('Error fetching campagne responses:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching campagne responses',
      error: error.message
    });
  }
});

// Get questions for a campagne (from the associated control)
router.get('/campagnes/:campagneId/questions', async (req, res) => {
  try {
    const { campagneId } = req.params;

    // First get the campagne to find the associated control
    const campagne = await Campagne.findByPk(campagneId);
    if (!campagne) {
      return res.status(404).json({
        success: false,
        message: 'Campagne not found'
      });
    }

    if (!campagne.controlId) {
      return res.status(400).json({
        success: false,
        message: 'No control associated with this campagne'
      });
    }

    // Get questions from the associated control
    const questions = await ControlQuestion.findAll({
      where: { controlID: campagne.controlId },
      order: [['order', 'ASC'], ['id', 'ASC']]
    });

    res.json({
      success: true,
      data: questions
    });
  } catch (error) {
    console.error('Error fetching campagne questions:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching campagne questions',
      error: error.message
    });
  }
});

// Save or update a response
router.post('/campagnes/:campagneId/responses', async (req, res) => {
  try {
    const { campagneId } = req.params;
    const { questionID, sampleNumber, response_value } = req.body;
    const userId = req.user.userId; // From auth middleware

    // Convert to proper data types (campagneId is now string)
    const questionIdInt = parseInt(questionID, 10);
    const userIdInt = parseInt(userId, 10);
    const sampleNumberInt = parseInt(sampleNumber, 10);

    // Validate required fields
    if (!questionIdInt || isNaN(sampleNumberInt) || response_value === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Question ID, sample number, and response value are required'
      });
    }

    // Check if response already exists
    console.log('Searching for existing response with:', {
      campagneID: campagneId,
      questionID: questionIdInt,
      userID: userIdInt,
      sampleNumber: sampleNumberInt,
      types: {
        campagneID: typeof campagneId,
        questionID: typeof questionIdInt,
        userID: typeof userIdInt,
        sampleNumber: typeof sampleNumberInt
      }
    });

    const existingResponse = await CampagneResponse.findOne({
      where: {
        campagneID: campagneId,
        questionID: questionIdInt,
        userID: userIdInt,
        sampleNumber: sampleNumberInt
      }
    });

    console.log('Found existing response:', existingResponse ? 'YES' : 'NO');
    if (existingResponse) {
      console.log('Existing response details:', {
        id: existingResponse.id,
        campagneID: existingResponse.campagneID,
        questionID: existingResponse.questionID,
        userID: existingResponse.userID,
        sampleNumber: existingResponse.sampleNumber
      });
    }

    let response;
    if (existingResponse) {
      // Update existing response
      console.log('Updating existing response with ID:', existingResponse.id);
      await existingResponse.update({
        response_value
      });
      response = existingResponse;
    } else {
      // Use findOrCreate as a fallback to handle race conditions
      console.log('No existing response found, using findOrCreate...');
      const [createdResponse, created] = await CampagneResponse.findOrCreate({
        where: {
          campagneID: campagneId,
          questionID: questionIdInt,
          userID: userIdInt,
          sampleNumber: sampleNumberInt
        },
        defaults: {
          response_value
        }
      });

      if (!created) {
        // Record was found, update it
        console.log('Found existing record during findOrCreate, updating...');
        await createdResponse.update({ response_value });
      } else {
        console.log('Created new response record');
      }

      response = createdResponse;
    }

    res.json({
      success: true,
      data: response,
      message: 'Response saved successfully'
    });
  } catch (error) {
    console.error('Error saving campagne response:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving campagne response',
      error: error.message
    });
  }
});

// Delete a response
router.delete('/campagnes/:campagneId/responses/:responseId', async (req, res) => {
  try {
    const { responseId } = req.params;
    const userId = req.user.userId;

    const response = await CampagneResponse.findOne({
      where: {
        id: responseId,
        userID: userId // Users can only delete their own responses
      }
    });

    if (!response) {
      return res.status(404).json({
        success: false,
        message: 'Response not found or you do not have permission to delete it'
      });
    }

    await response.destroy();

    res.json({
      success: true,
      message: 'Response deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting campagne response:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting campagne response',
      error: error.message
    });
  }
});

module.exports = router;
