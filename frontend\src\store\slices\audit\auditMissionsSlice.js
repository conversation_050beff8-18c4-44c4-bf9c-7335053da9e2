import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import * as missionService from '@/services/audit-mission-service';

const initialState = {
  items: [],
  currentItem: null,
  loading: false,
  error: null,
  lastFetched: null,
};

// Fetch all missions (optionally by plan)
export const fetchAuditMissionsByPlan = createAsyncThunk(
  'auditMissions/fetchByPlan',
  async ({ planId, signal }, { rejectWithValue }) => {
    try {
      const response = await missionService.getAuditMissionsByPlanId(planId, signal);
      if (!response) return null; // Request cancelled
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.message);
    } catch (error) {
      if (error.name === 'CanceledError') return null;
      return rejectWithValue(error.message);
    }
  }
);

export const fetchAuditMissionById = createAsyncThunk(
  'auditMissions/fetchById',
  async ({ id, signal }, { rejectWithValue }) => {
    try {
      const response = await missionService.getAuditMissionById(id, signal);
      if (!response) return null;
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.message);
    } catch (error) {
      if (error.name === 'CanceledError') return null;
      return rejectWithValue(error.message);
    }
  }
);

export const createAuditMission = createAsyncThunk(
  'auditMissions/create',
  async ({ missionData, signal }, { rejectWithValue }) => {
    try {
      const response = await missionService.createAuditMission(missionData, signal);
      if (!response) return null;
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.message);
    } catch (error) {
      if (error.name === 'CanceledError') return null;
      return rejectWithValue(error.message);
    }
  }
);

export const updateAuditMission = createAsyncThunk(
  'auditMissions/update',
  async ({ id, missionData, signal }, { rejectWithValue }) => {
    try {
      const response = await missionService.updateAuditMission(id, missionData, signal);
      if (!response) return null;
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.message);
    } catch (error) {
      if (error.name === 'CanceledError') return null;
      return rejectWithValue(error.message);
    }
  }
);

export const deleteAuditMission = createAsyncThunk(
  'auditMissions/delete',
  async ({ id, signal }, { rejectWithValue }) => {
    try {
      const response = await missionService.deleteAuditMission(id, signal);
      if (!response) return null;
      if (response.success) {
        return id;
      }
      return rejectWithValue(response.message);
    } catch (error) {
      if (error.name === 'CanceledError') return null;
      return rejectWithValue(error.message);
    }
  }
);

const auditMissionsSlice = createSlice({
  name: 'auditMissions',
  initialState,
  reducers: {
    clearCurrentMission: (state) => {
      state.currentItem = null;
      state.lastFetched = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch by plan
      .addCase(fetchAuditMissionsByPlan.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAuditMissionsByPlan.fulfilled, (state, action) => {
        if (action.payload === null) return;
        state.loading = false;
        state.items = action.payload;
        state.lastFetched = Date.now();
      })
      .addCase(fetchAuditMissionsByPlan.rejected, (state, action) => {
        if (action.payload === null) return;
        state.loading = false;
        state.error = action.payload;
      })
      // Fetch by id
      .addCase(fetchAuditMissionById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAuditMissionById.fulfilled, (state, action) => {
        if (action.payload === null) return;
        state.loading = false;
        state.currentItem = action.payload;
        state.lastFetched = Date.now();
        // Update in items array if exists
        const idx = state.items.findIndex(m => m.id === action.payload.id);
        if (idx !== -1) {
          state.items[idx] = action.payload;
        }
      })
      .addCase(fetchAuditMissionById.rejected, (state, action) => {
        if (action.payload === null) return;
        state.loading = false;
        state.error = action.payload;
      })
      // Create
      .addCase(createAuditMission.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createAuditMission.fulfilled, (state, action) => {
        if (action.payload === null) return;
        state.loading = false;
        state.items.unshift(action.payload);
      })
      .addCase(createAuditMission.rejected, (state, action) => {
        if (action.payload === null) return;
        state.loading = false;
        state.error = action.payload;
      })
      // Update
      .addCase(updateAuditMission.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateAuditMission.fulfilled, (state, action) => {
        if (action.payload === null) return;
        state.loading = false;
        // Update in items array
        const idx = state.items.findIndex(m => m.id === action.payload.id);
        if (idx !== -1) {
          state.items[idx] = action.payload;
        }
        // If currentItem is the updated one, update it
        if (state.currentItem && state.currentItem.id === action.payload.id) {
          state.currentItem = action.payload;
        }
      })
      .addCase(updateAuditMission.rejected, (state, action) => {
        if (action.payload === null) return;
        state.loading = false;
        state.error = action.payload;
      })
      // Delete
      .addCase(deleteAuditMission.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteAuditMission.fulfilled, (state, action) => {
        if (action.payload === null) return;
        state.loading = false;
        state.items = state.items.filter(m => m.id !== action.payload);
        if (state.currentItem && state.currentItem.id === action.payload) {
          state.currentItem = null;
        }
      })
      .addCase(deleteAuditMission.rejected, (state, action) => {
        if (action.payload === null) return;
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearCurrentMission, clearError } = auditMissionsSlice.actions;
export default auditMissionsSlice.reducer; 