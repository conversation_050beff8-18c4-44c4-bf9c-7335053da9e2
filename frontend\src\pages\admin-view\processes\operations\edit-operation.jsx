import { useEffect, useState, useCallback } from "react";
import { useParams, useNavigate, Outlet, useLocation } from "react-router-dom";
import { Workflow, FileText, Edit, Loader2 } from "lucide-react";
import DetailHeader from "@/components/ui/detail-header";
import TabBar from "@/components/ui/tabs/tab-bar";
import TabContent from "@/components/ui/tabs/tab-content";
import axios from "axios";
import { toast } from "sonner";
import { getApiBaseUrl } from "@/utils/api-config";
import operationIcon from "@/assets/operation.png";

function EditOperation() {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const API_BASE_URL = getApiBaseUrl();
  const [currentOperation, setCurrentOperation] = useState(null);
  const [organizationalProcesses, setOrganizationalProcesses] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState(null);

  // Get the current tab from the URL
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    if (path.includes('/features')) {
      return 'features';
    }
    return 'overview';
  }, [location.pathname]);

  const [activeTab, setActiveTab] = useState(getCurrentTab());

  // Update active tab when location changes
  useEffect(() => {
    setActiveTab(getCurrentTab());
  }, [location, getCurrentTab]);

  // Fetch organizational processes
  const fetchOrganizationalProcesses = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/organizationalProcesses`, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
      });
      if (response.data.success) {
        setOrganizationalProcesses(response.data.data);
      }
    } catch (error) {
      console.error("Failed to fetch organizational processes:", error);
    }
  };

  // Fetch operation on component mount
  useEffect(() => {
    const fetchOperation = async () => {
      if (!id) return;
      
      try {
        setIsLoading(true);
        const response = await axios.get(`${API_BASE_URL}/operations/${id}`, {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
        });
        
        if (response.data.success) {
          setCurrentOperation(response.data.data);
          setIsError(false);
          setError(null);
        } else {
          setIsError(true);
          setError(response.data.message || "Failed to fetch operation");
          toast.error(response.data.message || "Failed to fetch operation");
        }
      } catch (err) {
        setIsError(true);
        setError(err.message || "An error occurred while fetching the operation");
        toast.error(err.message || "An error occurred while fetching the operation");
      } finally {
        setIsLoading(false);
      }
    };

    // Fetch data
    const fetchData = async () => {
      await Promise.all([
        fetchOperation(),
        fetchOrganizationalProcesses()
      ]);
    };

    fetchData();
  }, [id]);

  // Get parent organizational process name
  const getParentOrgProcessName = useCallback(() => {
    if (!currentOperation?.parentOrganizationalProcess || !organizationalProcesses.length) {
      return "None";
    }
    
    const parent = organizationalProcesses.find(
      process => process.organizationalProcessID === currentOperation.parentOrganizationalProcess
    );
    
    return parent ? parent.name : "Unknown";
  }, [currentOperation, organizationalProcesses]);

  // Handle go back
  const handleGoBack = () => {
    navigate("/admin/processes/operations");
  };

  const tabs = [
    { id: "overview", label: "Overview", icon: <FileText className="h-4 w-4" /> },
    { id: "features", label: "Features", icon: <Edit className="h-4 w-4" /> },
  ];
  
  // Navigate to tab
  const navigateToTab = (tabId) => {
    switch (tabId) {
      case "overview":
        navigate(`/admin/processes/operations/${id}`);
        break;
      case "features":
        navigate(`/admin/processes/operations/${id}/features`);
        break;
      default:
        navigate(`/admin/processes/operations/${id}`);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          Error: {error}
        </div>
      </div>
    );
  }

  if (!currentOperation) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          Operation not found
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      {/* Header */}
      <DetailHeader
        title={currentOperation?.name}
        icon={<img src={operationIcon} alt="Opération" className="h-6 w-6" />}
        metadata={[
          currentOperation.code ? `Code: ${currentOperation.code}` : 'No code',
          `Parent: ${getParentOrgProcessName()}`,
          currentOperation.comment ? 
            `${currentOperation.comment.substring(0, 100)}${currentOperation.comment.length > 100 ? '...' : ''}` : 
            'No comment'
        ]}
        onBack={handleGoBack}
        backLabel="Back to Operations"
      />

      {/* Tabs */}
      <TabBar
        activeTab={activeTab}
        tabs={tabs}
        onTabChange={navigateToTab}
        className="mb-6"
      />

      {/* Tab Content */}
      <TabContent>
        <Outlet context={{ 
          operation: currentOperation, 
          organizationalProcesses,
          refreshOperation: async () => {
            setIsLoading(true);
            try {
              const response = await axios.get(`${API_BASE_URL}/operations/${id}`, {
                withCredentials: true,
                headers: { "Content-Type": "application/json" },
              });
              
              if (response.data.success) {
                setCurrentOperation(response.data.data);
              }
            } catch (error) {
              toast.error("Failed to refresh operation data");
            } finally {
              setIsLoading(false);
            }
          }
        }} />
      </TabContent>
    </div>
  );
}

export default EditOperation;
