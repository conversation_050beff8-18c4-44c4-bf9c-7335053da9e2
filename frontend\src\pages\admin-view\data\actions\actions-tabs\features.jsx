import { useState, useEffect } from "react";
import { useOutletContext, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Loader2, User, Calendar } from "lucide-react";
import { updateAction } from "@/store/slices/actionSlice";
import { getAllUsers } from "@/store/slices/userSlice";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";

function ActionFeatures() {
  const { action } = useOutletContext();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  // Get users from Redux store
  const { users, isLoading: isLoadingUsers } = useSelector((state) => state.user);
  
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    ...action,
    startDate: action.startDate ? new Date(action.startDate).toISOString().split('T')[0] : '',
    endDate: action.endDate ? new Date(action.endDate).toISOString().split('T')[0] : '',
    assigneeId: action.assigneeId || (action.assignee ? action.assignee.id : 'none')
  });
  
  // Fetch users on component mount
  useEffect(() => {
    dispatch(getAllUsers());
  }, [dispatch]);
  
  // Update form data when action changes
  useEffect(() => {
    setFormData({
      ...action,
      startDate: action.startDate ? new Date(action.startDate).toISOString().split('T')[0] : '',
      endDate: action.endDate ? new Date(action.endDate).toISOString().split('T')[0] : '',
      assigneeId: action.assigneeId || (action.assignee ? action.assignee.id : 'none')
    });
  }, [action]);
  
  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle select change
  const handleSelectChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!formData.name) {
      toast.error("Name is required");
      return;
    }
    
    setSubmitting(true);
    
    dispatch(updateAction({
      id: action.actionID,
      actionData: {
        name: formData.name,
        priority: formData.priority,
        status: formData.status,
        description: formData.description,
        startDate: formData.startDate || null,
        endDate: formData.endDate || null,
        assigneeId: formData.assigneeId !== "none" ? parseInt(formData.assigneeId) : null
      }
    }))
      .then((result) => {
        setSubmitting(false);
        if (!result.error) {
          toast.success("Action updated successfully");
        }
      });
  };
  
  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">Action Details</h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="flex flex-col md:col-span-3">
            <Label htmlFor="name" className="mb-2">Name *</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter action name"
              required
              className="w-full"
            />
          </div>
          
          <div className="flex flex-col">
            <Label htmlFor="priority" className="mb-2">Priority</Label>
            <Select
              value={formData.priority}
              onValueChange={(value) => handleSelectChange("priority", value)}
            >
              <SelectTrigger id="priority" className="w-full">
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Low">Low</SelectItem>
                <SelectItem value="Medium">Medium</SelectItem>
                <SelectItem value="High">High</SelectItem>
                <SelectItem value="Critical">Critical</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex flex-col">
            <Label htmlFor="status" className="mb-2">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleSelectChange("status", value)}
            >
              <SelectTrigger id="status" className="w-full">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Not Started">Not Started</SelectItem>
                <SelectItem value="In Progress">In Progress</SelectItem>
                <SelectItem value="Completed">Completed</SelectItem>
                <SelectItem value="Cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex flex-col">
            <Label htmlFor="assigneeId" className="mb-2 flex items-center">
              <User className="h-4 w-4 mr-1" />
              Assignee
            </Label>
            <Select
              value={formData.assigneeId ? formData.assigneeId.toString() : 'none'}
              onValueChange={(value) => handleSelectChange("assigneeId", value !== 'none' ? parseInt(value) : null)}
            >
              <SelectTrigger id="assigneeId" className="w-full">
                <SelectValue placeholder="Select assignee" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {users.map((user) => (
                  <SelectItem key={user.id} value={user.id.toString()}>
                    {user.username} ({user.role})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex flex-col">
            <Label htmlFor="startDate" className="mb-2 flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              Start Date
            </Label>
            <Input
              id="startDate"
              name="startDate"
              type="date"
              value={formData.startDate}
              onChange={handleInputChange}
              className="w-full"
            />
          </div>
          
          <div className="flex flex-col">
            <Label htmlFor="endDate" className="mb-2 flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              End Date
            </Label>
            <Input
              id="endDate"
              name="endDate"
              type="date"
              value={formData.endDate}
              onChange={handleInputChange}
              className="w-full"
            />
          </div>
          
          <div className="flex flex-col md:col-span-3">
            <Label htmlFor="description" className="mb-2">Description</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description || ''}
              onChange={handleInputChange}
              placeholder="Enter action description"
              rows={4}
              className="w-full"
            />
          </div>
        </div>
        
        <div className="flex justify-end">
          <Button type="submit" disabled={submitting}>
            {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Update Action
          </Button>
        </div>
      </form>
    </div>
  );
}

export default ActionFeatures;
