const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllBusinessProcesses,
  createBusinessProcess,
  getBusinessProcessById,
  updateBusinessProcess,
  deleteBusinessProcess
} = require('../../controllers/processes/business-process-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all business processes
router.get('/', getAllBusinessProcesses);

// Create new business process
router.post('/', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), createBusinessProcess);

// Get business process by ID
router.get('/:id', getBusinessProcessById);

// Update business process
router.put('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), updateBusinessProcess);

// Delete business process
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteBusinessProcess);

module.exports = router;
