const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllControlTypes,
  createControlType,
  getControlTypeById,
  updateControlType,
  deleteControlType
} = require('../../controllers/data/control-type-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all control types
router.get('/', getAllControlTypes);

// Create new control type
router.post('/', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), createControlType);

// Get control type by ID
router.get('/:id', getControlTypeById);

// Update control type
router.put('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), updateControlType);

// Delete control type
router.delete('/:id', authorizeRoles(['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager']), deleteControlType);

module.exports = router;
