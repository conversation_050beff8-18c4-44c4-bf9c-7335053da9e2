const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../../middleware/auth');
const missionChartsController = require('../../../controllers/audit/rapport/mission-charts-controller');

// Add specific CORS middleware for mission charts rapport routes
router.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:5173');
  res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, x-user-id');
  res.header('Access-Control-Allow-Credentials', 'true');
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  next();
});

// Apply authentication middleware to all routes
router.use(verifyToken);

// Activities progression
router.get('/mission-charts/:missionId/activities', authorizeRoles(['audit_director', 'auditor']), missionChartsController.getActivitiesProgression);
// Recommendations progression
router.get('/mission-charts/:missionId/recommendations', authorizeRoles(['audit_director', 'auditor']), missionChartsController.getRecommendationsProgression);
// Constats breakdown
router.get('/mission-charts/:missionId/constats', authorizeRoles(['audit_director', 'auditor']), missionChartsController.getConstatsBreakdown);
// DEBUG: Recommendations, action plans, and actions
router.get('/mission-charts/:missionId/recommendations-debug', authorizeRoles(['audit_director', 'auditor']), missionChartsController.getRecommendationsActionPlansActions);

module.exports = router; 