const { faker } = require('@faker-js/faker');
const db = require('../models');

// Your existing role IDs (do not touch Role table)
const ROLE_IDS = [1,2,3,4,5,6,7,8,9];

// Helper to bulk insert and log
async function bulkInsert(model, rows, name) {
  if (!rows.length) return;
  try {
    await model.bulkCreate(rows);
    console.log(`Seeded ${rows.length} rows for ${name}`);
  } catch (err) {
    console.error(`Error seeding ${name}:`, err);
  }
}

(async () => {
  try {
    // 1. Users
    const userCount = await db.User.count();
    const usersToAdd = 50 - userCount;
    let newUsers = [];
    if (usersToAdd > 0) {
      for (let i = 0; i < usersToAdd; i++) {
        newUsers.push({
          username: faker.internet.userName(),
          email: faker.internet.email(),
          password: faker.internet.password(12),
        });
      }
      await bulkInsert(db.User, newUsers, 'User');
    }
    const allUsers = await db.User.findAll();
    const newUserRows = allUsers.slice(-usersToAdd);

    // 2. UserRole (only for new users)
    if (db.UserRole && newUserRows.length) {
      const userRoles = [];
      newUserRows.forEach(user => {
        const assignedRoles = faker.helpers.arrayElements(ROLE_IDS, faker.number.int({ min: 1, max: 2 }));
        assignedRoles.forEach(roleId => {
          userRoles.push({ userId: user.id, roleId });
        });
      });
      await bulkInsert(db.UserRole, userRoles, 'UserRole');
    }

    // 3. BusinessLine
    const businessLineCount = await db.BusinessLine.count();
    const businessLinesToAdd = 50 - businessLineCount;
    let businessLines = [];
    for (let i = 0; i < businessLinesToAdd; i++) {
      businessLines.push({
        businessLineID: `BL_${faker.string.alphanumeric(8)}`,
        name: faker.company.name(),
        description: faker.company.catchPhrase(),
      });
    }
    await bulkInsert(db.BusinessLine, businessLines, 'BusinessLine');
    const allBusinessLines = await db.BusinessLine.findAll();

    // 4. Entity
    const entityCount = await db.Entity.count();
    const entitiesToAdd = 50 - entityCount;
    let entities = [];
    for (let i = 0; i < entitiesToAdd; i++) {
      entities.push({
        entityID: `EN_${faker.string.alphanumeric(8)}`,
        name: faker.company.name(),
        type: faker.company.buzzNoun(),
        localCurrency: faker.finance.currencyCode(),
        code: faker.string.alphanumeric(6),
        comment: faker.lorem.sentence(),
        internalExternal: faker.helpers.arrayElement(['Internal', 'External']),
        parentEntityID: null,
      });
    }
    await bulkInsert(db.Entity, entities, 'Entity');
    const allEntities = await db.Entity.findAll();

    // 5. Application
    const appCount = await db.Application.count();
    const appsToAdd = 50 - appCount;
    let applications = [];
    for (let i = 0; i < appsToAdd; i++) {
      applications.push({
        applicationID: `APP_${faker.string.alphanumeric(8)}`,
        name: faker.commerce.productName(),
        comment: faker.lorem.sentence(),
      });
    }
    await bulkInsert(db.Application, applications, 'Application');
    const allApplications = await db.Application.findAll();

    // 6. BusinessProcess
    const bpCount = await db.BusinessProcess.count();
    const bpsToAdd = 50 - bpCount;
    let businessProcesses = [];
    for (let i = 0; i < bpsToAdd; i++) {
      businessProcesses.push({
        businessProcessID: `BP_${faker.string.alphanumeric(8)}`,
        name: faker.commerce.department(),
        code: faker.string.alphanumeric(6),
        comment: faker.lorem.sentence(),
        parentBusinessProcessID: null,
      });
    }
    await bulkInsert(db.BusinessProcess, businessProcesses, 'BusinessProcess');
    const allBusinessProcesses = await db.BusinessProcess.findAll();

    // 7. OrganizationalProcess
    const opCount = await db.OrganizationalProcess.count();
    const opsToAdd = 50 - opCount;
    let orgProcesses = [];
    for (let i = 0; i < opsToAdd; i++) {
      orgProcesses.push({
        organizationalProcessID: `OP_${faker.string.alphanumeric(8)}`,
        name: faker.commerce.department(),
        code: faker.string.alphanumeric(6),
        comment: faker.lorem.sentence(),
        parentOrganizationalProcess: null,
        parentBusinessProcess: faker.helpers.arrayElement(allBusinessProcesses).businessProcessID,
      });
    }
    await bulkInsert(db.OrganizationalProcess, orgProcesses, 'OrganizationalProcess');
    const allOrgProcesses = await db.OrganizationalProcess.findAll();

    // 8. Operation
    const operationCount = await db.Operation.count();
    const operationsToAdd = 50 - operationCount;
    let operations = [];
    for (let i = 0; i < operationsToAdd; i++) {
      operations.push({
        operationID: `OPE_${faker.string.alphanumeric(8)}`,
        name: faker.commerce.productName(),
        code: faker.string.alphanumeric(6),
        comment: faker.lorem.sentence(),
        entityID: faker.helpers.arrayElement(allEntities).entityID,
      });
    }
    await bulkInsert(db.Operation, operations, 'Operation');
    const allOperations = await db.Operation.findAll();

    // 9. Control
    const controlCount = await db.Control.count();
    const controlsToAdd = 50 - controlCount;
    let controls = [];
    for (let i = 0; i < controlsToAdd; i++) {
      controls.push({
        controlID: `CTRL_${faker.string.alphanumeric(8)}`,
        name: faker.word.words(3),
        code: faker.string.alphanumeric(6),
        controlKey: faker.number.int({ min: 0, max: 1 }),
        controlExecutionMethod: faker.helpers.arrayElement(['Observation', 'Contrôle systématique', 'Test', 'Revue']),
        objective: faker.lorem.sentence(),
        executionProcedure: faker.lorem.sentence(),
        operationalCost: faker.number.float({ min: 1000, max: 100000, precision: 0.01 }),
        organizationalLevel: faker.helpers.arrayElement(['Global', 'Local']),
        sampleType: faker.helpers.arrayElement(['Facturé', 'Contractuel']),
        testingFrequency: faker.helpers.arrayElement(['Trimestrielle', 'Semestrielle', 'Annuelle']),
        testingMethod: faker.helpers.arrayElement(['Enquête', 'Observation', 'Test']),
        testingPopulationSize: faker.number.int({ min: 5, max: 100 }),
        testingProcedure: faker.lorem.sentence(),
        implementingActionPlan: faker.lorem.sentence(),
        designQuality: faker.helpers.arrayElement(['Satisfaisant', 'Insatisfaisant']),
        effectivenessLevel: faker.helpers.arrayElement(['Satisfaisant', 'Insatisfaisant']),
        businessProcess: faker.helpers.arrayElement(allBusinessProcesses).businessProcessID,
        organizationalProcess: faker.helpers.arrayElement(allOrgProcesses).organizationalProcessID,
        operation: faker.helpers.arrayElement(allOperations).operationID,
        application: faker.helpers.arrayElement(allApplications).applicationID,
        entity: faker.helpers.arrayElement(allEntities).entityID,
        controlType: null,
        risk: null,
        comment: faker.lorem.sentence(),
      });
    }
    await bulkInsert(db.Control, controls, 'Control');
    const allControls = await db.Control.findAll();

    // === CHECKS FOR DEPENDENCIES ===
    if (!allEntities.length) throw new Error('No entities found. Seed Entity table first.');
    if (!allBusinessProcesses.length) throw new Error('No business processes found. Seed BusinessProcess table first.');
    if (!allOrgProcesses.length) throw new Error('No organizational processes found. Seed OrganizationalProcess table first.');
    if (!allOperations.length) throw new Error('No operations found. Seed Operation table first.');
    if (!allApplications.length) throw new Error('No applications found. Seed Application table first.');
    if (!allControls.length) throw new Error('No controls found. Seed Control table first.');
    if (!allBusinessLines.length) throw new Error('No business lines found. Seed BusinessLine table first.');

    // 10. Risk
    await db.Incident.destroy({ where: {} });
    await db.Risk.destroy({ where: {} });

    const TOTAL_RISKS = 2000;
    const TOTAL_INCIDENTS = 1000;
    const RISK_BATCH_SIZE = 500;
    const INCIDENT_BATCH_SIZE = 500;
    const riskStartDate = new Date(2015, 0, 1);
    const riskEndDate = new Date(2025, 11, 31);
    const incidentStartDate = new Date(2015, 0, 1);
    const incidentEndDate = new Date(2025, 11, 31);

    function randomDate(start, end) {
      return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    }

    let allRiskIDs = [];
    for (let batch = 0; batch < TOTAL_RISKS / RISK_BATCH_SIZE; batch++) {
      let risks = [];
      for (let i = 0; i < RISK_BATCH_SIZE; i++) {
        const createdAt = randomDate(riskStartDate, riskEndDate);
        const updatedAt = randomDate(createdAt, riskEndDate);
        const riskID = `RISK_${faker.string.alphanumeric(12)}`;
        allRiskIDs.push(riskID);
        risks.push({
          riskID,
          name: faker.word.words(3),
          current_state: 'Risk Created',
          code: faker.string.alphanumeric(6),
          methodOfIdentification: faker.helpers.arrayElement(['survey', 'incident_database', 'audit_mission', 'workshop']),
          impact: faker.number.int({ min: 1, max: 5 }),
          DMR: faker.helpers.arrayElement([1, 4, 9, 16, 25]),
          probability: faker.number.int({ min: 1, max: 5 }),
          appetite: faker.number.int({ min: 1, max: 5 }),
          acceptance: faker.datatype.boolean(),
          avoidance: faker.datatype.boolean(),
          insurance: faker.datatype.boolean(),
          reduction: faker.datatype.boolean(),
          comment: faker.lorem.sentence(),
          mitigatingActionPlan: faker.lorem.sentence(),
          businessProcessID: faker.helpers.arrayElement(allBusinessProcesses).businessProcessID,
          organizationalProcessID: faker.helpers.arrayElement(allOrgProcesses).organizationalProcessID,
          operationID: faker.helpers.arrayElement(allOperations).operationID,
          applicationID: faker.helpers.arrayElement(allApplications).applicationID,
          entityID: faker.helpers.arrayElement(allEntities).entityID,
          riskTypeID: null,
          controlID: null,
          createdAt,
          updatedAt
        });
      }
      await bulkInsert(db.Risk, risks, `Risk batch ${batch + 1}`);
    }
    const allRisks = await db.Risk.findAll({ attributes: ['riskID'] });
    const riskIDs = allRisks.map(r => r.riskID);

    if (!riskIDs.length) throw new Error('No risks found after seeding.');

    // 11. Incident
    await db.Incident.destroy({ where: {} }); // Already cleared above, but safe
    for (let batch = 0; batch < TOTAL_INCIDENTS / INCIDENT_BATCH_SIZE; batch++) {
      let incidents = [];
      for (let i = 0; i < INCIDENT_BATCH_SIZE; i++) {
        const baseDate = randomDate(incidentStartDate, incidentEndDate);
        const declarationDate = baseDate;
        const detectionDate = randomDate(baseDate, incidentEndDate);
        const occurrenceDate = randomDate(baseDate, incidentEndDate);
        incidents.push({
          incidentID: `INC_${faker.string.alphanumeric(12)}`,
          riskID: faker.helpers.arrayElement(riskIDs),
          name: faker.word.words(3),
          description: faker.lorem.sentence(),
          declaredBy: faker.person.fullName(),
          declarationDate,
          declarantEntity: faker.helpers.arrayElement(allEntities).entityID,
          detectionDate,
          occurrenceDate,
          nearMiss: faker.datatype.boolean(),
          nature: faker.word.words(2),
          impact: faker.helpers.arrayElement(['Très fort', 'Fort', 'Moyen', 'Faible', 'Très faible']),
          priority: faker.helpers.arrayElement(['Critical', 'High', 'Medium', 'Low']),
          currency: faker.finance.currencyCode(),
          grossLoss: faker.number.float({ min: 0, max: 100000, precision: 0.01 }),
          recoveries: faker.number.float({ min: 0, max: 50000, precision: 0.01 }),
          provisions: faker.number.float({ min: 0, max: 50000, precision: 0.01 }),
          controlID: faker.helpers.arrayElement(allControls).controlID,
          entityID: faker.helpers.arrayElement(allEntities).entityID,
          businessLineID: faker.helpers.arrayElement(allBusinessLines).businessLineID,
          incidentTypeID: null,
          businessProcessID: faker.helpers.arrayElement(allBusinessProcesses).businessProcessID,
          organizationalProcessID: faker.helpers.arrayElement(allOrgProcesses).organizationalProcessID,
          productID: null,
          applicationID: faker.helpers.arrayElement(allApplications).applicationID,
          actionPlanID: null,
          current_state: 'Start',
        });
      }
      await bulkInsert(db.Incident, incidents, `Incident batch ${batch + 1}`);
    }

    // 12. ActionPlan
    const actionPlanCount = await db.ActionPlan.count();
    const actionPlansToAdd = 50 - actionPlanCount;
    let actionPlans = [];
    for (let i = 0; i < actionPlansToAdd; i++) {
      actionPlans.push({
        actionPlanID: `AP_${faker.string.alphanumeric(8)}`,
        name: faker.word.words(3),
        category: faker.helpers.arrayElement(['Security', 'Training', 'IT', 'Documentation', 'Compliance']),
        nature: faker.helpers.arrayElement(['Preventive', 'Corrective']),
        origin: faker.helpers.arrayElement(['Audit', 'Compliance', 'Event', 'Risk', 'RFC', 'Other']),
        priority: faker.helpers.arrayElement(['Low', 'Medium', 'High', 'Critical']),
        organizationalLevel: faker.helpers.arrayElement(['Local', 'Global']),
        means: faker.helpers.arrayElement(['Internal Resources', 'External Consultant', 'Mixed']),
        comment: faker.lorem.sentence(),
        plannedBeginDate: faker.date.past(),
        plannedEndDate: faker.date.future(),
        approverId: null,
        assigneeId: null,
        incidentID: faker.helpers.arrayElement(allIncidents).incidentID,
      });
    }
    await bulkInsert(db.ActionPlan, actionPlans, 'ActionPlan');
    const allActionPlans = await db.ActionPlan.findAll();

    // 13. AuditPlan
    const auditPlanCount = await db.AuditPlan.count();
    let auditPlans = [];
    if (auditPlanCount < 3) {
      for (let i = 0; i < 3 - auditPlanCount; i++) {
        auditPlans.push({
          id: `AUDITPLAN_${faker.string.alphanumeric(8)}`,
          name: faker.word.words(3),
          description: faker.lorem.sentence(),
          status: faker.helpers.arrayElement(['Planned', 'In Progress', 'Completed']),
          datedebut: faker.date.past(),
          datefin: faker.date.future(),
          avancement: faker.number.int({ min: 0, max: 100 }).toString(),
          calendrier: faker.number.int({ min: 2020, max: 2030 }),
          directeuraudit: faker.helpers.arrayElement(allUsers).id,
        });
      }
      await bulkInsert(db.AuditPlan, auditPlans, 'AuditPlan');
    }
    const allAuditPlans = await db.AuditPlan.findAll();

    // 14. AuditMission
    const auditMissionCount = await db.AuditMission.count();
    let auditMissions = [];
    if (auditMissionCount < 60) {
      for (let i = 0; i < 60 - auditMissionCount; i++) {
        auditMissions.push({
          id: `AUDITMISSION_${faker.string.alphanumeric(8)}`,
          name: faker.word.words(3),
          categorie: faker.commerce.department(),
          code: faker.string.alphanumeric(6),
          etat: faker.helpers.arrayElement(['Planned', 'In Progress', 'Completed']),
          chefmission: null,
          principalAudite: faker.person.fullName(),
          objectif: faker.lorem.sentence(),
          avancement: faker.number.int({ min: 0, max: 100 }).toString(),
          planifieInitialement: faker.datatype.boolean(),
          evaluation: faker.helpers.arrayElement(['Bon niveau', 'Peut être améliorée', 'Amélioration nécessaire', 'A risque']),
          datedebut: faker.date.past(),
          datefin: faker.date.future(),
          pointfort: faker.lorem.sentence(),
          pointfaible: faker.lorem.sentence(),
          auditplanID: faker.helpers.arrayElement(allAuditPlans).id,
        });
      }
      await bulkInsert(db.AuditMission, auditMissions, 'AuditMission');
    }
    const allAuditMissions = await db.AuditMission.findAll();

    // 15. AuditConstat
    const auditConstatCount = await db.AuditConstat.count();
    const auditConstatsToAdd = 500 - auditConstatCount;
    let auditConstats = [];
    for (let i = 0; i < auditConstatsToAdd; i++) {
      auditConstats.push({
        id: `CONST_${faker.string.alphanumeric(8)}`,
        name: faker.word.words(3),
        auditMissionID: faker.helpers.arrayElement(allAuditMissions).id,
        impact: faker.helpers.arrayElement(['Très fort', 'Fort', 'Moyen', 'Faible', 'Très faible']),
        description: faker.lorem.sentence(),
        recommendation: faker.lorem.sentence(),
        responsable: null,
        date: faker.date.past(),
      });
    }
    await bulkInsert(db.AuditConstat, auditConstats, 'AuditConstat');
    const allAuditConstats = await db.AuditConstat.findAll();

    // 16. AuditScope
    if (db.AuditScope) {
      const auditScopeCount = await db.AuditScope.count();
      const auditScopesToAdd = 50 - auditScopeCount;
      let auditScopes = [];
      for (let i = 0; i < auditScopesToAdd; i++) {
        auditScopes.push({
          id: `SCOPE_${faker.string.alphanumeric(8)}`,
          name: faker.word.words(3),
          auditMissionID: faker.helpers.arrayElement(allAuditMissions).id,
          description: faker.lorem.sentence(),
        });
      }
      await bulkInsert(db.AuditScope, auditScopes, 'AuditScope');
    }

    // 17. AuditRecommendation
    const auditRecoCount = await db.AuditRecommendation.count();
    const auditRecosToAdd = 500 - auditRecoCount;
    let auditRecommendations = [];
    for (let i = 0; i < auditRecosToAdd; i++) {
      auditRecommendations.push({
        id: `RECO_${faker.string.alphanumeric(8)}`,
        name: faker.word.words(3),
        auditMissionID: faker.helpers.arrayElement(allAuditMissions).id,
        description: faker.lorem.sentence(),
        responsable: null,
        date: faker.date.past(),
      });
    }
    await bulkInsert(db.AuditRecommendation, auditRecommendations, 'AuditRecommendation');

    // 18. AuditActivity
    let auditActivities = [];
    for (let i = 0; i < 500; i++) {
      auditActivities.push({
        id: `AUDITACT_${faker.string.alphanumeric(8)}`,
        name: faker.word.words(3),
        auditMissionID: faker.helpers.arrayElement(allAuditMissions).id,
        responsable: null,
        status: faker.helpers.arrayElement(['Not Started', 'In Progress', 'Completed']),
        datedebut: faker.date.past(),
        datefin: faker.date.future(),
        chargedetravailestimee: faker.number.int({ min: 10, max: 100 }),
        chargedetravaileffective: faker.number.int({ min: 10, max: 100 }),
        objectif: faker.lorem.sentence(),
        depense: faker.number.float({ min: 100, max: 10000, precision: 0.01 }),
      });
    }
    await bulkInsert(db.AuditActivity, auditActivities, 'AuditActivity');

    // 19. FicheDeTravail
    if (db.FicheDeTravail) {
      let fichesTravail = [];
      for (let i = 0; i < 500; i++) {
        fichesTravail.push({
          id: `FICHE_TRAVAIL_${faker.string.alphanumeric(8)}`,
          name: faker.word.words(3),
          auditMissionID: faker.helpers.arrayElement(allAuditMissions).id,
          description: faker.lorem.sentence(),
        });
      }
      await bulkInsert(db.FicheDeTravail, fichesTravail, 'FicheDeTravail');
    }

    // 20. FicheDeTest
    if (db.FicheDeTest) {
      let fichesTest = [];
      for (let i = 0; i < 500; i++) {
        fichesTest.push({
          id: `FICHE_TEST_${faker.string.alphanumeric(8)}`,
          name: faker.word.words(3),
          auditMissionID: faker.helpers.arrayElement(allAuditMissions).id,
          description: faker.lorem.sentence(),
        });
      }
      await bulkInsert(db.FicheDeTest, fichesTest, 'FicheDeTest');
    }

    // 21. Done
    console.log('All tables seeded to at least 50 rows!');
    console.log('Requested audit data seeded!');
    process.exit(0);
  } catch (err) {
    console.error('Seeding failed:', err);
    process.exit(1);
  }
})();