module.exports = (sequelize, DataTypes) => {
  const RiskContributor = sequelize.define('RiskContributor', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    risk_id: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Risk',
        key: 'riskID',
      },
      onDelete: 'CASCADE',
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    assigned_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id',
      },
    },
    assigned_date: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    }
  }, {
    tableName: 'RiskContributor',
    freezeTableName: true,
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['risk_id', 'user_id'],
        name: 'unique_risk_contributor'
      }
    ]
  });

  RiskContributor.associate = (models) => {
    // RiskContributor belongs to Risk
    RiskContributor.belongsTo(models.Risk, {
      foreignKey: 'risk_id',
      targetKey: 'riskID',
      as: 'risk'
    });

    // RiskContributor belongs to User
    RiskContributor.belongsTo(models.User, {
      foreignKey: 'user_id',
      targetKey: 'id',
      as: 'contributor'
    });

    // RiskContributor belongs to User (as assigner)
    RiskContributor.belongsTo(models.User, {
      foreignKey: 'assigned_by',
      targetKey: 'id',
      as: 'assigner'
    });
  };

  return RiskContributor;
}; 