// backend/scripts/migrate-user-roles.js
require('dotenv').config({ path: 'backend/.env' });
const { User, Role, sequelize } = require('../models');

async function migrateUserRoles() {
  console.log('Starting user role migration...');

  try {
    // Get all users
    const users = await User.findAll();
    console.log(`Found ${users.length} users to migrate`);

    // Get all roles
    const roles = await Role.findAll();
    if (roles.length === 0) {
      console.error('No roles found. Please run migrations first.');
      return;
    }

    // Create a map of legacy roles to new role codes
    const roleMap = {
      'super_admin': 'grc_admin',
      'admin': 'grc_manager',
      'user': 'grc_contributor'
    };

    // Create a transaction
    const transaction = await sequelize.transaction();

    try {
      let successCount = 0;
      let errorCount = 0;

      // Process each user
      for (const user of users) {
        try {
          // Get the appropriate role based on the user's legacy role
          const roleCode = roleMap[user.role] || 'grc_contributor';
          const role = roles.find(r => r.code === roleCode);

          if (!role) {
            console.error(`Role with code ${roleCode} not found for user ${user.id}`);
            errorCount++;
            continue;
          }

          // Check if the user already has this role
          const existingRoles = await user.getRoles({ transaction });
          if (existingRoles.some(r => r.id === role.id)) {
            console.log(`User ${user.id} already has role ${role.name}`);
            successCount++;
            continue;
          }

          // Assign the role to the user
          await user.addRole(role, { transaction });
          console.log(`Assigned role ${role.name} to user ${user.id}`);
          successCount++;
        } catch (error) {
          console.error(`Error migrating user ${user.id}:`, error);
          errorCount++;
        }
      }

      // Commit the transaction
      await transaction.commit();

      console.log(`Migration completed: ${successCount} users migrated successfully, ${errorCount} errors`);
    } catch (error) {
      // Rollback the transaction on error
      await transaction.rollback();
      console.error('Migration failed:', error);
    }
  } catch (error) {
    console.error('Migration script error:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the migration
migrateUserRoles()
  .then(() => {
    console.log('Migration script completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Migration script failed:', error);
    process.exit(1);
  });
