import { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { Plus, ChevronLeft, Trash2, Loader2, ArrowUpDown, Shield, ChevronUp } from 'lucide-react'; // Added icons
import { useSelector } from 'react-redux';
import { hasPermission } from '@/store/auth-slice';
import PageHeader from "@/components/ui/page-header";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from "react-i18next";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import TablePagination from "@/components/ui/table-pagination";
import FilterPanel from "@/components/ui/filter-panel";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function ControlTypesManagement() {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const canCreate = useSelector(state => hasPermission(state, 'create'));
  const canDelete = useSelector(state => hasPermission(state, 'delete'));
  const [searchQuery, setSearchQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [selectedControlTypes, setSelectedControlTypes] = useState([]);
  const [controlTypes, setControlTypes] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentControlType, setCurrentControlType] = useState(null);
  const [parentControlTypes, setParentControlTypes] = useState([]);
  const API_BASE_URL = getApiBaseUrl();
  // Filter states
  const [filters, setFilters] = useState({
    parentControlTypeID: 'all',
  });

  // Clear all filters
  const clearFilters = () => {
    console.log('Clearing all filters');
    const clearedFilters = {
      parentControlTypeID: 'all',
    };
    setFilters(clearedFilters);
    console.log('Current filters before clearing:', filters);
    console.log('Filters cleared to:', clearedFilters);
  };

  const columns = [
    { key: 'name', label: t('admin.control_types.columns.name', 'Name'), sortable: true },
    { key: 'code', label: t('admin.control_types.columns.code', 'Code'), sortable: true },
    { key: 'comment', label: t('admin.control_types.columns.comment', 'Comment'), sortable: true },
    { key: 'parentControlTypeID', label: t('admin.control_types.columns.parent', 'Parent Control Type'), sortable: true },
  ];

  const [newControlType, setNewControlType] = useState({
    name: '',
    code: '',
    comment: '',
    parentControlTypeID: 'none',
  });

  useEffect(() => {
    fetchControlTypes();
  }, []);

  const fetchControlTypes = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/controlTypes`, { withCredentials: true });
      if (response.data.success) {
        setControlTypes(response.data.data);
        setParentControlTypes(response.data.data);
      }
    } catch (_error) { // Renamed to _error
      console.error(t('admin.control_types.error.fetch_failed', 'Error fetching control types:'), _error);
      toast.error(_error.message || t('admin.control_types.error.fetch_failed_toast', 'Failed to fetch control types'));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      const controlTypeToCreate = {
        ...newControlType,
        controlTypeID: `CTL_TYPE_${Date.now()}`,
        parentControlTypeID: newControlType.parentControlTypeID === 'none' ? null : newControlType.parentControlTypeID
      };

      const response = await axios.post(`${API_BASE_URL}/controlTypes`, controlTypeToCreate, {
        withCredentials: true, headers: { "Content-Type": "application/json" }
      });

      if (response.data.success) {
        setIsOpen(false);
        setNewControlType({ name: '', code: '', comment: '', parentControlTypeID: 'none' });
        await fetchControlTypes();
        toast.success(t('admin.control_types.success.created', 'Control type created successfully'));
      }
    } catch (error) {
      console.error(t('admin.control_types.error.create_failed', 'Error creating control type:'), error);
      toast.error(error.response?.data?.message || error.message || t('admin.control_types.error.create_failed_toast', 'Failed to create control type'));
    } finally {
      setSubmitting(false);
    }
  };

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') direction = 'desc';
    setSortConfig({ key, direction });
    const sortedTypes = [...controlTypes].sort((a, b) => {
      if (a[key] < b[key]) return direction === 'asc' ? -1 : 1;
      if (a[key] > b[key]) return direction === 'asc' ? 1 : -1;
      return 0;
    });
    setControlTypes(sortedTypes);
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedControlTypes(controlTypes.map(type => type.controlTypeID));
    } else {
      setSelectedControlTypes([]);
    }
  };

  const handleSelectControlType = (controlTypeID) => {
    setSelectedControlTypes(prev =>
      prev.includes(controlTypeID) ? prev.filter(id => id !== controlTypeID) : [...prev, controlTypeID]
    );
  };

  const handleDeleteSelected = async () => {
    if (selectedControlTypes.length === 0) return;

    if (!window.confirm(t('admin.control_types.confirm.delete', 'Are you sure you want to delete {{count}} selected control type(s)?', { count: selectedControlTypes.length }))) return;

    try {
      setSubmitting(true);
      let successCount = 0;
      let failedItems = [];

      for (const controlTypeID of selectedControlTypes) {
        try {
          await axios.delete(`${API_BASE_URL}/controlTypes/${controlTypeID}`, { withCredentials: true });
          successCount++;
        } catch (err) {
          const errorMessage = err.response?.data?.message || 'Unknown error';
          failedItems.push({ id: controlTypeID, error: errorMessage });
        }
      }

      if (successCount > 0) toast.success(t('admin.control_types.success.deleted', 'Successfully deleted {{count}} control type(s)', { count: successCount }), { duration: 3000 });

      failedItems.forEach(item => {
        if (item.error.includes('referenced by other records') || item.error.includes('child')) {
          toast.error(t('admin.control_types.error.delete_parent', 'Cannot delete "{{id}}": It is referenced by other control types as a parent. You must first delete or reassign all child control types before deleting this parent.', { id: item.id }), {
            position: "top-center", duration: 6000, style: { background: '#f8d7da', color: '#721c24', border: '1px solid #f5c6cb', padding: '16px', maxWidth: '80%', textAlign: 'center' }
          });
        } else if (item.error.includes('Control')) {
          toast.error(t('admin.control_types.error.delete_in_use', 'Cannot delete "{{id}}": It is being used by one or more controls. You must first update or delete all controls using this control type before deleting it.', { id: item.id }), {
            position: "top-center", duration: 6000, style: { background: '#f8d7da', color: '#721c24', border: '1px solid #f5c6cb', padding: '16px', maxWidth: '80%', textAlign: 'center' }
          });
        } else {
          toast.error(t('admin.control_types.error.delete_failed_item', 'Failed to delete {{id}}: {{error}}', { id: item.id, error: item.error }), { duration: 5000 });
        }
      });

      setSelectedControlTypes([]);
      await fetchControlTypes();
    } catch (_error) { // Renamed to _error
      console.error(t('admin.control_types.error.delete_failed', 'Error deleting control types:'), _error);
      toast.error(_error.message || t('admin.control_types.error.delete_unexpected', 'An unexpected error occurred while deleting control types'));
    } finally {
      setSubmitting(false);
    }
  };

  const handleRowClick = (controlTypeID) => {
    navigate(`/admin/data/control-types/${controlTypeID}`);
  };

  const handleUpdateSubmit = async (e) => {
    e.preventDefault();
    try {
      setSubmitting(true);
      const updateData = {
        name: currentControlType.name,
        code: currentControlType.code,
        comment: currentControlType.comment,
        parentControlTypeID: currentControlType.parentControlTypeID === 'none' ? null : currentControlType.parentControlTypeID,
      };

      const response = await axios.put(`${API_BASE_URL}/controlTypes/${currentControlType.controlTypeID}`, updateData, {
        withCredentials: true, headers: { "Content-Type": "application/json" }
      });

      if (response.data.success) {
        setIsEditModalOpen(false);
        setCurrentControlType(null);
        await fetchControlTypes();
        toast.success(t('admin.control_types.success.updated', 'Control type updated successfully'));
      }
    } catch (error) {
      console.error(t('admin.control_types.error.update_failed', 'Error updating control type:'), error);
      toast.error(error.response?.data?.message || error.message || t('admin.control_types.error.update_failed_toast', 'Failed to update control type'));
    } finally {
      setSubmitting(false);
    }
  };

  // Filter control types based on search query and filters
  const filteredControlTypes = useMemo(() => {
    return controlTypes.filter(type => {
      // Apply search query filter
      if (searchQuery) {
        const matchesSearch =
          type.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          type.code?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          type.comment?.toLowerCase().includes(searchQuery.toLowerCase());
        if (!matchesSearch) return false;
      }

      // Apply parent control type filter
      if (filters.parentControlTypeID && filters.parentControlTypeID !== 'all') {
        console.log('Parent Control Type filter:', {
          filterValue: filters.parentControlTypeID,
          typeValue: type.parentControlTypeID,
          match: type.parentControlTypeID === filters.parentControlTypeID
        });
        if (type.parentControlTypeID !== filters.parentControlTypeID) return false;
      }

      return true;
    });
  }, [controlTypes, searchQuery, filters]);

  const totalPages = Math.ceil(filteredControlTypes.length / itemsPerPage);
  const indexOfFirstItem = (currentPage - 1) * itemsPerPage;
  const indexOfLastItem = indexOfFirstItem + itemsPerPage;
  const currentItems = filteredControlTypes.slice(indexOfFirstItem, indexOfLastItem);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) setCurrentPage(page);
  };

  const getParentControlTypeName = (parentId) => controlTypes.find(ct => ct.controlTypeID === parentId)?.name || parentId || '-';

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      <PageHeader
        title={t('admin.control_types.title', 'Control Types Management')}
        description={t('admin.control_types.description', 'Define and manage control types within your organization.')}
        section={t('admin.sidebar.controls', 'Controls')}
        currentPage={t('admin.sidebar.control_types', 'Control Types')}
        searchPlaceholder={t('admin.control_types.search_placeholder', 'Search control types...')}
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
        icon={Shield} // Using Shield for control types
      />

      {/* Filter Panel */}
      <div className="mb-6 filter-panel-container">
        <FilterPanel
          filters={[
            {
              id: "parentControlTypeID",
              label: t('admin.control_types.filters.parent', 'Parent Control Type'),
              component: (
                <Select
                  value={filters.parentControlTypeID}
                  onValueChange={(value) => setFilters({ ...filters, parentControlTypeID: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('admin.control_types.filters.select_parent', 'Select parent control type')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('common.status.all', 'All')}</SelectItem>
                    {controlTypes.map((type) => (
                      <SelectItem key={type.controlTypeID} value={type.controlTypeID}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
          ]}
          activeFilters={filters}
          onClearFilters={clearFilters}
        />
      </div>

      <div className="flex justify-between items-center gap-3 mb-4">
        <Button variant="ghost" className="p-2" onClick={() => navigate('/admin/team')}>
          <ChevronLeft className="h-5 w-5" />
        </Button>
        <div className="flex items-center gap-3">
          {selectedControlTypes.length > 0 && (
            <Button
              variant="outline"
              className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
                canDelete && !submitting
                  ? 'bg-[#F62D51]/90 text-white hover:bg-[#F62D51] hover:shadow-md'
                  : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
              } flex items-center gap-2 px-6 py-2 font-semibold`}
              onClick={handleDeleteSelected}
              disabled={!canDelete || submitting}
            >
              {submitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  {t('common.deleting', 'Deleting...')}
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  {t('common.buttons.delete', 'Delete')} ({selectedControlTypes.length})
                </>
              )}
            </Button>
          )}
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            {canCreate && (
              <DialogTrigger asChild>
                <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  {t('admin.control_types.buttons.add', 'Add Control Type')}
                </Button>
              </DialogTrigger>
            )}
            <DialogContent className="max-w-3xl p-8">
              <DialogHeader>
                <DialogTitle>{t('admin.control_types.dialog.title', 'Add New Control Type')}</DialogTitle>
                <DialogDescription>{t('admin.control_types.dialog.description', 'Fill in the details to create a new control type.')}</DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-8">
                <div className="flex flex-col">
                  <Label htmlFor="name" className="mb-2">{t('admin.control_types.form.name', 'Name')} *</Label>
                  <Input
                    id="name"
                    value={newControlType.name}
                    onChange={(e) => setNewControlType({ ...newControlType, name: e.target.value })}
                    placeholder={t('admin.control_types.form.name_placeholder', 'Enter control type name')}
                    required
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="code" className="mb-2">{t('admin.control_types.form.code', 'Code')}</Label>
                  <Input
                    id="code"
                    value={newControlType.code}
                    onChange={(e) => setNewControlType({ ...newControlType, code: e.target.value })}
                    placeholder={t('admin.control_types.form.code_placeholder', 'Enter code')}
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="comment" className="mb-2">{t('admin.control_types.form.comment', 'Comment')}</Label>
                  <Textarea
                    id="comment"
                    value={newControlType.comment}
                    onChange={(e) => setNewControlType({ ...newControlType, comment: e.target.value })}
                    placeholder={t('admin.control_types.form.comment_placeholder', 'Enter comment')}
                    className="w-full h-24 resize-y"
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="parentControlTypeID" className="mb-2">{t('admin.control_types.form.parent', 'Parent Control Type')}</Label>
                  <Select
                    value={newControlType.parentControlTypeID}
                    onValueChange={(value) => setNewControlType({ ...newControlType, parentControlTypeID: value })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={t('admin.control_types.form.parent_placeholder', 'Select parent control type')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">{t('admin.control_types.form.none', 'None')}</SelectItem>
                      {parentControlTypes.map(controlType => (
                        <SelectItem key={controlType.controlTypeID} value={controlType.controlTypeID}>
                          {controlType.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex justify-end gap-4">
                  <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                    {t('common.buttons.cancel', 'Cancel')}
                  </Button>
                  <Button type="submit" className="bg-[#F62D51] hover:bg-red-700" disabled={submitting}>
                    {submitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t('common.creating', 'Creating...')}
                      </>
                    ) : (
                      t('admin.control_types.buttons.create', 'Create Control Type')
                    )}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                      <Checkbox
                        checked={selectedControlTypes.length === controlTypes.length && controlTypes.length > 0}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all"
                      />
                    </th>
                    {columns.map(column => (
                      <th
                        key={column.key}
                        className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100"
                        onClick={() => column.sortable && handleSort(column.key)}
                      >
                        <div className="flex items-center gap-2">
                          {column.label}
                          {column.sortable && <ArrowUpDown className="h-4 w-4" />}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {currentItems.map((controlType, index) => (
                    <tr
                      key={controlType.controlTypeID}
                      className={`hover:bg-gray-50 cursor-pointer ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                      onClick={() => handleRowClick(controlType.controlTypeID)}
                    >
                      <td className="px-6 py-4">
                        <Checkbox
                          checked={selectedControlTypes.includes(controlType.controlTypeID)}
                          onCheckedChange={() => handleSelectControlType(controlType.controlTypeID)}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </td>

                      <td className="px-6 py-4 text-sm font-bold text-[#242A33] whitespace-nowrap">{controlType.name}</td>
                      <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">{controlType.code || '-'}</td>
                      <td className="px-6 py-4 text-sm text-[#555F6D]">{controlType.comment || '-'}</td>
                      <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">{getParentControlTypeName(controlType.parentControlTypeID)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">
                {t('admin.control_types.items_found', '{{count}} control type(s) found', { count: filteredControlTypes.length })}
              </span>
              {/* Show filter badge if any filter is active */}
              {filters.parentControlTypeID !== 'all' && (
                <Badge
                  variant="outline"
                  className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 cursor-pointer"
                  onClick={() => {
                    // Toggle filter panel visibility
                    const filterPanel = document.querySelector('.filter-panel-container');
                    if (filterPanel) {
                      filterPanel.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                >
                  Filtered
                  <ChevronUp className="ml-1 h-3 w-3" />
                </Badge>
              )}
            </div>
          </div>

          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={filteredControlTypes.length}
            onPageChange={handlePageChange}
            onItemsPerPageChange={(value) => {
              setItemsPerPage(value);
              setCurrentPage(1);
            }}
            startIndex={indexOfFirstItem}
            endIndex={indexOfLastItem}
          />
        </>
      )}

      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-3xl p-8">
          <DialogHeader>
            <DialogTitle>Edit Control Type</DialogTitle>
            <DialogDescription>Update the control type details.</DialogDescription>
          </DialogHeader>
          {currentControlType && (
            <form onSubmit={handleUpdateSubmit} className="space-y-8">
              <div className="flex flex-col">
                <Label htmlFor="edit-name" className="mb-2">Name *</Label>
                <Input
                  id="edit-name"
                  value={currentControlType.name}
                  onChange={(e) => setCurrentControlType({ ...currentControlType, name: e.target.value })}
                  placeholder="Enter control type name"
                  required
                  className="w-full"
                />
              </div>
              <div className="flex flex-col">
                <Label htmlFor="edit-code" className="mb-2">Code</Label>
                <Input
                  id="edit-code"
                  value={currentControlType.code || ''}
                  onChange={(e) => setCurrentControlType({ ...currentControlType, code: e.target.value })}
                  placeholder="Enter code"
                  className="w-full"
                />
              </div>
              <div className="flex flex-col">
                <Label htmlFor="edit-comment" className="mb-2">Comment</Label>
                <Textarea
                  id="edit-comment"
                  value={currentControlType.comment || ''}
                  onChange={(e) => setCurrentControlType({ ...currentControlType, comment: e.target.value })}
                  placeholder="Enter comment"
                  className="w-full h-24 resize-y"
                />
              </div>
              <div className="flex flex-col">
                <Label htmlFor="edit-parentControlTypeID" className="mb-2">Parent Control Type</Label>
                <Select
                  value={currentControlType.parentControlTypeID || ''}
                  onValueChange={(value) => setCurrentControlType({ ...currentControlType, parentControlTypeID: value })}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select parent control type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {parentControlTypes
                      .filter(ct => ct.controlTypeID !== currentControlType.controlTypeID)
                      .map(controlType => (
                        <SelectItem key={controlType.controlTypeID} value={controlType.controlTypeID}>
                          {controlType.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-4">
                <Button type="button" variant="outline" onClick={() => setIsEditModalOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" className="bg-[#F62D51] hover:bg-red-700" disabled={submitting}>
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    'Update Control Type'
                  )}
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}