const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middleware/auth');
const { getInherentRiskRapport } = require('../models/rapport/InherentRiskRapport');
const { getResidualRiskRapport } = require('../models/rapport/ResidualRiskRapport');
const { getBackTestingReport } = require('../models/rapport/RapportRiskIncident');
const { getIncidentsOverTime } = require('../models/rapport/IncidentsOverTime');
const { getIncidentsByType } = require('../models/rapport/IncidentsByType');
const { getLossesOverTime } = require('../models/rapport/LossesOverTime');
const { getLossesAndIncidentsOverTime } = require('../models/rapport/LossesAndIncidentsOverTime');
const db = require('../models');

router.get('/inherentRiskRapport', verifyToken, async (req, res) => {
  try {
    const data = await getInherentRiskRapport();
    if (!data) {
      return res.status(404).json({
        success: false,
        message: 'No data found',
      });
    }
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error in inherentRiskRapport route:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message,
    });
  }
});

router.get('/residualRiskRapport', verifyToken, async (req, res) => {
  try {
    const data = await getResidualRiskRapport();
    if (!data) {
      return res.status(404).json({
        success: false,
        message: 'No data found',
      });
    }
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error in residualRiskRapport route:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message,
    });
  }
});

router.get('/risk-incident', async (req, res) => {
  try {
    const data = await getBackTestingReport();
    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No back testing data found',
      });
    }
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error in back-testing route:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message,
    });
  }
});

router.get('/incidentsOverTime', verifyToken, async (req, res) => {
  try {
    const { timePeriod = 'month', start, end } = req.query;
    const allowedPeriods = ['day', 'month', 'year'];
    if (!allowedPeriods.includes(timePeriod)) {
      return res.status(400).json({ success: false, message: 'Invalid time period' });
    }
    const data = await getIncidentsOverTime(timePeriod, start, end);
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error in incidentsOverTime route:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message,
    });
  }
});

router.get('/incidentsByType', verifyToken, async (req, res) => {
  try {
    const data = await getIncidentsByType();
    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No incidents by type found',
      });
    }
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error in incidentsByType route:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message,
    });
  }
});

router.get('/lossesOverTime', verifyToken, async (req, res) => {
  try {
    const { timePeriod = 'month', start, end } = req.query;
    const allowedPeriods = ['day', 'month', 'year'];
    if (!allowedPeriods.includes(timePeriod)) {
      return res.status(400).json({ success: false, message: 'Invalid time period' });
    }
    const data = await getLossesOverTime(timePeriod, start, end);
    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No losses over time found',
      });
    }
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error in lossesOverTime route:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message,
    });
  }
});

router.get('/lossesAndIncidentsOverTime', verifyToken, async (req, res) => {
  try {
    const { 
      timePeriod = 'month', 
      start, 
      end, 
      riskLevels, 
      incidentTypes, 
      incidentTypeName,
      incidents,
      incidentName, // Add this parameter 
      entities, 
      businessProcesses, 
      organizationalProcesses,
      DMR
    } = req.query;
    
    const allowedPeriods = ['day', 'month', 'year'];
    if (!allowedPeriods.includes(timePeriod)) {
      return res.status(400).json({ success: false, message: 'Invalid time period' });
    }

    // Prepare filters object
    const filters = {};
    
    // Add incident name filter if provided
    if (incidentName) {
      filters.incidentName = incidentName;
    }
    
    // Add incident type name filter if provided
    if (incidentTypeName) {
      filters.incidentTypeName = incidentTypeName;
    }
    
    // Add DMR filter if provided
    if (DMR) {
      filters.DMR = Array.isArray(DMR) ? DMR : [DMR];
    }
    
    // Add risk levels filter if provided
    if (riskLevels) {
      filters.riskLevels = Array.isArray(riskLevels) ? riskLevels : [riskLevels];
    }
    
    // Add incident types filter if provided
    if (incidentTypes) {
      filters.incidentTypes = Array.isArray(incidentTypes) ? incidentTypes : [incidentTypes];
    }
    
    // Add incidents filter if provided
    if (incidents) {
      filters.incidents = Array.isArray(incidents) ? incidents : [incidents];
    }
    
    if (entities) {
      filters.entities = Array.isArray(entities) ? entities : [entities];
    }
    
    if (businessProcesses) {
      filters.businessProcesses = Array.isArray(businessProcesses) ? businessProcesses : [businessProcesses];
    }
    
    if (organizationalProcesses) {
      filters.organizationalProcesses = Array.isArray(organizationalProcesses) ? organizationalProcesses : [organizationalProcesses];
    }

    // Call the model function with filters
    const data = await getLossesAndIncidentsOverTime(timePeriod, start, end, filters);

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No losses and incidents data found',
      });
    }

    return res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    console.error('Error fetching losses and incidents over time:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching losses and incidents over time',
      error: error.message,
    });
  }
});

router.get('/lossesByIncidentType', verifyToken, async (req, res) => {
  try {
    const { start, end } = req.query;
    let query = `
      SELECT 
        i."incidentTypeID",
        COALESCE(it."name", 'type indéfini') as type_d_incident,
        SUM(i."grossLoss") as gross_loss,
        SUM(i."grossLoss" - i."recoveries") as net_loss
      FROM "Incident" i
      LEFT JOIN "IncidentType" it ON i."incidentTypeID" = it."incidentTypeID"
    `;
    const replacements = {};

    if (start && end) {
      query += ` WHERE i."occurrenceDate" BETWEEN :start AND :end`;
      replacements.start = start;
      replacements.end = end;
    }

    query += `
      GROUP BY i."incidentTypeID", it."name"
      ORDER BY i."incidentTypeID"
    `;

    const data = await db.sequelize.query(query, {
      replacements,
      type: db.Sequelize.QueryTypes.SELECT
    });

    if (!data || data.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No losses by incident type found',
      });
    }
    res.json({ success: true, data });
  } catch (error) {
    console.error('Error in lossesByIncidentType route:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message,
    });
  }
});

module.exports = router;
