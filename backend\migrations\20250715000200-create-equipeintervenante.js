'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('EquipeIntervenantes', {
      id: {
        type: Sequelize.STRING,
        primaryKey: true,
        allowNull: false
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: { model: 'Users', key: 'id' },
        onDelete: 'CASCADE'
      },
      auditMissionId: {
        type: Sequelize.STRING,
        allowNull: false,
        references: { model: 'AuditMissions', key: 'id' },
        onDelete: 'CASCADE'
      },
      auditActivityId: {
        type: Sequelize.STRING,
        allowNull: true,
        references: { model: 'AuditActivities', key: 'id' },
        onDelete: 'SET NULL'
      },
      auditConstatId: {
        type: Sequelize.STRING,
        allowNull: true,
        references: { model: 'AuditConstats', key: 'id' },
        onDelete: 'SET NULL'
      },
      auditRecommendationId: {
        type: Sequelize.STRING,
        allowNull: true,
        references: { model: 'AuditRecommendations', key: 'id' },
        onDelete: 'SET NULL'
      },
      chefdemission: {
        type: Sequelize.ENUM('oui', 'non'),
        allowNull: false,
        defaultValue: 'non'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW')
      }
    });
    // Only one chefdemission per mission
    await queryInterface.addIndex('EquipeIntervenantes', ['auditMissionId', 'chefdemission'], {
      unique: true,
      where: { chefdemission: 'oui' },
      name: 'unique_chef_per_mission'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('EquipeIntervenantes');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_EquipeIntervenantes_chefdemission";');
  }
}; 