const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllAuditSkills,
  getAuditSkillById,
  createAuditSkill,
  updateAuditSkill,
  deleteAuditSkill,
  getSkillsStatistics
} = require('../../controllers/audit/audit-skills-controller');

const {
  getAllAuditorsWithSkills,
  getAuditorSkills,
  assignSkillToAuditor,
  updateAuditorSkillRating,
  removeSkillFromAuditor,
  getAuditorSkillsStatistics
} = require('../../controllers/audit/auditor-skills-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Audit Skills CRUD routes - Only Audit Directors can manage skills
router.get('/skills', authorizeRoles(['audit_director', 'admin']), getAllAuditSkills);
router.get('/skills/statistics', authorizeRoles(['audit_director', 'admin']), getSkillsStatistics);
router.get('/skills/:id', authorizeRoles(['audit_director', 'admin']), getAuditSkillById);
router.post('/skills', authorizeRoles(['audit_director', 'admin']), createAuditSkill);
router.put('/skills/:id', authorizeRoles(['audit_director', 'admin']), updateAuditSkill);
router.delete('/skills/:id', authorizeRoles(['audit_director', 'admin']), deleteAuditSkill);

// Auditor Skills (Assignments) routes - Only Audit Directors can manage assignments
router.get('/auditors', authorizeRoles(['audit_director', 'admin']), getAllAuditorsWithSkills);
router.get('/auditors/:auditorId/skills', authorizeRoles(['audit_director', 'admin', 'auditor']), getAuditorSkills);
router.post('/assignments', authorizeRoles(['audit_director', 'admin']), assignSkillToAuditor);
router.put('/assignments/:id', authorizeRoles(['audit_director', 'admin']), updateAuditorSkillRating);
router.delete('/assignments/:id', authorizeRoles(['audit_director', 'admin']), removeSkillFromAuditor);
router.get('/assignments/statistics', authorizeRoles(['audit_director', 'admin']), getAuditorSkillsStatistics);

module.exports = router;
