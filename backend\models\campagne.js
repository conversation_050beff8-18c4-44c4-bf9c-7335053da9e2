module.exports = (sequelize, DataTypes) => {
const Campagne = sequelize.define('Campagne', {
  campagneID: {
    type: DataTypes.STRING,
    primaryKey: true,
    allowNull: false
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Le nom de la campagne est requis'
      },
      len: {
        args: [1, 255],
        msg: 'Le nom doit contenir entre 1 et 255 caractères'
      }
    }
  },
  code: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: {
        args: [0, 50],
        msg: 'Le code ne peut pas dépasser 50 caractères'
      }
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  controlId: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: {
        args: [0, 255],
        msg: 'L\'ID du contrôle ne peut pas dépasser 255 caractères'
      }
    }
  },
  statut: {
    type: DataTypes.ENUM('planifié', 'en cours', 'terminé', 'validé'),
    allowNull: false,
    defaultValue: 'planifié',
    validate: {
      isIn: {
        args: [['planifié', 'en cours', 'terminé', 'validé']],
        msg: 'Le statut doit être: planifié, en cours, terminé, ou validé'
      }
    }
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'Campagnes',
  timestamps: true,
  indexes: [
    {
      fields: ['name']
    },
    {
      fields: ['code']
    },
    {
      fields: ['statut']
    },
    {
      fields: ['createdAt']
    }
  ]
});

// Define associations
Campagne.associate = (models) => {
  // Many-to-many relationship with Users through UserCampagne
  Campagne.belongsToMany(models.User, {
    through: 'UserCampagne',
    foreignKey: 'campagneID',
    otherKey: 'userID',
    as: 'assignedUsers'
  });

  // Has many UserCampagne records (for accessing assignment details)
  Campagne.hasMany(models.UserCampagne, {
    foreignKey: 'campagneID',
    as: 'userAssignments'
  });
};

return Campagne;
};
