const { Sequelize } = require('sequelize');
require('dotenv').config();

async function checkRiskAttachmentTable() {
  const sequelize = new Sequelize(process.env.DB_NAME, process.env.DB_USER, process.env.DB_PASSWORD, {
    host: process.env.DB_HOST,
    dialect: 'postgres',
    logging: false
  });

  try {
    await sequelize.authenticate();
    console.log('Connection has been established successfully.');

    // Check if the RiskAttachment table exists
    const [tables] = await sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'RiskAttachment';
    `);
    
    if (tables.length > 0) {
      console.log('RiskAttachment table exists in the database.');
      
      // Check the structure of the RiskAttachment table
      const [columns] = await sequelize.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'RiskAttachment'
        ORDER BY ordinal_position;
      `);
      
      console.log('RiskAttachment table structure:');
      columns.forEach(column => {
        console.log(`${column.column_name} (${column.data_type}, ${column.is_nullable === 'YES' ? 'nullable' : 'not nullable'})`);
      });
      
      // Check if there are any records in the RiskAttachment table
      const [records] = await sequelize.query(`
        SELECT COUNT(*) as count FROM "RiskAttachment";
      `);
      
      console.log(`\nNumber of records in RiskAttachment table: ${records[0].count}`);
    } else {
      console.log('RiskAttachment table does not exist in the database.');
    }

  } catch (error) {
    console.error('Error checking RiskAttachment table:', error);
  } finally {
    await sequelize.close();
  }
}

checkRiskAttachmentTable();
