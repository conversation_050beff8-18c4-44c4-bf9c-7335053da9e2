import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

/**
 * User service with caching and fallback strategies
 */
class UserService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
    this.lastFetchTime = 0;
    this.isFetching = false;
    this.fetchPromise = null;
  }

  /**
   * Get user by ID with fallback
   */
  getUserById(userId) {
    if (!userId) return null;

    // Try to find in cache first
    const user = this.cache.get(userId.toString());
    if (user) {
      return user;
    }

    // Return fallback user object
    return {
      id: userId,
      username: `Utilisateur ${userId}`,
      email: 'Email non disponible',
      isFallback: true
    };
  }

  /**
   * Get user name by ID with fallback
   */
  getUserName(userId) {
    if (!userId) return 'Non assigné';

    const user = this.getUserById(userId);
    if (user.isFallback) {
      return `Utilisateur ${userId}`;
    }

    return user.username || user.email || `Utilisateur ${userId}`;
  }

  /**
   * Get all users from cache
   */
  getAllUsers() {
    return Array.from(this.cache.values()).filter(user => !user.isFallback);
  }

  /**
   * Check if cache is valid
   */
  isCacheValid() {
    return Date.now() - this.lastFetchTime < this.cacheExpiry;
  }

  /**
   * Fetch users from API with retry and caching
   */
  async fetchUsers(options = {}) {
    const {
      force = false,
      silent = true,
      timeout = 10000
    } = options;

    // Return cached data if valid and not forcing refresh
    if (!force && this.isCacheValid() && this.cache.size > 0) {
      return this.getAllUsers();
    }

    // If already fetching, return the existing promise
    if (this.isFetching && this.fetchPromise) {
      try {
        return await this.fetchPromise;
      } catch {
        return this.getAllUsers(); // Return cached data on error
      }
    }

    // Start new fetch
    this.isFetching = true;
    this.fetchPromise = this._performFetch(timeout, silent);

    try {
      const users = await this.fetchPromise;
      this.isFetching = false;
      this.fetchPromise = null;
      return users;
    } catch (error) {
      this.isFetching = false;
      this.fetchPromise = null;

      if (!silent) {
        console.warn('Failed to fetch users:', error.message);
      }

      // Return cached data as fallback
      return this.getAllUsers();
    }
  }

  /**
   * Internal method to perform the actual fetch
   */
  async _performFetch(timeout, silent) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await axios.get(`${getApiBaseUrl()}/users`, {
        signal: controller.signal,
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' },
        timeout: timeout
      });

      clearTimeout(timeoutId);

      if (response.data.success && Array.isArray(response.data.data)) {
        // Update cache
        this.cache.clear();
        response.data.data.forEach(user => {
          this.cache.set(user.id.toString(), user);
        });
        this.lastFetchTime = Date.now();

        if (!silent) {
          console.log(`Fetched ${response.data.data.length} users successfully`);
        }

        return response.data.data;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      clearTimeout(timeoutId);

      // Don't throw for aborted requests
      if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {
        if (!silent) {
          console.warn('User fetch request was aborted');
        }
        return this.getAllUsers();
      }

      throw error;
    }
  }

  /**
   * Preload users in background
   */
  preloadUsers() {
    if (!this.isCacheValid()) {
      this.fetchUsers({ silent: true }).catch(() => {
        // Ignore errors in background preload
      });
    }
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
    this.lastFetchTime = 0;
  }

  /**
   * Get cache stats for debugging
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      lastFetchTime: this.lastFetchTime,
      isValid: this.isCacheValid(),
      isFetching: this.isFetching
    };
  }
}

// Create singleton instance
const userService = new UserService();

// Preload users on service creation
userService.preloadUsers();

export default userService;
