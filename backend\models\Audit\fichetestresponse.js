'use strict';

module.exports = (sequelize, DataTypes) => {
  const FicheTestResponse = sequelize.define('FicheTestResponse', {
    id: {
      type: DataTypes.STRING(50),
      primaryKey: true,
      allowNull: false
    },
    ficheDeTravailID: {
      type: DataTypes.STRING(50),
      allowNull: false,
      references: {
        model: 'FicheDeTravail',
        key: 'id'
      }
    },
    questionID: {
      type: DataTypes.STRING(50),
      allowNull: false,
      references: {
        model: 'Questions',
        key: 'id'
      }
    },
    sampleNumber: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 99
      }
    },
    answer: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'FicheTestResponses',
    timestamps: true,
    indexes: []
  });

  FicheTestResponse.associate = function(models) {
    FicheTestResponse.belongsTo(models.FicheDeTravail, {
      foreignKey: 'ficheDeTravailID',
      as: 'ficheDeTravail'
    });
    
    FicheTestResponse.belongsTo(models.Question, {
      foreignKey: 'questionID',
      as: 'question'
    });
  };

  return FicheTestResponse;
}; 