const db = require('../../models');
const { AuditActivity, FicheDeTravail, AuditConstat, AuditRecommendation, Risk, User } = db;

/**
 * Get tree structure for audit activities with their fiches de travail, constats, risks, and recommendations
 */
const getAuditTreeByMissionId = async (req, res) => {
  try {
    const { missionId } = req.params;

    // Fetch activities with all related data
    const activities = await AuditActivity.findAll({
      where: { auditMissionID: missionId },
      include: [
        {
          model: FicheDeTravail,
          as: 'fichesDeTravail',
          attributes: ['id', 'name', 'tailleEchantillon', 'questionnaire', 'tacheDetaillees', 'commentaire'],
          required: false
        },
        {
          model: AuditConstat,
          as: 'constats',
          attributes: ['id', 'name', 'type', 'impact', 'description', 'causes', 'responsable'],
          required: false,
          include: [
            {
              model: User,
              as: 'responsableUser',
              attributes: ['id', 'username', 'email'],
              required: false
            },
            {
              model: AuditRecommendation,
              as: 'recommendations',
              attributes: ['id', 'name', 'priorite', 'description', 'planification', 'code', 'details', 'responsableId'],
              required: false,
              include: [
                {
                  model: User,
                  as: 'responsable',
                  attributes: ['id', 'username', 'email'],
                  required: false
                }
              ]
            }
          ]
        }
      ],
      order: [
        ['createdAt', 'ASC'],
        [{ model: FicheDeTravail, as: 'fichesDeTravail' }, 'createdAt', 'ASC'],
        [{ model: AuditConstat, as: 'constats' }, 'createdAt', 'ASC'],
        [{ model: AuditConstat, as: 'constats' }, { model: AuditRecommendation, as: 'recommendations' }, 'createdAt', 'ASC']
      ]
    });

    // Transform data into tree structure
    const treeData = activities.map(activity => {
      const activityNode = {
        id: activity.id,
        name: activity.name,
        type: 'activity',
        status: activity.status,
        dateDebut: activity.datedebut,
        dateFin: activity.datefin,
        responsable: activity.responsable,
        children: []
      };

      // Add fiches de travail as children
      if (activity.fichesDeTravail && activity.fichesDeTravail.length > 0) {
        activity.fichesDeTravail.forEach(fiche => {
          const ficheNode = {
            id: fiche.id,
            name: fiche.name,
            type: 'fiche-de-travail',
            tailleEchantillon: fiche.tailleEchantillon,
            questionnaire: fiche.questionnaire,
            tacheDetaillees: fiche.tacheDetaillees,
            commentaire: fiche.commentaire,
            parentId: activity.id,
            activityId: activity.id, // Add activity reference
            children: [
              {
                id: `note-synthese-${fiche.id}`,
                name: 'Notes de synthèse',
                type: 'note-synthese',
                parentId: fiche.id,
                activityId: activity.id, // Add activity reference
                children: []
              }
            ]
          };

          // Add constats and recommendations under note de synthèse
          if (activity.constats && activity.constats.length > 0) {
            const noteSyntheseNode = ficheNode.children[0];
            
            activity.constats.forEach(constat => {
              const constatNode = {
                id: constat.id,
                name: constat.name,
                type: 'constat',
                constatType: constat.type,
                impact: constat.impact,
                description: constat.description,
                causes: constat.causes,
                responsable: constat.responsable,
                responsableUser: constat.responsableUser,
                parentId: noteSyntheseNode.id,
                activityId: activity.id, // Add activity reference
                children: []
              };

              // Add recommendations as children of constats
              if (constat.recommendations && constat.recommendations.length > 0) {
                constat.recommendations.forEach(recommendation => {
                  const recommendationNode = {
                    id: recommendation.id,
                    name: recommendation.name,
                    type: 'recommendation',
                    priorite: recommendation.priorite,
                    description: recommendation.description,
                    planification: recommendation.planification,
                    code: recommendation.code,
                    details: recommendation.details,
                    responsableId: recommendation.responsableId,
                    responsable: recommendation.responsable,
                    parentId: constat.id,
                    activityId: activity.id, // Add activity reference
                    children: []
                  };
                  constatNode.children.push(recommendationNode);
                });
              }

              noteSyntheseNode.children.push(constatNode);
            });
          }

          activityNode.children.push(ficheNode);
        });
      }

      return activityNode;
    });

    return res.status(200).json({
      success: true,
      data: treeData
    });

  } catch (error) {
    console.error('Error fetching audit tree data:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch audit tree data',
      error: error.message
    });
  }
};

/**
 * Delete a tree node (activity, fiche, constat, or recommendation)
 */
const deleteTreeNode = async (req, res) => {
  try {
    const { nodeId, nodeType } = req.params;

    let deletedItem = null;
    let message = '';

    switch (nodeType) {
      case 'activity':
        deletedItem = await AuditActivity.findByPk(nodeId);
        if (deletedItem) {
          await deletedItem.destroy();
          message = 'Activity deleted successfully';
        }
        break;

      case 'fiche-de-travail':
        deletedItem = await FicheDeTravail.findByPk(nodeId);
        if (deletedItem) {
          await deletedItem.destroy();
          message = 'Fiche de travail deleted successfully';
        }
        break;

      case 'constat':
        deletedItem = await AuditConstat.findByPk(nodeId);
        if (deletedItem) {
          await deletedItem.destroy();
          message = 'Constat deleted successfully';
        }
        break;

      case 'recommendation':
        deletedItem = await AuditRecommendation.findByPk(nodeId);
        if (deletedItem) {
          await deletedItem.destroy();
          message = 'Recommendation deleted successfully';
        }
        break;

      case 'note-synthese':
        return res.status(400).json({
          success: false,
          message: 'Note de synthèse cannot be deleted as it is a structural element'
        });

      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid node type'
        });
    }

    if (!deletedItem) {
      return res.status(404).json({
        success: false,
        message: `${nodeType} not found`
      });
    }

    return res.status(200).json({
      success: true,
      message
    });

  } catch (error) {
    console.error('Error deleting tree node:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete tree node',
      error: error.message
    });
  }
};

module.exports = {
  getAuditTreeByMissionId,
  deleteTreeNode
};
