// backend/controllers/users/role-controller.js
const { User, Role, UserRole, sequelize } = require('../../models');
const { Op } = require('sequelize');

// Get all roles
const getAllRoles = async (req, res) => {
  try {
    const roles = await Role.findAll({
      order: [['id', 'ASC']]
    });

    res.json({
      success: true,
      data: roles
    });
  } catch (error) {
    console.error('Error fetching roles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch roles',
      error: error.message
    });
  }
};

// Get role by ID
const getRoleById = async (req, res) => {
  try {
    const { id } = req.params;
    const role = await Role.findByPk(id);

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    res.json({
      success: true,
      data: role
    });
  } catch (error) {
    console.error('Error fetching role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch role',
      error: error.message
    });
  }
};

// Get user roles
const getUserRoles = async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findByPk(userId, {
      include: [{
        model: Role,
        as: 'roles',
        through: { attributes: [] }
      }]
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: user.roles || []
    });
  } catch (error) {
    console.error('Error fetching user roles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user roles',
      error: error.message
    });
  }
};

// Assign roles to user
const assignRolesToUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const { roleIds } = req.body;

    if (!Array.isArray(roleIds)) {
      return res.status(400).json({
        success: false,
        message: 'roleIds must be an array'
      });
    }

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const roles = await Role.findAll({
      where: {
        id: {
          [Op.in]: roleIds
        }
      }
    });

    if (roles.length !== roleIds.length) {
      return res.status(400).json({
        success: false,
        message: 'One or more roles not found'
      });
    }

    // Start a transaction
    const transaction = await User.sequelize.transaction();

    try {
      // Remove all existing roles
      await UserRole.destroy({
        where: { userId },
        transaction
      });

      // Add new roles
      await user.addRoles(roles, { transaction });

      // Update legacy role field for backward compatibility
      if (roles.length > 0) {
        // Priority order: GRC Administrator > GRC Manager > Risk Manager > GRC Contributor
        const rolePriority = {
          'grc_admin': 1,
          'grc_manager': 2,
          'risk_manager': 3,
          'grc_contributor': 4
        };

        // Sort roles by priority and get the highest priority role
        const sortedRoles = [...roles].sort((a, b) =>
          (rolePriority[a.code] || 999) - (rolePriority[b.code] || 999)
        );

        // Map new role codes to old role names for backward compatibility
        const roleCodeMap = {
          'grc_admin': 'super_admin',
          'grc_manager': 'admin',
          'risk_manager': 'admin', // Map Risk Manager to admin for backward compatibility
          'grc_contributor': 'user'
        };

        const primaryRole = roleCodeMap[sortedRoles[0].code] || 'user';

        // No need to update role field as it has been removed
      }

      // Commit transaction
      await transaction.commit();

      // Get updated user with roles
      const updatedUser = await User.findByPk(userId, {
        include: [{
          model: Role,
          as: 'roles',
          through: { attributes: [] }
        }]
      });

      res.json({
        success: true,
        message: 'Roles assigned successfully',
        data: updatedUser.roles || []
      });
    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('Error assigning roles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign roles',
      error: error.message
    });
  }
};

// Create a new role
const createRole = async (req, res) => {
  try {
    const { name, description, code } = req.body;

    // Validate required fields
    if (!name || !code) {
      return res.status(400).json({
        success: false,
        message: 'Name and code are required'
      });
    }

    // Check if role with same name or code already exists
    const existingRole = await Role.findOne({
      where: {
        [Op.or]: [
          { name },
          { code }
        ]
      }
    });

    if (existingRole) {
      return res.status(400).json({
        success: false,
        message: 'Role with this name or code already exists'
      });
    }

    // Create the role
    const role = await Role.create({
      name,
      description,
      code
    });

    res.status(201).json({
      success: true,
      message: 'Role created successfully',
      data: role
    });
  } catch (error) {
    console.error('Error creating role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create role',
      error: error.message
    });
  }
};

// Update a role
const updateRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;

    // Find the role
    const role = await Role.findByPk(id);

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    // Don't allow updating the code as it's used for system identification
    await role.update({
      name: name || role.name,
      description: description !== undefined ? description : role.description
    });

    res.json({
      success: true,
      message: 'Role updated successfully',
      data: role
    });
  } catch (error) {
    console.error('Error updating role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update role',
      error: error.message
    });
  }
};

// Delete a role
const deleteRole = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    // Find the role
    const role = await Role.findByPk(id);

    if (!role) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    // Don't allow deleting system roles
    const systemRoles = ['grc_admin', 'grc_manager', 'risk_manager', 'grc_contributor'];
    if (systemRoles.includes(role.code)) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Cannot delete system roles'
      });
    }

    // Delete all user-role associations for this role
    await UserRole.destroy({
      where: { roleId: id },
      transaction
    });

    // Delete the role
    await role.destroy({ transaction });

    await transaction.commit();

    res.json({
      success: true,
      message: 'Role deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete role',
      error: error.message
    });
  }
};

// Remove a role from a user
const removeRoleFromUser = async (req, res) => {
  try {
    const { userId, roleId } = req.params;

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const role = await Role.findByPk(roleId);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    // Check if user has this role
    const userRole = await UserRole.findOne({
      where: {
        userId,
        roleId
      }
    });

    if (!userRole) {
      return res.status(400).json({
        success: false,
        message: 'User does not have this role'
      });
    }

    // Start a transaction
    const transaction = await sequelize.transaction();

    try {
      // Remove the role
      await userRole.destroy({ transaction });

      // Get user's remaining roles
      const remainingRoles = await user.getRoles({ transaction });

      // If user has no roles left, assign the default role (GRC Contributor)
      if (remainingRoles.length === 0) {
        const defaultRole = await Role.findOne({
          where: { code: 'grc_contributor' },
          transaction
        });

        if (defaultRole) {
          await user.addRole(defaultRole, { transaction });
        }
      }

      // Commit transaction
      await transaction.commit();

      // Get updated user with roles
      const updatedUser = await User.findByPk(userId, {
        include: [{
          model: Role,
          as: 'roles',
          through: { attributes: [] }
        }]
      });

      res.json({
        success: true,
        message: 'Role removed successfully',
        data: updatedUser.roles || []
      });
    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('Error removing role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove role',
      error: error.message
    });
  }
};

// Get users by role
const getUsersByRole = async (req, res) => {
  try {
    const { roleId } = req.params;

    const role = await Role.findByPk(roleId);
    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    const users = await role.getUsers({
      attributes: ['id', 'username', 'email'],
      through: { attributes: [] }
    });

    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Error fetching users by role:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users by role',
      error: error.message
    });
  }
};

module.exports = {
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  getUserRoles,
  assignRolesToUser,
  removeRoleFromUser,
  getUsersByRole
};
