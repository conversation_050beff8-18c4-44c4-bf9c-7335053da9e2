import React from "react";
import { useOutletContext } from "react-router-dom";

function ControlPreparation() {
  const { control } = useOutletContext();

  if (!control) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading control data...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6 bg-white rounded-lg shadow-sm p-6">
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">Préparation</h2>
        <p className="text-gray-500">
          Cette section sera développée prochainement.
        </p>
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-700">
            <strong>Contrôle:</strong> {control.name || 'Sans nom'}
          </p>
          <p className="text-sm text-blue-600 mt-1">
            <strong>ID:</strong> {control.controlID}
          </p>
        </div>
      </div>
    </div>
  );
}

export default ControlPreparation;
