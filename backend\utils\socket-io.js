// Socket.IO utility to avoid circular dependencies
let io = null;

// Function to set the Socket.IO instance after it's created in server.js
const setIo = (ioInstance) => {
  io = ioInstance;
  console.log('[Socket.IO Utils] Socket.IO instance registered for shared use across controllers');
};

// Function to get the Socket.IO instance in controllers
const getIo = () => {
  if (!io) {
    console.warn('[Socket.IO Utils] Socket.IO instance requested but not yet available. Make sure it was set correctly.');
  }
  return io;
};

module.exports = {
  setIo,
  getIo
}; 