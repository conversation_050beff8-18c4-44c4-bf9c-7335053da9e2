const { User } = require('../../models');
const bcrypt = require('bcryptjs');

const getAllUsers = async (req, res) => {
    try {
        const users = await User.findAll({
            attributes: ['id', 'username', 'email', 'createdAt'],
            include: [
                {
                    model: require('../../models').Role,
                    as: 'roles',
                    through: { attributes: [] },
                    attributes: ['id', 'name', 'code']
                }
            ]
        });

        res.status(200).json({
            success: true,
            data: users
        });
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch users'
        });
    }
};

const getUserStats = async (req, res) => {
    try {
        const totalUsers = await User.count();

        // Get users with admin roles
        const adminRoleCodes = ['grc_admin', 'grc_manager', 'risk_manager'];
        const adminUsers = await require('../../models').sequelize.query(`
            SELECT COUNT(DISTINCT "userId")
            FROM "UserRoles"
            JOIN "Roles" ON "UserRoles"."roleId" = "Roles"."id"
            WHERE "Roles"."code" IN ('grc_admin', 'grc_manager', 'risk_manager')
        `, { type: require('../../models').Sequelize.QueryTypes.SELECT });

        // Get users with only contributor role
        const regularUsers = await require('../../models').sequelize.query(`
            SELECT COUNT(DISTINCT u."id")
            FROM "Users" u
            WHERE u."id" NOT IN (
                SELECT DISTINCT "userId"
                FROM "UserRoles"
                JOIN "Roles" ON "UserRoles"."roleId" = "Roles"."id"
                WHERE "Roles"."code" IN ('grc_admin', 'grc_manager', 'risk_manager')
            )
        `, { type: require('../../models').Sequelize.QueryTypes.SELECT });

        res.status(200).json({
            success: true,
            data: {
                totalUsers,
                adminUsers: adminUsers[0].count || 0,
                regularUsers: regularUsers[0].count || 0
            }
        });
    } catch (error) {
        console.error('Error fetching user stats:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch user statistics'
        });
    }
};

const createUser = async (req, res) => {
    try {
        const { username, email, password, roles: requestedRoles, roleIds } = req.body;
        const { Role } = require('../../models');
        const transaction = await require('../../models').sequelize.transaction();

        try {
            const salt = await bcrypt.genSalt(10);
            const hashedPassword = await bcrypt.hash(password, salt);

            // Create user
            const user = await User.create({
                username,
                email,
                password: hashedPassword
            }, { transaction });

            // Get the default role (GRC Contributor) if no roles provided
            let rolesToAssign = [];

            // First check for roleIds (new format)
            if (roleIds && Array.isArray(roleIds) && roleIds.length > 0) {
                console.log("Using roleIds:", roleIds);
                rolesToAssign = await Role.findAll({
                    where: {
                        id: roleIds
                    }
                }, { transaction });
            }
            // Then check for requestedRoles (old format)
            else if (requestedRoles && Array.isArray(requestedRoles) && requestedRoles.length > 0) {
                console.log("Using requestedRoles:", requestedRoles);
                // If specific roles were requested, find them by code
                const roleCodes = requestedRoles.map(r => typeof r === 'string' ? r : r.code);
                rolesToAssign = await Role.findAll({
                    where: {
                        code: roleCodes
                    }
                }, { transaction });
            } else {
                console.log("No roles provided, using default role");
                // Default to GRC Contributor
                const defaultRole = await Role.findOne({
                    where: { code: 'grc_contributor' }
                }, { transaction });

                if (defaultRole) {
                    rolesToAssign = [defaultRole];
                }
            }

            // Assign roles to user
            if (rolesToAssign.length > 0) {
                await user.addRoles(rolesToAssign, { transaction });
            }

            // Commit transaction
            await transaction.commit();

            // Fetch the user with roles for the response
            const userWithRoles = await User.findByPk(user.id, {
                include: [{
                    model: Role,
                    as: 'roles',
                    through: { attributes: [] }
                }]
            });

            // Map roles for response
            const userRoles = userWithRoles.roles ? userWithRoles.roles.map(role => ({
                id: role.id,
                name: role.name,
                code: role.code
            })) : [];

            // For backward compatibility - determine primary role
            let primaryRole = 'user'; // Default

            if (userRoles.length > 0) {
                // Priority order: GRC Administrator > GRC Manager > Risk Manager > GRC Contributor
                const rolePriority = {
                    'grc_admin': 1,
                    'grc_manager': 2,
                    'risk_manager': 3,
                    'grc_contributor': 4
                };

                // Sort roles by priority and get the highest priority role
                const sortedRoles = [...userRoles].sort((a, b) =>
                    (rolePriority[a.code] || 999) - (rolePriority[b.code] || 999)
                );

                // Map new role codes to old role names for backward compatibility
                const roleCodeMap = {
                    'grc_admin': 'super_admin',
                    'grc_manager': 'admin',
                    'risk_manager': 'admin', // Map Risk Manager to admin for backward compatibility
                    'grc_contributor': 'user'
                };

                primaryRole = roleCodeMap[sortedRoles[0].code] || 'user';
            }

            res.status(201).json({
                success: true,
                message: 'User created successfully',
                data: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    role: primaryRole, // For backward compatibility
                    roles: userRoles // New field with all roles
                }
            });
        } catch (error) {
            // Rollback transaction on error
            await transaction.rollback();
            throw error;
        }
    } catch (error) {
        console.error('Error creating user:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create user'
        });
    }
};

const updateUser = async (req, res) => {
    try {
        const { id } = req.params;
        const { username, email, password, roles: requestedRoles, roleIds } = req.body;
        const { Role } = require('../../models');

        const user = await User.findByPk(id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Start a transaction
        const transaction = await require('../../models').sequelize.transaction();

        try {
            // Update basic user information
            const updates = {
                username,
                email
            };

            if (password) {
                const salt = await bcrypt.genSalt(10);
                updates.password = await bcrypt.hash(password, salt);
            }

            await user.update(updates, { transaction });

            // Update roles if provided
            // First check for roleIds (new format)
            if (roleIds && Array.isArray(roleIds) && roleIds.length > 0) {
                console.log("Updating user with roleIds:", roleIds);
                const roles = await Role.findAll({
                    where: {
                        id: roleIds
                    }
                }, { transaction });

                // Remove existing roles and add new ones
                await user.setRoles(roles, { transaction });
            }
            // Then check for requestedRoles (old format)
            else if (requestedRoles && Array.isArray(requestedRoles)) {
                console.log("Updating user with requestedRoles:", requestedRoles);
                // Find the roles
                const roleCodes = requestedRoles.map(r => typeof r === 'string' ? r : r.code);
                const roles = await Role.findAll({
                    where: {
                        code: roleCodes
                    }
                }, { transaction });

                // Remove existing roles and add new ones
                await user.setRoles(roles, { transaction });
            }

            // Commit transaction
            await transaction.commit();

            // Fetch the updated user with roles
            const updatedUser = await User.findByPk(id, {
                include: [{
                    model: Role,
                    as: 'roles',
                    through: { attributes: [] }
                }]
            });

            // Map roles for response
            const userRoles = updatedUser.roles ? updatedUser.roles.map(role => ({
                id: role.id,
                name: role.name,
                code: role.code
            })) : [];

            // For backward compatibility - determine primary role
            let primaryRole = 'user'; // Default

            if (userRoles.length > 0) {
                // Priority order: GRC Administrator > GRC Manager > Risk Manager > GRC Contributor
                const rolePriority = {
                    'grc_admin': 1,
                    'grc_manager': 2,
                    'risk_manager': 3,
                    'grc_contributor': 4
                };

                // Sort roles by priority and get the highest priority role
                const sortedRoles = [...userRoles].sort((a, b) =>
                    (rolePriority[a.code] || 999) - (rolePriority[b.code] || 999)
                );

                // Map new role codes to old role names for backward compatibility
                const roleCodeMap = {
                    'grc_admin': 'super_admin',
                    'grc_manager': 'admin',
                    'risk_manager': 'admin', // Map Risk Manager to admin for backward compatibility
                    'grc_contributor': 'user'
                };

                primaryRole = roleCodeMap[sortedRoles[0].code] || 'user';
            }

            res.json({
                success: true,
                message: 'User updated successfully',
                data: {
                    id: updatedUser.id,
                    username: updatedUser.username,
                    email: updatedUser.email,
                    role: primaryRole, // For backward compatibility
                    roles: userRoles // New field with all roles
                }
            });
        } catch (error) {
            // Rollback transaction on error
            await transaction.rollback();
            throw error;
        }
    } catch (error) {
        console.error('Error updating user:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update user'
        });
    }
};

module.exports = {
    getAllUsers,
    getUserStats,
    createUser,
    updateUser
};
