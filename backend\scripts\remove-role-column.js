// backend/scripts/remove-role-column.js
require('dotenv').config({ path: 'backend/.env' });
const { sequelize } = require('../models');

async function removeRoleColumn() {
  console.log('Starting role column removal...');
  
  try {
    // Check if the role column exists
    const tableDescription = await sequelize.getQueryInterface().describeTable('Users');
    
    if (tableDescription.role) {
      console.log('Role column exists, removing it...');
      
      // Remove the role column
      await sequelize.getQueryInterface().removeColumn('Users', 'role');
      
      console.log('Role column removed successfully!');
    } else {
      console.log('Role column does not exist, skipping...');
    }
  } catch (error) {
    console.error('Error removing role column:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the migration
removeRoleColumn()
  .then(() => {
    console.log('Role column removal completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Role column removal failed:', error);
    process.exit(1);
  });
