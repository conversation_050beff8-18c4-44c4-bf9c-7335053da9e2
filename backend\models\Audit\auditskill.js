'use strict';

module.exports = (sequelize, DataTypes) => {
  const AuditSkill = sequelize.define('AuditSkill', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
      defaultValue: () => `SKILL_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        len: [2, 255]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    updatedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  }, {
    tableName: 'AuditSkills',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['id']
      },
      {
        unique: true,
        fields: ['name']
      },
      {
        fields: ['isActive']
      },
      {
        fields: ['createdBy']
      },
      {
        fields: ['createdAt']
      }
    ]
  });

  AuditSkill.associate = function(models) {
    // AuditSkill belongs to User (creator)
    AuditSkill.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });

    // AuditSkill belongs to User (updater)
    AuditSkill.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater'
    });

    // AuditSkill has many AuditorSkills (many-to-many with Users)
    AuditSkill.hasMany(models.AuditorSkill, {
      foreignKey: 'skillId',
      as: 'auditorSkills'
    });

    // Many-to-many relationship with Users through AuditorSkill
    AuditSkill.belongsToMany(models.User, {
      through: models.AuditorSkill,
      foreignKey: 'skillId',
      otherKey: 'auditorId',
      as: 'auditors'
    });
  };

  return AuditSkill;
};
