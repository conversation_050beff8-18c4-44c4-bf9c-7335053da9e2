require('dotenv').config();
const { Server } = require('socket.io');
const http = require('http');
const express = require('express');
const jwt = require('jsonwebtoken');
const db = require('../models');
const notificationController = require('../controllers/notifications/notification-controller');

async function testSocketNotification() {
  try {
    console.log('[Test Script] Starting socket notification test...');
    
    // First, authenticate to database
    await db.sequelize.authenticate();
    console.log('[Test Script] Database connection established successfully.');
    
    // Check for a user to test with
    const user = await db.User.findOne();
    if (!user) {
      console.error('[Test Script] No users found in database!');
      process.exit(1);
    }
    
    console.log(`[Test Script] Found user to test with: ID: ${user.id}, Username: ${user.username || user.email}`);
    const userId = user.id;
    
    // Create a test notification in the database
    const notificationData = {
      type: 'test',
      id: 999,
      userId: userId,
      entityName: 'Test Entity',
      message: `This is a test notification sent at ${new Date().toISOString()}`,
      assignedBy: 'Test Socket Script'
    };
    
    console.log('[Test Script] Creating test notification in database:', notificationData);
    const dbNotification = await notificationController.createNotification(notificationData);
    
    if (!dbNotification) {
      console.error('[Test Script] Failed to create notification in database');
      process.exit(1);
    }
    
    console.log('[Test Script] Successfully created notification in database:', dbNotification.id);
    
    // Now set up mini server to test socket
    const app = express();
    const server = http.createServer(app);
    const io = new Server(server);
    
    console.log('[Test Script] Setting up test socket server...');
    
    // Simplified version of Socket.IO setup
    io.on('connection', (socket) => {
      console.log(`[Test Script] Socket connected: ${socket.id}`);
      
      // Immediately disconnect socket after sending notification
      socket.on('disconnect', () => {
        console.log(`[Test Script] Socket disconnected: ${socket.id}`);
      });
    });
    
    // Start the server on a different port
    const TEST_PORT = 5005;
    server.listen(TEST_PORT, () => {
      console.log(`[Test Script] Test server running on port ${TEST_PORT}`);
      
      // Send notification directly using the IO instance
      const targetRoom = `user-${userId}`;
      console.log(`[Test Script] Sending notification to room: ${targetRoom}`);
      
      io.to(targetRoom).emit('notification', notificationData);
      console.log('[Test Script] Notification sent to room:', notificationData);
      
      // Create a direct socket emission as well to verify
      console.log('[Test Script] Broadcasting notification to all connected clients');
      io.emit('notification', {
        ...notificationData,
        message: `${notificationData.message} (broadcast)`
      });
      
      // Close server and exit after 5 seconds
      setTimeout(() => {
        console.log('[Test Script] Test complete! Shutting down...');
        server.close();
        process.exit(0);
      }, 5000);
    });
    
  } catch (error) {
    console.error('[Test Script] Error occurred:', error);
    process.exit(1);
  }
}

testSocketNotification(); 