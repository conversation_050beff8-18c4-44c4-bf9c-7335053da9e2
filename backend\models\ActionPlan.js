module.exports = (sequelize, DataTypes) => {
  const ActionPlan = sequelize.define('ActionPlan', {
    actionPlanID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    category: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    nature: {
      type: DataTypes.STRING(50),
      allowNull: true,
      // Preventive, Corrective
    },
    origin: {
      type: DataTypes.STRING(50),
      allowNull: true,
      // Audit, Compliance, Event, Risk, RFC, Other
    },
    priority: {
      type: DataTypes.STRING(20),
      allowNull: true,
      // Low, Medium, High, Critical
    },
    organizationalLevel: {
      type: DataTypes.STRING(50),
      allowNull: true,
      // Local, Global
    },
    means: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    plannedBeginDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    plannedEndDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    approverId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    assigneeId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    incidentID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Incident',
        key: 'incidentID'
      }
    },
  }, {
    tableName: 'action_plan',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['actionPlanID'],
      },
    ],
  });

  // Define associations in a separate method to be called after all models are initialized
  ActionPlan.associate = (models) => {
    // User associations
    ActionPlan.belongsTo(models.User, {
      foreignKey: 'approverId',
      as: 'approver'
    });

    ActionPlan.belongsTo(models.User, {
      foreignKey: 'assigneeId',
      as: 'assignee'
    });

    // Incident association (ActionPlan belongs to one Incident)
    ActionPlan.belongsTo(models.Incident, {
      foreignKey: 'incidentID',
      as: 'incident'
    });

    // ActionPlan has many Incidents
    ActionPlan.hasMany(models.Incident, {
      foreignKey: 'actionPlanID',
      as: 'incidents'
    });

    // ActionPlan has many Actions
    ActionPlan.belongsToMany(models.Action, {
      through: 'ActionActionPlan',
      foreignKey: 'actionPlanID',
      otherKey: 'actionID',
      as: 'actions'
    });

    // Many-to-many relationships with timestamps
    ActionPlan.belongsToMany(models.Control, {
      through: {
        model: 'ActionPlanControl',
        timestamps: true
      },
      foreignKey: 'actionPlanID',
      otherKey: 'controlID'
    });

    ActionPlan.belongsToMany(models.Product, {
      through: {
        model: 'ActionPlanProduct',
        timestamps: true
      },
      foreignKey: 'actionPlanID',
      otherKey: 'productID'
    });

    ActionPlan.belongsToMany(models.BusinessLine, {
      through: {
        model: 'ActionPlanBusinessLine',
        timestamps: true
      },
      foreignKey: 'actionPlanID',
      otherKey: 'businessLineID'
    });

    ActionPlan.belongsToMany(models.Risk, {
      through: {
        model: 'ActionPlanRisk',
        timestamps: true
      },
      foreignKey: 'actionPlanID',
      otherKey: 'riskID'
    });

    ActionPlan.belongsToMany(models.OrganizationalProcess, {
      through: {
        model: 'ActionPlanOrgProcess',
        timestamps: true
      },
      foreignKey: 'actionPlanID',
      otherKey: 'organizationalProcessID'
    });

    ActionPlan.belongsToMany(models.BusinessProcess, {
      through: {
        model: 'ActionPlanBusinessProcess',
        timestamps: true
      },
      foreignKey: 'actionPlanID',
      otherKey: 'businessProcessID'
    });

    ActionPlan.belongsToMany(models.Operation, {
      through: {
        model: 'ActionPlanOperation',
        timestamps: true
      },
      foreignKey: 'actionPlanID',
      otherKey: 'operationID'
    });

    ActionPlan.belongsToMany(models.Entity, {
      through: {
        model: 'ActionPlanEntity',
        timestamps: true
      },
      foreignKey: 'actionPlanID',
      otherKey: 'entityID'
    });

    // Add association with ActionPlanAttachment
    ActionPlan.hasMany(models.ActionPlanAttachment, {
      foreignKey: 'actionPlanID',
      sourceKey: 'actionPlanID',
      as: 'attachments'
    });
  };

  return ActionPlan;
};