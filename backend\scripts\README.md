# Backend Scripts

This directory contains utility scripts for the backend.

## Toggle Debug Logs

The `toggle-debug-logs.js` script allows you to easily turn debug logs on or off without manually editing the .env file.

### Usage

To turn debug logs on:

```bash
node scripts/toggle-debug-logs.js on
```

This will:
- Set `NODE_ENV=development`
- Set `DEBUG_LOGS=true`
- Enable detailed logging throughout the application

To turn debug logs off:

```bash
node scripts/toggle-debug-logs.js off
```

This will:
- Set `NODE_ENV=production`
- Set `DEBUG_LOGS=false`
- Disable detailed logging, showing only errors and important messages

### Note

You need to restart the server after changing the debug log settings for the changes to take effect.
