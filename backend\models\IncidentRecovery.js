module.exports = (sequelize, DataTypes) => {
  const IncidentRecovery = sequelize.define('IncidentRecovery', {
    recoveryID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    amount: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
    localAmount: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    currency: {
      type: DataTypes.STRING(10),
      allowNull: true,
      defaultValue: 'XOF',
    },
    incidentID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Incident',
        key: 'incidentID',
      },
    },
  }, {
    tableName: 'incident_recovery',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['recoveryID'],
      },
      {
        fields: ['incidentID'],
      },
    ],
  });

  IncidentRecovery.associate = (models) => {
    // IncidentRecovery belongs to Incident
    IncidentRecovery.belongsTo(models.Incident, {
      foreignKey: 'incidentID',
      as: 'incident',
      onDelete: 'CASCADE',
    });
  };

  return IncidentRecovery;
};
