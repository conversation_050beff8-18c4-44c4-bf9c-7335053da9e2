module.exports = (sequelize, DataTypes) => {
  const Application = sequelize.define('Application', {
    applicationID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  }, {
    tableName: 'Application',
    timestamps: false,
  });

  return Application;
};