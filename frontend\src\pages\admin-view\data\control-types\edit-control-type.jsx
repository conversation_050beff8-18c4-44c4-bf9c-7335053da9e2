import { useEffect, useState, useCallback } from "react";
import { useParams, useNavigate, Outlet, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { getControlTypeById, getAllControlTypes, reset } from "@/store/slices/controlTypeSlice";
import { Shield, FileText, Edit, Loader2 } from "lucide-react";
import DetailHeader from "@/components/ui/detail-header";
import TabBar from "@/components/ui/tabs/tab-bar";
import TabContent from "@/components/ui/tabs/tab-content";
import { useTranslation } from "react-i18next";

function EditControlType() {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const { currentControlType, controlTypes, isLoading, isError, message } = useSelector(
    (state) => state.controlType
  );

  // Find parent control type name
  const getParentControlTypeName = useCallback(() => {
    // Check if parent ID is null, undefined, or empty string
    if (!currentControlType?.parentControlTypeID || currentControlType.parentControlTypeID === "") {
      return t('admin.control_types.form.none', 'None');
    }

    // Check if control types are loaded
    if (!controlTypes || controlTypes.length === 0) {
      return t('common.loading', 'Loading...');
    }

    // Find the parent control type
    const parent = controlTypes.find(type => type.controlTypeID === currentControlType.parentControlTypeID);

    return parent ? parent.name : t('admin.control_types.overview.unknown', 'Unknown');
  }, [currentControlType, controlTypes, t]);

  // Get the current tab from the URL
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    if (path.includes('/features')) {
      return 'features';
    }
    return 'overview';
  }, [location.pathname]);

  const [activeTab, setActiveTab] = useState(getCurrentTab());

  // Update active tab when location changes
  useEffect(() => {
    setActiveTab(getCurrentTab());
  }, [location, getCurrentTab]);

  // Fetch control type on component mount
  useEffect(() => {
    if (id) {
      dispatch(getControlTypeById(id));
    }

    // Reset state when component unmounts
    return () => {
      dispatch(reset());
    };
  }, [dispatch, id]);

  // Fetch all control types if needed
  useEffect(() => {
    const shouldFetch = !controlTypes || controlTypes.length === 0;
    if (shouldFetch) {
      dispatch(getAllControlTypes());
    }
  }, [dispatch]);

  // Handle go back
  const handleGoBack = () => {
    navigate("/admin/data/control-types");
  };

  const [tabs, setTabs] = useState([
    { id: "overview", label: t('common.tabs.overview', 'Overview'), icon: <FileText className="h-4 w-4" /> },
    { id: "features", label: t('common.tabs.features', 'Features'), icon: <Edit className="h-4 w-4" /> },
  ]);

  // Update tabs when language changes
  useEffect(() => {
    setTabs([
      { id: "overview", label: t('common.tabs.overview', 'Overview'), icon: <FileText className="h-4 w-4" /> },
      { id: "features", label: t('common.tabs.features', 'Features'), icon: <Edit className="h-4 w-4" /> },
    ]);
  }, [t]);

  // Navigate to tab
  const navigateToTab = (tabId) => {
    switch (tabId) {
      case "overview":
        navigate(`/admin/data/control-types/${id}`);
        break;
      case "features":
        navigate(`/admin/data/control-types/${id}/features`);
        break;
      default:
        navigate(`/admin/data/control-types/${id}`);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {t('admin.control_types.error.loading', 'Error: {{message}}', { message: message })}
        </div>
      </div>
    );
  }

  if (!currentControlType) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          {t('admin.control_types.error.not_found', 'Control type not found')}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      {/* Header */}
      <DetailHeader
        title={currentControlType.name}
        icon={<Shield className="h-6 w-6 text-[#F62D51]" />}
        metadata={[
          currentControlType.code ? `${t('admin.control_types.form.code', 'Code')}: ${currentControlType.code}` : t('admin.control_types.overview.no_code', 'No code'),
          `${t('admin.control_types.form.parent', 'Parent')}: ${getParentControlTypeName()}`,
          currentControlType.comment ?
            `${currentControlType.comment.substring(0, 100)}${currentControlType.comment.length > 100 ? '...' : ''}` :
            t('admin.control_types.overview.no_description', 'No description')
        ]}
        onBack={handleGoBack}
        backLabel={t('admin.control_types.back_to_list', 'Back to Control Types')}
      />

      {/* Tabs */}
      <TabBar
        activeTab={activeTab}
        tabs={tabs}
        onTabChange={navigateToTab}
        className="mb-6"
      />

      {/* Tab Content */}
      <TabContent>
        <Outlet context={{ controlType: currentControlType }} />
      </TabContent>
    </div>
  );
}

export default EditControlType;
