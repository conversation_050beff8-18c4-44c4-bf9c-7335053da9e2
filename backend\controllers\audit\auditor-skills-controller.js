const { AuditSkill, AuditorSkill, User, UserRole, Role } = require('../../models');
const { Op, sequelize } = require('sequelize');

// Get all auditors with their skills
const getAllAuditorsWithSkills = async (req, res) => {
  try {
    console.log('[AUDITOR_SKILLS] Getting all auditors with skills');

    // Get audit roles first
    const auditRoles = await Role.findAll({
      where: {
        code: {
          [Op.in]: ['auditor', 'audit_director']
        }
      }
    });

    const auditRoleIds = auditRoles.map(role => role.id);

    // Get users with audit roles
    const userRoles = await UserRole.findAll({
      where: {
        roleId: {
          [Op.in]: auditRoleIds
        }
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'email']
        },
        {
          model: Role,
          as: 'role',
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    // Get unique user IDs
    const userIds = [...new Set(userRoles.map(ur => ur.userId))];

    // Get users with their skills
    const users = await User.findAll({
      where: {
        id: {
          [Op.in]: userIds
        }
      },
      include: [
        {
          model: AuditorSkill,
          as: 'auditorSkills',
          where: { isActive: true },
          required: false,
          include: [
            {
              model: AuditSkill,
              as: 'skill',
              where: { isActive: true }
            },
            {
              model: User,
              as: 'rater',
              attributes: ['id', 'username', 'email']
            }
          ]
        }
      ],
      order: [['username', 'ASC']]
    });

    // Add role information to each user
    const auditorsWithRoles = users.map(user => {
      const userRoleData = userRoles.filter(ur => ur.userId === user.id);
      return {
        ...user.toJSON(),
        userRoles: userRoleData.map(ur => ({
          role: ur.role
        }))
      };
    });

    console.log(`[AUDITOR_SKILLS] Found ${auditorsWithRoles.length} auditors`);

    res.status(200).json({
      success: true,
      data: auditorsWithRoles
    });
  } catch (error) {
    console.error('[AUDITOR_SKILLS] Error getting auditors with skills:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des auditeurs et leurs compétences'
    });
  }
};

// Get auditor skills by auditor ID
const getAuditorSkills = async (req, res) => {
  try {
    const { auditorId } = req.params;
    console.log(`[AUDITOR_SKILLS] Getting skills for auditor: ${auditorId}`);
    
    const auditorSkills = await AuditorSkill.findAll({
      where: { 
        auditorId,
        isActive: true 
      },
      include: [
        {
          model: AuditSkill,
          as: 'skill',
          where: { isActive: true }
        },
        {
          model: User,
          as: 'auditor',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'rater',
          attributes: ['id', 'username', 'email']
        }
      ],
      order: [['ratedAt', 'DESC']]
    });

    console.log(`[AUDITOR_SKILLS] Found ${auditorSkills.length} skills for auditor`);
    
    res.status(200).json({
      success: true,
      data: auditorSkills
    });
  } catch (error) {
    console.error('[AUDITOR_SKILLS] Error getting auditor skills:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des compétences de l\'auditeur'
    });
  }
};

// Assign skill to auditor
const assignSkillToAuditor = async (req, res) => {
  try {
    const { auditorId, skillId, rating, comments } = req.body;
    console.log('[AUDITOR_SKILLS] Assigning skill to auditor:', { auditorId, skillId, rating });
    
    // Validate rating
    if (rating < 0 || rating > 5) {
      return res.status(400).json({
        success: false,
        message: 'La note doit être comprise entre 0 et 5'
      });
    }

    // Check if auditor exists and has auditor role
    const auditor = await User.findByPk(auditorId);
    if (!auditor) {
      return res.status(404).json({
        success: false,
        message: 'Auditeur non trouvé'
      });
    }

    // Check if user has audit role
    const auditRoles = await Role.findAll({
      where: {
        code: {
          [Op.in]: ['auditor', 'audit_director']
        }
      }
    });

    const auditRoleIds = auditRoles.map(role => role.id);

    const userRole = await UserRole.findOne({
      where: {
        userId: auditorId,
        roleId: {
          [Op.in]: auditRoleIds
        }
      }
    });

    if (!userRole) {
      return res.status(400).json({
        success: false,
        message: 'L\'utilisateur n\'a pas de rôle d\'audit'
      });
    }

    // Check if skill exists
    const skill = await AuditSkill.findByPk(skillId, {
      where: { isActive: true }
    });

    if (!skill) {
      return res.status(404).json({
        success: false,
        message: 'Compétence non trouvée'
      });
    }

    // Check if assignment already exists
    const existingAssignment = await AuditorSkill.findOne({
      where: { 
        auditorId,
        skillId,
        isActive: true 
      }
    });

    if (existingAssignment) {
      // Update existing assignment
      await existingAssignment.update({
        rating: parseFloat(rating),
        comments: comments?.trim() || null,
        ratedBy: req.user.userId,
        ratedAt: new Date()
      });

      console.log(`[AUDITOR_SKILLS] Updated existing assignment: ${existingAssignment.id}`);
      
      return res.status(200).json({
        success: true,
        data: existingAssignment,
        message: 'Assignation mise à jour avec succès'
      });
    }

    // Create new assignment
    const assignment = await AuditorSkill.create({
      auditorId,
      skillId,
      rating: parseFloat(rating),
      comments: comments?.trim() || null,
      ratedBy: req.user.userId,
      ratedAt: new Date()
    });

    console.log(`[AUDITOR_SKILLS] Created assignment with ID: ${assignment.id}`);
    
    res.status(201).json({
      success: true,
      data: assignment,
      message: 'Compétence assignée avec succès'
    });
  } catch (error) {
    console.error('[AUDITOR_SKILLS] Error assigning skill:', error);
    
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({
        success: false,
        message: 'Cette compétence est déjà assignée à cet auditeur'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'assignation de la compétence'
    });
  }
};

// Update auditor skill rating
const updateAuditorSkillRating = async (req, res) => {
  try {
    const { id } = req.params;
    const { rating, comments } = req.body;
    console.log(`[AUDITOR_SKILLS] Updating skill rating with ID: ${id}`);
    
    // Validate rating
    if (rating < 0 || rating > 5) {
      return res.status(400).json({
        success: false,
        message: 'La note doit être comprise entre 0 et 5'
      });
    }

    const auditorSkill = await AuditorSkill.findByPk(id);
    
    if (!auditorSkill) {
      return res.status(404).json({
        success: false,
        message: 'Assignation non trouvée'
      });
    }

    await auditorSkill.update({
      rating: parseFloat(rating),
      comments: comments?.trim() || auditorSkill.comments,
      ratedBy: req.user.userId,
      ratedAt: new Date()
    });

    console.log(`[AUDITOR_SKILLS] Updated skill rating: ${auditorSkill.id}`);
    
    res.status(200).json({
      success: true,
      data: auditorSkill,
      message: 'Note mise à jour avec succès'
    });
  } catch (error) {
    console.error('[AUDITOR_SKILLS] Error updating skill rating:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de la note'
    });
  }
};

// Remove skill from auditor (soft delete)
const removeSkillFromAuditor = async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`[AUDITOR_SKILLS] Removing skill assignment with ID: ${id}`);
    
    const auditorSkill = await AuditorSkill.findByPk(id);
    
    if (!auditorSkill) {
      return res.status(404).json({
        success: false,
        message: 'Assignation non trouvée'
      });
    }

    // Soft delete
    await auditorSkill.update({
      isActive: false
    });

    console.log(`[AUDITOR_SKILLS] Removed skill assignment: ${auditorSkill.id}`);
    
    res.status(200).json({
      success: true,
      message: 'Compétence retirée avec succès'
    });
  } catch (error) {
    console.error('[AUDITOR_SKILLS] Error removing skill:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors du retrait de la compétence'
    });
  }
};

// Get auditor skills statistics
const getAuditorSkillsStatistics = async (req, res) => {
  try {
    console.log('[AUDITOR_SKILLS] Getting auditor skills statistics');
    
    const totalAssignments = await AuditorSkill.count({
      where: { isActive: true }
    });

    // Get audit roles
    const auditRoles = await Role.findAll({
      where: {
        code: {
          [Op.in]: ['auditor', 'audit_director']
        }
      }
    });

    const auditRoleIds = auditRoles.map(role => role.id);

    const totalAuditors = await UserRole.count({
      where: {
        roleId: {
          [Op.in]: auditRoleIds
        }
      }
    });

    const averageRating = await AuditorSkill.findOne({
      where: { isActive: true },
      attributes: [
        [sequelize.fn('AVG', sequelize.col('rating')), 'avgRating']
      ],
      raw: true
    });

    const ratingDistribution = await AuditorSkill.findAll({
      where: { isActive: true },
      attributes: [
        [sequelize.fn('FLOOR', sequelize.col('rating')), 'ratingFloor'],
        [sequelize.fn('COUNT', '*'), 'count']
      ],
      group: [sequelize.fn('FLOOR', sequelize.col('rating'))],
      order: [[sequelize.fn('FLOOR', sequelize.col('rating')), 'ASC']],
      raw: true
    });

    const statistics = {
      totalAssignments,
      totalAuditors,
      averageRating: averageRating?.avgRating ? parseFloat(averageRating.avgRating).toFixed(2) : 0,
      ratingDistribution: ratingDistribution.map(item => ({
        rating: parseInt(item.ratingFloor),
        count: parseInt(item.count)
      }))
    };

    console.log('[AUDITOR_SKILLS] Statistics calculated');
    
    res.status(200).json({
      success: true,
      data: statistics
    });
  } catch (error) {
    console.error('[AUDITOR_SKILLS] Error getting statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques'
    });
  }
};

module.exports = {
  getAllAuditorsWithSkills,
  getAuditorSkills,
  assignSkillToAuditor,
  updateAuditorSkillRating,
  removeSkillFromAuditor,
  getAuditorSkillsStatistics
};
