const db = require('../models');

async function createTables() {
  try {
    console.log('🚀 Creating Campagne tables...');
    
    // Test database connection
    await db.sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Check if models are loaded
    console.log('📋 Available models:', Object.keys(db).filter(key => !['sequelize', 'Sequelize'].includes(key)));
    
    if (db.Campagne) {
      console.log('📋 Creating Campagne table...');
      await db.Campagne.sync({ force: false });
      console.log('✅ Campagne table created/verified successfully');
    } else {
      console.log('❌ Campagne model not found');
    }
    
    if (db.UserCampagne) {
      console.log('📋 Creating UserCampagne table...');
      await db.UserCampagne.sync({ force: false });
      console.log('✅ UserCampagne table created/verified successfully');
    } else {
      console.log('❌ UserCampagne model not found');
    }
    
    // Test creating a sample campagne (optional)
    console.log('🧪 Testing campagne creation...');
    const testCampagne = await db.Campagne.findOrCreate({
      where: { name: 'Test Campagne' },
      defaults: {
        name: 'Test Campagne',
        code: 'TEST-001',
        description: 'Campagne de test pour vérifier le fonctionnement',
        statut: 'planifié'
      }
    });
    
    if (testCampagne[1]) {
      console.log('✅ Test campagne created successfully');
    } else {
      console.log('ℹ️  Test campagne already exists');
    }
    
    console.log('✅ All operations completed successfully!');
    
  } catch (error) {
    console.error('❌ Error creating tables:', error);
  } finally {
    // Close the database connection
    await db.sequelize.close();
    console.log('Database connection closed');
  }
}

// Run the script
createTables();
