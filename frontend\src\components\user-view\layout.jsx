import { Outlet } from "react-router-dom";
import UserSideBar from "./sidebar";
import UserHeader from "./header";
import { useState } from "react";

function UserLayout({ notifications, unreadCount, markAsRead, markAllAsRead, setNotifications }) {
  const [openSidebar, setOpenSidebar] = useState(false);

  return (
    <div className="min-h-screen w-full bg-gray-100">
      {/* Fixed Header */}
      <div className="fixed top-0 right-0 left-0 z-20">
        <UserHeader
          setOpen={setOpenSidebar}
          notifications={notifications}
          unreadCount={unreadCount}
          markAsRead={markAsRead}
          markAllAsRead={markAllAsRead}
          setNotifications={setNotifications}
        />
      </div>

      {/* Fixed Sidebar */}
      <div className="fixed top-0 left-0 h-full z-30">
        <UserSideBar open={openSidebar} setOpen={setOpenSidebar} />
      </div>

      {/* Main Content Area with proper padding */}
      <div className="lg:ml-64 pt-[61px]"> {/* 61px is header height */}
        <main>
          <Outlet />
        </main>
      </div>
    </div>
  );
}

export default UserLayout;