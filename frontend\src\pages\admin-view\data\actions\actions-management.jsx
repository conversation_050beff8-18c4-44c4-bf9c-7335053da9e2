import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  Plus,
  Trash2,
  Loader2,
  Edit,
  User,
  Search,
  Filter,
  X
} from "lucide-react";
import { getAllUsers } from "@/store/slices/userSlice";
import {
  getAllActions,
  deleteAction,
  deleteMultipleActions,
  reset
} from "@/store/slices/actionSlice";
import { hasPermission } from '@/store/auth-slice';
import PageHeader from "@/components/ui/page-header";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import FilterPanel from "@/components/ui/filter-panel";

function ActionsManagement() {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Get users and actions from Redux store
  const { users } = useSelector((state) => state.user);
  const { actions, isLoading } = useSelector((state) => state.action);
  const canCreate = useSelector(state => hasPermission(state, 'create'));
  const canDelete = useSelector(state => hasPermission(state, 'delete'));
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedActions, setSelectedActions] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Filter states
  const [filters, setFilters] = useState({
    status: "all",
    priority: "all",
    assigneeId: "all"
  });

  // Fetch users and actions on component mount
  useEffect(() => {
    dispatch(getAllUsers());
    dispatch(getAllActions());

    return () => {
      dispatch(reset());
    };
  }, [dispatch]);

  // Handle checkbox change for selecting actions
  const handleSelectAction = (actionID) => {
    setSelectedActions(prev => {
      if (prev.includes(actionID)) {
        return prev.filter(id => id !== actionID);
      } else {
        return [...prev, actionID];
      }
    });
  };

  // Handle select all checkbox
  const handleSelectAll = () => {
    if (selectedActions.length === filteredActions.length) {
      setSelectedActions([]);
    } else {
      setSelectedActions(filteredActions.map(action => action.actionID));
    }
  };

  // Handle delete selected actions
  const handleDeleteSelected = () => {
    if (selectedActions.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedActions.length} selected action(s)?`)) {
      dispatch(deleteMultipleActions(selectedActions))
        .then(() => {
          setSelectedActions([]);
        });
    }
  };

  // Handle add new action
  const handleAddAction = () => {
    navigate("/admin/data/actions/add");
  };

  // Handle edit action
  const handleEditAction = (action) => {
    navigate(`/admin/data/actions/edit/${action.actionID}`);
  };

  // Handle delete action
  const handleDeleteAction = (actionID) => {
    if (window.confirm("Are you sure you want to delete this action?")) {
      dispatch(deleteAction(actionID));
    }
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      status: "all",
      priority: "all",
      assigneeId: "all"
    });
    setSearchQuery("");
  };

  // Apply filters
  const filteredActions = actions.filter(action => {
    return (
      (searchQuery === "" ||
        action.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        action.description.toLowerCase().includes(searchQuery.toLowerCase())
      ) &&
      (filters.status === "all" || action.status === filters.status) &&
      (filters.priority === "all" || action.priority === filters.priority) &&
      (filters.assigneeId === "all" ||
        (action.assignee && action.assignee.id.toString() === filters.assigneeId))
    );
  });

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentActions = filteredActions.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredActions.length / itemsPerPage);

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get priority badge color
  const getPriorityBadgeColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-100 text-red-800";
      case "High":
        return "bg-orange-100 text-orange-800";
      case "Medium":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-green-100 text-green-800";
    }
  };

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      <PageHeader
        title="Actions Management"
        description="Create and manage actions across your organization."
        section="Data"
        currentPage="Actions"
        searchPlaceholder="Search actions..."
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
        icon={Edit}
      />

      {/* Filter Panel */}
      <div className="mb-6 filter-panel-container">
        <FilterPanel
          filters={[
            {
              id: "assigneeId",
              label: "Assignee",
              component: (
                <Select
                  value={filters.assigneeId}
                  onValueChange={(value) => setFilters({ ...filters, assigneeId: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select assignee" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    {users.map(user => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.username} ({user.role})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "status",
              label: "Status",
              component: (
                <Select
                  value={filters.status}
                  onValueChange={(value) => setFilters({ ...filters, status: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="Not Started">Not Started</SelectItem>
                    <SelectItem value="In Progress">In Progress</SelectItem>
                    <SelectItem value="Completed">Completed</SelectItem>
                    <SelectItem value="Cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
            {
              id: "priority",
              label: "Priority",
              component: (
                <Select
                  value={filters.priority}
                  onValueChange={(value) => setFilters({ ...filters, priority: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="Low">Low</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="High">High</SelectItem>
                    <SelectItem value="Critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              ),
            },
          ]}
          activeFilters={filters}
          onClearFilters={clearFilters}
          activeFilterCount={
            Object.values(filters).filter(value => value !== "all").length +
            (searchQuery ? 1 : 0)
          }
        />
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center mb-4">
        <Button
          onClick={clearFilters}
          variant="outline"
          className="flex items-center gap-2"
          disabled={
            filters.status === "all" &&
            filters.priority === "all" &&
            filters.assigneeId === "all" &&
            searchQuery === ""
          }
        >
          <X className="h-4 w-4" />
          Clear Filters
        </Button>
        <div className="flex items-center gap-2">
          {selectedActions.length > 0 && (
            <Button
              variant="outline"
              className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
                canDelete && !isLoading
                  ? 'bg-[#F62D51]/90 text-white hover:bg-[#F62D51] hover:shadow-md'
                  : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
              } flex items-center gap-2 px-6 py-2 font-semibold`}
              onClick={handleDeleteSelected}
              disabled={!canDelete || isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  Delete ({selectedActions.length})
                </>
              )}
            </Button>
          )}
          {canCreate && (
            <Button
              onClick={handleAddAction}
              className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Action
            </Button>
          )}
        </div>
      </div>

      {/* Actions Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="p-4 border-b flex justify-between items-center">
          <div className="flex items-center">
            <h3 className="text-lg font-semibold">Actions</h3>
            <Badge className="ml-2 bg-gray-100 text-gray-800 hover:bg-gray-200">
              {filteredActions.length}
            </Badge>
          </div>
          <div className="flex gap-2">
            {selectedActions.length > 0 && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDeleteSelected}
                className="flex items-center gap-1"
              >
                <Trash2 className="h-4 w-4" />
                Delete Selected ({selectedActions.length})
              </Button>
            )}
            <Button
              onClick={handleAddAction}
              className="flex items-center gap-1"
            >
              <Plus className="h-4 w-4" />
              Add Action
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        ) : filteredActions.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No actions found.</p>
            <Button
              onClick={handleAddAction}
              variant="outline"
              className="mt-4"
            >
              Add your first action
            </Button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="w-10 px-6 py-3 text-left">
                    <Checkbox
                      checked={selectedActions.length === filteredActions.length && filteredActions.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Assignee
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Start Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    End Date
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {currentActions.map((action) => (
                  <tr key={action.actionID} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <Checkbox
                        checked={selectedActions.includes(action.actionID)}
                        onCheckedChange={() => handleSelectAction(action.actionID)}
                      />
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      <div className="font-medium cursor-pointer" onClick={() => handleEditAction(action)}>
                        {action.name}
                      </div>
                      <div className="text-xs text-gray-500 truncate max-w-xs">
                        {action.description}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityBadgeColor(action.priority)}`}>
                        {action.priority}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(action.status)}`}>
                        {action.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {action.assignee ? (
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-1 text-gray-500" />
                          {action.assignee.username}
                        </div>
                      ) : "-"}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {action.startDate ? new Date(action.startDate).toLocaleDateString() : "-"}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {action.endDate ? new Date(action.endDate).toLocaleDateString() : "-"}
                    </td>
                    <td className="px-6 py-4 text-sm text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditAction(action)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteAction(action.actionID)}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {filteredActions.length > 0 && (
          <div className="px-6 py-4 flex items-center justify-between border-t">
            <div className="flex items-center">
              <span className="text-sm text-gray-700">
                Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{" "}
                <span className="font-medium">
                  {Math.min(indexOfLastItem, filteredActions.length)}
                </span>{" "}
                of <span className="font-medium">{filteredActions.length}</span> results
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Select
                value={itemsPerPage.toString()}
                onValueChange={(value) => {
                  setItemsPerPage(Number(value));
                  setCurrentPage(1);
                }}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue placeholder={itemsPerPage} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex items-center space-x-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(1)}
                  disabled={currentPage === 1}
                >
                  First
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <span className="text-sm px-2">
                  {currentPage} / {totalPages || 1}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages || totalPages === 0}
                >
                  Next
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(totalPages)}
                  disabled={currentPage === totalPages || totalPages === 0}
                >
                  Last
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

    </div>
  );
}

export default ActionsManagement;
