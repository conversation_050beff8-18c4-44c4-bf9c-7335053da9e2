import { useEffect, useState, useCallback } from "react";
import { useParams, useNavigate, Outlet, useLocation } from "react-router-dom";
import { <PERSON>ertCircle, FileText, Edit, Loader2, <PERSON>lipboard<PERSON>heck, ShieldCheck, FileBarChart2, BarChart2, Activity, GitBranch, Trash2 } from "lucide-react";
import DetailHeader from "@/components/ui/detail-header";
import TabBar from "@/components/ui/tabs/tab-bar";
import TabContent from "@/components/ui/tabs/tab-content";
import axios from "axios";
import { toast } from "sonner";
import useReferenceData from "../../../hooks/useReferenceData";
import { useDispatch, useSelector } from "react-redux";
import { getAllActionPlans } from "../../../store/slices/actionPlanSlice";
import { hasPermission } from "../../../store/auth-slice";
import { But<PERSON> } from "../../../components/ui/button";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from 'react-i18next';
import riskIcon from "@/assets/risk.png";

function EditRisk() {
  const { t } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const auth = useSelector((state) => state.auth);
  const canDelete = useSelector((state) => hasPermission(state, 'delete'));
  const canUpdate = useSelector((state) => hasPermission(state, 'update'));
   const API_BASE_URL = getApiBaseUrl();
  const [currentRisk, setCurrentRisk] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [error, setError] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [workflowState, setWorkflowState] = useState(null);

  // Get the current tab from the URL
  const getCurrentTab = useCallback(() => {
    const path = location.pathname;
    if (path.includes('/features')) {
      return 'features';
    } else if (path.includes('/evaluation')) {
      return 'evaluation';
    } else if (path.includes('/mitigation')) {
      return 'risk-management';
    } else if (path.includes('/reports')) {
      return 'reports';
    } else if (path.includes('/activity-feed')) {
      return 'activity-feed';
    } else if (path.includes('/workflow')) {
      return 'workflow';
    } else if (path.includes('/action-plan')) {
      return 'action-plan';
    }
    return 'overview';
  }, [location.pathname]);

  const [activeTab, setActiveTab] = useState(getCurrentTab());

  // Update active tab when location changes
  useEffect(() => {
    setActiveTab(getCurrentTab());
  }, [location, getCurrentTab]);

  // Use the reference data hook
  const {
    businessProcesses,
    organizationalProcesses,
    operations,
    applications,
    entities,
    riskTypes,
    controls,
    isLoading: referenceDataLoading,
    reload,
  } = useReferenceData({ loadOnMount: false });

  // Get action plans from Redux
  const { actionPlans } = useSelector((state) => state.actionPlan);

  // Fetch risk on component mount
  useEffect(() => {
    const fetchRisk = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        const response = await axios.get(`${API_BASE_URL}/risk/${id}`, {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
        });

        if (response.data.success) {
          setCurrentRisk(response.data.data);
          setIsError(false);
          setError(null);

          // Fetch workflow state after risk data is loaded
          await fetchWorkflowState(id);
        } else {
          setIsError(true);
          setError(response.data.message || t('admin.risks.edit.error_loading', "Failed to load risk data. Please try again."));
          toast.error(response.data.message || t('admin.risks.edit.error_loading', "Failed to load risk data. Please try again."));
        }
      } catch (err) {
        setIsError(true);
        setError(err.message || t('admin.risks.edit.error_loading', "Failed to load risk data. Please try again."));
        toast.error(err.message || t('admin.risks.edit.error_loading', "Failed to load risk data. Please try again."));
      } finally {
        setIsLoading(false);
      }
    };

    // Fetch data
    const fetchData = async () => {
      // Fetch reference data once
      reload('all');

      // Fetch action plans
      dispatch(getAllActionPlans());

      // Fetch risk
      await fetchRisk();
    };

    fetchData();
  }, [id, reload, dispatch]);

  // Function to fetch workflow state
  const fetchWorkflowState = async (riskId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/risks/${riskId}/workflow/state`, {
        withCredentials: true
      });

      if (response.data && response.data.success) {
        setWorkflowState(response.data.data.current_state);
      }
    } catch (error) {
      console.error('Error fetching workflow state:', error);
    }
  };

  // Handle go back
  const handleGoBack = () => {
    navigate("/admin/risks");
  };

  // Handle delete risk
  const handleDelete = async () => {
    if (!canDelete) {
      toast.error(t('admin.risks.edit.delete_permission', "You don't have permission to delete risks"));
      return;
    }

    if (window.confirm(t('admin.risks.edit.delete_confirm', { name: currentRisk.name }))) {
      try {
        setIsDeleting(true);
        const response = await axios.delete(`http://localhost:5001/api/risk/${id}`, {
          withCredentials: true,
        });

        if (response.data.success) {
          toast.success(t('admin.risks.edit.delete_success', "Risk deleted successfully"));
          navigate("/admin/risks");
        } else {
          toast.error(response.data.message || t('admin.risks.edit.delete_error', "Failed to delete risk"));
        }
      } catch (err) {
        console.error("Error deleting risk:", err);
        const errorMessage = err.response?.data?.message || t('admin.risks.edit.delete_error', "Failed to delete risk");
        toast.error(errorMessage);
        setIsDeleting(false);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const tabs = [
    { id: "overview", label: t('admin.risks.edit.tabs.overview', "Overview"), icon: <FileText className="h-4 w-4" /> },
    // Only show these tabs if user has update permission
    ...(canUpdate ? [
      { id: "features", label: t('admin.risks.edit.tabs.contexte', "Contexte"), icon: <Edit className="h-4 w-4" /> },
      { id: "evaluation", label: t('admin.risks.edit.tabs.evaluation', "Evaluation"), icon: <ClipboardCheck className="h-4 w-4" /> },
      { id: "risk-management", label: t('admin.risks.edit.tabs.mitigation', "Mitigation"), icon: <FileBarChart2 className="h-4 w-4" /> },
      { id: "action-plan", label: t('admin.risks.edit.tabs.action_plan', "Action Plan"), icon: <FileText className="h-4 w-4" /> },
      { id: "reports", label: t('admin.risks.edit.tabs.reports', "Reports"), icon: <BarChart2 className="h-4 w-4" /> },
      { id: "activity-feed", label: t('admin.risks.edit.tabs.activity_feed', "Activity Feed"), icon: <Activity className="h-4 w-4" /> },
    ] : []),
    { id: "workflow", label: t('admin.risks.edit.tabs.workflow', "Workflow"), icon: <GitBranch className="h-4 w-4" /> },
  ];

  // Navigate to tab
  const navigateToTab = (tabId) => {
    // Get the current path to determine if we're in audit view
    const isAuditView = window.location.pathname.startsWith('/audit');
    const basePath = isAuditView ? '/audit' : '/admin';

    // Check if tab requires update permission
    const restrictedTabs = ["features", "evaluation", "risk-management", "action-plan", "reports", "activity-feed"];

    if (restrictedTabs.includes(tabId) && !canUpdate) {
      toast.error(t('admin.risks.edit.permission_error', "You don't have permission to access this page"));
      return;
    }

    switch (tabId) {
      case "overview":
        navigate(`${basePath}/risks/edit/${id}`);
        break;
      case "features":
        navigate(`${basePath}/risks/edit/${id}/features`);
        break;
      case "evaluation":
        navigate(`${basePath}/risks/edit/${id}/evaluation`);
        break;
      case "risk-management":
        navigate(`${basePath}/risks/edit/${id}/mitigation`);
        break;
      case "action-plan":
        navigate(`${basePath}/risks/edit/${id}/action-plan`);
        break;
      case "reports":
        navigate(`${basePath}/risks/edit/${id}/reports`);
        break;
      case "activity-feed":
        navigate(`${basePath}/risks/edit/${id}/activity-feed`);
        break;
      case "workflow":
        navigate(`${basePath}/risks/edit/${id}/workflow`);
        break;
      default:
        navigate(`${basePath}/risks/edit/${id}`);
    }
  };

  // Add an effect to check permissions for the current tab
  useEffect(() => {
    // Get current tab from URL
    const currentPath = location.pathname;
    const restrictedPaths = [
      `/admin/risks/edit/${id}/features`,
      `/admin/risks/edit/${id}/evaluation`,
      `/admin/risks/edit/${id}/mitigation`,
      `/admin/risks/edit/${id}/action-plan`,
      `/admin/risks/edit/${id}/reports`,
      `/admin/risks/edit/${id}/activity-feed`
    ];

    // If user doesn't have update permission and is on a restricted path, redirect to overview
    if (!canUpdate && restrictedPaths.some(path => currentPath === path)) {
      toast.error(t('admin.risks.edit.permission_error', "You don't have permission to access this page"));
      navigate(`/admin/risks/edit/${id}`);
    }
  }, [location.pathname, canUpdate, id, navigate]);

  if (isLoading || referenceDataLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          Error: {error}
        </div>
      </div>
    );
  }

  if (!currentRisk) {
    return (
      <div className="p-6 max-w-[1200px] mx-auto">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          {t('admin.risks.edit.risk_not_found', 'Risk not found')}
        </div>
      </div>
    );
  }

  // Helper functions for impact, DMR, and probability labels
  const getImpactLabel = (value) => {
    if (!value) return 'N/A';
    const impactLabels = {
      '1': 'Very Low',
      '2': 'Low',
      '3': 'Medium',
      '4': 'High',
      '5': 'Very High'
    };
    return impactLabels[value.toString()] || value;
  };

  const getControlLevelLabel = (value) => {
    if (!value) return 'N/A';
    const controlLevelLabels = {
      '1': 'Very Strong',
      '4': 'Strong',
      '9': 'Medium',
      '16': 'Weak',
      '25': 'Very Weak'
    };
    return controlLevelLabels[value.toString()] || value;
  };

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      {/* Header */}
      <div className="mb-6">
        <DetailHeader
          title={currentRisk.name || t('admin.risks.edit.unnamed', 'Unnamed Risk')}
          icon={<img src={riskIcon} alt="Risk" className="h-6 w-6" />}
          metadata={[
            currentRisk.code ? t('admin.risks.edit.metadata.code', { value: currentRisk.code }) : t('admin.risks.edit.metadata.code_na', 'Code: N/A'),
            t('admin.risks.edit.metadata.impact', { value: getImpactLabel(currentRisk.impact) }),
            t('admin.risks.edit.metadata.control_level', { value: getControlLevelLabel(currentRisk.DMR) }),
            currentRisk.comment ?
              `${currentRisk.comment.substring(0, 100)}${currentRisk.comment.length > 100 ? '...' : ''}` :
              null
          ].filter(Boolean)}
          onBack={handleGoBack}
          backLabel={t('admin.risks.edit.back_to_risks', 'Back to Risks')}
          actions={
            <Button
              variant="outline"
              size="sm"
              onClick={handleDelete}
              disabled={isDeleting || !canDelete}
              className={`ml-auto ${
                canDelete
                  ? "border-red-500 text-red-500 hover:bg-red-50"
                  : "border-gray-300 text-gray-400 bg-gray-100 hover:bg-gray-100 cursor-not-allowed"
              }`}
            >
              {isDeleting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Trash2 className="h-4 w-4 mr-2" />}
              {t('admin.risks.edit.delete_button', 'Delete Risk')}
            </Button>
          }
        />
      </div>

      {/* Tabs */}
      <TabBar
        activeTab={activeTab}
        tabs={tabs}
        onTabChange={navigateToTab}
        className="mb-6"
      />

      {/* Tab Content */}
      <TabContent>
        <Outlet context={{
          risk: currentRisk,
          workflowState,
          referenceData: {
            businessProcesses,
            organizationalProcesses,
            operations,
            applications,
            entities,
            riskTypes,
            controls,
            actionPlans
          },
          refreshRisk: async () => {
            setIsLoading(true);
            try {
              const response = await axios.get(`${API_BASE_URL}/risk/${id}`, {
                withCredentials: true,
                headers: { "Content-Type": "application/json" },
              });

              if (response.data.success) {
                setCurrentRisk(response.data.data);
              }
            } catch {
              // Catch any errors without using the error variable
              toast.error(t('admin.risks.edit.error_refresh', "Failed to refresh risk data"));
            } finally {
              setIsLoading(false);
            }
          }
        }} />
      </TabContent>
    </div>
  );
}

export default EditRisk;
