import React, { useState, useEffect, useCallback } from "react";
import { debounce } from "lodash";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Paperclip, ChevronUp, ChevronDown, Save, Loader2, FileText, Plus, Trash2, Upload, ArrowUp, ArrowDown, GripVertical, Edit, X, Check, Type, Hash, Calendar, Radio, CheckSquare, List } from "lucide-react";
import { AuditTravailFicheAttachmentsSection } from "@/components/attachments_audit/AuditTravailFicheAttachmentsSection";
import { useCustomOutletContext } from "../../edit-fiches-travail";
import { updateFicheDeTravail, deleteQuestion, reorderQuestions } from "@/services/fiche-de-travail-service";
import { toast } from "sonner";
import AddQuestionModal from "@/components/modals/AddQuestionModal";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { DateInput } from "@/components/ui/date-input";
import { Combobox } from "@/components/ui/combobox";
import {
  DndContext,
  PointerSensor,
  DragOverlay,
  closestCenter,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { useDraggable, useDroppable } from '@dnd-kit/core';
import {
  SortableContext,
  useSortable,
  arrayMove,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// Question types for drag and drop
const QUESTION_TYPES = [
  { type: 'text', label: 'Texte libre' },
  { type: 'number', label: 'Nombre' },
  { type: 'date', label: 'Date' },
  { type: 'radio', label: '' },
  { type: 'multi-select', label: 'Choix multiple' },
  { type: 'select', label: 'Liste déroulante' },
];

// Icon mapping for different question types
const getQuestionIcon = (type) => {
  switch (type) {
    case 'text': return '📝';
    case 'number': return '🔢';
    case 'date': return '📅';
    case 'radio': return '🔘';
    case 'multi-select': return '☑️';
    case 'select': return '📋';
    default: return '❓';
  }
};

// End drop zone component
function EndDropZone() {
  const { setNodeRef } = useDroppable({
    id: 'end-drop-zone'
  });
  return (
    <div
      ref={setNodeRef}
      style={{
        minHeight: 60,
        border: '2px dashed #d1d5db',
        borderRadius: 8,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: '#f9fafb',
        color: '#6b7280',
        fontSize: '14px',
        fontWeight: '500',
        transition: 'all 0.2s ease',
        marginTop: 16
      }}
    >
      {'Déposez ici pour ajouter à la fin'}
    </div>
  );
}

// Simple droppable component
function SimpleDroppable({ id, children }) {
  const { setNodeRef } = useDroppable({
    id: id,
  });
  return (
    <div ref={setNodeRef}>
      {children}
    </div>
  );
}

// Drop indicator component
function DropIndicator({ position, show }) {
  const { isOver, setNodeRef } = useDroppable({
    id: `drop-indicator-${position}`
  });

  if (!show && !isOver) return null;

  return (
    <div
      ref={setNodeRef}
      style={{
        height: 4,
        background: isOver ? '#10b981' : '#e5e7eb',
        borderRadius: 2,
        margin: '8px 0',
        transition: 'all 0.2s ease',
        opacity: isOver ? 1 : 0.5
      }}
    />
  );
}

// Draggable question type component
function DraggableType({ type, label }) {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: `type-${type}`
  });

  const style = {
    transform: transform ? CSS.Translate.toString(transform) : undefined,
    opacity: isDragging ? 0.7 : 1,
    padding: '12px 16px',
    background: '#ffffff',
    color: '#374151',
    borderRadius: 8,
    cursor: 'grab',
    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
    transition: 'all 0.2s ease',
    border: '2px solid #e5e7eb',
    userSelect: 'none',
    touchAction: 'none'
  };

  // Simple mock preview text
  const getMockPreview = (type) => {
    switch (type) {
      case 'text': return '[ Votre réponse... ]';
      case 'number': return '[ 0 ]';
      case 'date': return '[ jj/mm/aaaa ]';
      case 'radio': return '○ Option 1  ○ Option 2';
      case 'multi-select': return '☐ Option 1  ☐ Option 2';
      case 'select': return '[ Sélectionner... ]';
      default: return '[ Réponse ]';
    }
  };

  return (
    <div ref={setNodeRef} style={style} {...listeners} {...attributes}>
      <div style={{ display: 'flex', alignItems: 'center', gap: 12, marginBottom: 8 }}>
        <span style={{ fontSize: '16px' }}>{getQuestionIcon(type)}</span>
        <span style={{ fontWeight: '500', fontSize: '14px' }}>{label}</span>
      </div>

      {/* Mock preview */}
      <div style={{
        fontSize: '12px',
        color: '#9ca3af',
        padding: '6px 8px',
        background: '#f9fafb',
        borderRadius: 4,
        border: '1px dashed #d1d5db'
      }}>
        {getMockPreview(type)}
      </div>
    </div>
  );
}

function FichesTravailCaracteristiquesTab() {
  const context = useCustomOutletContext();
  const fiche = context?.fiche;

  // Section toggles
  const [isCaracteristiquesOpen, setIsCaracteristiquesOpen] = useState(true);
  const [isQuestionnaireOpen, setIsQuestionnaireOpen] = useState(true);
  const [isQuestionModalOpen, setIsQuestionModalOpen] = useState(false);
  const [isAttachmentsOpen, setIsAttachmentsOpen] = useState(true);

  // Drag and drop states
  const [showDndModal, setShowDndModal] = useState(false);
  const [dndQuestions, setDndQuestions] = useState([]);

  // Form state for fiche
  const [formData, setFormData] = useState({
    name: "",
    tailleEchantillon: "",
    tacheDetail: "",
    commentaire: ""
  });

  // Quiz state
  const [questions, setQuestions] = useState([]);

  // Debounced save function for main form fields
  const debouncedSaveFiche = useCallback(
    debounce(async (currentFormData) => {
      if (!fiche?.id) {
        console.warn("Fiche ID is missing for auto-save.");
        return;
      }
      try {
        // Only save form data, questions are saved separately
        const response = await updateFicheDeTravail(fiche.id, currentFormData);
        if (response && response.success) {
          toast.success("Fiche de travail sauvegardée automatiquement");
          if (context?.setFiche) {
            context.setFiche(response.data);
          }
        } else {
          throw new Error(response?.message || "Erreur lors de la sauvegarde automatique");
        }
      } catch (error) {
        console.error('Error auto-saving fiche:', error);
        toast.error(error.message || "Erreur lors de la sauvegarde automatique de la fiche de travail");
      }
    }, 1000), // 1 second debounce delay
    [fiche?.id, context?.setFiche]
  );

  // Initialize form data when fiche is loaded
  useEffect(() => {
    if (fiche) {
      setFormData({
        name: fiche.name || "",
        tailleEchantillon: fiche.tailleEchantillon || 'N/A',
        tacheDetail: fiche.tacheDetail || '',
        commentaire: fiche.commentaire || ''
      });
      // Initialize questions from fiche if available
      setQuestions(fiche.questions || []);
      // Initialize dnd questions with mapped format
      const mappedQuestions = (fiche.questions || []).map(q => ({
        id: String(q.id),
        title: q.question_text,
        type: q.input_type,
        options: q.options || []
      }));
      setDndQuestions(mappedQuestions);
    }
  }, [fiche]);

  // Handlers for fiche form
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      const newState = { ...prev, [name]: value };
      debouncedSaveFiche(newState); // Trigger debounced save
      return newState;
    });
  };



  const handleAddQuestion = async (questionData) => {
    // Add the new question to the local state
    const updatedQuestions = [
      ...questions,
      {
        // No id, let backend assign
        question_text: questionData.question_text,
        input_type: questionData.input_type,
        options: ["radio", "select", "multi-select"].includes(questionData.input_type) ? [...questionData.options] : null
      }
    ];

    // Save immediately to backend
    if (!fiche?.id) {
      toast.error("ID de fiche manquant");
      throw new Error("ID de fiche manquant");
    }

    try {
      const response = await updateFicheDeTravail(fiche.id, { ...formData, questions: updatedQuestions });
      if (response && response.success) {
        toast.success("Question ajoutée et sauvegardée avec succès");
        // Update local state with backend's latest questions (with IDs)
        if (response.data?.questions) {
          setQuestions(response.data.questions);
        } else {
          setQuestions(updatedQuestions);
        }
        if (context?.setFiche) {
          context.setFiche(response.data);
        }
      } else {
        throw new Error(response?.message || "Erreur lors de la sauvegarde de la question");
      }
    } catch (error) {
      console.error('Error saving question:', error);
      toast.error(error.message || "Erreur lors de la sauvegarde de la question");
      throw error;
    }
  };

  if (!fiche) {
    return (
      <div className="flex justify-center items-center h-48">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        <p className="text-gray-500 ml-2">Chargement de la fiche de travail...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      {/* Section 1: Caractéristiques de la fiche de travail */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsCaracteristiquesOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isCaracteristiquesOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <span className="text-lg font-medium text-blue-800">Caractéristiques du Document de Travail</span>
          </div>
        </button>
        {isCaracteristiquesOpen && (
          <div className="p-5 bg-white space-y-4">
            {/* Mission d'audit and Activity labels above the form */}
            <div className="mb-4 space-y-2">
              <span className="text-base font-semibold text-blue-800 flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-500" />
                Mission d'audit : <span className="ml-1 text-blue-900">{fiche?.auditMission?.name || <span className="text-gray-400">Non renseigné</span>}</span>
              </span>
              <span className="text-base font-semibold text-blue-800 flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-500" />
                Activité d'audit : <span className="ml-1 text-blue-900">{fiche?.auditActivity?.name || <span className="text-gray-400">Non renseigné</span>}</span>
              </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nom du Document de Travail</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Entrez le nom de la fiche"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tailleEchantillon">Taille de l'échantillon</Label>
                <Input
                  id="tailleEchantillon"
                  name="tailleEchantillon"
                  value={formData.tailleEchantillon}
                  onChange={handleInputChange}
                  placeholder="Ex: 100"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tacheDetail">Détail de la Tâche</Label>
              <Textarea
                id="tacheDetail"
                name="tacheDetail"
                value={formData.tacheDetail}
                onChange={handleInputChange}
                placeholder="Décrivez les détails de la tâche"
                rows="3"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="commentaire">Commentaire</Label>
              <Textarea
                id="commentaire"
                name="commentaire"
                value={formData.commentaire}
                onChange={handleInputChange}
                placeholder="Ajoutez un commentaire"
                rows="3"
              />
            </div>
          </div>
        )}
      </div>

      {/* Section 2: Questionnaire */}
      <div className="border rounded-lg shadow-sm mt-6">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-green-50 to-teal-50 rounded-t-lg"
          onClick={() => setIsQuestionnaireOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isQuestionnaireOpen ? (
              <ChevronUp className="h-5 w-5 text-green-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-green-600" />
            )}
            <span className="text-lg font-medium text-green-800">Questionnaire</span>
          </div>
        </button>
        {isQuestionnaireOpen && (
          <div className="p-5 bg-white space-y-6">
            <div className="flex items-center justify-between">
              <h4 className="text-md font-semibold text-gray-700">Constructeur de questions</h4>
              <Button
                onClick={() => setShowDndModal(true)}
                className="bg-[#F62D51] hover:bg-red-700 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Créer des questions
              </Button>
            </div>

            {dndQuestions.length > 0 ? (
              <>
                <div style={{
                  marginTop: 0,
                  padding: 16,
                  background: 'linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%)',
                  borderRadius: 12,
                  border: '1px solid #a7f3d0',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 16
                }}>
                  <div style={{
                    width: 40,
                    height: 40,
                    borderRadius: '50%',
                    background: '#10b981',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '16px',
                    fontWeight: 'bold',
                    boxShadow: '0 2px 4px rgba(16, 185, 129, 0.3)'
                  }}>
                    {dndQuestions.length}
                  </div>
                  <div>
                    <p style={{ fontSize: '14px', color: '#065f46', margin: 0, fontWeight: '600' }}>
                      Question{dndQuestions.length !== 1 ? 's' : ''} créée{dndQuestions.length !== 1 ? 's' : ''}
                    </p>
                    <p style={{ fontSize: '12px', color: '#047857', margin: 0, opacity: 0.8 }}>
                      Questionnaire prêt pour la fiche de travail
                    </p>
                  </div>
                  <div style={{ marginLeft: 'auto', display: 'flex', alignItems: 'center', gap: 8 }}>
                    <div
                      onClick={() => setShowDndModal(true)}
                      style={{
                        width: 24,
                        height: 24,
                        borderRadius: '50%',
                        background: '#6b7280',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => e.target.style.background = '#4b5563'}
                      onMouseLeave={(e) => e.target.style.background = '#6b7280'}
                      title="Modifier les questions"
                    >
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="white">
                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  {dndQuestions.map((question, index) => (
                    <div key={question.id} className="p-4 bg-gray-50 rounded-lg border">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">
                            {index + 1}. {question.title}
                          </p>
                          <p className="text-sm text-gray-600 mt-1">
                            Type: {QUESTION_TYPES.find(t => t.type === question.type)?.label || question.type}
                          </p>
                          {question.options && question.options.length > 0 && (
                            <p className="text-xs text-gray-500 mt-1">
                              Options: {question.options.join(", ")}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune question créée</h3>
                <p className="text-sm text-gray-500 mb-4">
                  Utilisez le constructeur drag & drop pour créer vos questions
                </p>
                <Button
                  onClick={() => setShowDndModal(true)}
                  className="bg-[#F62D51] hover:bg-red-700 text-white"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Commencer
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Section 3: Pièces jointes */}
      <div className="border rounded-lg shadow-sm mt-6">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-t-lg"
          onClick={() => setIsAttachmentsOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isAttachmentsOpen ? (
              <ChevronUp className="h-5 w-5 text-purple-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-purple-600" />
            )}
            <Paperclip className="h-5 w-5 text-purple-600" />
            <span className="text-lg font-medium text-purple-800">Pièces jointes</span>
          </div>
        </button>
        {isAttachmentsOpen && (
          <div className="p-5 bg-white space-y-6">
            <AuditTravailFicheAttachmentsSection ficheTravail={fiche} />
          </div>
        )}
      </div>

      {/* Add Question Modal */}
      <AddQuestionModal
        isOpen={isQuestionModalOpen}
        onClose={() => setIsQuestionModalOpen(false)}
        onAddQuestion={handleAddQuestion}
        isLoading={false}
      />

      {/* Drag & Drop Question Builder Modal */}
      <DndQuestionBuilderModal
        isOpen={showDndModal}
        onClose={() => {
          // Sync the main questions state with dndQuestions when modal closes
          const mainQuestionsFormat = dndQuestions.map(q => ({
            id: q.id,
            question_text: q.title,
            input_type: q.type,
            options: q.options
          }));
          setQuestions(mainQuestionsFormat);
          setShowDndModal(false);
        }}
        questions={dndQuestions}
        setQuestions={setDndQuestions}
        ficheId={fiche?.id}
        context={context}
        fiche={fiche}
        setDndQuestions={setDndQuestions}
        dndQuestions={dndQuestions}
      />
    </div>
  );
}

// Enhanced Drag and Drop Question Builder
function EnhancedDndQuestionBuilder({ questions, setQuestions, ficheId, context, fiche, setDndQuestions, dndQuestions }) {
  const [activeId, setActiveId] = useState(null);

  // Backend sync helpers
  const addQuestionBackend = async (type, insertIndex = -1) => {
    const typeLabel = QUESTION_TYPES.find(qt => qt.type === type)?.label || type;

    try {
      // Create new question object
      const newQuestion = {
        question_text: `Nouvelle question ${typeLabel}`,
        input_type: type,
        options: ['radio', 'multi-select', 'select'].includes(type) ? ['Option 1', 'Option 2'] : null
      };

      // Get current fiche data and add the new question at the correct position
      const currentQuestions = questions.map(q => ({
        id: q.id,
        question_text: q.title,
        input_type: q.type,
        options: q.options
      }));

      // Insert at the correct position
      const updatedQuestions = [...currentQuestions];
      if (insertIndex >= 0 && insertIndex <= currentQuestions.length) {
        updatedQuestions.splice(insertIndex, 0, newQuestion);
      } else {
        updatedQuestions.push(newQuestion);
      }

      // Update the fiche with new questions
      const response = await updateFicheDeTravail(ficheId, { questions: updatedQuestions });

      if (response && response.success) {
        toast.success("Question ajoutée avec succès");
        // Don't reload questions immediately, let the local state update handle it
        // await reloadQuestions?.();

        // Find the newly created question by position
        const newQuestionIndex = insertIndex >= 0 ? insertIndex : response.data.questions.length - 1;
        return response.data.questions[newQuestionIndex];
      } else {
        throw new Error(response?.message || "Erreur lors de l'ajout de la question");
      }
    } catch (error) {
      console.error('Error adding question:', error);
      toast.error(error.message || "Erreur lors de l'ajout de la question");
      throw error;
    }
  };

  const deleteQuestionBackend = async (questionId) => {
    try {
      await deleteQuestion(questionId);
      toast.success("Question supprimée avec succès");
      // Don't reload questions, let local state handle it
    } catch (error) {
      console.error('Error deleting question:', error);
      toast.error("Erreur lors de la suppression de la question");
      throw error;
    }
  };

  const reorderQuestionsBackend = async (newQuestions) => {
    if (!ficheId) return;
    try {
      await reorderQuestions(ficheId, newQuestions.map(q => q.id));
      // Don't reload questions, let local state handle it
    } catch (error) {
      console.error('Error reordering questions:', error);
      toast.error("Erreur lors de la réorganisation des questions");
    }
  };

  const editQuestionBackend = async (questionId, updatedQuestion) => {
    try {
      // Get current questions and update the specific one
      const currentQuestions = questions.map(q => {
        if (String(q.id) === String(questionId)) {
          return {
            id: q.id,
            question_text: updatedQuestion.title,
            input_type: updatedQuestion.type,
            options: updatedQuestion.options
          };
        }
        return {
          id: q.id,
          question_text: q.title,
          input_type: q.type,
          options: q.options
        };
      });

      // Update the fiche with modified questions
      const response = await updateFicheDeTravail(ficheId, { questions: currentQuestions });

      if (response && response.success) {
        toast.success("Question modifiée avec succès");
        return true;
      } else {
        throw new Error(response?.message || "Erreur lors de la modification de la question");
      }
    } catch (error) {
      console.error('Error editing question:', error);
      toast.error(error.message || "Erreur lors de la modification de la question");
      throw error;
    }
  };

  function handleDragStart(event) {
    setActiveId(event.active.id);
  }

  async function handleDragEnd(event) {
    const { active, over } = event;
    setActiveId(null);
    const activeIdStr = String(active.id);
    const overIdStr = over ? String(over.id) : null;

    if (!over) return;

    // Handle dropping new question types
    if (activeIdStr.startsWith('type-')) {
      const type = activeIdStr.replace('type-', '');

      // Determine insertion position
      let insertIndex = questions.length; // Default to end

      // Debug logging
      console.log('Drop event:', { activeIdStr, overIdStr, questionsLength: questions.length });

      // Handle different drop targets
      if (overIdStr === 'drop-indicator-start') {
        insertIndex = 0; // Insert at the beginning
        console.log('Inserting at start, index:', insertIndex);
      } else if (overIdStr === 'end-drop-zone') {
        insertIndex = questions.length; // Insert at the end
        console.log('Inserting at end, index:', insertIndex);
      } else if (overIdStr && overIdStr.startsWith('drop-indicator-after-')) {
        // Extract the index from the drop indicator ID
        const afterIndex = parseInt(overIdStr.replace('drop-indicator-after-', ''));
        insertIndex = afterIndex + 1; // Insert after the specified question
        console.log('Inserting after index', afterIndex, 'new index:', insertIndex);
      } else if (overIdStr && overIdStr !== 'enhanced-drop-zone' && !overIdStr.startsWith('type-') && !overIdStr.startsWith('drop-indicator-')) {
        // If dropped on an existing question, insert before it
        const targetIndex = questions.findIndex(q => String(q.id) === overIdStr);
        if (targetIndex !== -1) {
          insertIndex = targetIndex;
          console.log('Inserting before question at index:', insertIndex);
        }
      } else {
        console.log('Using default insertion at end, index:', insertIndex);
      }

      try {
        // First, optimistically update the UI
        const tempQuestion = {
          id: `temp-${Date.now()}`,
          title: `Nouvelle question ${QUESTION_TYPES.find(qt => qt.type === type)?.label || type}`,
          type: type,
          options: ['radio', 'multi-select', 'select'].includes(type) ? ['Option 1', 'Option 2'] : []
        };

        // Update UI immediately for both states
        const newQuestions = [...questions];
        newQuestions.splice(insertIndex, 0, tempQuestion);
        setQuestions(newQuestions);
        setDndQuestions(newQuestions);

        // Then save to backend
        const newQuestion = await addQuestionBackend(type, insertIndex);
        if (newQuestion) {
          const mappedQuestion = {
            id: String(newQuestion.id),
            title: newQuestion.question_text,
            type: newQuestion.input_type,
            options: newQuestion.options || []
          };

          // Replace the temp question with the real one in both states
          const finalQuestions = [...newQuestions];
          finalQuestions[insertIndex] = mappedQuestion;
          setQuestions(finalQuestions);
          setDndQuestions(finalQuestions);

          // Convert to main questions format for context
          const mainQuestionsFormat = finalQuestions.map(q => ({
            id: q.id,
            question_text: q.title,
            input_type: q.type,
            options: q.options
          }));

          // Update the fiche context so other tabs see the changes
          if (context?.setFiche && fiche) {
            const updatedFiche = {
              ...fiche,
              questions: mainQuestionsFormat
            };
            context.setFiche(updatedFiche);
          }
        }
      } catch (error) {
        console.error('Error adding question:', error);
        // Revert the optimistic update on error
        setQuestions([...questions]);
      }
    }

    // Handle reordering existing questions
    if (activeIdStr !== overIdStr && !activeIdStr.startsWith('type-') && !overIdStr.startsWith('drop-indicator-') && overIdStr !== 'end-drop-zone') {
      const activeIndex = questions.findIndex(q => String(q.id) === activeIdStr);
      const overIndex = questions.findIndex(q => String(q.id) === overIdStr);

      if (activeIndex !== -1 && overIndex !== -1) {
        const newQuestions = arrayMove(questions, activeIndex, overIndex);
        setQuestions(newQuestions);
        setDndQuestions(newQuestions);

        // Convert to main questions format for context
        const mainQuestionsFormat = newQuestions.map(q => ({
          id: q.id,
          question_text: q.title,
          input_type: q.type,
          options: q.options
        }));

        // Update the fiche context so other tabs see the changes
        if (context?.setFiche && fiche) {
          const updatedFiche = {
            ...fiche,
            questions: mainQuestionsFormat
          };
          context.setFiche(updatedFiche);
        }

        await reorderQuestionsBackend(newQuestions);
      }
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const handleDragOver = (event) => {
    const { active, over } = event;
    const activeIdStr = String(active.id);

    // Only provide visual feedback for new question types being dragged
    if (activeIdStr.startsWith('type-') && over) {
      // You can add additional visual feedback logic here if needed
    }
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <div style={{
        display: 'flex',
        gap: 32,
        padding: 24,
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        borderRadius: 12,
        minHeight: 500,
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)'
      }}>

        {/* Left: Question Types Palette */}
        <div style={{ width: 280, minWidth: 260 }}>

          <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
            {QUESTION_TYPES.map(qt => (
              <DraggableType key={qt.type} type={qt.type} label={qt.label} />
            ))}
          </div>
        </div>

        {/* Right: Drop Zone */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>

          <SimpleDroppable id="enhanced-drop-zone">
            <div style={{
              minHeight: 400,
              padding: 24,
              background: 'white',
              borderRadius: 12,
              boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
              display: 'flex',
              flexDirection: 'column'
            }}>

              {questions.length === 0 && (
                <div style={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  textAlign: 'center',
                  minHeight: 350,
                  border: '2px dashed #e5e7eb',
                  borderRadius: 16,
                  background: 'linear-gradient(135deg, #fafbfc 0%, #f1f5f9 100%)',
                  color: '#64748b',
                  position: 'relative',
                  overflow: 'hidden'
                }}>
                  {/* Background decoration */}
                  <div style={{
                    position: 'absolute',
                    top: -50,
                    right: -50,
                    width: 100,
                    height: 100,
                    borderRadius: '50%',
                    background: 'linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%)',
                    opacity: 0.3
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: -30,
                    left: -30,
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    background: 'linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%)',
                    opacity: 0.2
                  }} />

                  {/* Main content */}
                  <div style={{ position: 'relative', zIndex: 1 }}>
                    <div style={{
                      fontSize: '64px',
                      marginBottom: 20,
                      background: 'linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text'
                    }}>
                      📋
                    </div>
                    <h3 style={{
                      fontSize: '28px',
                      fontWeight: '700',
                      marginBottom: 16,
                      color: '#1e293b',
                      letterSpacing: '-0.025em'
                    }}>
                      Construisez votre questionnaire
                    </h3>
                    <p style={{
                      fontSize: '16px',
                      marginBottom: 12,
                      maxWidth: 450,
                      lineHeight: 1.6,
                      color: '#475569'
                    }}>
                      Glissez et déposez les types de questions depuis la palette de gauche pour créer votre formulaire de contrôle personnalisé
                    </p>
                  </div>
                </div>
              )}

              {/* Questions List - Sortable */}
              {questions.length > 0 && (
                <SortableContext items={questions.map(q => q.id)} strategy={verticalListSortingStrategy}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>

                    {/* Drop indicator at the beginning */}
                    <DropIndicator
                      position="start"
                      show={activeId && String(activeId).startsWith('type-')}
                    />

                    {questions.map((q, index) => (
                      <div key={q.id}>
                        <SortableQuestionCard
                          id={q.id}
                          question={q}
                          index={index}
                          activeId={activeId}
                          onEdit={async (id, updated) => {
                            try {
                              // Optimistically update both states
                              setQuestions(prev => prev.map(qq => qq.id === id ? updated : qq));
                              setDndQuestions(prev => prev.map(qq => qq.id === id ? updated : qq));

                              // Then save to backend
                              await editQuestionBackend(id, updated);

                              // Update the main questions state and context
                              const updatedMainQuestions = questions.map(q => {
                                if (String(q.id) === String(id)) {
                                  return {
                                    id: updated.id,
                                    question_text: updated.title,
                                    input_type: updated.type,
                                    options: updated.options
                                  };
                                }
                                return q;
                              });
                              setQuestions(updatedMainQuestions);

                              // Update the fiche context so other tabs see the changes
                              if (context?.setFiche && fiche) {
                                const updatedFiche = {
                                  ...fiche,
                                  questions: updatedMainQuestions
                                };
                                context.setFiche(updatedFiche);
                              }
                            } catch (error) {
                              console.error('Error editing question:', error);
                              // Revert the optimistic updates on error
                              setQuestions(prev => prev.map(qq => qq.id === id ? questions.find(q => q.id === id) || qq : qq));
                              setDndQuestions(prev => prev.map(qq => qq.id === id ? dndQuestions.find(q => q.id === id) || qq : qq));
                            }
                          }}
                          onDelete={async (id) => {
                            try {
                              // Optimistically update both states
                              setQuestions(prev => prev.filter(qq => qq.id !== id));
                              setDndQuestions(prev => prev.filter(qq => qq.id !== id));

                              // Delete from backend
                              await deleteQuestionBackend(id);

                              // Update the main questions state
                              const updatedMainQuestions = questions.filter(q => String(q.id) !== String(id));
                              setQuestions(updatedMainQuestions);

                              // Update the fiche context so other tabs see the changes
                              if (context?.setFiche && fiche) {
                                const updatedFiche = {
                                  ...fiche,
                                  questions: updatedMainQuestions
                                };
                                context.setFiche(updatedFiche);
                              }
                            } catch (error) {
                              console.error('Error deleting question:', error);
                              // Revert the optimistic updates on error
                              const originalQuestion = questions.find(q => String(q.id) === String(id));
                              const originalDndQuestion = dndQuestions.find(q => String(q.id) === String(id));
                              if (originalQuestion) {
                                setQuestions(prev => [...prev, originalQuestion]);
                              }
                              if (originalDndQuestion) {
                                setDndQuestions(prev => [...prev, originalDndQuestion]);
                              }
                            }
                          }}
                        />

                        {/* Drop indicator after each question (except the last one) */}
                        {index < questions.length - 1 && (
                          <DropIndicator
                            position={`after-${index}`}
                            show={activeId && String(activeId).startsWith('type-')}
                          />
                        )}
                      </div>
                    ))}

                    {/* Drop zone at the end */}
                    <EndDropZone />
                  </div>
                </SortableContext>
              )}
            </div>
          </SimpleDroppable>
        </div>
      </div>

      {/* Simple Drag Overlay */}
      <DragOverlay>
        {activeId && String(activeId).startsWith('type-') && (
          <div style={{
            padding: '12px 16px',
            background: '#ffffff',
            color: '#374151',
            borderRadius: 8,
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            border: '2px solid #e5e7eb',
            opacity: 0.9
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
              <span style={{ fontSize: '16px' }}>
                {getQuestionIcon(String(activeId).replace('type-', ''))}
              </span>
              <span style={{ fontWeight: '500', fontSize: '14px' }}>
                {QUESTION_TYPES.find(qt => qt.type === String(activeId).replace('type-', ''))?.label}
              </span>
            </div>
          </div>
        )}
      </DragOverlay>
    </DndContext>
  );
}

// Sortable Question Card Component
function SortableQuestionCard({ id, question, index, activeId, onEdit, onDelete }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
    isOver,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  // Show drop indicator when dragging a new question type over this question
  const showDropIndicator = isOver && activeId && String(activeId).startsWith('type-');

  return (
    <div style={{ position: 'relative' }}>
      {/* Drop indicator - shows when dragging a new question type over this question */}
      {showDropIndicator && (
        <div style={{
          position: 'absolute',
          top: -8,
          left: 0,
          right: 0,
          height: 4,
          background: 'linear-gradient(90deg, #3b82f6, #1d4ed8)',
          borderRadius: 2,
          zIndex: 10,
          boxShadow: '0 0 8px rgba(59, 130, 246, 0.5)'
        }} />
      )}

      <div ref={setNodeRef} style={style} {...attributes}>
        <EnhancedQuestionCard
          question={question}
          index={index}
          onEdit={onEdit}
          onDelete={onDelete}
          dragHandle={listeners}
        />
      </div>
    </div>
  );
}

// Enhanced Question Card Component
function EnhancedQuestionCard({ question, index, onEdit, onDelete, dragHandle }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedQuestion, setEditedQuestion] = useState(question);

  // Update editedQuestion when question prop changes
  useEffect(() => {
    setEditedQuestion(question);
  }, [question]);

  const getTypeLabel = (type) => {
    const found = QUESTION_TYPES.find(qt => qt.type === type);
    return found ? found.label : type;
  };

  const handleSave = async () => {
    try {
      await onEdit(question.id, { ...editedQuestion, id: question.id });
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving question:', error);
      // Keep editing mode open on error
    }
  };

  const handleCancel = () => {
    setEditedQuestion(question);
    setIsEditing(false);
  };

  const addOption = () => {
    if (['radio', 'multi-select'].includes(question.type)) {
      setEditedQuestion(prev => ({
        ...prev,
        options: [...(prev.options || []), `Option ${(prev.options?.length || 0) + 1}`]
      }));
    }
  };

  const updateOption = (optionIndex, value) => {
    setEditedQuestion(prev => ({
      ...prev,
      options: prev.options?.map((opt, idx) => idx === optionIndex ? value : opt) || []
    }));
  };

  const removeOption = (optionIndex) => {
    setEditedQuestion(prev => ({
      ...prev,
      options: prev.options?.filter((_, idx) => idx !== optionIndex) || []
    }));
  };

  return (
    <div style={{
      background: 'white',
      border: '1px solid #e5e7eb',
      borderRadius: 12,
      padding: 20,
      boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
      position: 'relative',
      transition: 'all 0.2s ease'
    }}>
      {/* Question Number Badge */}
      <div style={{
        position: 'absolute',
        left: -10,
        top: -10,
        background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
        color: 'white',
        borderRadius: '50%',
        width: 32,
        height: 32,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '14px',
        fontWeight: 'bold',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        {index + 1}
      </div>

      {/* Question Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
        paddingLeft: 16
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          {/* Drag Handle */}
          <div
            {...dragHandle}
            style={{
              cursor: 'grab',
              padding: '4px',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              color: '#9ca3af'
            }}
          >
            <GripVertical style={{ width: 16, height: 16 }} />
          </div>
          <div>
            <h4 style={{
              margin: 0,
              fontSize: '18px',
              fontWeight: '600',
              color: '#1f2937'
            }}>
              {question.title || 'Question sans titre'}
            </h4>
            <p style={{
              margin: 0,
              fontSize: '14px',
              color: '#6b7280'
            }}>
              {getTypeLabel(question.type)}
            </p>
          </div>
        </div>

        <div style={{ display: 'flex', gap: 8 }}>
          <Button
            onClick={() => setIsEditing(!isEditing)}
            variant="outline"
            size="sm"
            style={{ minWidth: 'auto', padding: '6px 12px' }}
          >
            {isEditing ? <FileText style={{ width: 16, height: 16 }} /> : <Edit style={{ width: 16, height: 16 }} />}
          </Button>
          <Button
            onClick={() => onDelete(question.id)}
            variant="outline"
            size="sm"
            style={{
              minWidth: 'auto',
              padding: '6px 12px',
              color: '#dc2626',
              borderColor: '#dc2626'
            }}
          >
            <Trash2 style={{ width: 16, height: 16 }} />
          </Button>
        </div>
      </div>

      {/* Question Content */}
      {isEditing ? (
        <div style={{ paddingLeft: 16 }}>
          {/* Title Input */}
          <div style={{ marginBottom: 16 }}>
            <label style={{
              display: 'block',
              fontSize: '14px',
              fontWeight: '500',
              color: '#374151',
              marginBottom: 4
            }}>
              Nom de la question
            </label>
            <Input
              value={editedQuestion.title || ''}
              onChange={(e) => setEditedQuestion(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Entrez le nom de la question"
              style={{ width: '100%' }}
            />
          </div>

          {/* Only show options for radio/multi-select */}
          {['radio', 'multi-select'].includes(question.type) && (
            <div style={{ marginBottom: 16 }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 8
              }}>
                <label style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151'
                }}>
                  Options de réponse
                </label>
                <Button
                  onClick={addOption}
                  variant="outline"
                  size="sm"
                >
                  + Ajouter option
                </Button>
              </div>
              {(editedQuestion.options || []).map((option, idx) => (
                <div key={idx} style={{
                  display: 'flex',
                  gap: 8,
                  marginBottom: 8,
                  alignItems: 'center'
                }}>
                  <span style={{ fontSize: '14px', color: '#6b7280', minWidth: '20px' }}>
                    {idx + 1}.
                  </span>
                  <Input
                    value={option}
                    onChange={(e) => updateOption(idx, e.target.value)}
                    placeholder={`Option ${idx + 1}`}
                    style={{ flex: 1 }}
                  />
                  <Button
                    onClick={() => removeOption(idx)}
                    variant="outline"
                    size="sm"
                    style={{
                      minWidth: 'auto',
                      padding: '6px',
                      color: '#dc2626',
                      borderColor: '#dc2626'
                    }}
                  >
                    ×
                  </Button>
                </div>
              ))}
            </div>
          )}

          {/* Save/Cancel Buttons */}
          <div style={{ display: 'flex', gap: 8, justifyContent: 'flex-end' }}>
            <Button onClick={handleCancel} variant="outline">
              Annuler
            </Button>
            <Button onClick={handleSave} style={{ background: '#10b981', color: 'white' }}>
              Sauvegarder
            </Button>
          </div>
        </div>
      ) : (
        <div style={{ paddingLeft: 16 }}>
          {/* Preview Mode */}
          <div style={{
            padding: 16,
            background: '#f9fafb',
            borderRadius: 8,
            border: '1px solid #e5e7eb'
          }}>
            <h5 style={{
              margin: '0 0 8px 0',
              fontSize: '16px',
              color: '#1f2937'
            }}>
              {question.title || 'Question sans titre'}
            </h5>

            {/* Question Preview */}
            <div style={{ marginTop: 12 }}>
              {question.type === 'text' && (
                <Input
                  placeholder={question.title || 'Votre réponse...'}
                  disabled
                  style={{ background: '#f3f4f6' }}
                />
              )}
              {question.type === 'number' && (
                <Input
                  type="number"
                  placeholder={question.title || 'Entrez un nombre'}
                  disabled
                  style={{ background: '#f3f4f6' }}
                />
              )}
              {question.type === 'date' && (
                <Input
                  type="date"
                  disabled
                  style={{ background: '#f3f4f6' }}
                />
              )}
              {question.type === 'radio' && (
                <div>
                  {(question.options || ['Option 1', 'Option 2']).map((option, idx) => (
                    <div key={idx} style={{ marginBottom: 8 }}>
                      <label style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <input type="radio" name={`preview-${question.id}`} disabled />
                        <span style={{ fontSize: '14px' }}>{option}</span>
                      </label>
                    </div>
                  ))}
                </div>
              )}
              {question.type === 'multi-select' && (
                <div>
                  {(question.options || ['Option 1', 'Option 2']).map((option, idx) => (
                    <div key={idx} style={{ marginBottom: 8 }}>
                      <label style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <input type="checkbox" disabled />
                        <span style={{ fontSize: '14px' }}>{option}</span>
                      </label>
                    </div>
                  ))}
                </div>
              )}
              {question.type === 'select' && (
                <select disabled style={{
                  width: '100%',
                  padding: '8px 12px',
                  background: '#f3f4f6',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px'
                }}>
                  <option>Sélectionner une option...</option>
                  {(question.options || ['Option 1', 'Option 2']).map((option, idx) => (
                    <option key={idx}>{option}</option>
                  ))}
                </select>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Drag and Drop Question Builder Modal
function DndQuestionBuilderModal({ isOpen, onClose, questions, setQuestions, ficheId, context, fiche, setDndQuestions, dndQuestions }) {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000,
        padding: '20px'
      }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          width: '100vw',
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          overflow: 'hidden'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div style={{
          padding: '16px 24px',
          borderBottom: '1px solid #e5e7eb',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white'
        }}>
          <h2 style={{ margin: 0, fontSize: '20px', fontWeight: '600' }}>
            🎯 Constructeur de Questions Drag & Drop
          </h2>
          <Button
            onClick={onClose}
            variant="ghost"
            style={{
              color: 'white',
              fontSize: '24px',
              padding: '8px',
              minWidth: 'auto',
              height: 'auto'
            }}
          >
            ×
          </Button>
        </div>

        {/* Modal Content */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          padding: '16px',
          background: '#f8fafc'
        }}>
          <EnhancedDndQuestionBuilder
            questions={questions}
            setQuestions={setQuestions}
            ficheId={ficheId}
            context={context}
            fiche={fiche}
            setDndQuestions={setDndQuestions}
            dndQuestions={dndQuestions}
          />
        </div>

        {/* Modal Footer */}
        <div style={{
          padding: '12px 24px',
          borderTop: '1px solid #e5e7eb',
          background: 'white',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ fontSize: '14px', color: '#6b7280' }}>
            {questions.length} question{questions.length !== 1 ? 's' : ''} créée{questions.length !== 1 ? 's' : ''}
          </div>
          <div style={{ display: 'flex', gap: '12px' }}>
            <Button variant="outline" onClick={onClose}>
              Fermer
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FichesTravailCaracteristiquesTab;