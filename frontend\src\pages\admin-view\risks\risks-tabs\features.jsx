import { useState, useEffect } from "react";
import { useNavigate, useOutletContext } from "react-router-dom";
import { Loader2, Alert<PERSON>riangle, MessageSquare, Link2, Info, Gauge, BarChart3, Shield, Users } from "lucide-react";
import axios from "axios";
import { toast } from "sonner";
import { Button } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import { Textarea } from "../../../../components/ui/textarea";
import { Checkbox } from "../../../../components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../components/ui/select";
import { Combobox } from "../../../../components/ui/combobox";
import { Badge } from "../../../../components/ui/badge";
import ContributorsSection from "../../../../components/risk/ContributorsSection";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from 'react-i18next';

function RisksFeatures() {
  const { t } = useTranslation();
  const { risk: initialRisk, refreshRisk, referenceData } = useOutletContext();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const API_BASE_URL = getApiBaseUrl();
  // Helper function to get more vibrant color
  const getVibrantColor = (colorClass) => {
    // Extract base color (blue, green, yellow, orange, red, gray)
    const baseColor = colorClass.match(/bg-(blue|green|yellow|orange|red|gray)/)?.[1];
    return baseColor ? `bg-${baseColor}-500` : 'bg-gray-500';
  };

  // Helper functions for impact, DMR, and probability labels
  const getImpactLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const impactLabels = {
      '1': { label: 'Très Faible', color: 'bg-blue-100 text-blue-800' },
      '2': { label: 'Faible', color: 'bg-green-100 text-green-800' },
      '3': { label: 'Moyen', color: 'bg-yellow-100 text-yellow-800' },
      '4': { label: 'Élevé', color: 'bg-orange-100 text-orange-800' },
      '5': { label: 'Très Élevé', color: 'bg-red-100 text-red-800' }
    };
    return impactLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const getControlLevelLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const controlLevelLabels = {
      '1': { label: 'Très Fort', color: 'bg-green-100 text-green-800' },
      '4': { label: 'Fort', color: 'bg-blue-100 text-blue-800' },
      '9': { label: 'Moyen', color: 'bg-yellow-100 text-yellow-800' },
      '16': { label: 'Faible', color: 'bg-orange-100 text-orange-800' },
      '25': { label: 'Très Faible', color: 'bg-red-100 text-red-800' }
    };
    return controlLevelLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const getProbabilityLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const probabilityLabels = {
      '1': { label: 'Très Faible', color: 'bg-blue-100 text-blue-800' },
      '2': { label: 'Faible', color: 'bg-green-100 text-green-800' },
      '3': { label: 'Moyen', color: 'bg-yellow-100 text-yellow-800' },
      '4': { label: 'Élevé', color: 'bg-orange-100 text-orange-800' },
      '5': { label: 'Très Élevé', color: 'bg-red-100 text-red-800' }
    };
    return probabilityLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const _getAppetiteLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const appetiteLabels = {
      '1': { label: 'Très Faible', color: 'bg-blue-100 text-blue-800' },
      '2': { label: 'Faible', color: 'bg-green-100 text-green-800' },
      '3': { label: 'Moyen', color: 'bg-yellow-100 text-yellow-800' },
      '4': { label: 'Élevé', color: 'bg-orange-100 text-orange-800' },
      '5': { label: 'Très Élevé', color: 'bg-red-100 text-red-800' }
    };
    return appetiteLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const [risk, setRisk] = useState({
    riskID: "",
    name: "",
    code: "",
    methodOfIdentification: "",
    impact: "",
    DMR: "",
    probability: "",
    appetite: "",
    acceptance: false,
    avoidance: false,
    insurance: false,
    reduction: false,
    comment: "",
    mitigatingActionPlan: "",
    businessProcessID: "",
    organizationalProcessID: "",
    operationID: "",
    applicationID: "",
    entityID: "",
    riskTypeID: "",
    controlID: "",
    major: false,
  });

  // Use reference data directly from context
  const businessProcesses = referenceData?.businessProcesses || [];
  const organizationalProcesses = referenceData?.organizationalProcesses || [];
  const operations = referenceData?.operations || [];
  const applications = referenceData?.applications || [];
  const entities = referenceData?.entities || [];
  const riskTypes = referenceData?.riskTypes || [];
  const controls = referenceData?.controls || [];
  const _actionPlans = referenceData?.actionPlans || [];

  // Load risk data directly if needed
  const loadRiskData = async (riskID) => {
    if (!riskID) return;

    try {
      const response = await axios.get(`${API_BASE_URL}/risk/${riskID}`, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
      });

      if (response.data.success) {
        const riskData = response.data.data;
        return riskData;
      }
    } catch (error) {
      console.error("Error loading risk:", error);
      return null;
    }
  };

  // Initialize form data when risk is loaded
  useEffect(() => {
    const setupRisk = async () => {
      setIsLoading(true);

      let riskData = initialRisk;

      // If no initial risk data, try to load it
      if (!riskData && initialRisk?.riskID) {
        riskData = await loadRiskData(initialRisk.riskID);
      }

      if (riskData) {
      setRisk({
          ...riskData,
          impact: riskData.impact ? riskData.impact.toString() : "",
          DMR: riskData.DMR ? riskData.DMR.toString() : "",
          probability: riskData.probability ? riskData.probability.toString() : "",
          appetite: riskData.appetite ? riskData.appetite.toString() : "",
          acceptance: Boolean(riskData.acceptance),
          avoidance: Boolean(riskData.avoidance),
          insurance: Boolean(riskData.insurance),
          reduction: Boolean(riskData.reduction),
          major: Boolean(riskData.major),
          businessProcessID: riskData.businessProcessID || "",
          organizationalProcessID: riskData.organizationalProcessID || "",
          operationID: riskData.operationID || "",
          applicationID: riskData.applicationID || "",
          entityID: riskData.entityID || "",
          riskTypeID: riskData.riskTypeID || "",
          controlID: riskData.controlID || "",
          comment: riskData.comment || "",
          mitigatingActionPlan: riskData.mitigatingActionPlan || "",
        });
      }

      setIsLoading(false);
    };

    setupRisk();
  }, [initialRisk]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const riskData = {
        ...risk,
        impact: risk.impact ? parseInt(risk.impact) : null,
        DMR: risk.DMR ? parseInt(risk.DMR) : null,
        probability: risk.probability ? parseInt(risk.probability) : null,
        appetite: risk.appetite ? parseInt(risk.appetite) : null,
        acceptance: Boolean(risk.acceptance),
        avoidance: Boolean(risk.avoidance),
        insurance: Boolean(risk.insurance),
        reduction: Boolean(risk.reduction),
        major: Boolean(risk.major),
        // Ensure mitigatingActionPlan is included
        mitigatingActionPlan: risk.mitigatingActionPlan || null,
        businessProcessID: risk.businessProcessID || null,
        organizationalProcessID: risk.organizationalProcessID || null,
        operationID: risk.operationID || null,
        applicationID: risk.applicationID || null,
        entityID: risk.entityID || null,
        riskTypeID: risk.riskTypeID || null,
        controlID: risk.controlID || null,
      };

      const response = await axios.put(
        `${API_BASE_URL}/risk/${risk.riskID}`,
        riskData,
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
        }
      );

      if (response.data.success) {
        toast.success(t('admin.risks.features.success.updated', "Risk updated successfully"));
        refreshRisk();
        navigate(`/admin/risks/edit/${risk.riskID}`);
      }
    } catch (error) {
      toast.error(error.response?.data?.message || t('admin.risks.features.error.update', "Failed to update risk"));
      console.error("Error updating risk:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
          <span className="mt-4 text-gray-600">{t('admin.risks.features.loading', 'Loading risk data...')}</span>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <h2 className="text-xl font-semibold mb-6">{t('admin.risks.features.title', 'Edit Risk Details')}</h2>

      {/* Basic Information Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gray-50 px-4 py-3 border-b">
          <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <Info className="h-5 w-5 mr-2 text-blue-500" />
          {t('admin.risks.features.basic_information', 'Basic Information')}
        </h3>
        </div>

        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left half - Name and Code */}
            <div className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  {t('admin.risks.features.name', 'Name')}
                </label>
          <Input
                  id="name"
            value={risk.name}
            onChange={(e) => setRisk({ ...risk, name: e.target.value })}
                  className="mt-1"
          />
        </div>

              <div>
                <label htmlFor="code" className="block text-sm font-medium text-gray-700">
                  {t('admin.risks.features.code', 'Code')}
                </label>
          <Input
                  id="code"
                  value={risk.code}
            onChange={(e) => setRisk({ ...risk, code: e.target.value })}
                  className="mt-1"
          />
        </div>
            </div>

            {/* Right half - Method and Major Risk */}
            <div className="space-y-4">
              <div>
                <label htmlFor="methodOfIdentification" className="block text-sm font-medium text-gray-700">
                  {t('admin.risks.features.method_of_identification', 'Method of Identification')}
                </label>
                <Select
                  value={risk.methodOfIdentification}
                  onValueChange={(value) => setRisk({ ...risk, methodOfIdentification: value })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder={t('admin.risks.features.select_method', 'Select method')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="survey">{t('admin.risks.features.survey', 'Survey')}</SelectItem>
                    <SelectItem value="incident_database">{t('admin.risks.features.incident_database', 'Incident Database')}</SelectItem>
                    <SelectItem value="audit_mission">{t('admin.risks.features.audit_mission', 'Audit Mission')}</SelectItem>
                    <SelectItem value="workshop">{t('admin.risks.features.workshop', 'Workshop')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
            <Checkbox
              id="major"
              checked={risk.major}
              onCheckedChange={(checked) => setRisk({ ...risk, major: checked })}
            />
                <label htmlFor="major" className="text-sm font-medium text-gray-700">
                  {t('admin.risks.features.major_risk', 'Major Risk')}
            </label>
              </div>
            </div>
        </div>
      </div>
      </div>

      {/* Horizontal separator */}
      <div className="border-t border-gray-200 my-2"></div>

      {/* Risk Assessment Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-5">
        <h3 className="text-md font-medium text-gray-700 mb-4 flex items-center">
          <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
          {t('admin.risks.features.risk_assessment', 'Risk Assessment')}
        </h3>

        {/* Control Level only */}
      <div className="grid grid-cols-1 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-[#242A33]">{t('admin.risks.features.control_level', 'Control Level')}</label>
          <Select
            value={risk.DMR ? risk.DMR.toString() : ""}
            onValueChange={(value) => setRisk({ ...risk, DMR: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder={t('admin.risks.features.select_control_level', 'Select Control Level')}>
                {risk.DMR ? (
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full ${getVibrantColor(getControlLevelLabel(risk.DMR).color)} mr-2`}></div>
                    {getControlLevelLabel(risk.DMR).label}
                  </div>
                ) : t('admin.risks.features.select_control_level', 'Select Control Level')}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              {[1, 4, 9, 16, 25].map((level) => (
                <SelectItem key={level} value={level.toString()}>
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full ${getVibrantColor(getControlLevelLabel(level).color)} mr-2`}></div>
                    {getControlLevelLabel(level).label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      </div>

      {/* Contributors Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-5 mt-4">
        <h3 className="text-md font-medium text-gray-700 mb-4 flex items-center">
          <Users className="h-5 w-5 mr-2 text-indigo-600" />
          {t('admin.risks.features.contributors', 'Contributors')}
        </h3>

        <div className="mb-2">
          {risk.riskID ? (
            <ContributorsSection riskId={risk.riskID} />
          ) : (
            <p className="text-sm text-gray-500 py-2">{t('admin.risks.features.save_first', 'Save the risk first to add contributors')}</p>
          )}
        </div>
      </div>

      {/* Horizontal separator */}
      <div className="border-t border-gray-200 my-2"></div>

      {/* Related Information Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-5">
        <h3 className="text-md font-medium text-gray-700 mb-4 flex items-center">
          <Link2 className="h-5 w-5 mr-2 text-green-600" />
          {t('admin.risks.features.related_information', 'Related Information')}
        </h3>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-[#242A33]">{t('admin.risks.features.business_process', 'Business Process')}</label>
          <Combobox
            options={[
              { value: "none", label: t('admin.risks.features.none', 'None') },
              ...businessProcesses.map((bp) => ({
                value: bp.businessProcessID,
                label: bp.name
              }))
            ]}
            value={risk.businessProcessID || "none"}
            onChange={(value) =>
              setRisk({ ...risk, businessProcessID: value === "none" ? null : value })
            }
            placeholder={t('admin.risks.features.select_business_process', 'Select business process')}
            searchPlaceholder={t('admin.risks.features.search_business_processes', 'Search business processes...')}
            className="w-full"
            popoverClassName="w-full"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-[#242A33]">{t('admin.risks.features.organizational_process', 'Organizational Process')}</label>
          <Combobox
            options={[
              { value: "none", label: t('admin.risks.features.none', 'None') },
              ...organizationalProcesses.map((op) => ({
                value: op.organizationalProcessID,
                label: op.name
              }))
            ]}
            value={risk.organizationalProcessID || "none"}
            onChange={(value) =>
              setRisk({ ...risk, organizationalProcessID: value === "none" ? null : value })
            }
            placeholder={t('admin.risks.features.select_organizational_process', 'Select organizational process')}
            searchPlaceholder={t('admin.risks.features.search_organizational_processes', 'Search organizational processes...')}
            className="w-full"
            popoverClassName="w-full"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-[#242A33]">{t('admin.risks.features.operation', 'Operation')}</label>
          <Combobox
            options={[
              { value: "none", label: t('admin.risks.features.none', 'None') },
              ...operations.map((op) => ({
                value: op.operationID,
                label: op.name
              }))
            ]}
            value={risk.operationID || "none"}
            onChange={(value) =>
              setRisk({ ...risk, operationID: value === "none" ? null : value })
            }
            placeholder={t('admin.risks.features.select_operation', 'Select operation')}
            searchPlaceholder={t('admin.risks.features.search_operations', 'Search operations...')}
            className="w-full"
            popoverClassName="w-full"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-[#242A33]">{t('admin.risks.features.application', 'Application')}</label>
          <Combobox
            options={[
              { value: "none", label: t('admin.risks.features.none', 'None') },
              ...applications.map((app) => ({
                value: app.applicationID,
                label: app.name
              }))
            ]}
            value={risk.applicationID || "none"}
            onChange={(value) =>
              setRisk({ ...risk, applicationID: value === "none" ? null : value })
            }
            placeholder={t('admin.risks.features.select_application', 'Select application')}
            searchPlaceholder={t('admin.risks.features.search_applications', 'Search applications...')}
            className="w-full"
            popoverClassName="w-full"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-[#242A33]">{t('admin.risks.features.entity', 'Entity')}</label>
          <Combobox
            options={[
              { value: "none", label: t('admin.risks.features.none', 'None') },
              ...entities.map((entity) => ({
                value: entity.entityID,
                label: entity.name
              }))
            ]}
            value={risk.entityID || "none"}
            onChange={(value) =>
              setRisk({ ...risk, entityID: value === "none" ? null : value })
            }
            placeholder={t('admin.risks.features.select_entity', 'Select entity')}
            searchPlaceholder={t('admin.risks.features.search_entities', 'Search entities...')}
            className="w-full"
            popoverClassName="w-full"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-[#242A33]">{t('admin.risks.features.risk_type', 'Risk Type')}</label>
          <Combobox
            options={[
              { value: "none", label: t('admin.risks.features.none', 'None') },
              ...riskTypes.map((rt) => ({
                value: rt.riskTypeID,
                label: rt.name
              }))
            ]}
            value={risk.riskTypeID || "none"}
            onChange={(value) =>
              setRisk({ ...risk, riskTypeID: value === "none" ? null : value })
            }
            placeholder={t('admin.risks.features.select_risk_type', 'Select risk type')}
            searchPlaceholder={t('admin.risks.features.search_risk_types', 'Search risk types...')}
            className="w-full"
            popoverClassName="w-full"
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-[#242A33]">{t('admin.risks.features.control', 'Control')}</label>
          <Combobox
            options={[
              { value: "none", label: t('admin.risks.features.none', 'None') },
              ...controls.map((control) => ({
                value: control.controlID,
                label: control.name
              }))
            ]}
            value={risk.controlID || "none"}
            onChange={(value) =>
              setRisk({ ...risk, controlID: value === "none" ? null : value })
            }
            placeholder={t('admin.risks.features.select_control', 'Select control')}
            searchPlaceholder={t('admin.risks.features.search_controls', 'Search controls...')}
            className="w-full"
            popoverClassName="w-full"
          />
        </div>
      </div>
      </div>

      {/* Horizontal separator */}
      <div className="border-t border-gray-200 my-2"></div>

      {/* Comment Section - Moved to the end */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-5">
        <div className="space-y-2">
          <label className="text-sm font-medium text-[#242A33] flex items-center">
            <MessageSquare className="h-4 w-4 mr-2 text-gray-600" />
            {t('admin.risks.features.comment', 'Comment')}
          </label>
          <Textarea
            value={risk.comment || ""}
            onChange={(e) => setRisk({ ...risk, comment: e.target.value })}
            className="min-h-[100px] border-gray-200"
            placeholder={t('admin.risks.features.comment_placeholder', 'Add your comments here...')}
          />
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4 pt-6 mt-6 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={() => navigate(`/admin/risks/edit/${risk.riskID}`)}
          className="px-6"
        >
          {t('admin.risks.features.cancel', 'Cancel')}
        </Button>
        <Button
          type="submit"
          className="bg-[#F62D51] hover:bg-red-700 px-6"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('admin.risks.features.saving', 'Saving...')}
            </>
          ) : (
            t('admin.risks.features.save_changes', 'Save Changes')
          )}
        </Button>
      </div>
    </form>
  );
}

export default RisksFeatures;
