module.exports = (sequelize, DataTypes) => {
  const Attachment = sequelize.define('Attachment', {
    attachmentID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: false
    },
    filePath: {
      type: DataTypes.STRING,
      allowNull: false
    },
    uploadDate: {
      type: DataTypes.DATE,
      allowNull: false
    },
    type: {
      type: DataTypes.ENUM('business-document', 'external-reference'),
      allowNull: false
    },
    incidentID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Incident',
        key: 'incidentID'
      }
    }
  }, {
    tableName: 'Attachment',
    timestamps: false
  });

  Attachment.associate = (models) => {
    Attachment.belongsTo(models.Incident, {
      foreignKey: 'incidentID',
      targetKey: 'incidentID',
      as: 'incident'
    });
  };

  return Attachment;
};
