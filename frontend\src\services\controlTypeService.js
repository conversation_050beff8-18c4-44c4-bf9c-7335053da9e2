import axios from 'axios';
import { getAuthHeaders } from '@/utils/auth-headers';
import { getApiBaseUrl } from "@/utils/api-config";

const API_BASE_URL = getApiBaseUrl();
const API_URL = `${API_BASE_URL}/controlTypes`;

// Get all control types
const getAllControlTypes = async () => {
  try {
    const headers = getAuthHeaders();
    const response = await axios.get(API_URL, {
      withCredentials: true,
      headers: headers
    });
    return response.data;
  } catch (error) {
    console.error('Error in getAllControlTypes service:', error.response?.data || error.message);
    throw error;
  }
};

// Get control type by ID
const getControlTypeById = async (id) => {
  try {
    const headers = getAuthHeaders();
    const response = await axios.get(`${API_URL}/${id}`, {
      withCredentials: true,
      headers: headers
    });

    console.log(`Control type ${id} fetched:`, response.data);

    // Ensure parentControlTypeID is properly handled
    if (response.data.data) {
      console.log('Parent control type ID in response:', response.data.data.parentControlTypeID);
    }

    return response.data;
  } catch (error) {
    console.error('Error in getControlTypeById service:', error.response?.data || error.message);
    throw error;
  }
};

// Create new control type
const createControlType = async (controlTypeData) => {
  try {
    // Ensure parentControlTypeID is null if it's empty or "none"
    const sanitizedData = {
      ...controlTypeData,
      parentControlTypeID: (!controlTypeData.parentControlTypeID ||
                           controlTypeData.parentControlTypeID === "" ||
                           controlTypeData.parentControlTypeID === "none")
                           ? null
                           : controlTypeData.parentControlTypeID
    };

    console.log('Creating control type with sanitized data:', sanitizedData);
    const headers = getAuthHeaders();

    const response = await axios.post(API_URL, sanitizedData, {
      withCredentials: true,
      headers: headers
    });

    return response.data;
  } catch (error) {
    console.error('Error in createControlType service:', error.response?.data || error.message);
    if (error.response?.data?.parent?.detail) {
      console.error('Database error detail:', error.response.data.parent.detail);
    }
    throw error;
  }
};

// Update control type
const updateControlType = async (id, controlTypeData) => {
  try {
    // Ensure parentControlTypeID is null if it's empty or "none"
    const sanitizedData = {
      ...controlTypeData,
      parentControlTypeID: (!controlTypeData.parentControlTypeID ||
                           controlTypeData.parentControlTypeID === "" ||
                           controlTypeData.parentControlTypeID === "none")
                           ? null
                           : controlTypeData.parentControlTypeID
    };

    console.log(`Updating control type ${id} with sanitized data:`, sanitizedData);
    const headers = getAuthHeaders();

    const response = await axios.put(`${API_URL}/${id}`, sanitizedData, {
      withCredentials: true,
      headers: headers
    });

    console.log('Update response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error in updateControlType service:', error.response?.data || error.message);
    if (error.response?.data?.parent?.detail) {
      console.error('Database error detail:', error.response.data.parent.detail);
    }
    throw error;
  }
};

// Delete control type
const deleteControlType = async (id) => {
  try {
    const headers = getAuthHeaders();
    const response = await axios.delete(`${API_URL}/${id}`, {
      withCredentials: true,
      headers: headers
    });
    return response.data;
  } catch (error) {
    console.error('Error in deleteControlType service:', error.response?.data || error.message);
    throw error;
  }
};

const controlTypeService = {
  getAllControlTypes,
  getControlTypeById,
  createControlType,
  updateControlType,
  deleteControlType
};

export default controlTypeService;
