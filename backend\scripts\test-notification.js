require('dotenv').config();
const { Sequelize } = require('sequelize');
const db = require('../models');

async function testNotificationSystem() {
  try {
    console.log('[Test Script] Testing database connection...');
    await db.sequelize.authenticate();
    console.log('[Test Script] Database connection established successfully.');
    
    // Print all available models
    console.log('[Test Script] Available models:', Object.keys(db));
    
    // Check if Notification model exists
    if (!db.Notification) {
      console.error('[Test Script] Notification model not found! Check model loading.');
      return;
    }
    
    console.log('[Test Script] Checking if Notifications table exists...');
    try {
      // Try to query the table
      const result = await db.sequelize.query(
        "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'Notifications')",
        { type: Sequelize.QueryTypes.SELECT }
      );
      console.log('[Test Script] Table check result:', result);
      
      if (!result[0].exists) {
        console.error('[Test Script] Notifications table does not exist! Attempting to create...');
        
        // Create the table if it doesn't exist
        await db.sequelize.getQueryInterface().createTable('Notifications', {
          id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            autoIncrement: true
          },
          user_id: {
            type: Sequelize.INTEGER,
            allowNull: false
          },
          type: {
            type: Sequelize.STRING,
            allowNull: false
          },
          entity_id: {
            type: Sequelize.INTEGER,
            allowNull: false
          },
          entity_name: {
            type: Sequelize.STRING,
            allowNull: true
          },
          message: {
            type: Sequelize.TEXT,
            allowNull: false
          },
          assigned_by: {
            type: Sequelize.STRING,
            allowNull: true
          },
          is_read: {
            type: Sequelize.BOOLEAN,
            defaultValue: false
          },
          created_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false
          },
          updated_at: {
            type: Sequelize.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            allowNull: false
          }
        });
        
        console.log('[Test Script] Table created successfully!');
      } else {
        console.log('[Test Script] Notifications table exists.');
      }
    } catch (error) {
      console.error('[Test Script] Error checking/creating table:', error);
      return;
    }
    
    // Get a user ID to use for the test notification
    console.log('[Test Script] Getting a user ID for test notification...');
    let userId;
    try {
      const user = await db.User.findOne();
      if (!user) {
        console.error('[Test Script] No users found in database!');
        return;
      }
      userId = user.id;
      console.log(`[Test Script] Using user ID: ${userId}`);
    } catch (error) {
      console.error('[Test Script] Error finding user:', error);
      return;
    }
    
    // Create a test notification
    console.log('[Test Script] Creating test notification...');
    try {
      const newNotification = await db.Notification.create({
        user_id: userId,
        type: 'test',
        entity_id: 1,
        entity_name: 'Test Entity',
        message: 'This is a test notification from the test script',
        assigned_by: 'Test Script',
        is_read: false
      });
      
      console.log('[Test Script] Test notification created successfully!', newNotification.toJSON());
      
      // Count notifications
      const count = await db.Notification.count();
      console.log(`[Test Script] Total notifications in database: ${count}`);
      
      // Get all notifications for the user
      const userNotifications = await db.Notification.findAll({
        where: { user_id: userId },
        order: [['created_at', 'DESC']]
      });
      
      console.log(`[Test Script] Found ${userNotifications.length} notifications for user ${userId}:`);
      userNotifications.forEach(notification => {
        console.log(`- ID: ${notification.id}, Type: ${notification.type}, Message: ${notification.message.substring(0, 30)}...`);
      });
    } catch (error) {
      console.error('[Test Script] Error creating test notification:', error);
    }
    
    console.log('[Test Script] Test complete!');
  } catch (error) {
    console.error('[Test Script] Test failed:', error);
  } finally {
    process.exit(0);
  }
}

testNotificationSystem(); 