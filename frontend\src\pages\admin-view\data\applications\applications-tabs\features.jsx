import { useState, useEffect } from "react";
import { useOutletContext, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { Loader2, ChevronLeft } from "lucide-react";
import { updateApplication, getApplicationById } from "@/store/slices/applicationSlice";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

function ApplicationFeatures() {
  const { application } = useOutletContext();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const { isLoading } = useSelector((state) => state.application);

  const [formData, setFormData] = useState({
    name: "",
    comment: ""
  });

  // Set form data when application is loaded
  useEffect(() => {
    if (application) {
      setFormData({
        name: application.name || "",
        comment: application.comment || ""
      });
    }
  }, [application]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const updateData = {
        applicationID: application.applicationID,
        name: formData.name,
        comment: formData.comment
      };

      await dispatch(updateApplication({
        id: application.applicationID,
        applicationData: updateData
      })).unwrap();

      // Refresh the current application data
      dispatch(getApplicationById(application.applicationID));
    } catch (error) {
      toast.error(error.message || t('admin.applications.error.update_failed', 'Failed to update application'));
    }
  };

  const handleGoBack = () => {
    navigate("/admin/data/applications");
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 bg-white rounded-lg shadow-sm p-6">
      <h2 className="text-xl font-semibold mb-6">{t('admin.applications.features.title', 'Edit Application')}</h2>
      <div className="grid grid-cols-1 gap-6">
        <div className="space-y-2">
          <Label htmlFor="name">{t('admin.applications.form.name', 'Name')} *</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder={t('admin.applications.form.name_placeholder', 'Enter application name')}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="comment">{t('admin.applications.form.comment', 'Comment')}</Label>
          <Textarea
            id="comment"
            name="comment"
            value={formData.comment}
            onChange={handleInputChange}
            placeholder={t('admin.applications.form.comment_placeholder', 'Enter comment')}
            className="min-h-[100px]"
          />
        </div>
      </div>

      <div className="flex justify-end gap-4 mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={handleGoBack}
        >
          {t('common.buttons.cancel', 'Cancel')}
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-[#F62D51] hover:bg-red-700"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('common.saving', 'Saving...')}
            </>
          ) : (
            t('common.buttons.save', 'Save Changes')
          )}
        </Button>
      </div>
    </form>
  );
}

export default ApplicationFeatures;