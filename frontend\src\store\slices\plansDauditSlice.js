import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { toast } from "sonner";
import { getAuthHeaders } from "@/utils/auth-headers";
import { getApiBaseUrl } from '../utils/api-config';

// API URL for plans d'audit
const API_URL = `${getApiBaseUrl()}/audit/plans-daudit`;

// Mock data for plans d'audit
const mockPlansData = [
  { 
    id: 1, 
    name: "Audit des processus financiers", 
    status: "Completed", 
    date: "2023-12-15", 
    year: "2023", 
    department: "Finance",
    description: "Évaluation complète des processus financiers et des contrôles internes",
    objectives: "Identifier les risques financiers et évaluer l'efficacité des contrôles",
    scope: "Tous les processus financiers, y compris la comptabilité, la trésorerie et les rapports financiers",
    resources: "2 auditeurs seniors, 1 expert financier",
    startDate: "2023-10-01",
    endDate: "2023-12-15",
    findings: 12,
    recommendations: 8,
    missions: [
      {
        id: 1,
        name: "Revue des processus comptables",
        status: "Completed",
        startDate: "2023-10-01",
        endDate: "2023-10-15",
        auditor: "Jean Dupont",
        findings: 5,
        recommendations: 3
      },
      {
        id: 2,
        name: "Audit de la trésorerie",
        status: "Completed",
        startDate: "2023-10-20",
        endDate: "2023-11-05",
        auditor: "Marie Martin",
        findings: 4,
        recommendations: 2
      },
      {
        id: 3,
        name: "Revue des rapports financiers",
        status: "Completed",
        startDate: "2023-11-10",
        endDate: "2023-12-01",
        auditor: "Pierre Durand",
        findings: 3,
        recommendations: 3
      }
    ]
  },
  { 
    id: 2, 
    name: "Audit de conformité réglementaire", 
    status: "In Progress", 
    date: "2024-03-10", 
    year: "2024", 
    department: "Compliance",
    description: "Vérification de la conformité aux réglementations bancaires et financières",
    objectives: "Assurer la conformité aux lois et réglementations applicables",
    scope: "Toutes les activités soumises à la réglementation financière",
    resources: "3 auditeurs, 1 expert juridique",
    startDate: "2024-01-15",
    endDate: "2024-03-30",
    findings: 5,
    recommendations: 7,
    missions: [
      {
        id: 4,
        name: "Audit des contrôles de conformité",
        status: "In Progress",
        startDate: "2024-01-15",
        endDate: "2024-02-15",
        auditor: "Sophie Leroy",
        findings: 2,
        recommendations: 1
      },
      {
        id: 5,
        name: "Revue des procédures de sécurité",
        status: "Planned",
        startDate: "2024-02-20",
        endDate: "2024-03-15",
        auditor: "Thomas Bernard",
        findings: 0,
        recommendations: 0
      }
    ]
  }
];

// Get all plans d'audit
export const getPlansAudit = createAsyncThunk(
  "plansAudit/getPlansAudit",
  async (_, { rejectWithValue }) => {
    try {
      // Simulate API call with mock data
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(mockPlansData);
        }, 500);
      });
      
      // Real API call (commented for now)
      // const response = await axios.get(API_URL, {
      //   withCredentials: true,
      //   headers: getAuthHeaders()
      // });
      // return response.data.data;
    } catch (error) {
      console.error("Error in getPlansAudit:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to fetch plans d'audit");
    }
  }
);

// Get plan d'audit by ID
export const getPlanAuditById = createAsyncThunk(
  "plansAudit/getPlanAuditById",
  async (id, { rejectWithValue }) => {
    try {
      // Simulate API call with mock data
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          const plan = mockPlansData.find(p => p.id === parseInt(id));
          if (plan) {
            resolve(plan);
          } else {
            reject(new Error("Plan d'audit not found"));
          }
        }, 500);
      });
      
      // Real API call (commented for now)
      // const response = await axios.get(`${API_URL}/${id}`, {
      //   withCredentials: true,
      //   headers: getAuthHeaders()
      // });
      // return response.data.data;
    } catch (error) {
      console.error("Error in getPlanAuditById:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to fetch plan d'audit");
    }
  }
);

// Create new plan d'audit
export const createPlanAudit = createAsyncThunk(
  "plansAudit/createPlanAudit",
  async (planData, { rejectWithValue }) => {
    try {
      // Simulate API call with mock data
      return new Promise((resolve) => {
        setTimeout(() => {
          const newPlan = {
            ...planData,
            id: mockPlansData.length + 1,
            missions: []
          };
          resolve(newPlan);
        }, 500);
      });
      
      // Real API call (commented for now)
      // const response = await axios.post(API_URL, planData, {
      //   withCredentials: true,
      //   headers: getAuthHeaders()
      // });
      // return response.data.data;
    } catch (error) {
      console.error("Error in createPlanAudit:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to create plan d'audit");
    }
  }
);

// Update plan d'audit
export const updatePlanAudit = createAsyncThunk(
  "plansAudit/updatePlanAudit",
  async ({ id, planData }, { rejectWithValue }) => {
    try {
      // Simulate API call with mock data
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          const planIndex = mockPlansData.findIndex(p => p.id === parseInt(id));
          if (planIndex !== -1) {
            const updatedPlan = {
              ...mockPlansData[planIndex],
              ...planData,
              id: parseInt(id)
            };
            resolve(updatedPlan);
          } else {
            reject(new Error("Plan d'audit not found"));
          }
        }, 500);
      });
      
      // Real API call (commented for now)
      // const response = await axios.put(`${API_URL}/${id}`, planData, {
      //   withCredentials: true,
      //   headers: getAuthHeaders()
      // });
      // return response.data.data;
    } catch (error) {
      console.error("Error in updatePlanAudit:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to update plan d'audit");
    }
  }
);

// Delete plan d'audit
export const deletePlanAudit = createAsyncThunk(
  "plansAudit/deletePlanAudit",
  async (id, { rejectWithValue }) => {
    try {
      // Simulate API call with mock data
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(id);
        }, 500);
      });
      
      // Real API call (commented for now)
      // await axios.delete(`${API_URL}/${id}`, {
      //   withCredentials: true,
      //   headers: getAuthHeaders()
      // });
      // return id;
    } catch (error) {
      console.error("Error in deletePlanAudit:", error.response?.data || error.message);
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      }
      return rejectWithValue(error.response?.data?.message || "Failed to delete plan d'audit");
    }
  }
);

// Initial state
const initialState = {
  plansAudit: [],
  currentPlanAudit: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  error: null,
};

// Create slice
const plansAuditSlice = createSlice({
  name: "plansAudit",
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.error = null;
    },
    clearCurrentPlanAudit: (state) => {
      state.currentPlanAudit = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all plans d'audit
      .addCase(getPlansAudit.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getPlansAudit.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.plansAudit = action.payload;
      })
      .addCase(getPlansAudit.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      
      // Get plan d'audit by ID
      .addCase(getPlanAuditById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getPlanAuditById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentPlanAudit = action.payload;
      })
      .addCase(getPlanAuditById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      
      // Create plan d'audit
      .addCase(createPlanAudit.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createPlanAudit.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.plansAudit.push(action.payload);
      })
      .addCase(createPlanAudit.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      
      // Update plan d'audit
      .addCase(updatePlanAudit.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updatePlanAudit.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentPlanAudit = action.payload;
        state.plansAudit = state.plansAudit.map((plan) =>
          plan.id === action.payload.id ? action.payload : plan
        );
      })
      .addCase(updatePlanAudit.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      
      // Delete plan d'audit
      .addCase(deletePlanAudit.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deletePlanAudit.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.plansAudit = state.plansAudit.filter((plan) => plan.id !== action.payload);
      })
      .addCase(deletePlanAudit.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      });
  },
});

export const { reset, clearCurrentPlanAudit } = plansAuditSlice.actions;
export default plansAuditSlice.reducer;
