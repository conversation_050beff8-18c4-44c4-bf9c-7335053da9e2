import { useState, useEffect, useCallback } from "react";
import {
  ChevronDown,
  ChevronUp,
  Plus,
  TrendingDown,
  TrendingUp,
  Undo,
  PiggyBank,
  Trash2,
  <PERSON>ader2,
  Refresh<PERSON><PERSON>
} from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { Checkbox } from "../ui/checkbox";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "../ui/dialog";
import { Input } from "../ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { currencies } from "../../config";
import { toast } from "sonner";
import axios from "axios";
import { getApiBaseUrl } from '../../utils/api-config';
import { useTranslation } from "react-i18next";
// API Base URL - use dynamic URL from utility function
const API_BASE_URL = getApiBaseUrl();
const CATEGORIES = {
  LOSSES: 'losses',
  GAINS: 'gains',
  RECOVERIES: 'recoveries',
  PROVISIONS: 'provisions'
};

const CATEGORY_LABELS = {
  [CATEGORIES.LOSSES]: { label: 'Losses', icon: <TrendingDown className="h-4 w-4" /> },
  [CATEGORIES.GAINS]: { label: 'Gains', icon: <TrendingUp className="h-4 w-4" /> },
  [CATEGORIES.RECOVERIES]: { label: 'Recoveries', icon: <Undo className="h-4 w-4" /> },
  [CATEGORIES.PROVISIONS]: { label: 'Provisions', icon: <PiggyBank className="h-4 w-4" /> }
};

// Helper function to safely calculate total from an array
const safeArraySum = (array) => {
  if (!Array.isArray(array)) return 0;
  return array.reduce((sum, item) => sum + (Number(item.amount) || 0), 0);
};

// Helper functions for ID standardization
const getStandardIdPrefix = (category) => {
  switch(category) {
    case CATEGORIES.LOSSES: return 'loss_';
    case CATEGORIES.GAINS: return 'gain_';
    case CATEGORIES.RECOVERIES: return 'recovery_';
    case CATEGORIES.PROVISIONS: return 'provision_';
    default: return `${category.slice(0, -1)}_`;
  }
};

const normalizeId = (id, category) => {
  // If the ID doesn't match our expected format, standardize it
  const standardPrefix = getStandardIdPrefix(category);
  
  // If the ID already starts with the standard prefix, return it as is
  if (id && id.startsWith(standardPrefix)) return id;
  
  // If it's a different format (like 'losse_' instead of 'loss_'), generate a new one
  return `${standardPrefix}${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
};

export function FinancialAnalysisSectionV2({
  incident,
  handleInputChange
}) {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(true);
  const [activeCategory, setActiveCategory] = useState(CATEGORIES.LOSSES);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [newEntry, setNewEntry] = useState({
    name: '',
    amount: '',
    localAmount: '',
    currency: 'XOF' // default currency
  });
  const [financialData, setFinancialData] = useState({
    losses: [],
    gains: [],
    recoveries: [],
    provisions: []
  });
  const [totals, setTotals] = useState({
    losses: 0,
    gains: 0,
    recoveries: 0,
    provisions: 0
  });

  // Fetch financial data from the server
  const fetchFinancialData = useCallback(async () => {
    if (!incident?.incidentID) return;

    setIsLoading(true);
    try {
      const response = await axios.get(
        `${API_BASE_URL}/financial-v2/incident/${incident.incidentID}`,
        { 
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
        }
      );

      if (response.data.success) {
        // Transform backend financial entries to match the frontend format
        const transformedData = {};
        Object.keys(response.data.data).forEach(category => {
          transformedData[category] = response.data.data[category].map(entry => {
            const idField = `${category.slice(0, -1)}ID`; // Convert 'losses' to 'lossID', etc.
            // Use the standardized ID format if the original ID doesn't match our expected format
            const originalId = entry[idField];
            const normalizedId = normalizeId(originalId, category);
            
            return {
              id: normalizedId,
            name: entry.name,
            amount: Number(entry.amount) || 0,
            localAmount: entry.localAmount ? Number(entry.localAmount) : 0,
            currency: entry.currency || 'XOF'
            };
          });
        });

        console.log("Fetched and normalized financial data:", transformedData);
        setFinancialData(transformedData);

        // Calculate totals
        const newTotals = {
          losses: safeArraySum(transformedData.losses),
          gains: safeArraySum(transformedData.gains),
          recoveries: safeArraySum(transformedData.recoveries),
          provisions: safeArraySum(transformedData.provisions)
        };
        setTotals(newTotals);

        // Update incident totals if they're different from what we calculated
        if (incident.grossLoss !== newTotals.losses) {
          handleInputChange('grossLoss', newTotals.losses);
        }
        if (incident.recoveries !== newTotals.recoveries) {
          handleInputChange('recoveries', newTotals.recoveries);
        }
        if (incident.provisions !== newTotals.provisions) {
          handleInputChange('provisions', newTotals.provisions);
        }
      }
    } catch (error) {
      console.error("Error fetching financial data:", error);
      
      // Improved error handling
      let errorMessage = "Failed to load financial data";
      
      if (error.code === 'ERR_NETWORK') {
        errorMessage = "Network error: Please check if the server is running at port 5001";
      } else if (error.response) {
        // Server responded with a status other than 200
        errorMessage = `Server error: ${error.response.status} ${error.response.data?.message || error.message}`;
      }
      
      toast.error(errorMessage);
      
      // Fall back to using incident data if available
      if (incident) {
        const initialData = {
          losses: incident.losses || [],
          gains: incident.gains || [],
          recoveries: incident.financialRecoveries || [],
          provisions: incident.financialProvisions || []
        };
        
        console.log("Using local financial data from incident:", initialData);
        setFinancialData(initialData);
        
        const newTotals = {
          losses: safeArraySum(initialData.losses),
          gains: safeArraySum(initialData.gains),
          recoveries: safeArraySum(initialData.recoveries),
          provisions: safeArraySum(initialData.provisions)
        };
        setTotals(newTotals);
      }
    } finally {
      setIsLoading(false);
    }
  }, [incident?.incidentID, handleInputChange, incident]);

  // Fetch financial data when component mounts or incident ID changes
  useEffect(() => {
    fetchFinancialData();
  }, [fetchFinancialData]);

  // Initialize financial data from incident prop if available
  useEffect(() => {
    if (incident) {
      const initialData = {
        losses: incident.losses || [],
        gains: incident.gains || [],
        recoveries: incident.financialRecoveries || [],
        provisions: incident.financialProvisions || []
      };

      // Only update if we have data and haven't already fetched from the server
      if (
        (initialData.losses.length > 0 ||
         initialData.gains.length > 0 ||
         initialData.recoveries.length > 0 ||
         initialData.provisions.length > 0) &&
        (financialData.losses.length === 0 &&
         financialData.gains.length === 0 &&
         financialData.recoveries.length === 0 &&
         financialData.provisions.length === 0)
      ) {
        console.log("Initializing financial data from incident prop:", initialData);
        setFinancialData(initialData);

        // Calculate totals
        const newTotals = {
          losses: safeArraySum(initialData.losses),
          gains: safeArraySum(initialData.gains),
          recoveries: safeArraySum(initialData.recoveries),
          provisions: safeArraySum(initialData.provisions)
        };
        setTotals(newTotals);
      }
    }
  }, [incident, financialData]);

  // Calculate totals whenever financial data changes
  useEffect(() => {
    if (financialData) {
        const newTotals = {
        losses: safeArraySum(financialData.losses || []),
        gains: safeArraySum(financialData.gains || []),
        recoveries: safeArraySum(financialData.recoveries || []),
        provisions: safeArraySum(financialData.provisions || [])
        };
        setTotals(newTotals);

      // Update incident totals if they're different from what we calculated
      if (incident && newTotals.losses !== incident.grossLoss) {
        handleInputChange('grossLoss', newTotals.losses);
      }
      if (incident && newTotals.recoveries !== incident.recoveries) {
        handleInputChange('recoveries', newTotals.recoveries);
      }
      if (incident && newTotals.provisions !== incident.provisions) {
        handleInputChange('provisions', newTotals.provisions);
      }
    }
  }, [financialData, incident, handleInputChange]);

  // Validate financial data structure before component operations
  useEffect(() => {
    // Ensure all financial data categories exist as arrays
    const ensureFinancialArrays = () => {
      const updatedData = { ...financialData };
      let hasChanges = false;

      Object.values(CATEGORIES).forEach(category => {
        if (!Array.isArray(updatedData[category])) {
          updatedData[category] = [];
          hasChanges = true;
        }
      });

      if (hasChanges) {
        console.log("Initializing missing financial data arrays");
        setFinancialData(updatedData);
      }
    };

    ensureFinancialArrays();
  }, [financialData]);

  // Save financial data to the server with an optional data parameter
  const saveFinancialData = async (dataToSave = null) => {
    if (!incident?.incidentID) return;

    // Use provided data or current state
    const data = dataToSave || financialData;

    setIsSaving(true);
    try {
      console.log("Sending financial data to server:", data);
      const response = await axios.put(
        `${API_BASE_URL}/financial-v2/incident/${incident.incidentID}`,
        data,
        {
          withCredentials: true,
          headers: { "Content-Type": "application/json" },
        }
      );

      if (response.data.success) {
        toast.success("Financial data saved successfully");
        // Don't fetch immediately after saving as it may return stale data
        // The next time the user visits this page, data will be fetched fresh
      } else {
        toast.error("Failed to save financial data: " + (response.data.message || "Unknown error"));
      }
    } catch (error) {
      console.error("Error saving financial data:", error);
      
      // Improved error handling for save function
      let errorMessage = "Failed to save financial data";
      
      if (error.code === 'ERR_NETWORK') {
        errorMessage = "Network error: Please check if the server is running at port 5001";
      } else if (error.response) {
        // Server responded with an error status
        errorMessage = `Server error: ${error.response.status} ${error.response.data?.message || error.message}`;
      }
      
      toast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  const handleAddItem = () => {
    if (!newEntry.name || !newEntry.amount) {
      toast.error("Please fill in at least the name and amount fields");
      return;
    }

    // Use the standardized helper function for ID prefix
    const idPrefix = getStandardIdPrefix(activeCategory);

    const newItem = {
      id: `${idPrefix}${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
      name: newEntry.name,
      amount: Number(newEntry.amount) || 0,
      localAmount: Number(newEntry.localAmount) || 0,
      currency: newEntry.currency || 'XOF'
    };

    console.log(`Adding new ${activeCategory} entry:`, newItem);

    // Update financial data - append to existing array rather than replacing it
    const updatedData = {
      ...financialData,
      [activeCategory]: [...(financialData[activeCategory] || []), newItem]
    };
    setFinancialData(updatedData);

    // Update totals
    setTotals(prev => ({
      ...prev,
      [activeCategory]: prev[activeCategory] + (Number(newItem.amount) || 0)
    }));

    setNewEntry({ name: '', amount: '', localAmount: '', currency: 'XOF' });
    setIsModalOpen(false);
    toast.success(`New ${activeCategory} entry added successfully`);
    
    // Automatically save to the database
    saveFinancialData(updatedData);
  };

  const handleDeleteItem = (id) => {
    console.log(`Deleting item: ${id} in ${activeCategory}`);
    
    // Find the item to delete
    const itemToDelete = financialData[activeCategory]?.find(item => item.id === id);
    
    if (!itemToDelete) {
      console.error(`Item with id ${id} not found in ${activeCategory}`);
      return;
    }

    // Calculate the new total
    const deletedAmount = Number(itemToDelete.amount) || 0;

    // Create a deep copy of the current data to avoid reference issues
    const newFinancialData = JSON.parse(JSON.stringify(financialData));
    
    // Fix for losses and recoveries - ensure we're only removing the specific item
    if (activeCategory === CATEGORIES.LOSSES || activeCategory === CATEGORIES.RECOVERIES) {
      // Make sure we're preserving the same ID format
      if (Array.isArray(newFinancialData[activeCategory])) {
        // Keep only the rows that don't match the ID we want to delete
        newFinancialData[activeCategory] = newFinancialData[activeCategory].filter(
          item => item.id !== id
        );
      }
    } else {
      // Standard deletion for other categories
      newFinancialData[activeCategory] = Array.isArray(newFinancialData[activeCategory])
        ? newFinancialData[activeCategory].filter(item => item.id !== id)
        : [];
    }
    
    console.log(`Updated ${activeCategory} data:`, newFinancialData[activeCategory]);
    
    // Update financial data state with the new object
    setFinancialData(newFinancialData);

    // Update totals
    setTotals(prev => ({
      ...prev,
      [activeCategory]: Math.max(0, prev[activeCategory] - deletedAmount)
    }));

    toast.success(`Item deleted successfully`);
    
    // Automatically save to the database with the updated data
    saveFinancialData(newFinancialData);
  };

  return (
    <div className="border rounded-lg">
      <button
        type="button"
        className="w-full flex items-center p-4 bg-gray-50 rounded-t-lg"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center gap-2">
          {isOpen ? (
            <ChevronUp className="h-5 w-5" />
          ) : (
            <ChevronDown className="h-5 w-5" />
          )}
          <span className="text-lg font-medium">{t('admin.incidents.financial.title')}</span>
        </div>
      </button>

      {isOpen && (
        <div className="p-4">
          {/* Financial Categories Section */}
          <div className="space-y-6">
            {/* Financial Summary Section */}
            <div className="border rounded-lg p-4 mb-6">
              <h3 className="text-lg font-medium mb-4">{t('admin.incidents.financial.summary')}</h3>

              <div className="grid grid-cols-4 gap-x-8 gap-y-6 w-full">
                {/* Currency */}
                <div className="space-y-2 w-full">
                  <label className="text-sm font-medium">{t('admin.incidents.financial.currency')}</label>
                  <Select
                    value={incident.currency || 'XOF'}
                    onValueChange={(value) => handleInputChange('currency', value)}
                    className="w-full"
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      {currencies.map((currency) => (
                        <SelectItem key={currency.code} value={currency.code}>
                          {currency.code} - {currency.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Gross Loss */}
                <div className="space-y-2 w-full">
                  <label className="text-sm font-medium">{t('admin.incidents.financial.grossLoss')}</label>
                  <Input
                    type="number"
                    value={totals.losses || 0}
                    readOnly
                    placeholder="0"
                    className="w-full bg-gray-50"
                  />
                </div>

                {/* Recoveries */}
                <div className="space-y-2 w-full">
                  <label className="text-sm font-medium">{t('admin.incidents.financial.recoveries')}</label>
                  <Input
                    type="number"
                    value={totals.recoveries || 0}
                    readOnly
                    placeholder="0"
                    className="w-full bg-gray-50"
                  />
                </div>

                {/* Provisions */}
                <div className="space-y-2 w-full">
                  <label className="text-sm font-medium">{t('admin.incidents.financial.provisions')}</label>
                  <Input
                    type="number"
                    value={totals.provisions || 0}
                    readOnly
                    placeholder="0"
                    className="w-full bg-gray-50"
                  />
                </div>
              </div>
            </div>

            {/* Financial Analysis Section */}
            <div className="border rounded-lg p-4">
              <h3 className="text-lg font-medium mb-4">
                {t('admin.incidents.financial.analysisTitle')}
              </h3>

              {/* Updated Category Tabs */}
              <div className="flex gap-2 mb-4">
                {Object.entries(CATEGORY_LABELS).map(([key, { label, icon }]) => (
                  <button
                    key={key}
                    type="button"
                    onClick={(e) => {
                      e.preventDefault();
                      // Reset selected rows when changing category
                      setActiveCategory(key);
                      console.log(`Changed to category: ${key}`);
                    }}
                    className={`px-4 py-2 rounded-md transition-colors flex items-center gap-2 ${
                      activeCategory === key
                        ? 'bg-red-50 text-red-500 border border-red-200'
                        : 'bg-gray-50 text-gray-700 border border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    {icon}
                    {label}
                  </button>
                ))}
              </div>

              {/* Toolbar */}
              <div className="flex gap-2 mb-4">
                <Button
                  type="button"
                  className="bg-[#F62D51] hover:bg-red-700"
                  onClick={() => setIsModalOpen(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  {t('admin.incidents.financial.newEntry')}
                </Button>
              </div>

              {/* Add Entry Modal */}
              <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>
                      {t('admin.incidents.financial.addNewEntry', { category: CATEGORY_LABELS[activeCategory].label })}
                    </DialogTitle>
                  </DialogHeader>

                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">{t('admin.incidents.financial.name')}</label>
                      <Input
                        value={newEntry.name}
                        onChange={(e) => setNewEntry(prev => ({ ...prev, name: e.target.value }))}
                        placeholder={t('admin.incidents.financial.entryNamePlaceholder')}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">{t('admin.incidents.financial.amount')}</label>
                      <Input
                        type="number"
                        value={newEntry.amount}
                        onChange={(e) => setNewEntry(prev => ({ ...prev, amount: e.target.value }))}
                        placeholder={t('admin.incidents.financial.amountPlaceholder')}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">{t('admin.incidents.financial.localAmount')}</label>
                      <div className="flex gap-2">
                        <Input
                          type="number"
                          className="w-1/2"
                          value={newEntry.localAmount}
                          onChange={(e) => setNewEntry(prev => ({ ...prev, localAmount: e.target.value }))}
                          placeholder={t('admin.incidents.financial.localAmountPlaceholder')}
                        />
                        <Select
                          value={newEntry.currency}
                          onValueChange={(value) => setNewEntry(prev => ({ ...prev, currency: value }))}
                          className="w-1/2"
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                          <SelectContent>
                            {currencies.map((currency) => (
                              <SelectItem key={currency.code} value={currency.code}>
                                {currency.code} - {currency.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsModalOpen(false)}
                    >
                      {t('admin.incidents.financial.cancel')}
                    </Button>
                    <Button
                      type="button"
                      className="bg-[#F62D51] hover:bg-red-700"
                      onClick={handleAddItem}
                    >
                      {t('admin.incidents.financial.add')}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              {/* Table */}
              {isLoading ? (
                <div className="flex justify-center items-center py-12 border rounded-lg">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                </div>
              ) : (
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.incidents.financial.name')}</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.incidents.financial.amount')}</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.incidents.financial.localAmount')}</th>
                        <th className="w-[80px] px-4 py-3 text-left text-sm font-medium text-gray-600">{t('admin.incidents.financial.actions')}</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {(financialData[activeCategory]?.length > 0) ? (
                        financialData[activeCategory].map((item) => (
                          <tr key={item.id || `${activeCategory}_${Math.random()}`} className="hover:bg-gray-50">
                            <td className="px-4 py-3 text-sm">{item.name}</td>
                            <td className="px-4 py-3 text-sm">{Number(item.amount).toLocaleString()}</td>
                            <td className="px-4 py-3 text-sm">
                              {item.localAmount ? `${Number(item.localAmount).toLocaleString()} ${item.currency || incident.currency}` : '-'}
                            </td>
                            <td className="px-4 py-3 text-sm">
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                                onClick={() => handleDeleteItem(item.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan="4" className="px-4 py-8 text-center text-gray-500">
                            {t('admin.incidents.financial.noEntriesFound')}
                          </td>
                        </tr>
                      )}
                    </tbody>
                    {financialData[activeCategory]?.length > 0 && (
                      <tfoot className="bg-gray-50">
                        <tr>
                          <td colSpan="2" className="px-4 py-3 text-right font-medium">{t('admin.incidents.financial.total')}:</td>
                          <td className="px-4 py-3 font-medium">
                            {totals[activeCategory].toLocaleString()} {incident.currency}
                          </td>
                          <td></td>
                        </tr>
                      </tfoot>
                    )}
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
