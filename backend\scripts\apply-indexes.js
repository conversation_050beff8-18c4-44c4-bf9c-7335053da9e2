const { Sequelize } = require('sequelize');
const config = require('../config/config.js');

async function applyIndexes() {
  const sequelize = new Sequelize(config.production);
  
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Database connection established.');
    
    console.log('Applying performance indexes...');
    
    // FicheDeTravail indexes
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_fichedetravail_auditactivityid ON "FicheDeTravail" ("auditActivityID");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_fichedetravail_auditmissionid ON "FicheDeTravail" ("auditMissionID");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_fichedetravail_name ON "FicheDeTravail" ("name");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_fichedetravail_createdat ON "FicheDeTravail" ("createdAt");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_fichedetravail_updatedat ON "FicheDeTravail" ("updatedAt");');
    
    // FicheDeTest indexes
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_fichedetest_fichedetravailid ON "FicheDeTest" ("ficheDeTravailID");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_fichedetest_responsableid ON "FicheDeTest" ("responsableID");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_fichedetest_titre ON "FicheDeTest" ("titre");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_fichedetest_elementtrouve ON "FicheDeTest" ("elementTrouve");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_fichedetest_signe ON "FicheDeTest" ("signe");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_fichedetest_datesignature ON "FicheDeTest" ("dateSignature");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_fichedetest_createdat ON "FicheDeTest" ("createdAt");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_fichedetest_updatedat ON "FicheDeTest" ("updatedAt");');
    
    // Questions indexes
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_questions_fichedetravailid ON "Questions" ("ficheDeTravailID");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_questions_orderindex ON "Questions" ("order_index");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_questions_fichedetravailid_orderindex ON "Questions" ("ficheDeTravailID", "order_index");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_questions_createdat ON "Questions" ("createdAt");');
    await sequelize.query('CREATE INDEX IF NOT EXISTS idx_questions_updatedat ON "Questions" ("updatedAt");');
    
    console.log('✅ All performance indexes applied successfully!');
    
  } catch (error) {
    console.error('❌ Error applying indexes:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the script
applyIndexes(); 