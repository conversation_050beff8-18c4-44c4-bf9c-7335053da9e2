import { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Loader2, Trash2, ArrowUpDown, Workflow, Filter, ChevronUp } from 'lucide-react';
import { useSelector } from 'react-redux';
import { hasPermission } from '@/store/auth-slice';
import PageHeader from '@/components/ui/page-header';
import axios from 'axios';
import { getApiBaseUrl } from "@/utils/api-config";
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import TablePagination from "../../../../components/ui/table-pagination";
import FilterPanel from "../../../../components/ui/filter-panel";
import bpsIcon from '@/assets/BPS.png';

function BusinessProcessesManagement() {
  const navigate = useNavigate();
  const canCreate = useSelector(state => hasPermission(state, 'create'));
  const canDelete = useSelector(state => hasPermission(state, 'delete'));
  const [businessProcesses, setBusinessProcesses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProcesses, setSelectedProcesses] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [sortConfig, setSortConfig] = useState({ key: 'businessProcessID', direction: 'asc' });
  const API_BASE_URL = getApiBaseUrl();
  // Filter states
  const [filters, setFilters] = useState({
    parentBusinessProcessID: 'all',
  });

  // Clear all filters
  const clearFilters = () => {
    console.log('Clearing all filters');
    const clearedFilters = {
      parentBusinessProcessID: 'all',
    };
    setFilters(clearedFilters);
    console.log('Current filters before clearing:', filters);
    console.log('Filters cleared to:', clearedFilters);
  };

  const [newBusinessProcess, setNewBusinessProcess] = useState({
    name: '',
    code: '',
    comment: '',
    parentBusinessProcessID: null,
  });

  // Fetch business processes on component mount
  useEffect(() => {
    fetchBusinessProcesses();
  }, []);

  // Fetch business processes from API
  const fetchBusinessProcesses = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${API_BASE_URL}/businessProcesses`, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
      });
      // Log the response to debug
      console.log('API Response:', response.data);
      // Ensure the response data is an array; fallback to empty array if not
      const processes = response.data?.success ? response.data.data : [];
      if (!Array.isArray(processes)) {
        console.error('Expected an array for business processes, but got:', processes);
        setBusinessProcesses([]);
      } else {
        setBusinessProcesses(processes);
      }
    } catch (error) {
      console.error('Error fetching business processes:', error);
      toast.error('Failed to load business processes');
      setBusinessProcesses([]); // Fallback to empty array on error
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission for creating a new business process
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const response = await axios.post(`${API_BASE_URL}/businessProcesses`, newBusinessProcess, {
        withCredentials: true,
        headers: { "Content-Type": "application/json" },
      });
      // Assuming the response contains the newly created process in response.data.data
      const newProcess = response.data?.success ? response.data.data : response.data;
      setBusinessProcesses([...businessProcesses, newProcess]);
      setNewBusinessProcess({
        name: '',
        code: '',
        comment: '',
        parentBusinessProcessID: null,
      });
      setIsOpen(false);
      toast.success('Business process created successfully');
    } catch (error) {
      console.error('Error creating business process:', error);
      toast.error('Failed to create business process');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle row click to navigate to edit page
  const handleRowClick = (id) => {
    // Get the current path to determine if we're in audit view
    const isAuditView = window.location.pathname.startsWith('/audit');
    const basePath = isAuditView ? '/audit' : '/admin';
    // Navigate to the business process edit page with overview tab
    navigate(`${basePath}/processes/business-processes/${id}/overview`);
  };

  // Handle checkbox selection for business processes
  const handleSelectProcess = (id) => {
    setSelectedProcesses((prev) => {
      if (prev.includes(id)) {
        return prev.filter((processId) => processId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  // Handle select all checkbox
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedProcesses(currentBusinessProcesses.map((process) => process.businessProcessID));
    } else {
      setSelectedProcesses([]);
    }
  };

  // Handle deletion of selected business processes
  const handleDeleteSelected = async () => {
    if (selectedProcesses.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedProcesses.length} business process(es)?`)) {
      setLoading(true);
      const failedDeletions = [];

      // Helper function to attempt deletion with retry
      const attemptDelete = async (id) => {
        try {
          await axios.delete(`${API_BASE_URL}/businessProcesses/${id}`, {
            withCredentials: true,
            headers: { "Content-Type": "application/json" },
          });
          return true;
        } catch (error) {
          console.error(`Error deleting business process ${id}:`, error);
          return false;
        }
      };

      try {
        // Process deletions in smaller batches
        const batchSize = 3;
        for (let i = 0; i < selectedProcesses.length; i += batchSize) {
          const batch = selectedProcesses.slice(i, i + batchSize);
          await Promise.all(
            batch.map(async (id) => {
              const success = await attemptDelete(id);
              if (!success) failedDeletions.push(id);
              // Add delay between individual deletions within batch
              await new Promise(resolve => setTimeout(resolve, 500));
              return success;
            })
          );

          // Add delay between batches
          if (i + batchSize < selectedProcesses.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        // Refresh the list after all deletions are attempted
        await fetchBusinessProcesses();

        // Clear selected business processes
        setSelectedProcesses([]);

        // Show appropriate toast message
        if (failedDeletions.length > 0) {
          toast.error(`Failed to delete ${failedDeletions.length} business process(es): ${failedDeletions.join(', ')}`);
        } else {
          toast.success("All selected business processes deleted successfully");
        }
      } catch (error) {
        console.error('Error in batch deletion:', error);
        toast.error('An error occurred during deletion');
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle sorting
  const handleSort = (key) => {
    setSortConfig((prevConfig) => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Filter business processes based on search query and filters
  const filteredBusinessProcesses = useMemo(() => {
    if (!Array.isArray(businessProcesses)) return [];

    return businessProcesses.filter((process) => {
      // Apply search query filter
      if (searchQuery) {
        const matchesSearch = Object.values(process).some(value =>
          value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
        );
        if (!matchesSearch) return false;
      }

      // Apply parent business process filter
      if (filters.parentBusinessProcessID && filters.parentBusinessProcessID !== 'all') {
        console.log('Parent Business Process filter:', {
          filterValue: filters.parentBusinessProcessID,
          processValue: process.parentBusinessProcessID,
          match: process.parentBusinessProcessID === filters.parentBusinessProcessID
        });
        if (process.parentBusinessProcessID !== filters.parentBusinessProcessID) return false;
      }

      return true;
    });
  }, [businessProcesses, searchQuery, filters]);

  // Sort the filtered business processes
  const sortedBusinessProcesses = [...filteredBusinessProcesses].sort((a, b) => {
    if (!sortConfig.key) return 0;

    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];

    if (aValue === null || aValue === undefined) return 1;
    if (bValue === null || bValue === undefined) return -1;

    if (sortConfig.key === 'parentBusinessProcessID') {
      const aParent = businessProcesses.find(p => p.businessProcessID === aValue);
      const bParent = businessProcesses.find(p => p.businessProcessID === bValue);
      return sortConfig.direction === 'asc'
        ? (aParent?.name || '').localeCompare(bParent?.name || '')
        : (bParent?.name || '').localeCompare(aParent?.name || '');
    }

    if (sortConfig.direction === 'asc') {
      return aValue.toString().localeCompare(bValue.toString(), undefined, { numeric: true });
    } else {
      return bValue.toString().localeCompare(aValue.toString(), undefined, { numeric: true });
    }
  });

  const totalPages = Math.ceil(sortedBusinessProcesses.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentBusinessProcesses = sortedBusinessProcesses.slice(indexOfFirstItem, indexOfLastItem);

  // Handle page change
  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // Define columns configuration
  const columns = [
    { key: 'name', label: 'Name', sortable: true },
    { key: 'code', label: 'Code', sortable: true },
    { key: 'parentBusinessProcessID', label: 'Parent Process', sortable: true },
    { key: 'comment', label: 'Comment', sortable: true },
  ];

  return (
    <div className="p-6">
      <PageHeader
        title="Business Processes Management"
        description="Define and manage business processes to map your organization's core activities and workflows."
        section="Processes"
        currentPage="Business"
        icon={Workflow}
        searchPlaceholder="Search business processes..."
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
      />

      {/* Filter Panel */}
      <div className="mb-6 filter-panel-container">
        <FilterPanel
          filters={[
            {
              id: "parentBusinessProcessID",
              label: "Parent Business Process",
              component: (
                <Select
                  value={filters.parentBusinessProcessID}
                  onValueChange={(value) => setFilters({ ...filters, parentBusinessProcessID: value })}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder="Select parent process" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    {businessProcesses && businessProcesses.map((process) => (
                      <SelectItem key={process.businessProcessID} value={process.businessProcessID}>
                        {process.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ),
            },
          ]}
          activeFilters={filters}
          onClearFilters={clearFilters}
        />
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end items-center gap-3 mb-4">
        {selectedProcesses.length > 0 && (
          <Button
            variant="outline"
            className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
              canDelete && !loading
                ? 'bg-[#F62D51]/90 text-white hover:bg-[#F62D51] hover:shadow-md'
                : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
            } flex items-center gap-2 px-6 py-2 font-semibold`}
            onClick={handleDeleteSelected}
            disabled={!canDelete || loading}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                Delete ({selectedProcesses.length})
              </>
            )}
          </Button>
        )}
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          {canCreate && (
            <DialogTrigger asChild>
              <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add Process
              </Button>
            </DialogTrigger>
          )}
          <DialogContent className="max-w-3xl p-8">
            <DialogHeader>
              <DialogTitle>Add New Business Process</DialogTitle>
              <DialogDescription>
                Fill in the details to create a new business process.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-8">
              <div className="grid grid-cols-2 gap-6">
                <div className="flex flex-col">
                  <Label htmlFor="name" className="mb-2">Name *</Label>
                  <Input
                    id="name"
                    value={newBusinessProcess.name}
                    onChange={(e) => setNewBusinessProcess({
                      ...newBusinessProcess,
                      name: e.target.value,
                    })}
                    placeholder="Enter business process name"
                    required
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="code" className="mb-2">Code</Label>
                  <Input
                    id="code"
                    value={newBusinessProcess.code}
                    onChange={(e) => setNewBusinessProcess({
                      ...newBusinessProcess,
                      code: e.target.value,
                    })}
                    placeholder="Enter business process code"
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="parentBusinessProcess" className="mb-2">Parent Business Process</Label>
                  <Select
                    value={newBusinessProcess.parentBusinessProcessID || 'none'}
                    onValueChange={(value) => setNewBusinessProcess({
                      ...newBusinessProcess,
                      parentBusinessProcessID: value === 'none' ? null : value,
                    })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select parent process" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {businessProcesses.map((process) => (
                        <SelectItem key={process.businessProcessID} value={process.businessProcessID}>
                          {process.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex flex-col">
                  <Label htmlFor="comment" className="mb-2">Comment</Label>
                  <Textarea
                    id="comment"
                    value={newBusinessProcess.comment || ''}
                    onChange={(e) => setNewBusinessProcess({
                      ...newBusinessProcess,
                      comment: e.target.value,
                    })}
                    placeholder="Enter comment"
                    className="w-full"
                    rows={4}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-[#F62D51] hover:bg-red-700"
                  disabled={submitting}
                >
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Business Process'
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {loading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : (
        <>
          <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                      <Checkbox
                        checked={selectedProcesses.length === currentBusinessProcesses.length && currentBusinessProcesses.length > 0}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all"
                      />
                    </th>
                    {columns.map((column) => (
                      <th
                        key={column.key}
                        className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100"
                        onClick={() => column.sortable && handleSort(column.key)}
                      >
                        <div className="flex items-center gap-1">
                          {column.label}
                          {sortConfig.key === column.key && (
                            <ArrowUpDown className="w-4 h-4" />
                          )}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {currentBusinessProcesses.map((process, index) => (
                    <tr
                      key={process.businessProcessID}
                      className={`hover:bg-gray-50 cursor-pointer ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                      onClick={() => handleRowClick(process.businessProcessID)}
                    >
                      <td className="px-6 py-4">
                        <Checkbox
                          checked={selectedProcesses.includes(process.businessProcessID)}
                          onCheckedChange={() => handleSelectProcess(process.businessProcessID)}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </td>

                      <td className="px-6 py-4 text-sm whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          <img
                            src={bpsIcon}
                            alt="business process"
                            className="w-5 h-5 object-contain"
                          />
                          <span className="font-bold text-[#242A33]">{process.name}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                        {process.code || '-'}
                      </td>
                      <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                        {process.parentBusinessProcessID ? (
                          businessProcesses.find(p => p.businessProcessID === process.parentBusinessProcessID)?.name || '-'
                        ) : '-'}
                      </td>
                      <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">
                        {process.comment || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">
                {filteredBusinessProcesses.length} process{filteredBusinessProcesses.length !== 1 ? 'es' : ''} found
              </span>
              {/* Show filter badge if any filter is active */}
              {filters.parentBusinessProcessID !== 'all' && (
                <Badge
                  variant="outline"
                  className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 cursor-pointer"
                  onClick={() => {
                    // Toggle filter panel visibility
                    const filterPanel = document.querySelector('.filter-panel-container');
                    if (filterPanel) {
                      filterPanel.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                >
                  Filtered
                  <ChevronUp className="ml-1 h-3 w-3" />
                </Badge>
              )}
            </div>
          </div>

          <TablePagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={filteredBusinessProcesses.length}
            onPageChange={handlePageChange}
            onItemsPerPageChange={(value) => {
              setItemsPerPage(value);
              setCurrentPage(1);
            }}
            startIndex={indexOfFirstItem}
            endIndex={indexOfLastItem}
          />
        </>
      )}
    </div>
  );
}

export default BusinessProcessesManagement;
