const db = require('../models');

async function debugControlActivityLogs() {
  try {
    console.log('🔍 Debugging Control Activity Logs...\n');

    // Connect to database
    await db.sequelize.authenticate();
    console.log('✅ Database connected successfully\n');

    // Check if ControlActivityLog table exists
    console.log('📋 Checking if ControlActivityLog table exists...');
    try {
      const tableInfo = await db.sequelize.query(
        `SELECT column_name, data_type, is_nullable 
         FROM information_schema.columns 
         WHERE table_name = 'ControlActivityLogs' 
         ORDER BY ordinal_position;`,
        { type: db.sequelize.QueryTypes.SELECT }
      );

      if (tableInfo.length === 0) {
        console.log('❌ ControlActivityLogs table does not exist!');
        
        // Check for similar table names
        const tables = await db.sequelize.query(
          `SELECT table_name FROM information_schema.tables 
           WHERE table_name LIKE '%activity%' OR table_name LIKE '%Activity%' 
           AND table_schema = 'public';`,
          { type: db.sequelize.QueryTypes.SELECT }
        );
        
        console.log('📋 Tables containing "activity":', tables);
      } else {
        console.log('✅ ControlActivityLogs table found with columns:');
        tableInfo.forEach(col => {
          console.log(`   - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
        });
      }
    } catch (error) {
      console.log('❌ Error checking table:', error.message);
    }

    // Check ENUM values
    console.log('\n📋 Checking ENUM values for type column...');
    try {
      const enumValues = await db.sequelize.query(
        `SELECT unnest(enum_range(NULL::enum_ControlActivityLogs_type)) AS enum_value;`,
        { type: db.sequelize.QueryTypes.SELECT }
      );
      
      console.log('✅ Current ENUM values:');
      enumValues.forEach(row => {
        console.log(`   - ${row.enum_value}`);
      });
    } catch (error) {
      console.log('❌ Error checking ENUM values:', error.message);
    }

    // Check if ControlActivityLog model is loaded
    console.log('\n📋 Checking if ControlActivityLog model is loaded...');
    if (db.ControlActivityLog) {
      console.log('✅ ControlActivityLog model is loaded');
      
      // Try to count existing records
      try {
        const count = await db.ControlActivityLog.count();
        console.log(`📊 Total activity logs in database: ${count}`);
        
        // Get recent logs
        const recentLogs = await db.ControlActivityLog.findAll({
          limit: 5,
          order: [['timestamp', 'DESC']],
          attributes: ['id', 'control_id', 'user', 'type', 'timestamp', 'details']
        });
        
        if (recentLogs.length > 0) {
          console.log('\n📋 Recent activity logs:');
          recentLogs.forEach(log => {
            console.log(`   - ${log.timestamp}: ${log.user} ${log.type} on ${log.control_id}`);
          });
        } else {
          console.log('\n⚠️  No activity logs found in database');
        }
      } catch (error) {
        console.log('❌ Error querying activity logs:', error.message);
      }
    } else {
      console.log('❌ ControlActivityLog model is NOT loaded');
    }

    // Test creating a sample activity log
    console.log('\n🧪 Testing activity log creation...');
    try {
      // Find a control to test with
      const testControl = await db.Control.findOne({
        attributes: ['controlID', 'name'],
        limit: 1
      });
      
      if (testControl) {
        console.log(`📋 Using test control: ${testControl.controlID} - ${testControl.name}`);
        
        // Try to create a test activity log
        const testLog = await db.ControlActivityLog.create({
          control_id: testControl.controlID,
          user: 'Debug Test User',
          type: 'update',
          field: 'name',
          old_value: 'Old Value',
          new_value: 'New Value',
          details: 'Debug test activity log'
        });
        
        console.log('✅ Test activity log created successfully:', testLog.id);
        
        // Clean up - delete the test log
        await testLog.destroy();
        console.log('✅ Test activity log cleaned up');
        
      } else {
        console.log('❌ No controls found to test with');
      }
    } catch (error) {
      console.log('❌ Error creating test activity log:', error.message);
      console.log('Full error:', error);
    }

    // Check if activity logging functions are working
    console.log('\n🧪 Testing activity logging functions...');
    try {
      const { logCreationActivity, logUpdateActivities } = require('../controllers/data/control-activity-controller');
      
      const testControl = await db.Control.findOne({
        attributes: ['controlID', 'name'],
        limit: 1
      });
      
      if (testControl) {
        // Test creation activity logging
        const creationResult = await logCreationActivity(testControl, 'Debug Test User');
        console.log(`✅ Creation activity logging test: ${creationResult ? 'SUCCESS' : 'FAILED'}`);
        
        // Test update activity logging
        const updateResult = await logUpdateActivities(
          testControl, 
          { name: 'Updated Name', code: 'Updated Code' }, 
          'Debug Test User'
        );
        console.log(`✅ Update activity logging test: ${updateResult.length > 0 ? 'SUCCESS' : 'FAILED'}`);
        console.log(`   Changes logged: ${updateResult.join(', ')}`);
      }
    } catch (error) {
      console.log('❌ Error testing activity logging functions:', error.message);
    }

    console.log('\n🎯 Debug completed!');

  } catch (error) {
    console.error('❌ Error during debug:', error);
  } finally {
    await db.sequelize.close();
  }
}

// Run the debug
debugControlActivityLogs();
