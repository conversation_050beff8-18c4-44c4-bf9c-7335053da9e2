import React, { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search, Edit, ExternalLink, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useNavigate, useParams } from "react-router-dom";
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';
import TablePagination from "@/components/ui/table-pagination";

// Import recommandation icon
import recommandationIcon from '@/assets/recommandation.png';

function RecommandationsTab() {
  const { planId, missionAuditId } = useParams();
  const navigate = useNavigate();

  const [recommendations, setRecommendations] = useState([]);
  const [allUsers, setAllUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Fetch all users from the system first
  useEffect(() => {
    const fetchAllUsers = async () => {
      try {
        const response = await axios.get(`${getApiBaseUrl()}/users`, {
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' }
        });

        if (response.data.success && response.data.data) {
          setAllUsers(response.data.data);
        }
      } catch (error) {
        console.error("Error fetching all users:", error);
        // Don't show toast error for this as it's not critical
      }
    };

    fetchAllUsers();
  }, []);

  useEffect(() => {
    const fetchRecommendations = async () => {
      if (!planId || !missionAuditId) return;
      try {
        setLoading(true);
        const response = await axios.get(`${getApiBaseUrl()}/audit-recommendations/audit-plan/${planId}`, {
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' }
        });
        if (response.data.success && response.data.data) {
          const recommendationsData = response.data.data.filter(r => r.missionId === missionAuditId);

          // Map recommendations with user data from cached users
          const recommendationsWithResponsable = recommendationsData.map(recommendation => {
            if (recommendation.responsableId) {
              // Find user in cached users
              const user = allUsers.find(u =>
                u.id === recommendation.responsableId ||
                u.id === parseInt(recommendation.responsableId) ||
                u.id.toString() === recommendation.responsableId.toString()
              );

              if (user) {
                return {
                  ...recommendation,
                  responsable: user
                };
              } else {
                // User not found in cache, add fallback
                console.warn(`User ${recommendation.responsableId} not found in cached users`);
                return {
                  ...recommendation,
                  responsable: {
                    id: recommendation.responsableId,
                    username: `Utilisateur ${recommendation.responsableId}`,
                    email: 'Email non disponible'
                  }
                };
              }
            }
            return recommendation;
          });

          setRecommendations(recommendationsWithResponsable);
        } else {
          setRecommendations([]);
        }
      } catch (error) {
        console.error("Error fetching recommendations:", error);
        if (error.response?.status === 404) {
          setRecommendations([]);
        } else {
          toast.error("Erreur lors du chargement des recommandations");
        }
      } finally {
        setLoading(false);
      }
    };
    fetchRecommendations();
  }, [planId, missionAuditId, allUsers]);

  // Filter recommendations based on search query
  const filteredRecommendations = recommendations.filter(rec =>
    rec.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    rec.constatName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    rec.activityName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    rec.missionName?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Pagination
  const totalPages = Math.ceil(filteredRecommendations.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, filteredRecommendations.length);
  const currentRecommendations = filteredRecommendations.slice(startIndex, endIndex);

  const handleRowClick = (recommendation) => {
    navigate(`/audit/plans-daudit/${planId}/missions-audits/${recommendation.missionId}/activites/${recommendation.activityId}/constats/${recommendation.constatId}/recommandations/${recommendation.id}`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <img src={recommandationIcon} alt="Recommandation" className="h-6 w-6 mr-2 flex-shrink-0" />
        <h2 className="text-xl font-semibold text-gray-900">Recommandations</h2>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Rechercher par recommandation, constat, activité ou mission..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Table */}
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table className="min-w-full">
              <TableHeader>
                <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recommandation</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Responsable</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Constat</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activité</TableHead>
                  <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mission</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="divide-y divide-gray-200">
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} className="px-4 py-10 text-center">
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#F62D51]"></div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : currentRecommendations.length > 0 ? (
                  currentRecommendations.map((recommendation, index) => (
                    <TableRow
                      key={`${recommendation.id}-${recommendation.constatId}-${index}`}
                      className="hover:bg-gray-50/50 cursor-pointer"
                      onClick={() => handleRowClick(recommendation)}
                    >
                      <TableCell className="px-4 py-3 text-sm font-medium text-gray-900 whitespace-nowrap">
                        <div className="flex items-center">
                          <img src={recommandationIcon} alt="Recommandation" className="h-4 w-4 mr-2 flex-shrink-0" />
                          <span>{recommendation.name || 'Sans nom'}</span>
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">
                        {recommendation.responsable ? (
                          <div className="flex items-center">
                            <div className="h-6 w-6 rounded-full bg-[#F62D51] text-white text-xs flex items-center justify-center mr-2">
                              {recommendation.responsable.username ? recommendation.responsable.username.charAt(0).toUpperCase() : 'U'}
                            </div>
                            <span className={recommendation.responsable.username?.startsWith('Utilisateur') ? 'text-gray-500 italic' : ''}>
                              {recommendation.responsable.username || recommendation.responsable.email || 'Utilisateur inconnu'}
                            </span>
                          </div>
                        ) : recommendation.responsableId ? (
                          <div className="flex items-center">
                            <div className="h-6 w-6 rounded-full bg-gray-400 text-white text-xs flex items-center justify-center mr-2">
                              U
                            </div>
                            <span className="text-gray-500 italic">Utilisateur {recommendation.responsableId}</span>
                          </div>
                        ) : (
                          <span className="text-gray-400">Non assigné</span>
                        )}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{recommendation.constatName || '-'}</TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{recommendation.activityName || '-'}</TableCell>
                      <TableCell className="px-4 py-3 text-sm whitespace-nowrap">{recommendation.missionName || '-'}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="px-4 py-10 text-center text-sm text-gray-500">
                      Aucune recommandation trouvée.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <TablePagination
        totalPages={totalPages}
        currentPage={currentPage}
        onPageChange={setCurrentPage}
        hasItems={filteredRecommendations.length > 0}
        totalItems={filteredRecommendations.length}
        itemsPerPage={itemsPerPage}
        startIndex={startIndex}
        endIndex={endIndex}
      />
    </div>
  );
}

export default RecommandationsTab;