import { createRoot } from 'react-dom/client'
import './index.css'
import './styles/global.css'
import App from './App.jsx'
import { BrowserRouter } from 'react-router-dom'
import { Provider } from 'react-redux'
import store from './store/store'
import { Toaster } from 'sonner'
import './utils/axios-config' // Import axios config with interceptors
import { ThemeProvider } from './components/theme-provider' // Import our ThemeProvider
import './i18n' // Import i18n configuration
import { LanguageProvider } from './contexts/LanguageContext' // Import language provider

// Log authentication token on startup
console.log('Initial auth token present:', !!localStorage.getItem('authToken'));

createRoot(document.getElementById('root')).render(
  <ThemeProvider>
    <BrowserRouter>
      <Provider store={store}>
        <LanguageProvider>
          <App />
          <Toaster theme="light" />
        </LanguageProvider>
      </Provider>
    </BrowserRouter>
  </ThemeProvider>,
)
