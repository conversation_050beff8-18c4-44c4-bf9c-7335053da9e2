module.exports = (sequelize, DataTypes) => {
  const CampagneResponse = sequelize.define('CampagneResponse', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    campagneID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Campagnes',
        key: 'campagneID'
      }
    },
    questionID: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'controlQuestions',
        key: 'id'
      }
    },
    userID: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    sampleNumber: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    response_value: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Stores the response value (text, number, date, array for multiple choice, etc.)'
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'CampagneResponses',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['campagneID', 'questionID', 'userID', 'sampleNumber'],
        name: 'unique_campagne_question_user_sample'
      },
      {
        fields: ['campagneID']
      },
      {
        fields: ['questionID']
      },
      {
        fields: ['userID']
      }
    ]
  });

  CampagneResponse.associate = function(models) {
    CampagneResponse.belongsTo(models.Campagne, {
      foreignKey: 'campagneID',
      as: 'campagne'
    });
    
    CampagneResponse.belongsTo(models.ControlQuestion, {
      foreignKey: 'questionID',
      as: 'question'
    });
    
    CampagneResponse.belongsTo(models.User, {
      foreignKey: 'userID',
      as: 'user'
    });
  };

  return CampagneResponse;
};
