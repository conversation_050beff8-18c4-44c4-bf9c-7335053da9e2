import { Outlet, useLocation } from "react-router-dom";
import AdminSideBar from "./sidebar";
import AdminHeader from "./header";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { useTranslation } from "react-i18next";
import TranslateWrapper from "../TranslateWrapper";

function AdminLayout({ notifications, unreadCount, markAsRead, markAllAsRead, setNotifications }) {
  const [openSidebar, setOpenSidebar] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const location = useLocation();
  const { t } = useTranslation();

  // Check sidebar collapsed state from localStorage
  useEffect(() => {
    const checkSidebarState = () => {
      const savedState = localStorage.getItem('sidebarCollapsed');
      setIsSidebarCollapsed(savedState === 'true');
    };

    // Initial check
    checkSidebarState();

    // Listen for storage events to update in real-time across tabs
    const handleStorageChange = (e) => {
      if (e.key === 'sidebarCollapsed') {
        checkSidebarState();
      }
    };

    // Listen for custom events for changes within the same tab
    const handleCustomEvent = () => {
      checkSidebarState();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('sidebarStateChanged', handleCustomEvent);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('sidebarStateChanged', handleCustomEvent);
    };
  }, []);

  // Check if current route is the AI page
  const isAIPage = location.pathname.includes('/incident/ai');

  return (
    <div className={`min-h-screen w-full ${isAIPage ? 'bg-slate-900' : 'bg-gray-100'} flex flex-col`}>
      {/* Fixed Header */}
      <div className="fixed top-0 right-0 left-0 z-20">
        <AdminHeader
          setOpen={setOpenSidebar}
          notifications={notifications}
          unreadCount={unreadCount}
          markAsRead={markAsRead}
          markAllAsRead={markAllAsRead}
        />
      </div>

      {/* Fixed Sidebar */}
      <div className="fixed top-0 left-0 h-full z-30">
        <AdminSideBar
          open={openSidebar}
          setOpen={setOpenSidebar}
          onCollapseChange={setIsSidebarCollapsed}
        />
      </div>

      {/* Main Content Area with proper padding */}
      <div className={cn(
        "pt-[61px] transition-all duration-300 flex-grow", /* 61px is header height */
        isSidebarCollapsed ? "lg:ml-16" : "lg:ml-64"
      )}>
        <main className={`${isAIPage ? 'p-0' : 'bg-gray-100'} min-h-[calc(100vh-61px)] h-full`}>
          <TranslateWrapper>
            <Outlet />
          </TranslateWrapper>
        </main>
      </div>
    </div>
  );
}

export default AdminLayout;
