import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  BookOpen,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

const ListeCompetences = () => {
  const [competences, setCompetences] = useState([]);
  const [filteredCompetences, setFilteredCompetences] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedCompetence, setSelectedCompetence] = useState(null);
  const [formData, setFormData] = useState({ name: '', description: '' });

  // Fetch competences from API
  const fetchCompetences = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${getApiBaseUrl()}/audit/skills`, {
        withCredentials: true
      });

      if (response.data.success) {
        setCompetences(response.data.data);
        setFilteredCompetences(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching competences:', error);
      toast.error('Erreur lors du chargement des compétences');
    } finally {
      setLoading(false);
    }
  };

  // Filter competences based on search
  const handleSearch = (term) => {
    setSearchTerm(term);
    if (term === '') {
      setFilteredCompetences(competences);
    } else {
      const filtered = competences.filter(comp =>
        comp.name.toLowerCase().includes(term.toLowerCase())
      );
      setFilteredCompetences(filtered);
    }
  };

  // Create competence
  const handleCreateCompetence = async () => {
    if (!formData.name.trim()) {
      toast.error('Le nom de la compétence est requis');
      return;
    }

    try {
      const response = await axios.post(`${getApiBaseUrl()}/audit/skills`, {
        name: formData.name.trim(),
        description: formData.description.trim() || null
      }, {
        withCredentials: true
      });

      if (response.data.success) {
        toast.success('Compétence créée avec succès');
        setIsCreateModalOpen(false);
        setFormData({ name: '', description: '' });
        fetchCompetences();
      }
    } catch (error) {
      console.error('Error creating competence:', error);
      toast.error(error.response?.data?.message || 'Erreur lors de la création de la compétence');
    }
  };

  // Update competence
  const handleUpdateCompetence = async () => {
    if (!formData.name.trim()) {
      toast.error('Le nom de la compétence est requis');
      return;
    }

    try {
      const response = await axios.put(`${getApiBaseUrl()}/audit/skills/${selectedCompetence.id}`, {
        name: formData.name.trim(),
        description: formData.description.trim() || null
      }, {
        withCredentials: true
      });

      if (response.data.success) {
        toast.success('Compétence mise à jour avec succès');
        setIsEditModalOpen(false);
        setSelectedCompetence(null);
        setFormData({ name: '', description: '' });
        fetchCompetences();
      }
    } catch (error) {
      console.error('Error updating competence:', error);
      toast.error(error.response?.data?.message || 'Erreur lors de la mise à jour de la compétence');
    }
  };

  // Delete competence
  const handleDeleteCompetence = async (competenceId, competenceName) => {
    // First confirmation - basic delete warning
    const basicConfirm = confirm(
      `Vous êtes sur le point de supprimer définitivement la compétence "${competenceName}".\n\n` +
      `Êtes-vous sûr de vouloir continuer ?`
    );

    if (!basicConfirm) {
      return;
    }

    try {
      // First attempt - check if confirmation is needed
      const response = await axios.delete(`${getApiBaseUrl()}/audit/skills/${competenceId}`, {
        withCredentials: true
      });

      if (response.data.success) {
        toast.success(response.data.message);
        fetchCompetences();
      }
    } catch (error) {
      console.error('Error deleting competence:', error);

      if (error.response?.status === 409 && error.response?.data?.requiresConfirmation) {
        // Handle confirmation needed for assigned skills
        const confirmData = error.response.data;
        const auditorNames = confirmData.assignments.map(a => a.auditor.username).join(', ');

        const confirmMessage =
          `🚨 SUPPRESSION AVEC ASSIGNATIONS ACTIVES\n\n` +
          `${confirmData.message}\n\n` +
          `Auditeurs concernés: ${auditorNames}\n\n` +
          `⚠️ CETTE ACTION VA :\n` +
          `• Supprimer définitivement la compétence "${competenceName}"\n` +
          `• Supprimer définitivement ${confirmData.assignedCount} évaluation(s)\n` +
          `• Retirer cette compétence du profil des auditeurs\n\n` +
          `❌ CETTE ACTION EST IRRÉVERSIBLE ❌\n\n` +
          `Confirmez-vous la suppression définitive ?`;

        if (confirm(confirmMessage)) {
          // Force delete with confirmation
          try {
            const forceResponse = await axios.delete(`${getApiBaseUrl()}/audit/skills/${competenceId}?force=true`, {
              withCredentials: true
            });

            if (forceResponse.data.success) {
              toast.success(forceResponse.data.message);
              fetchCompetences();
            }
          } catch (forceError) {
            console.error('Error force deleting competence:', forceError);
            toast.error(forceError.response?.data?.message || 'Erreur lors de la suppression définitive de la compétence');
          }
        }
      } else {
        toast.error(error.response?.data?.message || 'Erreur lors de la suppression de la compétence');
      }
    }
  };

  // Open edit modal
  const openEditModal = (competence) => {
    setSelectedCompetence(competence);
    setFormData({
      name: competence.name,
      description: competence.description || ''
    });
    setIsEditModalOpen(true);
  };

  // Load competences on component mount
  useEffect(() => {
    fetchCompetences();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-[#F62D51]" />
        <span className="ml-2 text-gray-600">Chargement des compétences...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6 border border-green-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <BookOpen className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Liste des compétences
              </h1>
              <p className="text-gray-600 text-sm">
                Gérez la liste des compétences disponibles pour l'équipe d'audit
              </p>
            </div>
          </div>

          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button className="bg-green-600 hover:bg-green-700">
                <Plus className="h-4 w-4 mr-2" />
                Nouvelle Compétence
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Créer une nouvelle compétence</DialogTitle>
              <DialogDescription>
                Ajoutez une nouvelle compétence à la liste
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">Nom</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="col-span-3"
                  placeholder="Nom de la compétence"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      handleCreateCompetence();
                    }
                  }}
                />
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="description" className="text-right">Description</Label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="col-span-3 min-h-[80px] px-3 py-2 border border-gray-300 rounded-md resize-none"
                  placeholder="Description de la compétence (optionnel)"
                  rows={3}
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                Annuler
              </Button>
              <Button onClick={handleCreateCompetence} disabled={!formData.name.trim()}>
                Créer
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Search className="h-5 w-5" />
            Recherche
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Rechercher une compétence..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <Card className="shadow-sm w-fit">
        <CardContent className="p-3">
          <div className="flex items-center">
            <BookOpen className="h-5 w-5 text-green-600" />
            <div className="ml-2">
              <p className="text-xs font-medium text-gray-600">Total des compétences</p>
              <p className="text-base font-bold text-gray-900">{competences.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Competences List */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Compétences ({filteredCompetences.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {filteredCompetences.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {filteredCompetences.map((competence, index) => (
                <div key={competence.id} className="p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <span className="text-green-600 font-medium text-sm">
                            {index + 1}
                          </span>
                        </div>
                        <div>
                          <h3 className="text-base font-medium text-gray-900">
                            {competence.name}
                          </h3>
                          {competence.description && (
                            <p className="text-sm text-gray-500 mt-1">
                              {competence.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditModal(competence)}
                        className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteCompetence(competence.id, competence.name)}
                        className="text-red-600 hover:text-red-800 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-12 text-center">
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune compétence trouvée</h3>
              <p className="text-gray-600">
                {searchTerm
                  ? 'Aucune compétence ne correspond à votre recherche.'
                  : 'Commencez par créer votre première compétence.'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {filteredCompetences.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune compétence trouvée</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm
                ? 'Aucune compétence ne correspond à votre recherche.'
                : 'Commencez par créer votre première compétence.'}
            </p>
            {!searchTerm && (
              <Button onClick={() => setIsCreateModalOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Créer une compétence
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Modifier la compétence</DialogTitle>
            <DialogDescription>
              Modifiez le nom de la compétence
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">Nom</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    handleUpdateCompetence();
                  }
                }}
              />
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="edit-description" className="text-right">Description</Label>
              <textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="col-span-3 min-h-[80px] px-3 py-2 border border-gray-300 rounded-md resize-none"
                placeholder="Description de la compétence (optionnel)"
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              Annuler
            </Button>
            <Button onClick={handleUpdateCompetence} disabled={!formData.name.trim()}>
              Mettre à jour
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ListeCompetences;
