-- <PERSON><PERSON><PERSON> to create the RiskAttachment table in pgAdmin
-- Run this script directly in pgAdmin Query Tool

-- Check if the table already exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'RiskAttachment'
    ) THEN
        -- Create the RiskAttachment table
        CREATE TABLE "RiskAttachment" (
            "attachmentID" VARCHAR(255) PRIMARY KEY,
            "fileName" VARCHAR(255) NOT NULL,
            "fileSize" INTEGER NOT NULL,
            "fileType" VARCHAR(255) NOT NULL,
            "filePath" VARCHAR(255) NOT NULL,
            "uploadDate" TIMESTAMP NOT NULL,
            "type" VARCHAR(20) NOT NULL CHECK ("type" IN ('business-document', 'external-reference')),
            "riskID" VARCHAR(255) NOT NULL REFERENCES "Risk"("riskID") ON DELETE CASCADE
        );
        
        RAISE NOTICE 'RiskAttachment table created successfully';
    ELSE
        RAISE NOTICE 'RiskAttachment table already exists';
    END IF;
END
$$;
