module.exports = (sequelize, DataTypes) => {
  const ControlQuestion = sequelize.define('ControlQuestion', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  controlID: {
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'controlID'
  },
  question_text: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  input_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'text'
  },
  options: {
    type: DataTypes.JSONB,
    allowNull: true
  },
  order: {
    type: DataTypes.INTEGER,
    defaultValue: 1
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'controlQuestions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['controlID']
    },
    {
      fields: ['controlID', 'order']
    }
  ]
  });

  return ControlQuestion;
};
