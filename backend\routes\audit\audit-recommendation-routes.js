'use strict';

const express = require('express');
const router = express.Router();
const { verifyToken, authorizeRoles } = require('../../middleware/auth');
const {
  getAllRecommendations,
  getRecommendationsByConstatId,
  createRecommendation,
  getRecommendationById,
  updateRecommendation,
  deleteRecommendation,
  getRecommendationByActionPlanId,
  getRecommendationsByAuditPlanId
} = require('../../controllers/audit/audit-recommendation-controller');

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all recommendations
router.get('/', authorizeRoles(['audit_director', 'auditor']), getAllRecommendations);

// Get recommendations by constat ID
router.get('/constat/:constatId', authorizeRoles(['audit_director', 'auditor']), getRecommendationsByConstatId);

// Create new recommendation
router.post('/', authorizeRoles(['audit_director', 'auditor']), createRecommendation);

// Get recommendation by ID
router.get('/:id', authorizeRoles(['audit_director', 'auditor']), getRecommendationById);

// Update recommendation
router.put('/:id', authorizeRoles(['audit_director', 'auditor']), updateRecommendation);

// Delete recommendation
router.delete('/:id', authorizeRoles(['audit_director', 'auditor']), deleteRecommendation);

// Get recommendation by action plan ID
router.get('/action-plan/:actionPlanId', authorizeRoles(['audit_director', 'auditor']), getRecommendationByActionPlanId);

// Get recommendations by audit plan ID
router.get('/audit-plan/:planId', authorizeRoles(['audit_director', 'auditor']), getRecommendationsByAuditPlanId);

module.exports = router;
