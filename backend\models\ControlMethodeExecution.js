module.exports = (sequelize, DataTypes) => {
  const ControlMethodeExecution = sequelize.define('ControlMethodeExecution', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    controlID: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true, // One execution method per control
      field: 'controlID'
    },
    frequenceExecution: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Frequency of execution (cotidienne, hebdomadaire, mensuel, etc.)'
    },
    methode: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Execution method (controle_par_sondage, controle_systematique, observation)'
    },
    calendrierPilotage: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Piloting calendar (campagne_execution, campagne_hebdomadaire, etc.)'
    },
    taillePopulationTotale: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Total population size'
    },
    tailleEchantillon: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Sample size as percentage (%)'
    },
    seuilTauxConformite: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Compliance rate threshold as percentage (%)'
    },
    procedureExecution: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Execution procedure description'
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'controlMethodeExecution',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        unique: true,
        fields: ['controlID']
      }
    ]
  });

  // Define associations
  ControlMethodeExecution.associate = function(models) {
    if (models.Control) {
      ControlMethodeExecution.belongsTo(models.Control, {
        foreignKey: 'controlID',
        targetKey: 'controlID',
        as: 'control'
      });
      models.Control.hasOne(ControlMethodeExecution, {
        foreignKey: 'controlID',
        sourceKey: 'controlID',
        as: 'methodeExecution'
      });
    }
  };

  return ControlMethodeExecution;
};
