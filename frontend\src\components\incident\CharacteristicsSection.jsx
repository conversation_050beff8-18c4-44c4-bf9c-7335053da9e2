import { useState } from "react";
import axios from "axios";
import { toast } from "react-hot-toast";
import { 
  ChevronDown, 
  ChevronUp, 
  Plus, 
  Link, 
  User, 
  Building, 
  Calendar, 
  Clock, 
  AlertTriangle, 
  FileText, 
  Info, 
  Check, 
  Package, 
  Database, 
  BarChart, 
  Briefcase, 
  Shield,
  File,
  FileType,
  Users
} from "lucide-react";
import { Input } from "../../components/ui/input";
import { Button } from "../../components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import { Textarea } from "../../components/ui/textarea";
import { NewRiskModal } from "./NewRiskModal";
import { RiskSelectionModal } from "./RiskSelectionModal";
import { NewControlModal } from "./NewControlModal";
import { ControlSelectionModal } from "./ControlSelectionModal";
import { NewIncidentTypeModal } from "./NewIncidentTypeModal";
import { IncidentTypeSelectionModal } from "./IncidentTypeSelectionModal";
import { Combobox } from "@/components/ui/combobox";
import ContributorsSection from "@/components/incident/ContributorsSection";
import { getApiBaseUrl } from '../../utils/api-config';
import { useTranslation } from "react-i18next";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Label,
} from "@/components/ui/label";


export function CharacteristicsSection({
  incident,
  handleInputChange,
  entities = [],
  businessLines = [],
  businessProcesses = [],
  organizationalProcesses = [],
  products = [],
  applications = [],
  incidentTypes = [],
  controls = [],
  risks = []
}) {
  const { t } = useTranslation();

  // API Base URL - use dynamic URL from utility function
  const API_BASE_URL = getApiBaseUrl();

  // Helper function to get more vibrant color
  const getVibrantColor = (colorClass) => {
    // Extract base color (blue, green, yellow, orange, red, gray)
    const baseColor = colorClass.match(/bg-(blue|green|yellow|orange|red|gray)/)?.[1];
    return baseColor ? `bg-${baseColor}-500` : 'bg-gray-500';
  };

  // Helper functions for impact and priority labels
  const getImpactLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const impactLabels = {
      'Very High': { label: 'Très Élevé', color: 'bg-red-100 text-red-800' },
      'High': { label: 'Élevé', color: 'bg-orange-100 text-orange-800' },
      'Medium': { label: 'Moyen', color: 'bg-yellow-100 text-yellow-800' },
      'Low': { label: 'Faible', color: 'bg-green-100 text-green-800' },
      'Very Low': { label: 'Très Faible', color: 'bg-blue-100 text-blue-800' }
    };
    return impactLabels[value] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  const getPriorityLabel = (value) => {
    if (!value) return { label: '-', color: 'bg-gray-100 text-gray-500' };
    const priorityLabels = {
      'High': { label: 'Élevé', color: 'bg-red-100 text-red-800' },
      'Medium': { label: 'Moyen', color: 'bg-orange-100 text-orange-800' },
      'Low': { label: 'Faible', color: 'bg-green-100 text-green-800' }
    };
    return priorityLabels[value] || { label: value, color: 'bg-gray-100 text-gray-500' };
  };

  // Component for managing incident characteristics
  const [isCharacteristicsOpen, setIsCharacteristicsOpen] = useState(true);
  const [isQualitativeOpen, setIsQualitativeOpen] = useState(true);
  const [isNewRiskModalOpen, setIsNewRiskModalOpen] = useState(false);
  const [isRiskSelectionModalOpen, setIsRiskSelectionModalOpen] = useState(false);
  const [isNewControlModalOpen, setIsNewControlModalOpen] = useState(false);
  const [isControlSelectionModalOpen, setIsControlSelectionModalOpen] = useState(false);
  const [isNewIncidentTypeModalOpen, setIsNewIncidentTypeModalOpen] = useState(false);
  const [isIncidentTypeSelectionModalOpen, setIsIncidentTypeSelectionModalOpen] = useState(false);
  const [newRisk, setNewRisk] = useState({ name: '', comment: '' });

  const handleNewRiskSubmit = async (newRisk) => {
    try {
      // Create the risk with minimal fields
      const response = await axios.post(`${API_BASE_URL}/risk`, {
        name: newRisk.name,
        comment: newRisk.comment || '',
        methodOfIdentification: 'incident_database',
        // Set other fields to null
        impact: null,
        DMR: null,
        probability: null,
        businessProcessID: null,
        organizationalProcessID: null,
        operationID: null,
        applicationID: null,
        entityID: null,
        controlID: null,
        riskTypeID: null
      }, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.data.success) {
        const createdRisk = response.data.data;
        
        // Update the incident with the new risk ID and remove any previous risk
        await axios.put(`${API_BASE_URL}/incident/${incident.incidentID}`, {
          ...incident,
          riskID: createdRisk.riskID
        }, {
          withCredentials: true,
          headers: { 'Content-Type': 'application/json' },
        });

        // Update local state
        await handleInputChange({
          target: {
            name: 'riskID',
            value: createdRisk.riskID
          }
        });

        toast.success("Risk created and attached to incident successfully");
        setIsNewRiskModalOpen(false);
      }
    } catch (error) {
      console.error('Error creating risk:', error);
      toast.error(error.response?.data?.message || "Failed to create risk");
    }
  };

  const handleRiskSelection = async (selectedRisk) => {
    // Close the modal
    setIsRiskSelectionModalOpen(false);

    try {
      // Update the riskID field with the selected risk's ID
      if (selectedRisk && (selectedRisk.riskID || selectedRisk.id || selectedRisk._id)) {
        const riskId = selectedRisk.riskID || selectedRisk.id || selectedRisk._id;
        handleInputChange('riskID', riskId);
        toast.success(t('admin.incidents.characteristics.riskAssigned', 'Risk assigned successfully'));
      } else {
        toast.error(t('admin.incidents.characteristics.invalidRiskSelected', 'Invalid risk selected'));
      }
    } catch {
      toast.error(t('admin.incidents.characteristics.failedToAssignRisk', 'Failed to assign risk'));
    }
  };

  // Function to get the name of the assigned risk
  const getAssignedRiskName = () => {
    if (!incident || !incident.riskID) {
      return t('admin.incidents.characteristics.noRiskAssigned', 'No risk assigned');
    }

    if (!risks || risks.length === 0) {
      return t('admin.incidents.characteristics.loadingRisks', 'Loading risks...');
    }

    // Try to find the risk by ID, handling different ID field names and types
    const assignedRisk = risks.find(risk => {
      const riskId = risk.riskID || risk._id || risk.id;
      const incidentRiskId = incident.riskID;

      return String(riskId) === String(incidentRiskId);
    });

    if (assignedRisk) {
      return assignedRisk.name || t('admin.incidents.characteristics.unnamedRisk', 'Unnamed Risk');
    } else {
      return t('admin.incidents.characteristics.riskID', 'Risk ID: {riskID}', { riskID: incident.riskID });
    }
  };

  // Function to get the name of the assigned control
  const getAssignedControlName = () => {
    if (!incident || !incident.controlID) {
      return t('admin.incidents.characteristics.noControlAssigned', 'No control assigned');
    }

    if (!controls || controls.length === 0) {
      return t('admin.incidents.characteristics.loadingControls', 'Loading controls...');
    }

    // Try to find the control by ID, handling different ID field names and types
    const assignedControl = controls.find(control => {
      const controlId = control.controlID || control._id || control.id;
      const incidentControlId = incident.controlID;

      return String(controlId) === String(incidentControlId);
    });

    if (assignedControl) {
      return assignedControl.name || t('admin.incidents.characteristics.unnamedControl', 'Unnamed Control');
    } else {
      return t('admin.incidents.characteristics.controlID', 'Control ID: {controlID}', { controlID: incident.controlID });
    }
  };

  // Function to get the name of the assigned incident type
  const getAssignedIncidentTypeName = () => {
    if (!incident || !incident.incidentTypeID) {
      return t('admin.incidents.characteristics.noIncidentTypeAssigned', 'No incident type assigned');
    }

    if (!incidentTypes || incidentTypes.length === 0) {
      return t('admin.incidents.characteristics.loadingIncidentTypes', 'Loading incident types...');
    }

    // Try to find the incident type by ID, handling different ID field names and types
    const assignedIncidentType = incidentTypes.find(type => {
      const typeId = type.incidentTypeID || type._id || type.id;
      const incidentTypeId = incident.incidentTypeID;

      return String(typeId) === String(incidentTypeId);
    });

    if (assignedIncidentType) {
      return assignedIncidentType.name || t('admin.incidents.characteristics.unnamedIncidentType', 'Unnamed Incident Type');
    } else {
      return t('admin.incidents.characteristics.incidentTypeID', 'Incident Type ID: {incidentTypeID}', { incidentTypeID: incident.incidentTypeID });
    }
  };

  // Handle new control submission
  const handleNewControlSubmit = async (newControl) => {
    try {
      // First validate the form data
      if (!newControl.name) {
        toast.error(t('admin.incidents.characteristics.controlNameRequired', 'Control name is required'));
        return;
      }

      // Close the modal
      setIsNewControlModalOpen(false);

      // Make an API call to create a new control
      const response = await axios.post(`${API_BASE_URL}/controls`, {
        controlID: `CTRL_${Date.now()}`,
        name: newControl.name,
        code: newControl.code || '',
        comment: newControl.comment || ''
      }, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.data.success) {
        // Update the controlID field with the new control's ID
        const createdControl = response.data.data;
        handleInputChange('controlID', createdControl.controlID || createdControl._id);
        toast.success(t('admin.incidents.characteristics.controlCreated', 'Control created and assigned successfully'));
      } else {
        toast.error(response.data.message || t('admin.incidents.characteristics.failedToCreateControl', 'Failed to create control'));
      }
    } catch (error) {
      toast.error(error.response?.data?.message || t('admin.incidents.characteristics.failedToCreateControl', 'Failed to create control'));
    }
  };

  // Handle control selection
  const handleControlSelection = async (selectedControl) => {
    // Close the modal
    setIsControlSelectionModalOpen(false);

    try {
      // Update the controlID field with the selected control's ID
      if (selectedControl && (selectedControl.controlID || selectedControl.id || selectedControl._id)) {
        const controlId = selectedControl.controlID || selectedControl.id || selectedControl._id;
        handleInputChange('controlID', controlId);
        toast.success(t('admin.incidents.characteristics.controlAssigned', 'Control assigned successfully'));
      } else {
        toast.error(t('admin.incidents.characteristics.invalidControlSelected', 'Invalid control selected'));
      }
    } catch {
      toast.error(t('admin.incidents.characteristics.failedToAssignControl', 'Failed to assign control'));
    }
  };

  // Handle new incident type submission
  const handleNewIncidentTypeSubmit = async (newIncidentType) => {
    try {
      // First validate the form data
      if (!newIncidentType.name) {
        toast.error(t('admin.incidents.characteristics.incidentTypeNameRequired', 'Incident type name is required'));
        return;
      }

      // Close the modal
      setIsNewIncidentTypeModalOpen(false);

      // Make an API call to create a new incident type
      const response = await axios.post(`${API_BASE_URL}/incidentTypes`, {
        incidentTypeID: `INC_TYPE_${Date.now()}`,
        name: newIncidentType.name,
        description: newIncidentType.comment || ''
      }, {
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.data.success) {
        // Update the incidentTypeID field with the new incident type's ID
        const createdIncidentType = response.data.data;
        handleInputChange('incidentTypeID', createdIncidentType.incidentTypeID || createdIncidentType._id);
        toast.success(t('admin.incidents.characteristics.incidentTypeCreated', 'Incident type created and assigned successfully'));
      } else {
        toast.error(response.data.message || t('admin.incidents.characteristics.failedToCreateIncidentType', 'Failed to create incident type'));
      }
    } catch (error) {
      toast.error(error.response?.data?.message || t('admin.incidents.characteristics.failedToCreateIncidentType', 'Failed to create incident type'));
    }
  };

  // Handle incident type selection
  const handleIncidentTypeSelection = async (selectedIncidentType) => {
    // Close the modal
    setIsIncidentTypeSelectionModalOpen(false);

    try {
      // Update the incidentTypeID field with the selected incident type's ID
      if (selectedIncidentType && (selectedIncidentType.incidentTypeID || selectedIncidentType.id || selectedIncidentType._id)) {
        const incidentTypeId = selectedIncidentType.incidentTypeID || selectedIncidentType.id || selectedIncidentType._id;
        handleInputChange('incidentTypeID', incidentTypeId);
        toast.success(t('admin.incidents.characteristics.incidentTypeAssigned', 'Incident type assigned successfully'));
      } else {
        toast.error(t('admin.incidents.characteristics.invalidIncidentTypeSelected', 'Invalid incident type selected'));
      }
    } catch {
      toast.error(t('admin.incidents.characteristics.failedToAssignIncidentType', 'Failed to assign incident type'));
    }
  };

  return (
    <div className="space-y-6">
      {/* Characteristics subSection */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsCharacteristicsOpen(!isCharacteristicsOpen)}
        >
          <div className="flex items-center gap-2">
            {isCharacteristicsOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <Info className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">{t('admin.incidents.characteristics.characteristics', 'Characteristics')}</span>
          </div>
        </button>

        {isCharacteristicsOpen && (
          <div className="p-5 bg-white">
            <div className="grid grid-cols-3 gap-x-8 gap-y-6 w-full">
              {/* Basic Information Section */}
              <div className="col-span-3 bg-gray-50 p-3 rounded-md mb-4">
                <h3 className="text-md font-medium text-gray-700 mb-2 flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-blue-600" />
                  {t('admin.incidents.characteristics.basicInformation', 'Basic Information')}
                </h3>
              </div>
              
              {/* First row */}
              <div className="space-y-2 col-span-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-blue-600" />
                  {t('admin.incidents.characteristics.name', 'Name')}
                </label>
                <div className="w-full">
                  <Input
                    value={incident.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder={t('admin.incidents.characteristics.incidentName', 'Incident name')}
                    required
                    className="w-full block"
                  />
                </div>
              </div>
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <User className="h-4 w-4 mr-2 text-indigo-600" />
                  {t('admin.incidents.characteristics.declaredBy', 'Declared By')}
                </label>
                <div className="w-full">
                  <Input
                    value={incident.declaredBy}
                    onChange={(e) => handleInputChange('declaredBy', e.target.value)}
                    placeholder={t('admin.incidents.characteristics.declarantName', 'Declarant name')}
                    className="w-full block"
                  />
                </div>
              </div>
              
              {/* Second row */}
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <Building className="h-4 w-4 mr-2 text-blue-600" />
                  {t('admin.incidents.characteristics.declarantEntity', 'Declarant Entity')}
                </label>
                <div className="w-full">
                  <Input
                    value={incident.declarantEntity}
                    onChange={(e) => handleInputChange('declarantEntity', e.target.value)}
                    placeholder={t('admin.incidents.characteristics.declarantEntity', 'Declarant entity')}
                    className="w-full block"
                  />
                </div>
              </div>
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-green-600" />
                  {t('admin.incidents.characteristics.declarationDate', 'Declaration Date')}
                </label>
                <Input
                  type="date"
                  value={incident.declarationDate || ''}
                  onChange={(e) => handleInputChange('declarationDate', e.target.value)}
                  className="w-full block"
                />
              </div>
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-purple-600" />
                  {t('admin.incidents.characteristics.detectionDate', 'Detection Date')}
                </label>
                <Input
                  type="date"
                  value={incident.detectionDate || ''}
                  onChange={(e) => handleInputChange('detectionDate', e.target.value)}
                  className="w-full block"
                />
              </div>
              
              {/* Dates section */}
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-orange-600" />
                  {t('admin.incidents.characteristics.occurrenceDate', 'Occurrence Date')}
                </label>
                <Input
                  type="date"
                  value={incident.occurrenceDate || ''}
                  onChange={(e) => handleInputChange('occurrenceDate', e.target.value)}
                  className="w-full block"
                />
              </div>
              
              {/* Contributors section header */}
              <div className="col-span-3 bg-gray-50 p-3 rounded-md mb-4 mt-2">
                <h3 className="text-md font-medium text-gray-700 mb-0 flex items-center">
                  <Users className="h-4 w-4 mr-2 text-indigo-600" />
                  {t('admin.incidents.characteristics.contributors', 'Contributors')}
                </h3>
              </div>
              
              {/* Contributors section - spans full width */}
              <div className="col-span-3">
                {incident.incidentID && <ContributorsSection incidentId={incident.incidentID} />}
              </div>
              
              {/* Evaluation section header */}
              <div className="col-span-3 bg-gray-50 p-3 rounded-md mb-3 mt-4">
                <h3 className="text-md font-medium text-gray-700 mb-0 flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2 text-amber-600" />
                  {t('admin.incidents.characteristics.incidentEvaluation', 'Incident Evaluation')}
                </h3>
              </div>
              
              {/* Third row - Evaluation */}
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <Check className="h-4 w-4 mr-2 text-emerald-600" />
                  {t('admin.incidents.characteristics.nearMiss', 'Near Miss')}
                </label>
                <Select
                  value={incident.nearMiss?.toString()}
                  onValueChange={(value) => handleInputChange('nearMiss', value === 'true')}
                  className="w-full"
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={t('admin.incidents.characteristics.selectNearMiss', 'Select near miss')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="false">0</SelectItem>
                    <SelectItem value="true">1</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <Info className="h-4 w-4 mr-2 text-blue-600" />
                  {t('admin.incidents.characteristics.nature', 'Nature')}
                </label>
                <Select
                  value={incident.nature}
                  onValueChange={(value) => handleInputChange('nature', value)}
                  className="w-full"
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={t('admin.incidents.characteristics.selectNature', 'Select nature')} />
                  </SelectTrigger>
                  <SelectContent>
                    {["Financial", "Non-Financial"].map((option) => {
                      const frenchLabels = {
                        "Financial": "Financier",
                        "Non-Financial": "Non-Financier"
                      };
                      return (
                        <SelectItem key={option} value={option}>{frenchLabels[option] || option}</SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2 text-red-600" />
                  {t('admin.incidents.characteristics.impact', 'Impact')}
                </label>
                <Select
                  value={incident.impact}
                  onValueChange={(value) => handleInputChange('impact', value)}
                  className="w-full"
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={t('admin.incidents.characteristics.selectImpact', 'Select impact')}>
                      {incident.impact ? (
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full ${getVibrantColor(getImpactLabel(incident.impact).color)} mr-2`}></div>
                          {getImpactLabel(incident.impact).label}
                        </div>
                      ) : t('admin.incidents.characteristics.selectImpact', 'Select impact')}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {["Very High", "High", "Medium", "Low", "Very Low",].map((option) => (
                      <SelectItem key={option} value={option}>
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full ${getVibrantColor(getImpactLabel(option).color)} mr-2`}></div>
                          {getImpactLabel(option).label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* Fourth row */}
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2 text-orange-600" />
                  {t('admin.incidents.characteristics.priority', 'Priority')}
                </label>
                <Select
                  value={incident.priority}
                  onValueChange={(value) => handleInputChange('priority', value)}
                  className="w-full"
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={t('admin.incidents.characteristics.selectPriority', 'Select priority')}>
                      {incident.priority ? (
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full ${getVibrantColor(getPriorityLabel(incident.priority).color)} mr-2`}></div>
                          {getPriorityLabel(incident.priority).label}
                        </div>
                      ) : t('admin.incidents.characteristics.selectPriority', 'Select priority')}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {["High", "Medium", "Low"].map((option) => (
                      <SelectItem key={option} value={option}>
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full ${getVibrantColor(getPriorityLabel(option).color)} mr-2`}></div>
                          {getPriorityLabel(option).label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* Reference data section header */}
              <div className="col-span-3 bg-gray-50 p-3 rounded-md mb-3 mt-4">
                <h3 className="text-md font-medium text-gray-700 mb-0 flex items-center">
                  <Link className="h-4 w-4 mr-2 text-indigo-600" />
                  {t('admin.incidents.characteristics.referenceData', 'Reference Data')}
                </h3>
              </div>
              
              {/* Reference data fields */}
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <Building className="h-4 w-4 mr-2 text-blue-600" />
                  {t('admin.incidents.characteristics.entity', 'Entity')}
                </label>
                <Select
                  value={incident.entityID || ''}
                  onValueChange={(value) => handleInputChange('entityID', value)}
                  className="w-full"
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={t('admin.incidents.characteristics.selectEntity', 'Select entity')} />
                  </SelectTrigger>
                  <SelectContent>
                    {entities.map((entity) => (
                      <SelectItem key={entity.entityID} value={entity.entityID}>
                        {entity.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <Briefcase className="h-4 w-4 mr-2 text-green-600" />
                  {t('admin.incidents.characteristics.businessLine', 'Business Line')}
                </label>
                <Combobox
                  options={businessLines.map(businessLine => ({
                    label: businessLine.name,
                    value: businessLine.businessLineID
                  }))}
                  value={incident.businessLineID || ''}
                  onChange={(value) => handleInputChange('businessLineID', value)}
                  placeholder={t('admin.incidents.characteristics.selectBusinessLine', 'Select business line')}
                  emptyMessage={t('admin.incidents.characteristics.noBusinessLinesFound', 'No business lines found.')}
                  searchPlaceholder={t('admin.incidents.characteristics.searchBusinessLines', 'Search business lines...')}
                  className="w-full"
                  popoverClassName="w-full"
                />
              </div>
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <BarChart className="h-4 w-4 mr-2 text-amber-600" />
                  {t('admin.incidents.characteristics.businessProcess', 'Business Process')}
                </label>
                <Combobox
                  options={businessProcesses.map(businessProcess => ({
                    label: businessProcess.name,
                    value: businessProcess.businessProcessID
                  }))}
                  value={incident.businessProcessID || ''}
                  onChange={(value) => handleInputChange('businessProcessID', value)}
                  placeholder={t('admin.incidents.characteristics.selectBusinessProcess', 'Select business process')}
                  emptyMessage={t('admin.incidents.characteristics.noBusinessProcessesFound', 'No business processes found.')}
                  searchPlaceholder={t('admin.incidents.characteristics.searchBusinessProcesses', 'Search business processes...')}
                  className="w-full"
                  popoverClassName="w-full"
                />
              </div>
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <BarChart className="h-4 w-4 mr-2 text-purple-600" />
                  {t('admin.incidents.characteristics.organizationalProcess', 'Organizational Process')}
                </label>
                <Combobox
                  options={organizationalProcesses.map(orgProcess => ({
                    label: orgProcess.name,
                    value: orgProcess.organizationalProcessID
                  }))}
                  value={incident.organizationalProcessID || ''}
                  onChange={(value) => handleInputChange('organizationalProcessID', value)}
                  placeholder={t('admin.incidents.characteristics.selectOrganizationalProcess', 'Select organizational process')}
                  emptyMessage={t('admin.incidents.characteristics.noOrganizationalProcessesFound', 'No organizational processes found.')}
                  searchPlaceholder={t('admin.incidents.characteristics.searchOrganizationalProcesses', 'Search organizational processes...')}
                  className="w-full"
                  popoverClassName="w-full"
                />
              </div>
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <Package className="h-4 w-4 mr-2 text-cyan-600" />
                  {t('admin.incidents.characteristics.product', 'Product')}
                </label>
                <Combobox
                  options={products.map(product => ({
                    label: product.name,
                    value: product.productID
                  }))}
                  value={incident.productID || ''}
                  onChange={(value) => handleInputChange('productID', value)}
                  placeholder={t('admin.incidents.characteristics.selectProduct', 'Select product')}
                  emptyMessage={t('admin.incidents.characteristics.noProductsFound', 'No products found.')}
                  searchPlaceholder={t('admin.incidents.characteristics.searchProducts', 'Search products...')}
                  className="w-full"
                  popoverClassName="w-full"
                />
              </div>
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <Database className="h-4 w-4 mr-2 text-indigo-600" />
                  {t('admin.incidents.characteristics.application', 'Application')}
                </label>
                <Combobox
                  options={applications.map(app => ({
                    label: app.name,
                    value: app.applicationID
                  }))}
                  value={incident.applicationID || ''}
                  onChange={(value) => handleInputChange('applicationID', value)}
                  placeholder={t('admin.incidents.characteristics.selectApplication', 'Select application')}
                  emptyMessage={t('admin.incidents.characteristics.noApplicationsFound', 'No applications found.')}
                  searchPlaceholder={t('admin.incidents.characteristics.searchApplications', 'Search applications...')}
                  className="w-full"
                  popoverClassName="w-full"
                />
              </div>
              
              {/* Description section header */}
              <div className="col-span-3 bg-gray-50 p-3 rounded-md mb-3 mt-4">
                <h3 className="text-md font-medium text-gray-700 mb-0 flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-gray-600" />
                  {t('admin.incidents.characteristics.description', 'Description')}
                </h3>
              </div>
              
              {/* Description field spanning full width */}
              <div className="space-y-2 col-span-3 w-full">
                <Textarea
                  value={incident.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder={t('admin.incidents.characteristics.incidentDescription', 'Incident description')}
                  className="min-h-[100px] w-full block"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Qualitative Analysis Subsection */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-t-lg"
          onClick={() => setIsQualitativeOpen(!isQualitativeOpen)}
        >
          <div className="flex items-center gap-2">
            {isQualitativeOpen ? (
              <ChevronUp className="h-5 w-5 text-amber-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-amber-600" />
            )}
            <AlertTriangle className="h-5 w-5 text-amber-600 mr-1" />
            <span className="text-lg font-medium text-amber-800">{t('admin.incidents.characteristics.qualitativeAnalysis', 'Qualitative Analysis')}</span>
          </div>
        </button>
        {isQualitativeOpen && (
          <div className="p-5 bg-white">
            <div className="grid grid-cols-3 gap-x-8 gap-y-6 w-full">
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2 text-red-600" />
                  {t('admin.incidents.characteristics.assignedRisk', 'Assigned Risk')}
                </label>
                <div className="flex gap-2">
                  <div className="w-full">
                    <Input
                      value={getAssignedRiskName()}
                      readOnly
                      placeholder={t('admin.incidents.characteristics.noRiskAssigned', 'No risk assigned')}
                      className="w-full bg-gray-50"
                    />
                    {/* Hidden input to ensure riskID is included in form submission */}
                    <input
                      type="hidden"
                      name="riskID"
                      value={incident.riskID || ''}
                      data-testid="hidden-risk-id"
                    />
                  </div>
                  <Button
                    type="button"
                    className="bg-[#F62D51] hover:bg-red-700"
                    title={t('admin.incidents.characteristics.addNewRisk', 'Add new risk')}
                    onClick={() => setIsNewRiskModalOpen(true)}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    title={t('admin.incidents.characteristics.linkToExistingRisk', 'Link to existing risk')}
                    onClick={() => setIsRiskSelectionModalOpen(true)}
                  >
                    <Link className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <Shield className="h-4 w-4 mr-2 text-green-600" />
                  {t('admin.incidents.characteristics.control', 'Control')}
                </label>
                <div className="flex gap-2">
                  <div className="w-full">
                    <Input
                      value={getAssignedControlName()}
                      readOnly
                      placeholder={t('admin.incidents.characteristics.noControlAssigned', 'No control assigned')}
                      className="w-full bg-gray-50"
                    />
                    {/* Hidden input to ensure controlID is included in form submission */}
                    <input
                      type="hidden"
                      name="controlID"
                      value={incident.controlID || ''}
                      data-testid="hidden-control-id"
                    />
                  </div>
                  <Button
                    type="button"
                    className="bg-[#F62D51] hover:bg-red-700"
                    title={t('admin.incidents.characteristics.addNewControl', 'Add new control')}
                    onClick={() => setIsNewControlModalOpen(true)}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    title={t('admin.incidents.characteristics.linkToExistingControl', 'Link to existing control')}
                    onClick={() => setIsControlSelectionModalOpen(true)}
                  >
                    <Link className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <FileType className="h-4 w-4 mr-2 text-purple-600" />
                  {t('admin.incidents.characteristics.incidentType', 'Incident Type')}
                </label>
                <div className="flex gap-2">
                  <div className="w-full">
                    <Input
                      value={getAssignedIncidentTypeName()}
                      readOnly
                      placeholder={t('admin.incidents.characteristics.noIncidentTypeAssigned', 'No incident type assigned')}
                      className="w-full bg-gray-50"
                    />
                    {/* Hidden input to ensure incidentTypeID is included in form submission */}
                    <input
                      type="hidden"
                      name="incidentTypeID"
                      value={incident.incidentTypeID || ''}
                      data-testid="hidden-incident-type-id"
                    />
                  </div>
                  <Button
                    type="button"
                    className="bg-[#F62D51] hover:bg-red-700"
                    title={t('admin.incidents.characteristics.addNewIncidentType', 'Add new incident type')}
                    onClick={() => setIsNewIncidentTypeModalOpen(true)}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    title={t('admin.incidents.characteristics.linkToExistingIncidentType', 'Link to existing incident type')}
                    onClick={() => setIsIncidentTypeSelectionModalOpen(true)}
                  >
                    <Link className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <Dialog open={isNewRiskModalOpen} onOpenChange={setIsNewRiskModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Risk</DialogTitle>
            <DialogDescription>
              Create a new risk to attach to this incident.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={(e) => {
            e.preventDefault();
            handleNewRiskSubmit({
              name: newRisk.name,
              comment: newRisk.comment
            });
            setIsNewRiskModalOpen(false);
          }}>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={newRisk.name}
                  onChange={(e) => setNewRisk({ ...newRisk, name: e.target.value })}
                  placeholder="Enter risk name"
                  required
                />
              </div>
              <div>
                <Label htmlFor="comment">Comment</Label>
                <Textarea
                  id="comment"
                  value={newRisk.comment}
                  onChange={(e) => setNewRisk({ ...newRisk, comment: e.target.value })}
                  placeholder="Enter comment"
                  rows={3}
                />
              </div>
            </div>
            <div className="flex justify-end gap-3 mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsNewRiskModalOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white"
              >
                Create Risk
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      <RiskSelectionModal
        open={isRiskSelectionModalOpen}
        onClose={() => setIsRiskSelectionModalOpen(false)}
        onSelect={handleRiskSelection}
        risks={risks}
      />

      <NewControlModal
        open={isNewControlModalOpen}
        onClose={() => setIsNewControlModalOpen(false)}
        onSubmit={handleNewControlSubmit}
      />

      <ControlSelectionModal
        open={isControlSelectionModalOpen}
        onClose={() => setIsControlSelectionModalOpen(false)}
        onSelect={handleControlSelection}
        controls={controls}
      />

      <NewIncidentTypeModal
        open={isNewIncidentTypeModalOpen}
        onClose={() => setIsNewIncidentTypeModalOpen(false)}
        onSubmit={handleNewIncidentTypeSubmit}
      />

      <IncidentTypeSelectionModal
        open={isIncidentTypeSelectionModalOpen}
        onClose={() => setIsIncidentTypeSelectionModalOpen(false)}
        onSelect={handleIncidentTypeSelection}
        incidentTypes={incidentTypes}
      />
    </div>
  );
}





