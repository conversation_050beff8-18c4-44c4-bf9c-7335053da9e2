const { sequelize } = require('../models/index');
const db = require('../models');

async function syncRapportSupportTable() {
  try {
    console.log('Starting database sync for AuditMissionRapportSupport table...');
    
    // Sync only the AuditMissionRapportSupport table
    await db.AuditMissionRapportSupport.sync({ force: false });
    
    console.log('✅ AuditMissionRapportSupport table synced successfully!');
    
    // Test the table by checking if it exists
    const tableExists = await sequelize.getQueryInterface().showAllTables();
    const hasTable = tableExists.includes('AuditMissionRapportSupports');
    
    if (hasTable) {
      console.log('✅ Table AuditMissionRapportSupports exists in database');
    } else {
      console.log('❌ Table AuditMissionRapportSupports not found in database');
    }
    
    // Close the connection
    await sequelize.close();
    console.log('Database connection closed.');
    
  } catch (error) {
    console.error('❌ Error syncing table:', error);
    process.exit(1);
  }
}

// Run the sync
syncRapportSupportTable();
