const { Sequelize } = require('sequelize');
require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });

// Create Sequelize instance
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: console.log
  }
);

async function runMigration() {
  try {
    console.log('🚀 Starting Campagnes migration...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Import and run the migration
    const migration = require('../migrations/20241201000000-create-campagnes.js');
    
    console.log('Running migration UP...');
    await migration.up(sequelize.getQueryInterface(), Sequelize);
    
    console.log('✅ Migration completed successfully!');
    console.log('Campagnes and UserCampagne tables have been created.');
    
    // Verify the migration by checking if the tables exist
    const [campagnesResult] = await sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'Campagnes'
    `);
    
    const [userCampagneResult] = await sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'UserCampagne'
    `);
    
    if (campagnesResult.length > 0) {
      console.log('✅ Campagnes table created successfully');
    } else {
      console.log('❌ Campagnes table was not created');
    }
    
    if (userCampagneResult.length > 0) {
      console.log('✅ UserCampagne table created successfully');
    } else {
      console.log('❌ UserCampagne table was not created');
    }
    
  } catch (error) {
    console.error('❌ Error running migration:', error);
    
    // Check if it's a "table already exists" error
    if (error.message.includes('already exists')) {
      console.log('⚠️  Tables already exist, skipping migration...');
    } else {
      throw error;
    }
  } finally {
    // Close the database connection
    await sequelize.close();
    console.log('Database connection closed');
  }
}

// Run the migration
runMigration();
