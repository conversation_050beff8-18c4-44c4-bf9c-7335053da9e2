pipeline {
    agent any
    stages {
        stage('Checkout') {
            steps {
                git url: 'https://github.com/KarimDerbel98/Vitalis-mern.git', branch: 'merge', credentialsId: 'github-pat'
            }
        }
        stage('Build and Deploy') {
            steps {
                bat 'docker-compose -f C:\\Users\\<USER>\\Documents\\GitHub\\Vitalis-mern\\docker-compose.yml up --build -d'
            }
        }
    }
    post {
        always {
            cleanWs()
        }
    }
}