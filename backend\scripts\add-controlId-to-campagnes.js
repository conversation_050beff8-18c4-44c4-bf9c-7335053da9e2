const { Sequelize } = require('sequelize');
require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });

// Create Sequelize instance
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: console.log
  }
);

async function addControlIdColumn() {
  try {
    console.log('🚀 Adding controlId column to Campagnes table...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Check if column already exists
    const [results] = await sequelize.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'Campagnes' 
      AND column_name = 'controlId'
    `);

    if (results.length > 0) {
      console.log('⚠️  controlId column already exists in Campagnes table');
      return;
    }

    // Import and run the migration
    const migration = require('../migrations/20241201000001-add-controlId-to-campagnes.js');
    
    console.log('Running migration UP...');
    await migration.up(sequelize.getQueryInterface(), Sequelize);
    
    console.log('✅ Migration completed successfully!');
    console.log('controlId column has been added to Campagnes table.');
    
    // Verify the migration by checking if the column exists
    const [verifyResults] = await sequelize.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'Campagnes' 
      AND column_name = 'controlId'
    `);
    
    if (verifyResults.length > 0) {
      console.log('✅ controlId column verified successfully');
    } else {
      console.log('❌ controlId column was not created');
    }
    
  } catch (error) {
    console.error('❌ Error running migration:', error);
    
    // Check if it's a "column already exists" error
    if (error.message.includes('already exists')) {
      console.log('⚠️  Column already exists, skipping migration...');
    } else {
      throw error;
    }
  } finally {
    // Close the database connection
    await sequelize.close();
    console.log('Database connection closed');
  }
}

// Run the migration
addControlIdColumn();
