import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { ClipboardCheck, Calendar, FileText, AlertTriangle, BarChart, Users, CheckCircle, Activity, Target, TrendingUp, Loader2, RefreshCw } from 'lucide-react';
import axios from 'axios';
import { getApiBaseUrl } from '@/utils/api-config';

function AuditWelcome() {
  const navigate = useNavigate();
  const user = useSelector((state) => state.auth.user);

  // Determine if user is an Audit Director or Auditor
  const isAuditDirector = user?.roles?.some(role => role.code === 'audit_director');
  const roleTitle = isAuditDirector ? 'Audit Director' : 'Auditor';

  // State for real statistics
  const [statistics, setStatistics] = useState({
    totalPlans: 0,
    totalMissions: 0,
    totalActivities: 0,
    totalConstats: 0,
    totalRecommendations: 0,
    plansByStatus: {},
    missionsByStatus: {},
    constatsByRisk: {},
    recommendationsByStatus: {}
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastFetchTime, setLastFetchTime] = useState(null);
  const [fetchDuration, setFetchDuration] = useState(null);
  const abortControllerRef = useRef(null);

  const API_BASE_URL = getApiBaseUrl();

  // Cache statistics for 5 minutes to avoid unnecessary requests
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Fetch audit statistics - OPTIMIZED VERSION with caching
  const fetchAuditStatistics = async (forceRefresh = false) => {
    try {
      // Check cache first (unless force refresh)
      if (!forceRefresh && lastFetchTime && Date.now() - lastFetchTime < CACHE_DURATION) {
        console.log('Using cached statistics data');
        return;
      }

      setLoading(true);
      setError(null);

      // Cancel any existing request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();

      console.log('Fetching audit statistics...');
      const startTime = Date.now();

      // Use the new optimized statistics endpoint
      const response = await axios.get(`${API_BASE_URL}/audit-statistics`, {
        withCredentials: true,
        timeout: 10000, // 10 second timeout
        signal: abortControllerRef.current.signal
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`Statistics fetched in ${duration}ms`);
      setFetchDuration(duration);

      if (response.data.success) {
        setStatistics(response.data.data);
        setLastFetchTime(Date.now());
        console.log('Statistics loaded successfully:', response.data.data);
      } else {
        throw new Error(response.data.message || 'Failed to fetch statistics');
      }

    } catch (err) {
      // Don't show error for aborted requests
      if (err.name === 'CanceledError' || err.code === 'ERR_CANCELED') {
        console.log('Request was cancelled');
        return;
      }

      console.error('Error fetching audit statistics:', err);

      // Provide more specific error messages
      if (err.code === 'ECONNABORTED') {
        setError('Timeout: Les statistiques prennent trop de temps à charger');
      } else if (err.response?.status === 401) {
        setError('Session expirée. Veuillez vous reconnecter.');
      } else if (err.response?.status >= 500) {
        setError('Erreur serveur. Veuillez réessayer plus tard.');
      } else {
        setError('Erreur lors du chargement des statistiques');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAuditStatistics();

    // Cleanup function to cancel requests on unmount
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [API_BASE_URL]);

  // Defensive fallback for missing recommendationsByStatus (backend may omit it)
  const safeRecommendationsByStatus = statistics.recommendationsByStatus || {};

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Chargement des statistiques d'audit...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Erreur de chargement</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Réessayer
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-transparent rounded-xl p-8 border border-border/50">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Bienvenue, {user?.username || 'User'}
            </h1>
            <p className="text-muted-foreground text-lg">
              Vous êtes connecté en tant que <span className="font-semibold text-primary">{roleTitle}</span>
            </p>
          </div>
          <div className="hidden md:flex items-center space-x-4">
            <div className="text-right">
              {fetchDuration && (
                <p className="text-xs text-muted-foreground">
                  Chargé en {fetchDuration}ms
                </p>
              )}
              {lastFetchTime && (
                <p className="text-xs text-muted-foreground">
                  Mis à jour: {new Date(lastFetchTime).toLocaleTimeString()}
                </p>
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchAuditStatistics(true)}
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Actualiser
            </Button>
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
              <BarChart className="h-6 w-6 text-primary" />
            </div>
          </div>
        </div>
        <div className="mt-6 flex flex-wrap gap-3">
          <Button
            className="bg-primary hover:bg-primary/90 shadow-lg hover:shadow-xl transition-all duration-200"
            onClick={() => navigate('/audit/plans-daudit')}
          >
            <ClipboardCheck className="mr-2 h-4 w-4" />
            Plans d'audit
          </Button>
          <Button variant="outline" className="hover:bg-primary/5 transition-colors">
            <Calendar className="mr-2 h-4 w-4" />
            Calendrier d'audit
          </Button>
          <Button variant="outline" className="hover:bg-primary/5 transition-colors">
            <FileText className="mr-2 h-4 w-4" />
            Rapports
          </Button>
        </div>
      </div>

      {/* Main Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Audit Plans Card */}
        <Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-950/50 dark:to-blue-900/30">
          <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-full -translate-y-10 translate-x-10"></div>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <ClipboardCheck className="h-6 w-6 text-blue-600" />
              </div>
              <TrendingUp className="h-4 w-4 text-blue-500" />
            </div>
            <CardTitle className="text-lg font-semibold text-blue-900 dark:text-blue-100">Plans d'audit</CardTitle>
            <CardDescription className="text-blue-700/70 dark:text-blue-300/70">Total des plans créés</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-900 dark:text-blue-100 mb-2">{statistics.totalPlans}</div>
            <div className="text-sm text-blue-600/80 dark:text-blue-400/80">
              {statistics.plansByStatus['Completed'] || 0} terminés, {statistics.plansByStatus['In Progress'] || 0} en cours
            </div>
          </CardContent>
        </Card>

        {/* Audit Missions Card */}
        <Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-950/50 dark:to-green-900/30">
          <div className="absolute top-0 right-0 w-20 h-20 bg-green-500/10 rounded-full -translate-y-10 translate-x-10"></div>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="p-2 bg-green-500/10 rounded-lg">
                <Target className="h-6 w-6 text-green-600" />
              </div>
              <TrendingUp className="h-4 w-4 text-green-500" />
            </div>
            <CardTitle className="text-lg font-semibold text-green-900 dark:text-green-100">Missions d'audit</CardTitle>
            <CardDescription className="text-green-700/70 dark:text-green-300/70">Total des missions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-900 dark:text-green-100 mb-2">{statistics.totalMissions}</div>
            <div className="text-sm text-green-600/80 dark:text-green-400/80">
              {statistics.missionsByStatus['Completed'] || 0} terminées, {statistics.missionsByStatus['In Progress'] || 0} en cours
            </div>
          </CardContent>
        </Card>

        {/* Audit Activities Card */}
        <Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-950/50 dark:to-purple-900/30">
          <div className="absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full -translate-y-10 translate-x-10"></div>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="p-2 bg-purple-500/10 rounded-lg">
                <Activity className="h-6 w-6 text-purple-600" />
              </div>
              <TrendingUp className="h-4 w-4 text-purple-500" />
            </div>
            <CardTitle className="text-lg font-semibold text-purple-900 dark:text-purple-100">Activités d'audit</CardTitle>
            <CardDescription className="text-purple-700/70 dark:text-purple-300/70">Total des activités</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-900 dark:text-purple-100 mb-2">{statistics.totalActivities}</div>
            <div className="text-sm text-purple-600/80 dark:text-purple-400/80">
              Activités planifiées et exécutées
            </div>
          </CardContent>
        </Card>

        {/* Constats Card */}
        <Card className="relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-orange-50 to-orange-100/50 dark:from-orange-950/50 dark:to-orange-900/30">
          <div className="absolute top-0 right-0 w-20 h-20 bg-orange-500/10 rounded-full -translate-y-10 translate-x-10"></div>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="p-2 bg-orange-500/10 rounded-lg">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
              </div>
              <TrendingUp className="h-4 w-4 text-orange-500" />
            </div>
            <CardTitle className="text-lg font-semibold text-orange-900 dark:text-orange-100">Constats d'audit</CardTitle>
            <CardDescription className="text-orange-700/70 dark:text-orange-300/70">Total des constats</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-orange-900 dark:text-orange-100 mb-2">{statistics.totalConstats}</div>
            <div className="text-sm text-orange-600/80 dark:text-orange-400/80">
              {statistics.constatsByRisk['tres fort'] || 0} critiques, {statistics.constatsByRisk['fort'] || 0} élevés
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Risk Analysis Card */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-red-50/50 to-orange-50/50 dark:from-red-950/20 dark:to-orange-950/20">
          <CardHeader className="pb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-red-500/10 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <CardTitle className="text-xl font-semibold">Analyse des risques</CardTitle>
                <CardDescription>Répartition des constats par niveau de risque</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Very High Risk */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-red-600"></div>
                    <span className="font-medium text-red-900 dark:text-red-100">Très élevé</span>
                  </div>
                  <span className="font-bold text-red-900 dark:text-red-100">{statistics.constatsByRisk['tres fort'] || 0}</span>
                </div>
                <div className="w-full bg-red-100 dark:bg-red-900/30 rounded-full h-2">
                  <div
                    className="bg-red-600 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${statistics.totalConstats > 0 ? ((statistics.constatsByRisk['tres fort'] || 0) / statistics.totalConstats) * 100 : 0}%` }}
                  ></div>
                </div>
              </div>

              {/* High Risk */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-orange-500"></div>
                    <span className="font-medium text-orange-900 dark:text-orange-100">Élevé</span>
                  </div>
                  <span className="font-bold text-orange-900 dark:text-orange-100">{statistics.constatsByRisk['fort'] || 0}</span>
                </div>
                <div className="w-full bg-orange-100 dark:bg-orange-900/30 rounded-full h-2">
                  <div
                    className="bg-orange-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${statistics.totalConstats > 0 ? ((statistics.constatsByRisk['fort'] || 0) / statistics.totalConstats) * 100 : 0}%` }}
                  ></div>
                </div>
              </div>

              {/* Medium Risk */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <span className="font-medium text-yellow-900 dark:text-yellow-100">Moyen</span>
                  </div>
                  <span className="font-bold text-yellow-900 dark:text-yellow-100">{statistics.constatsByRisk['moyen'] || 0}</span>
                </div>
                <div className="w-full bg-yellow-100 dark:bg-yellow-900/30 rounded-full h-2">
                  <div
                    className="bg-yellow-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${statistics.totalConstats > 0 ? ((statistics.constatsByRisk['moyen'] || 0) / statistics.totalConstats) * 100 : 0}%` }}
                  ></div>
                </div>
              </div>

              {/* Low Risk */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    <span className="font-medium text-green-900 dark:text-green-100">Faible</span>
                  </div>
                  <span className="font-bold text-green-900 dark:text-green-100">{statistics.constatsByRisk['faible'] || 0}</span>
                </div>
                <div className="w-full bg-green-100 dark:bg-green-900/30 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${statistics.totalConstats > 0 ? ((statistics.constatsByRisk['faible'] || 0) / statistics.totalConstats) * 100 : 0}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recommendations Progress Card */}
        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50/50 to-emerald-50/50 dark:from-green-950/20 dark:to-emerald-950/20">
          <CardHeader className="pb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-500/10 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <CardTitle className="text-xl font-semibold">Recommandations</CardTitle>
                <CardDescription>Suivi des recommandations d'audit</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Progress Circle */}
              <div className="flex items-center justify-center">
                <div className="relative w-32 h-32">
                  <svg viewBox="0 0 36 36" className="w-full h-full transform -rotate-90">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      className="text-gray-200 dark:text-gray-700"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeDasharray={`${statistics.totalRecommendations > 0 ? ((safeRecommendationsByStatus['Fermé'] || 0) / statistics.totalRecommendations) * 100 : 0}, 100`}
                      strokeLinecap="round"
                      className="text-green-500 transition-all duration-1000"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                        {statistics.totalRecommendations > 0 ? Math.round(((safeRecommendationsByStatus['Fermé'] || 0) / statistics.totalRecommendations) * 100) : 0}%
                      </div>
                      <div className="text-xs text-green-700 dark:text-green-300">Terminées</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Status Breakdown */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-green-100 dark:bg-green-900/30 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {safeRecommendationsByStatus['Fermé'] || 0}
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300">Fermées</div>
                </div>
                <div className="bg-orange-100 dark:bg-orange-900/30 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {safeRecommendationsByStatus['Ouvert'] || 0}
                  </div>
                  <div className="text-sm text-orange-700 dark:text-orange-300">Ouvertes</div>
                </div>
              </div>

              {/* Additional Status */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-100 dark:bg-blue-900/30 rounded-lg p-4 text-center">
                  <div className="text-lg font-bold text-blue-900 dark:text-blue-100">
                    {safeRecommendationsByStatus['En cours'] || 0}
                  </div>
                  <div className="text-xs text-blue-700 dark:text-blue-300">En cours</div>
                </div>
                <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 text-center">
                  <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
                    {statistics.totalRecommendations}
                  </div>
                  <div className="text-xs text-gray-700 dark:text-gray-300">Total</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="border-0 shadow-lg bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-900/50 dark:to-gray-900/50">
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <BarChart className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-xl font-semibold">Actions rapides</CardTitle>
              <CardDescription>Accédez rapidement aux fonctionnalités principales</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button
              variant="outline"
              className="h-auto flex flex-col items-center p-6 space-y-3 hover:bg-primary/5 hover:border-primary/20 transition-all duration-200 group"
              onClick={() => navigate('/audit/plans-daudit')}
            >
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors">
                <ClipboardCheck className="h-6 w-6 text-blue-600" />
              </div>
              <span className="text-sm font-medium text-center">Nouveau plan d'audit</span>
            </Button>

            <Button
              variant="outline"
              className="h-auto flex flex-col items-center p-6 space-y-3 hover:bg-primary/5 hover:border-primary/20 transition-all duration-200 group"
            >
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg group-hover:bg-green-200 dark:group-hover:bg-green-900/50 transition-colors">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
              <span className="text-sm font-medium text-center">Générer un rapport</span>
            </Button>

            <Button
              variant="outline"
              className="h-auto flex flex-col items-center p-6 space-y-3 hover:bg-primary/5 hover:border-primary/20 transition-all duration-200 group"
            >
              <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg group-hover:bg-purple-200 dark:group-hover:bg-purple-900/50 transition-colors">
                <BarChart className="h-6 w-6 text-purple-600" />
              </div>
              <span className="text-sm font-medium text-center">Tableau de bord</span>
            </Button>

            <Button
              variant="outline"
              className="h-auto flex flex-col items-center p-6 space-y-3 hover:bg-primary/5 hover:border-primary/20 transition-all duration-200 group"
            >
              <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg group-hover:bg-orange-200 dark:group-hover:bg-orange-900/50 transition-colors">
                <Users className="h-6 w-6 text-orange-600" />
              </div>
              <span className="text-sm font-medium text-center">Équipe d'audit</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default AuditWelcome;
