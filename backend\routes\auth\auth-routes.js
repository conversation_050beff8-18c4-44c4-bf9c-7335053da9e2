const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { User, Role } = require('../../models');
// const { loadUserRoles } = require('../../middleware/auth'); 
const router = express.Router();

// Register route
router.post('/register', async (req, res) => {
    // console.log("[BACKEND /register] ROUTE HANDLER ENTERED"); 
    try {
        const { username, email, password, roles: requestedRoles } = req.body;

        // Check if any required fields are missing
        if (!username || !email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Missing required fields',
                receivedData: { username, email, password: password ? '[HIDDEN]' : null }
            });
        }

        // Check if user already exists
        const userExists = await User.findOne({ where: { email } });
        if (userExists) {
            return res.status(400).json({
                success: false,
                message: 'User already exists'
            });
        }

        // Hash password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);

        // Start a transaction
        const transaction = await User.sequelize.transaction();

        try {
            // Create user
            const user = await User.create({
                username,
                email,
                password: hashedPassword
            }, { transaction });

            // Get the default role (GRC Contributor) if no roles provided
            let rolesToAssign = [];

            if (requestedRoles && Array.isArray(requestedRoles) && requestedRoles.length > 0) {
                // If specific roles were requested, find them by code
                const roleCodes = requestedRoles.map(r => typeof r === 'string' ? r : r.code);
                rolesToAssign = await Role.findAll({
                    where: {
                        code: roleCodes
                    }
                }, { transaction });
            } else {
                // Default to GRC Contributor
                const defaultRole = await Role.findOne({
                    where: { code: 'grc_contributor' }
                }, { transaction });

                if (defaultRole) {
                    rolesToAssign = [defaultRole];
                }
            }

            // Assign roles to user
            if (rolesToAssign.length > 0) {
                await user.addRoles(rolesToAssign, { transaction });
            }

            // Commit transaction
            await transaction.commit();

            // Fetch the user with roles for the response
            const userWithRoles = await User.findByPk(user.id, {
                include: [{
                    model: Role,
                    as: 'roles',
                    through: { attributes: [] }
                }]
            });

            // Map roles for response
            const userRoles = userWithRoles.roles ? userWithRoles.roles.map(role => ({
                id: role.id,
                name: role.name,
                code: role.code
            })) : [];

            // For backward compatibility - determine primary role
            let primaryRoleForRegister = 'user'; // Default

            if (userRoles.length > 0) {
                // Priority order: GRC Administrator > GRC Manager > Risk Manager > GRC Contributor
                const rolePriority = {
                    'grc_admin': 1,
                    'grc_manager': 2,
                    'risk_manager': 3,
                    'grc_contributor': 4
                };

                // Sort roles by priority and get the highest priority role
                const sortedRoles = [...userRoles].sort((a, b) =>
                    (rolePriority[a.code] || 999) - (rolePriority[b.code] || 999)
                );

                // Map new role codes to old role names for backward compatibility
                const roleCodeMap = {
                    'grc_admin': 'super_admin',
                    'grc_manager': 'admin',
                    'risk_manager': 'admin', // Map Risk Manager to admin for backward compatibility
                    'grc_contributor': 'user'
                };

                primaryRoleForRegister = roleCodeMap[sortedRoles[0].code] || 'user';
            }

            // No need to update role field as it has been removed

            res.status(201).json({
                success: true,
                message: 'User created successfully',
                user: {
                    id: user.id,
                    username: user.username,
                    email: user.email,
                    role: primaryRoleForRegister, // For backward compatibility
                    roles: userRoles // New field with all roles
                }
            });
        } catch (error) {
            // Rollback transaction on error
            await transaction.rollback();
            throw error;
        }
    } catch (error) {
        console.error('[BACKEND /register] Error:', error);
        res.status(500).json({ success: false, message: 'Server error during registration' });
    }
});

// Login route
router.post('/login', async (req, res) => {
    console.log(`[BACKEND /login] Attempt for: ${req.body.email}`);
    console.log(`[BACKEND /login] Request origin: ${req.headers.origin}`);
    console.log(`[BACKEND /login] Request host: ${req.headers.host}`);
    
    try {
        const { email, password } = req.body;
        const user = await User.findOne({
            where: { email },
            include: [{ model: Role, as: 'roles', through: { attributes: [] } }]
        });
        
        if (!user) return res.status(400).json({ success: false, message: 'Invalid credentials' });
        
        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) return res.status(400).json({ success: false, message: 'Invalid credentials' });
        
        const userRoles = user.roles ? user.roles.map(r => ({ id: r.id, name: r.name, code: r.code })) : [];
        const token = jwt.sign({ userId: user.id }, process.env.JWT_SECRET, { expiresIn: '12h' });
        
        // Set cookie with more permissive settings for IP-based access
        const cookieOptions = { 
            httpOnly: true, 
            secure: process.env.NODE_ENV === 'production', 
            sameSite: 'none', // Changed to 'none' to allow cross-site access
            maxAge: 3600000,
            // Remove domain restriction completely
        };
        
        console.log(`[BACKEND /login] Setting cookie with options:`, cookieOptions);
        res.cookie('token', token, cookieOptions);
        
        // Log response headers to see Set-Cookie
        res.on('finish', () => {
            console.log(`[BACKEND /login] Response headers:`, res.getHeaders());
            console.log(`[BACKEND /login] Set-Cookie header:`, res.getHeader('Set-Cookie'));
        });
        
        console.log(`[BACKEND /login] Setting cookie for: ${req.body.email}, token length: ${token.length}`);
        
        let primaryRole = 'user'; 
        if (userRoles.length > 0) {
            const rolePriority = { 'grc_admin': 1, 'grc_manager': 2, 'risk_manager': 3, 'incident_manager': 4, 'internal_controller': 5, 'compliance_manager': 6, 'audit_director': 7, 'auditor': 8, 'grc_contributor': 9 };
            const sortedRoles = [...userRoles].sort((a, b) => (rolePriority[a.code] || 999) - (rolePriority[b.code] || 999));
            const roleCodeMap = { 'grc_admin': 'super_admin', 'grc_manager': 'admin', 'risk_manager': 'admin', 'incident_manager': 'admin', 'internal_controller': 'admin', 'compliance_manager': 'admin', 'audit_director': 'admin', 'auditor': 'admin', 'grc_contributor': 'user' };
            if (sortedRoles.length > 0 && sortedRoles[0]) primaryRole = roleCodeMap[sortedRoles[0].code] || 'user';
        }
        const finalUserObject = { id: user.id, username: user.username, email: user.email, role: primaryRole, roles: userRoles };
        
        // Include token in the response body for cross-domain requests
        res.json({ 
            success: true, 
            message: 'Logged in successfully', 
            user: finalUserObject,
            token: token // Add token to response
        });
    } catch (error) {
        console.error('[BACKEND /login] Error:', error);
        res.status(500).json({ success: false, message: 'Server error during login' });
    }
});

// Logout route
router.post('/logout', (req, res) => {
    // console.log("[BACKEND /logout] Called"); 
    res.clearCookie('token');
    res.json({ success: true, message: 'Logged out successfully' });
});

// Check auth status route
router.get('/check-auth', async (req, res) => {
    try {
        // Get token from cookie or Authorization header
        const token = req.cookies.token || req.headers.authorization?.split(' ')[1];
        
        console.log(`[BACKEND /check-auth] Token present: ${!!token}`);
        console.log(`[BACKEND /check-auth] Token source: ${req.cookies.token ? 'cookie' : (req.headers.authorization ? 'header' : 'none')}`);
        
        if (!token) return res.status(401).json({ success: false, message: 'No token found' });

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findByPk(decoded.userId, {
            include: [{ model: Role, as: 'roles', through: { attributes: [] } }]
        });

        if (!user) return res.status(401).json({ success: false, message: 'User not found' });
        
        // Refresh the cookie with the same settings when checking auth
        res.cookie('token', token, { 
            httpOnly: true, 
            secure: process.env.NODE_ENV === 'production', 
            sameSite: 'none', // Changed to 'none' to allow cross-site access
            maxAge: 3600000,
            // Remove domain restriction completely
        });
        
        const userRoles = user.roles ? user.roles.map(r => ({ id: r.id, name: r.name, code: r.code })) : [];
        
        // Determine primary role
        let primaryRole = 'user';
        if (userRoles.length > 0) {
            const rolePriority = { 'grc_admin': 1, 'grc_manager': 2, 'risk_manager': 3, 'incident_manager': 4, 'internal_controller': 5, 'compliance_manager': 6, 'audit_director': 7, 'auditor': 8, 'grc_contributor': 9 };
            const sortedRoles = [...userRoles].sort((a, b) => (rolePriority[a.code] || 999) - (rolePriority[b.code] || 999));
            const roleCodeMap = { 'grc_admin': 'super_admin', 'grc_manager': 'admin', 'risk_manager': 'admin', 'incident_manager': 'admin', 'internal_controller': 'admin', 'compliance_manager': 'admin', 'audit_director': 'admin', 'auditor': 'admin', 'grc_contributor': 'user' };
            if (sortedRoles.length > 0 && sortedRoles[0]) primaryRole = roleCodeMap[sortedRoles[0].code] || 'user';
        }
        const finalUserObject = { id: user.id, username: user.username, email: user.email, role: primaryRole, roles: userRoles };
        
        res.json({ 
            success: true, 
            user: finalUserObject,
            token: token // Include token in response for token renewal
        });
    } catch (error) {
        console.error('[BACKEND /check-auth] Error:', error);
        if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
            return res.status(401).json({ success: false, message: 'Invalid or expired token' });
        }
        res.status(500).json({ success: false, message: 'Server error during auth check' });
    }
});

module.exports = router;
