import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, ArrowUpDown, Trash2, Loader2, Activity } from 'lucide-react';
import { useSelector } from 'react-redux';
import { hasPermission } from '@/store/auth-slice';
import PageHeader from "@/components/ui/page-header";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import TablePagination from "@/components/ui/table-pagination";
import FilterPanel from "@/components/ui/filter-panel";
import { toast } from "react-hot-toast";
import controlIcon from '@/assets/campagne.png';
import { getAllCampagnes, createCampagne, deleteCampagne } from '@/services/campagne-service';

function CampagnesManagement() {
  const navigate = useNavigate();
  const canCreate = useSelector(state => hasPermission(state, 'create'));
  const canDelete = useSelector(state => hasPermission(state, 'delete'));
  const [searchQuery, setSearchQuery] = useState('');
  const [campagnes, setCampagnes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [selectedCampagnes, setSelectedCampagnes] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [isOpen, setIsOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [activeFilters, setActiveFilters] = useState({
    status: "all"
  });

  const initialFormState = {
    name: '',
    code: '',
    description: '',
  };

  const [newCampagne, setNewCampagne] = useState(initialFormState);

  const fetchCampagnes = useCallback(async () => {
    try {
      setLoading(true);

      const params = {
        page: currentPage,
        limit: itemsPerPage,
        search: searchQuery,
        statut: activeFilters.status !== 'all' ? activeFilters.status : '',
        sortBy: sortConfig.key || 'createdAt',
        sortOrder: sortConfig.direction || 'DESC'
      };

      const response = await getAllCampagnes(params);

      if (response.success) {
        setCampagnes(response.data.campagnes || []);
        setError(null);
      } else {
        throw new Error(response.message || 'Erreur lors du chargement des campagnes');
      }
    } catch (error) {
      console.error('Error fetching campagnes:', error);
      toast.error('Erreur lors du chargement des campagnes. Veuillez actualiser la page.');
      if (campagnes.length === 0) {
        setCampagnes([]);
      }
      setError('Une erreur s\'est produite lors du chargement des campagnes');
    } finally {
      setLoading(false);
    }
  }, [currentPage, itemsPerPage, searchQuery, activeFilters.status, sortConfig.key, sortConfig.direction]);

  useEffect(() => {
    fetchCampagnes();
  }, [fetchCampagnes]);

  const handleSort = (key) => {
    setSortConfig((prevConfig) => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc',
    }));
  };

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedCampagnes(currentCampagnes.map(campagne => campagne.campagneID));
    } else {
      setSelectedCampagnes([]);
    }
  };

  const handleSelectCampagne = (id) => {
    setSelectedCampagnes(prev => prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]);
  };

  const handleRowClick = (id) => {
    const path = window.location.pathname;
    if (path.includes('/audit')) {
      navigate(`/audit/campagnes/edit/${id}`);
    } else {
      navigate(`/admin/campagnes/edit/${id}`);
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedCampagnes.length === 0) {
      toast.error('Aucune campagne sélectionnée');
      return;
    }

    if (window.confirm(`Êtes-vous sûr de vouloir supprimer ${selectedCampagnes.length} campagne(s) ?`)) {
      try {
        setLoading(true);

        // Delete each selected campagne
        const deletePromises = selectedCampagnes.map(id => deleteCampagne(id));
        await Promise.all(deletePromises);

        setSelectedCampagnes([]);
        toast.success(`${selectedCampagnes.length} campagne(s) supprimée(s) avec succès`);

        // Refresh the list
        await fetchCampagnes();
      } catch (error) {
        console.error('Error deleting campagnes:', error);
        toast.error('Erreur lors de la suppression des campagnes');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const campagneData = {
        name: newCampagne.name,
        code: newCampagne.code,
        description: newCampagne.description,
        statut: 'planifié' // Use backend ENUM value
      };

      const response = await createCampagne(campagneData);

      if (response.success) {
        setNewCampagne(initialFormState);
        setIsOpen(false);
        toast.success('Campagne créée avec succès');

        // Refresh the list
        await fetchCampagnes();
      } else {
        throw new Error(response.message || 'Erreur lors de la création de la campagne');
      }
    } catch (error) {
      console.error('Error creating campagne:', error);
      toast.error(error.message || 'Erreur lors de la création de la campagne');
    } finally {
      setSubmitting(false);
    }
  };

  const handleFilterChange = (filterKey, value) => {
    setActiveFilters(prev => ({ ...prev, [filterKey]: value }));
    setCurrentPage(1);
  };

  const handleClearFilters = () => {
    setActiveFilters({
      status: "all"
    });
    setCurrentPage(1);
  };

  // Since filtering and sorting is now handled by the backend,
  // we can use the campagnes directly
  const currentCampagnes = campagnes;
  const sortedCampagnes = campagnes;

  // Pagination info will come from the API response
  const totalPages = Math.ceil(campagnes.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const columns = [
    { key: 'name', label: 'Nom', sortable: true },
    { key: 'code', label: 'Code', sortable: true },
    { key: 'description', label: 'Description', sortable: true },
    { key: 'statut', label: 'Statut', sortable: true },
    { key: 'createdAt', label: 'Date de création', sortable: true },
  ];

  const filters = [
    {
      id: 'status',
      label: 'Statut',
      component: (
        <Select
          value={activeFilters.status}
          onValueChange={(value) => handleFilterChange('status', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Sélectionner un statut" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tous</SelectItem>
            <SelectItem value="planifié">Planifié</SelectItem>
            <SelectItem value="en cours">En cours</SelectItem>
            <SelectItem value="terminé">Terminé</SelectItem>
            <SelectItem value="validé">Validé</SelectItem>
          </SelectContent>
        </Select>
      )
    }
  ];

  return (
    <div className="p-6 max-w-[1200px] mx-auto">
      <PageHeader
        title="Gestion des Campagnes"
        description="Définir et gérer les campagnes au sein de votre organisation."
        section="Campagnes"
        currentPage="Campagnes"
        searchPlaceholder="Rechercher des campagnes..."
        searchValue={searchQuery}
        onSearchChange={(e) => setSearchQuery(e.target.value)}
        icon={Activity}
      />

      <FilterPanel
        filters={filters}
        activeFilters={activeFilters}
        onClearFilters={handleClearFilters}
        className="mb-4"
      />

      <div className="flex justify-end items-center gap-3 mb-4">
        {selectedCampagnes.length > 0 && !loading && (
          <Button
            variant="outline"
            className={`border-2 border-red-500 rounded-lg shadow-sm transition-all duration-300 ${
              canDelete && !loading
                ? 'bg-[#F62D51]/90 text-white hover:bg-[#F62D51] hover:shadow-md'
                : 'text-gray-400 bg-gray-100 cursor-not-allowed hover:bg-gray-100'
            } flex items-center gap-2 px-6 py-2 font-semibold`}
            onClick={handleDeleteSelected}
            disabled={!canDelete || loading}
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Suppression...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                Supprimer ({selectedCampagnes.length})
              </>
            )}
          </Button>
        )}
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          {canCreate && (
            <DialogTrigger asChild>
              <Button className="bg-[#F62D51]/90 hover:bg-[#F62D51] text-white shadow-md flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Ajouter une Campagne
              </Button>
            </DialogTrigger>
          )}
          <DialogContent className="sm:max-w-[600px] p-6">
            <DialogHeader>
              <DialogTitle>Créer une Nouvelle Campagne</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Nom <span className="text-red-500">*</span></Label>
                <Input
                  required
                  id="name"
                  value={newCampagne.name}
                  onChange={(e) => setNewCampagne({ ...newCampagne, name: e.target.value })}
                  className="h-9 text-sm"
                  placeholder="Entrer le nom de la campagne"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="code">Code</Label>
                <Input
                  id="code"
                  value={newCampagne.code}
                  onChange={(e) => setNewCampagne({ ...newCampagne, code: e.target.value })}
                  className="h-9 text-sm"
                  placeholder="Entrer le code de la campagne"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={newCampagne.description}
                  onChange={(e) => setNewCampagne({ ...newCampagne, description: e.target.value })}
                  className="h-9 text-sm"
                  placeholder="Entrer la description"
                />
              </div>
              <div className="flex justify-end gap-4 mt-6">
                <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                  Annuler
                </Button>
                <Button type="submit" className="bg-[#F62D51] hover:bg-red-700" disabled={submitting}>
                  {submitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Création...
                    </>
                  ) : (
                    'Créer la Campagne'
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden flex-1 mb-16">
        {loading ? (
          // Loading state
          <div className="flex flex-col items-center justify-center py-16">
            <Loader2 className="h-8 w-8 animate-spin text-red-600 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Chargement des campagnes...
            </h3>
            <p className="text-gray-500 text-center">
              Récupération de vos campagnes assignées
            </p>
          </div>
        ) : currentCampagnes.length === 0 ? (
          // Empty state
          <div className="flex flex-col items-center justify-center py-16">
            <Activity className="h-16 w-16 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Aucune campagne assignée
            </h3>
            <p className="text-gray-500 text-center max-w-md">
              Vous n'êtes actuellement assigné à aucune campagne. Les campagnes vous seront visibles une fois que vous y serez assigné par un administrateur.
            </p>
          </div>
        ) : (
          // Table with data
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-sm font-medium text-[#555F6D]">
                    <Checkbox
                      checked={selectedCampagnes.length === currentCampagnes.length && currentCampagnes.length > 0}
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all"
                    />
                  </th>
                  {columns.map(column => (
                    <th
                      key={column.key}
                      className="px-6 py-3 text-left text-sm font-medium text-[#555F6D] cursor-pointer hover:bg-gray-100"
                      onClick={() => column.sortable && handleSort(column.key)}
                    >
                      <div className="flex items-center gap-1">
                        {column.label}
                        {sortConfig.key === column.key && <ArrowUpDown className="w-4 h-4" />}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {currentCampagnes.map((campagne, index) => (
                  <tr
                    key={campagne.campagneID}
                    className={`hover:bg-gray-50 cursor-pointer ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}
                    onClick={() => handleRowClick(campagne.campagneID)}
                  >
                    <td className="px-6 py-4">
                      <Checkbox
                        checked={selectedCampagnes.includes(campagne.campagneID)}
                        onCheckedChange={() => handleSelectCampagne(campagne.campagneID)}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </td>
                    <td className="px-6 py-4 text-sm font-bold text-[#242A33] whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <img src={controlIcon} alt="Campagne" className="w-5 h-5" />
                        {campagne.name}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">{campagne.code || '-'}</td>
                    <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">{campagne.description || '-'}</td>
                    <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        campagne.statut === 'en cours' ? 'bg-green-100 text-green-800' :
                        campagne.statut === 'planifié' ? 'bg-blue-100 text-blue-800' :
                        campagne.statut === 'terminé' ? 'bg-gray-100 text-gray-800' :
                        campagne.statut === 'validé' ? 'bg-purple-100 text-purple-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {campagne.statut || '-'}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-[#555F6D] whitespace-nowrap">
                      {campagne.createdAt ? new Date(campagne.createdAt).toLocaleDateString('fr-FR') : '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {!loading && currentCampagnes.length > 0 && (
        <TablePagination
          currentPage={currentPage}
          totalPages={totalPages}
          itemsPerPage={itemsPerPage}
          totalItems={sortedCampagnes.length}
          onPageChange={handlePageChange}
          onItemsPerPageChange={(value) => {
            setItemsPerPage(value);
            setCurrentPage(1);
          }}
          startIndex={indexOfFirstItem}
          endIndex={indexOfLastItem}
        />
      )}
    </div>
  );
}

export default CampagnesManagement;
