// Reference data caching utility to improve performance
// This prevents redundant API calls for reference data that doesn't change frequently

class ReferenceDataCache {
  constructor() {
    this.cache = new Map();
    this.cacheTimestamps = new Map();
    this.CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  }

  // Check if cached data is still valid
  isValid(key) {
    const timestamp = this.cacheTimestamps.get(key);
    if (!timestamp) return false;
    
    return Date.now() - timestamp < this.CACHE_DURATION;
  }

  // Get cached data if valid
  get(key) {
    if (this.isValid(key)) {
      return this.cache.get(key);
    }
    return null;
  }

  // Set cached data with timestamp
  set(key, data) {
    this.cache.set(key, data);
    this.cacheTimestamps.set(key, Date.now());
  }

  // Clear specific cache entry
  clear(key) {
    this.cache.delete(key);
    this.cacheTimestamps.delete(key);
  }

  // Clear all cache
  clearAll() {
    this.cache.clear();
    this.cacheTimestamps.clear();
  }

  // Get cache status for debugging
  getStatus() {
    const status = {};
    for (const [key] of this.cache) {
      status[key] = {
        cached: true,
        valid: this.isValid(key),
        age: Date.now() - (this.cacheTimestamps.get(key) || 0)
      };
    }
    return status;
  }
}

// Create singleton instance
const referenceDataCache = new ReferenceDataCache();

export default referenceDataCache;

// Helper function to create cache-aware thunk
export const createCachedThunk = (key, originalThunk) => {
  return async (dispatch, getState) => {
    // Check cache first
    const cachedData = referenceDataCache.get(key);
    if (cachedData) {
      return cachedData;
    }

    // If not cached or expired, fetch from API
    try {
      const result = await dispatch(originalThunk()).unwrap();
      
      // Cache the result
      referenceDataCache.set(key, result);
      
      return result;
    } catch (error) {
      throw error;
    }
  };
};

// Cache keys for different reference data types
export const CACHE_KEYS = {
  CONTROL_TYPES: 'controlTypes',
  RISKS: 'risks',
  BUSINESS_PROCESSES: 'businessProcesses',
  ORGANIZATIONAL_PROCESSES: 'organizationalProcesses',
  OPERATIONS: 'operations',
  APPLICATIONS: 'applications',
  ENTITIES: 'entities',
  ACTION_PLANS: 'actionPlans'
};
