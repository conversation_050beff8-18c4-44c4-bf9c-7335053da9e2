{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.3", "@reduxjs/toolkit": "^2.6.0", "@tailwindcss/vite": "^4.0.9", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.8.4", "@tanstack/react-table": "^8.21.3", "axios": "^1.8.4", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-zoom": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "framer-motion": "^12.6.3", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "lucide-react": "^0.477.0", "next-themes": "^0.4.4", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.3", "react-redux": "^9.2.0", "react-router-dom": "^7.2.0", "react-select": "^5.10.1", "recharts": "^2.15.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.9", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}