module.exports = (sequelize, DataTypes) => {
  const BusinessProcess = sequelize.define('BusinessProcess', {
    businessProcessID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    parentBusinessProcessID: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'BusinessProcess',
        key: 'businessProcessID',
      },
    },
  }, {
    tableName: 'BusinessProcess',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['businessProcessID'],
      },
    ],
  });

  BusinessProcess.associate = function(models) {
    BusinessProcess.belongsToMany(models.AuditScope, {
      through: 'AuditScopeBusinessProcesses',
      foreignKey: 'businessProcessID',
      otherKey: 'auditScopeID',
      as: 'auditScopes'
    });
  };

  return BusinessProcess;
};

/*-- Remove the 'description' column from the 'BusinessProcess' table
ALTER TABLE "BusinessProcess" DROP COLUMN "description";

-- Add the 'code' column to the 'BusinessProcess' table
ALTER TABLE "BusinessProcess" ADD COLUMN "code" VARCHAR(255);

-- Add the 'comment' column to the 'BusinessProcess' table
ALTER TABLE "BusinessProcess" ADD COLUMN "comment" TEXT;

-- Add the 'parentBusinessProcessID' column to the 'BusinessProcess' table
ALTER TABLE "BusinessProcess" ADD COLUMN "parentBusinessProcessID" VARCHAR(255);

-- Add a foreign key constraint to 'parentBusinessProcessID' referencing 'businessProcessID' in the same table
ALTER TABLE "BusinessProcess"
ADD CONSTRAINT "fk_parentBusinessProcess"
FOREIGN KEY ("parentBusinessProcessID")
REFERENCES "BusinessProcess" ("businessProcessID")
ON DELETE SET NULL
ON UPDATE CASCADE;*/