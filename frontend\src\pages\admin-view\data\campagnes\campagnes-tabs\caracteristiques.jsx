import { useState, useEffect } from "react";
import { useOutletContext } from "react-router-dom";
import { Loader2, Info, ChevronDown, ChevronUp, FileText, Hash, Activity, ScrollText } from "lucide-react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { updateCampagne } from "@/services/campagne-service";

function Caracteristiques() {
  const { campagne: initialCampagne, refreshCampagne } = useOutletContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isCharacteristicsOpen, setIsCharacteristicsOpen] = useState(true);

  const [campagne, setCampagne] = useState({
    name: "",
    code: "",
    description: "",
    statut: "",
  });

  // Load campagne data when component mounts or when campagne data changes
  useEffect(() => {
    if (initialCampagne) {
      console.log('🔄 [DEBUG] Updating local campagne state with:', initialCampagne);
      setCampagne({
        name: initialCampagne.name || "",
        code: initialCampagne.code || "",
        description: initialCampagne.description || "",
        statut: initialCampagne.statut || "",
      });
      setIsLoading(false);
    }
  }, [initialCampagne]);



  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!initialCampagne?.campagneID) return;

    setIsSubmitting(true);
    try {
      const response = await updateCampagne(initialCampagne.campagneID, campagne);

      if (response.success) {
        toast.success("Campagne mise à jour avec succès");

        // Update local state immediately with the saved data
        console.log('✅ [DEBUG] Campagne updated successfully, refreshing parent data');

        if (refreshCampagne) {
          await refreshCampagne();
        }

        // Also update local state to ensure immediate UI update
        if (response.data) {
          setCampagne({
            name: response.data.name || "",
            code: response.data.code || "",
            description: response.data.description || "",
            statut: response.data.statut || "",
          });
        }
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "Erreur lors de la mise à jour de la campagne");
      console.error("Error updating campagne:", error);
    } finally {
      setIsSubmitting(false);
    }
  };



  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-[#F62D51]" />
          <span className="mt-4 text-gray-600">Chargement des données de la campagne...</span>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Collapsible Information Section */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <button
          type="button"
          onClick={() => setIsCharacteristicsOpen(!isCharacteristicsOpen)}
          className="w-full bg-blue-50 hover:bg-blue-100 transition-colors duration-200 border-b border-blue-200"
        >
          <div className="flex items-center p-4">
            {isCharacteristicsOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <Info className="h-5 w-5 text-blue-600 mr-1 ml-2" />
            <span className="text-lg font-medium text-blue-800">Informations Générales</span>
          </div>
        </button>

        {isCharacteristicsOpen && (
          <div className="p-5 bg-white">
            <div className="grid grid-cols-3 gap-x-8 gap-y-6 w-full">

              {/* First row - Name and Code */}
              <div className="space-y-2 col-span-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-blue-600" />
                  Nom
                </label>
                <div className="w-full">
                  <Input
                    value={campagne.name}
                    onChange={(e) => setCampagne({ ...campagne, name: e.target.value })}
                    placeholder="Nom de la campagne"
                    className="w-full block"
                  />
                </div>
              </div>
              <div className="space-y-2 w-full">
                <label className="text-sm font-medium flex items-center">
                  <Hash className="h-4 w-4 mr-2 text-indigo-600" />
                  Code
                </label>
                <div className="w-full">
                  <Input
                    value={campagne.code}
                    onChange={(e) => setCampagne({ ...campagne, code: e.target.value })}
                    placeholder="Code de la campagne"
                    className="w-full block"
                  />
                </div>
              </div>

              {/* Second row - Status */}
              <div className="space-y-2 col-span-1 w-full">
                <label className="text-sm font-medium flex items-center">
                  <Activity className="h-4 w-4 mr-2 text-purple-600" />
                  Statut
                </label>
                <Select
                  value={campagne.statut}
                  onValueChange={(value) => setCampagne({ ...campagne, statut: value })}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Sélectionner un statut" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="planifié">Planifié</SelectItem>
                    <SelectItem value="en cours">En cours</SelectItem>
                    <SelectItem value="terminé">Terminé</SelectItem>
                    <SelectItem value="validé">Validé</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Third row - Description (full width) */}
              <div className="space-y-2 col-span-3 w-full">
                <label className="text-sm font-medium flex items-center">
                  <ScrollText className="h-4 w-4 mr-2 text-green-600" />
                  Description
                </label>
                <div className="w-full">
                  <Textarea
                    value={campagne.description}
                    onChange={(e) => setCampagne({ ...campagne, description: e.target.value })}
                    placeholder="Description de la campagne..."
                    rows={4}
                    className="w-full block"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>



      {/* Submit Button */}
      <div className="flex justify-end pt-6">
        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-[#F62D51] hover:bg-[#F62D51]/90 text-white px-8 py-2"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Mise à jour...
            </>
          ) : (
            'Sauvegarder les modifications'
          )}
        </Button>
      </div>
    </form>
  );
}

export default Caracteristiques;