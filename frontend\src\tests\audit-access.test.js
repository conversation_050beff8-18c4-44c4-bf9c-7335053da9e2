// This is a test file to verify that users with the "Audit Director" or "Auditor" roles
// can access the audit features, and users without these roles cannot.

import { isUserAuditor } from '../components/common/check-auth';

// Mock user data
const mockUsers = {
  auditDirector: {
    id: 1,
    username: 'audit_director',
    roles: [
      { id: 7, name: 'Audit Director', code: 'audit_director' }
    ]
  },
  auditor: {
    id: 2,
    username: 'auditor',
    roles: [
      { id: 8, name: 'Auditor', code: 'auditor' }
    ]
  },
  grcAdmin: {
    id: 3,
    username: 'grc_admin',
    roles: [
      { id: 1, name: 'GRC Administrator', code: 'grc_admin' }
    ]
  },
  grcManager: {
    id: 4,
    username: 'grc_manager',
    roles: [
      { id: 2, name: 'GRC Manager', code: 'grc_manager' }
    ]
  },
  multiRole: {
    id: 5,
    username: 'multi_role',
    roles: [
      { id: 2, name: 'GRC Manager', code: 'grc_manager' },
      { id: 8, name: 'Auditor', code: 'auditor' }
    ]
  },
  noRoles: {
    id: 6,
    username: 'no_roles',
    roles: []
  },
  nullRoles: {
    id: 7,
    username: 'null_roles',
    roles: null
  }
};

// Test cases
describe('Audit Access Control', () => {
  test('Audit Director should have audit access', () => {
    expect(isUserAuditor(mockUsers.auditDirector)).toBe(true);
  });

  test('Auditor should have audit access', () => {
    expect(isUserAuditor(mockUsers.auditor)).toBe(true);
  });

  test('GRC Admin should not have audit access', () => {
    expect(isUserAuditor(mockUsers.grcAdmin)).toBe(false);
  });

  test('GRC Manager should not have audit access', () => {
    expect(isUserAuditor(mockUsers.grcManager)).toBe(false);
  });

  test('User with multiple roles including Auditor should have audit access', () => {
    expect(isUserAuditor(mockUsers.multiRole)).toBe(true);
  });

  test('User with no roles should not have audit access', () => {
    expect(isUserAuditor(mockUsers.noRoles)).toBe(false);
  });

  test('User with null roles should not have audit access', () => {
    expect(isUserAuditor(mockUsers.nullRoles)).toBe(false);
  });
});

// Note: This test assumes that the isUserAuditor function is exported from check-auth.jsx.
// If it's not exported, you'll need to modify the import or export the function.
