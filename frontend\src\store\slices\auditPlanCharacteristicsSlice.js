import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { updateAuditPlan } from '@/services/audit-plan-service';

// Async thunk for updating audit plan characteristics
export const updateAuditPlanCharacteristics = createAsyncThunk(
  'auditPlanCharacteristics/update',
  async ({ id, characteristics }, { rejectWithValue, signal }) => {
    try {
      const response = await updateAuditPlan(id, characteristics, signal);
      if (!response) return null; // Request cancelled
      if (response.success) {
        return response.data;
      }
      return rejectWithValue(response.message || 'Failed to update audit plan characteristics');
    } catch (error) {
      if (error.name === 'CanceledError') return null;
      return rejectWithValue(error.message || 'Failed to update audit plan characteristics');
    }
  }
);

const initialState = {
  loading: false,
  error: null,
  lastUpdated: null
};

const auditPlanCharacteristicsSlice = createSlice({
  name: 'auditPlanCharacteristics',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(updateAuditPlanCharacteristics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateAuditPlanCharacteristics.fulfilled, (state, action) => {
        if (action.payload === null) return; // Request cancelled
        state.loading = false;
        state.lastUpdated = Date.now();
      })
      .addCase(updateAuditPlanCharacteristics.rejected, (state, action) => {
        if (action.payload === null) return; // Request cancelled
        state.loading = false;
        state.error = action.payload;
      });
  }
});

export const { clearError } = auditPlanCharacteristicsSlice.actions;
export default auditPlanCharacteristicsSlice.reducer; 