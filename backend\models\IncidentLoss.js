module.exports = (sequelize, DataTypes) => {
  const IncidentLoss = sequelize.define('IncidentLoss', {
    lossID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    amount: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0,
    },
    localAmount: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    currency: {
      type: DataTypes.STRING(10),
      allowNull: true,
      defaultValue: 'XOF',
    },
    incidentID: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Incident',
        key: 'incidentID',
      },
    },
  }, {
    tableName: 'incident_loss',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['lossID'],
      },
      {
        fields: ['incidentID'],
      },
    ],
  });

  IncidentLoss.associate = (models) => {
    // IncidentLoss belongs to Incident
    IncidentLoss.belongsTo(models.Incident, {
      foreignKey: 'incidentID',
      as: 'incident',
      onDelete: 'CASCADE',
    });
  };

  return IncidentLoss;
};
