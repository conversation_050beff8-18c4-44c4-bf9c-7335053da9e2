import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import controlTypeService from '../../services/controlTypeService';
import { toast } from 'sonner';

// Initial state
const initialState = {
  controlTypes: [],
  currentControlType: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: ''
};

// Get all control types
export const getAllControlTypes = createAsyncThunk(
  'controlTypes/getAll',
  async (_, thunkAPI) => {
    try {
      return await controlTypeService.getAllControlTypes();
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch control types';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get control type by ID
export const getControlTypeById = createAsyncThunk(
  'controlTypes/getById',
  async (id, thunkAPI) => {
    try {
      return await controlTypeService.getControlTypeById(id);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to fetch control type';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create new control type
export const createControlType = createAsyncThunk(
  'controlTypes/create',
  async (controlTypeData, thunkAPI) => {
    try {
      return await controlTypeService.createControlType(controlTypeData);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to create control type';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update control type
export const updateControlType = createAsyncThunk(
  'controlTypes/update',
  async ({ id, controlTypeData }, thunkAPI) => {
    try {
      console.log('updateControlType thunk called with:', { id, controlTypeData });
      const response = await controlTypeService.updateControlType(id, controlTypeData);
      console.log('updateControlType thunk response:', response);
      return response;
    } catch (error) {
      console.error('Error in updateControlType thunk:', error);
      const message = error.response?.data?.message || error.message || 'Failed to update control type';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete control type
export const deleteControlType = createAsyncThunk(
  'controlTypes/delete',
  async (id, thunkAPI) => {
    try {
      return await controlTypeService.deleteControlType(id);
    } catch (error) {
      const message = error.response?.data?.message || error.message || 'Failed to delete control type';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Control type slice
const controlTypeSlice = createSlice({
  name: 'controlType',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = '';
    }
  },
  extraReducers: (builder) => {
    builder
      // Get all control types
      .addCase(getAllControlTypes.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllControlTypes.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.controlTypes = action.payload.data;
      })
      .addCase(getAllControlTypes.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Get control type by ID
      .addCase(getControlTypeById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getControlTypeById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;

        // Log the data received from the API
        console.log('getControlTypeById fulfilled with data:', action.payload.data);

        // Store the control type in the state
        state.currentControlType = action.payload.data;
      })
      .addCase(getControlTypeById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Create control type
      .addCase(createControlType.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createControlType.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.controlTypes.push(action.payload.data);
        toast.success('Control type created successfully');
      })
      .addCase(createControlType.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Update control type
      .addCase(updateControlType.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateControlType.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.controlTypes = state.controlTypes.map(controlType =>
          controlType.controlTypeID === action.payload.data.controlTypeID ? action.payload.data : controlType
        );
        state.currentControlType = action.payload.data;
        toast.success('Control type updated successfully');
      })
      .addCase(updateControlType.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })

      // Delete control type
      .addCase(deleteControlType.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteControlType.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.controlTypes = state.controlTypes.filter(controlType => controlType.controlTypeID !== action.meta.arg);
        toast.success('Control type deleted successfully');
      })
      .addCase(deleteControlType.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
  }
});

export const { reset } = controlTypeSlice.actions;
export default controlTypeSlice.reducer;
