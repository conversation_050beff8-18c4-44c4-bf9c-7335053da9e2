module.exports = (sequelize, DataTypes) => {
  const IncidentType = sequelize.define('IncidentType', {
    incidentTypeID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  }, {
    tableName: 'IncidentType',
    timestamps: false,
  });

  return IncidentType;
};