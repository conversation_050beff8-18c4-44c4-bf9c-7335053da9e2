const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');
const db = require('../../models');
const { Sequelize, DataTypes } = require('sequelize');

const debug = (message, data) => {
  if (process.env.DEBUG_LOGS === 'true') {
    console.log(`DEBUG: ${message}`, data);
  }
};

// Create imports directory if it doesn't exist
const importsDir = path.join(__dirname, '../../imports');
if (!fs.existsSync(importsDir)) {
  fs.mkdirSync(importsDir);
}

// Get list of all tables in the database
const getTablesList = async (req, res) => {
  try {
    const [results] = await db.sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `);

    const tables = results.map(row => row.table_name);
    
    res.status(200).json({
      success: true,
      data: tables
    });
  } catch (error) {
    console.error('Error fetching tables list:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch tables list',
      error: error.message
    });
  }
};

// Get table structure
const getTableStructure = async (req, res) => {
  try {
    const { tableName } = req.params;
    
    // Validate table name to prevent SQL injection
    const tableCheck = await db.sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = :tableName
    `, {
      replacements: { tableName },
      type: Sequelize.QueryTypes.SELECT
    });
    
    if (tableCheck.length === 0) {
      return res.status(404).json({
        success: false,
        message: `Table "${tableName}" not found`
      });
    }
    
    // Get column information
    const columns = await db.sequelize.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = :tableName
      ORDER BY ordinal_position
    `, {
      replacements: { tableName },
      type: Sequelize.QueryTypes.SELECT
    });
    
    // Get primary key information
    const primaryKeysResult = await db.sequelize.query(`
      SELECT c.column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_name)
      JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema
        AND tc.table_name = c.table_name AND ccu.column_name = c.column_name
      WHERE tc.constraint_type = 'PRIMARY KEY' AND tc.table_name = :tableName
    `, {
      replacements: { tableName },
      type: Sequelize.QueryTypes.SELECT
    });
    
    // Extract primary key column names
    const primaryKeyColumns = primaryKeysResult.map(pk => pk.column_name);
    
    // Enhance column info with primary key status
    const columnInfo = columns.map(col => ({
      ...col,
      isPrimaryKey: primaryKeyColumns.includes(col.column_name)
    }));
    
    res.status(200).json({
      success: true,
      data: {
        tableName,
        columns: columnInfo
      }
    });
  } catch (error) {
    console.error('Error fetching table structure:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch table structure',
      error: error.message
    });
  }
};

// Validate Excel file against table structure
const validateExcelFile = async (filePath, tableName) => {
  try {
    // Get table structure from database
    const columns = await db.sequelize.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = :tableName
      ORDER BY ordinal_position
    `, {
      replacements: { tableName },
      type: Sequelize.QueryTypes.SELECT
    });
    
    // Read Excel file
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);
    
    if (data.length === 0) {
      return {
        valid: false,
        message: 'Excel file is empty',
        differences: []
      };
    }
    
    // Get Excel columns
    const excelColumns = Object.keys(data[0]);
    
    // Get database columns
    const dbColumns = columns.map(col => col.column_name);
    
    // Find missing columns in Excel
    const missingColumns = dbColumns.filter(col => !excelColumns.includes(col));
    
    // Find extra columns in Excel
    const extraColumns = excelColumns.filter(col => !dbColumns.includes(col));
    
    // Check if there are any differences
    const hasDifferences = missingColumns.length > 0 || extraColumns.length > 0;
    
    // Check if missing columns are nullable or are timestamp columns that can be auto-generated
    const nonNullableMissingColumns = missingColumns.filter(colName => {
      // Sequelize timestamp columns can be auto-generated
      if (colName === 'createdAt' || colName === 'updatedAt') {
        return false;
      }
      
      const column = columns.find(col => col.column_name === colName);
      return column && column.is_nullable === 'NO';
    });
    
    // If all missing columns are nullable or auto-generated, we can proceed with import
    const canProceedWithImport = nonNullableMissingColumns.length === 0;
    
    return {
      valid: canProceedWithImport,
      message: hasDifferences 
        ? (canProceedWithImport 
            ? 'Missing columns will be set to NULL or auto-generated' 
            : 'Excel structure does not match table structure (non-nullable columns missing)')
        : 'Excel structure matches table structure',
      differences: {
        missingColumns,
        extraColumns,
        nonNullableMissingColumns
      },
      data
    };
  } catch (error) {
    console.error('Error validating Excel file:', error);
    return {
      valid: false,
      message: `Error validating Excel file: ${error.message}`,
      differences: []
    };
  }
};

// Import data from Excel to a single table
const importTableFromExcel = async (req, res) => {
  try {
    if (!req.files || !req.files.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }
    
    const { tableName } = req.params;
    const { mode = 'validate' } = req.body; // 'validate' or 'import'
    
    // Validate table name to prevent SQL injection
    const tableCheck = await db.sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = :tableName
    `, {
      replacements: { tableName },
      type: Sequelize.QueryTypes.SELECT
    });
    
    if (tableCheck.length === 0) {
      return res.status(404).json({
        success: false,
        message: `Table "${tableName}" not found`
      });
    }
    
    // Save uploaded file
    const file = req.files.file;
    const fileExtension = path.extname(file.name);
    
    if (!['.xlsx', '.xls'].includes(fileExtension.toLowerCase())) {
      return res.status(400).json({
        success: false,
        message: 'Only Excel files (.xlsx, .xls) are allowed'
      });
    }
    
    const fileName = `${tableName}_import_${Date.now()}${fileExtension}`;
    const filePath = path.join(importsDir, fileName);
    
    await file.mv(filePath);
    
    // Validate Excel structure against table structure
    const validation = await validateExcelFile(filePath, tableName);
    
    // If validation mode or validation failed, return validation results
    if (mode === 'validate' || !validation.valid) {
      // Clean up the file
      fs.unlinkSync(filePath);
      
      return res.status(validation.valid ? 200 : 400).json({
        success: validation.valid,
        message: validation.message,
        differences: validation.differences
      });
    }
    
    // If import mode and validation passed, import the data
    const data = validation.data;
    
    // Get all columns for the table
    const allColumns = await db.sequelize.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = :tableName
      ORDER BY ordinal_position
    `, {
      replacements: { tableName },
      type: Sequelize.QueryTypes.SELECT
    });
    
    const allColumnNames = allColumns.map(col => col.column_name);
    
    // Get primary key columns
    const primaryKeysResult = await db.sequelize.query(`
      SELECT c.column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_name)
      JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema
        AND tc.table_name = c.table_name AND ccu.column_name = c.column_name
      WHERE tc.constraint_type = 'PRIMARY KEY' AND tc.table_name = :tableName
    `, {
      replacements: { tableName },
      type: Sequelize.QueryTypes.SELECT
    });
    
    // Extract primary key column names
    const primaryKeyColumns = primaryKeysResult.map(pk => pk.column_name);
    
    // Import data
    let inserted = 0;
    let updated = 0;
    let errors = 0;
    const errorDetails = [];
    
    for (const row of data) {
      try {
        // Check if record exists (by primary key)
        let existingRecord = null;
        
        if (primaryKeyColumns.length > 0) {
          const whereClause = {};
          let hasPrimaryKey = true;
          
          for (const pkCol of primaryKeyColumns) {
            if (row[pkCol] === undefined) {
              hasPrimaryKey = false;
              break;
            }
            whereClause[pkCol] = row[pkCol];
          }
          
          if (hasPrimaryKey) {
            // Find the model for this table
            const modelName = Object.keys(db).find(
              key => db[key].tableName === tableName
            );
            
            if (modelName) {
              existingRecord = await db[modelName].findOne({
                where: whereClause
              });
            } else {
              // Use raw query if model not found
              const records = await db.sequelize.query(
                `SELECT * FROM "${tableName}" WHERE ${primaryKeyColumns
                  .map(col => `"${col}" = :${col}`)
                  .join(' AND ')}`,
                {
                  replacements: whereClause,
                  type: Sequelize.QueryTypes.SELECT
                }
              );
              
              existingRecord = records.length > 0 ? records[0] : null;
            }
          }
        }
        
        if (existingRecord) {
          // Update existing record
          const updateColumns = Object.keys(row)
            .filter(col => !primaryKeyColumns.includes(col))
            .map(col => `"${col}" = :${col}`)
            .join(', ');
          
          // Add updatedAt timestamp for Sequelize
          const updateData = { ...row, updatedAt: new Date() };
          
          if (updateColumns) {
            await db.sequelize.query(
              `UPDATE "${tableName}" SET ${updateColumns}, "updatedAt" = :updatedAt WHERE ${primaryKeyColumns
                .map(col => `"${col}" = :${col}`)
                .join(' AND ')}`,
              {
                replacements: updateData,
                type: Sequelize.QueryTypes.UPDATE
              }
            );
            updated++;
          }
        } else {
          // Insert new record - include all columns from the database
          // For columns not in Excel, set NULL values
          const insertData = { ...row };
          
          // Add NULL for missing columns
          allColumnNames.forEach(colName => {
            if (insertData[colName] === undefined) {
              insertData[colName] = null;
            }
          });
          
          // Add timestamps for Sequelize
          const now = new Date();
          insertData.createdAt = now;
          insertData.updatedAt = now;
          
          // Only include columns that exist in the database
          const validColumns = Object.keys(insertData)
            .filter(col => allColumnNames.includes(col));
          
          const columns = validColumns.map(col => `"${col}"`).join(', ');
          const values = validColumns.map(col => `:${col}`).join(', ');
          
          await db.sequelize.query(
            `INSERT INTO "${tableName}" (${columns}) VALUES (${values})`,
            {
              replacements: insertData,
              type: Sequelize.QueryTypes.INSERT
            }
          );
          inserted++;
        }
      } catch (rowError) {
        errors++;
        errorDetails.push({
          row,
          error: rowError.message
        });
      }
    }
    
    // Clean up the file
    fs.unlinkSync(filePath);
    
    res.status(200).json({
      success: true,
      message: `Import completed: ${inserted} rows inserted, ${updated} rows updated, ${errors} errors`,
      stats: {
        inserted,
        updated,
        errors
      },
      errorDetails: errorDetails.length > 0 ? errorDetails : undefined
    });
  } catch (error) {
    console.error('Error importing table from Excel:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to import table from Excel',
      error: error.message
    });
  }
};

// Import data from Excel to multiple tables
const importMultipleTables = async (req, res) => {
  try {
    if (!req.files || !req.files.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }
    
    const { mode = 'validate' } = req.body; // 'validate' or 'import'
    
    // Save uploaded file
    const file = req.files.file;
    const fileExtension = path.extname(file.name);
    
    if (!['.xlsx', '.xls'].includes(fileExtension.toLowerCase())) {
      return res.status(400).json({
        success: false,
        message: 'Only Excel files (.xlsx, .xls) are allowed'
      });
    }
    
    const fileName = `multi_import_${Date.now()}${fileExtension}`;
    const filePath = path.join(importsDir, fileName);
    
    await file.mv(filePath);
    
    // Read Excel file
    const workbook = XLSX.readFile(filePath);
    const sheetNames = workbook.SheetNames;
    
    // Validate each sheet against corresponding table
    const validationResults = {};
    const importResults = {};
    
    for (const sheetName of sheetNames) {
      // Check if table exists
      const tableCheck = await db.sequelize.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = :tableName
      `, {
        replacements: { tableName: sheetName },
        type: Sequelize.QueryTypes.SELECT
      });
      
      if (tableCheck.length === 0) {
        validationResults[sheetName] = {
          valid: false,
          message: `Table "${sheetName}" not found in database`
        };
        continue;
      }
      
      // Extract data from sheet
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);
      
      if (data.length === 0) {
        validationResults[sheetName] = {
          valid: false,
          message: 'Sheet is empty'
        };
        continue;
      }
      
      // Get table structure
      const columns = await db.sequelize.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = :tableName
        ORDER BY ordinal_position
      `, {
        replacements: { tableName: sheetName },
        type: Sequelize.QueryTypes.SELECT
      });
      
      // Get Excel columns
      const excelColumns = Object.keys(data[0]);
      
      // Get database columns
      const dbColumns = columns.map(col => col.column_name);
      
      // Find missing columns in Excel
      const missingColumns = dbColumns.filter(col => !excelColumns.includes(col));
      
      // Find extra columns in Excel
      const extraColumns = excelColumns.filter(col => !dbColumns.includes(col));
      
      // Check if missing columns are nullable or are timestamp columns that can be auto-generated
      const nonNullableMissingColumns = missingColumns.filter(colName => {
        // Sequelize timestamp columns can be auto-generated
        if (colName === 'createdAt' || colName === 'updatedAt') {
          return false;
        }
        
        const column = columns.find(col => col.column_name === colName);
        return column && column.is_nullable === 'NO';
      });
      
      // If all missing columns are nullable or auto-generated, we can proceed with import
      const canProceedWithImport = nonNullableMissingColumns.length === 0;
      
      validationResults[sheetName] = {
        valid: canProceedWithImport,
        message: missingColumns.length > 0 || extraColumns.length > 0
          ? (canProceedWithImport 
              ? 'Missing columns will be set to NULL or auto-generated' 
              : 'Excel structure does not match table structure (non-nullable columns missing)')
          : 'Excel structure matches table structure',
        differences: {
          missingColumns,
          extraColumns,
          nonNullableMissingColumns
        }
      };
      
      // If import mode and validation passed, import the data
      if (mode === 'import' && canProceedWithImport) {
        // Get all columns for the table
        const allColumnNames = dbColumns;
        
        // Get primary key columns
        const primaryKeysResult = await db.sequelize.query(`
          SELECT c.column_name
          FROM information_schema.table_constraints tc
          JOIN information_schema.constraint_column_usage AS ccu USING (constraint_schema, constraint_name)
          JOIN information_schema.columns AS c ON c.table_schema = tc.constraint_schema
            AND tc.table_name = c.table_name AND ccu.column_name = c.column_name
          WHERE tc.constraint_type = 'PRIMARY KEY' AND tc.table_name = :tableName
        `, {
          replacements: { tableName: sheetName },
          type: Sequelize.QueryTypes.SELECT
        });
        
        // Extract primary key column names
        const primaryKeyColumns = primaryKeysResult.map(pk => pk.column_name);
        
        // Import data
        let inserted = 0;
        let updated = 0;
        let errors = 0;
        const errorDetails = [];
        
        for (const row of data) {
          try {
            // Check if record exists (by primary key)
            let existingRecord = null;
            
            if (primaryKeyColumns.length > 0) {
              const whereClause = {};
              let hasPrimaryKey = true;
              
              for (const pkCol of primaryKeyColumns) {
                if (row[pkCol] === undefined) {
                  hasPrimaryKey = false;
                  break;
                }
                whereClause[pkCol] = row[pkCol];
              }
              
              if (hasPrimaryKey) {
                // Find the model for this table
                const modelName = Object.keys(db).find(
                  key => db[key].tableName === sheetName
                );
                
                if (modelName) {
                  existingRecord = await db[modelName].findOne({
                    where: whereClause
                  });
                } else {
                  // Use raw query if model not found
                  const records = await db.sequelize.query(
                    `SELECT * FROM "${sheetName}" WHERE ${primaryKeyColumns
                      .map(col => `"${col}" = :${col}`)
                      .join(' AND ')}`,
                    {
                      replacements: whereClause,
                      type: Sequelize.QueryTypes.SELECT
                    }
                  );
                  
                  existingRecord = records.length > 0 ? records[0] : null;
                }
              }
            }
            
            if (existingRecord) {
              // Update existing record
              const updateColumns = Object.keys(row)
                .filter(col => !primaryKeyColumns.includes(col))
                .map(col => `"${col}" = :${col}`)
                .join(', ');
              
              // Add updatedAt timestamp for Sequelize
              const updateData = { ...row, updatedAt: new Date() };
              
              if (updateColumns) {
                await db.sequelize.query(
                  `UPDATE "${sheetName}" SET ${updateColumns}, "updatedAt" = :updatedAt WHERE ${primaryKeyColumns
                    .map(col => `"${col}" = :${col}`)
                    .join(' AND ')}`,
                  {
                    replacements: updateData,
                    type: Sequelize.QueryTypes.UPDATE
                  }
                );
                updated++;
              }
            } else {
              // Insert new record - include all columns from the database
              // For columns not in Excel, set NULL values
              const insertData = { ...row };
              
              // Add NULL for missing columns
              allColumnNames.forEach(colName => {
                if (insertData[colName] === undefined) {
                  insertData[colName] = null;
                }
              });
              
              // Add timestamps for Sequelize
              const now = new Date();
              insertData.createdAt = now;
              insertData.updatedAt = now;
              
              // Only include columns that exist in the database
              const validColumns = Object.keys(insertData)
                .filter(col => allColumnNames.includes(col));
              
              const columns = validColumns.map(col => `"${col}"`).join(', ');
              const values = validColumns.map(col => `:${col}`).join(', ');
              
              await db.sequelize.query(
                `INSERT INTO "${sheetName}" (${columns}) VALUES (${values})`,
                {
                  replacements: insertData,
                  type: Sequelize.QueryTypes.INSERT
                }
              );
              inserted++;
            }
          } catch (rowError) {
            errors++;
            errorDetails.push({
              row,
              error: rowError.message
            });
          }
        }
        
        importResults[sheetName] = {
          inserted,
          updated,
          errors,
          errorDetails: errorDetails.length > 0 ? errorDetails : undefined
        };
      }
    }
    
    // Clean up the file
    fs.unlinkSync(filePath);
    
    res.status(200).json({
      success: true,
      mode,
      validation: validationResults,
      import: mode === 'import' ? importResults : undefined
    });
  } catch (error) {
    console.error('Error importing multiple tables from Excel:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to import tables from Excel',
      error: error.message
    });
  }
};

// Generate Excel template for a table
const generateTableTemplate = async (req, res) => {
  try {
    const { tableName } = req.params;
    
    // Validate table name to prevent SQL injection
    const [tableCheck] = await db.sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = :tableName
    `, {
      replacements: { tableName },
      type: Sequelize.QueryTypes.SELECT
    });
    
    if (!tableCheck) {
      return res.status(404).json({
        success: false,
        message: `Table "${tableName}" not found`
      });
    }
    
    // Get column information
    const [columns] = await db.sequelize.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = :tableName
      ORDER BY ordinal_position
    `, {
      replacements: { tableName },
      type: Sequelize.QueryTypes.SELECT
    });
    
    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    
    // Create template with column headers
    const template = {};
    columns.forEach(col => {
      template[col.column_name] = '';
    });
    
    const worksheet = XLSX.utils.json_to_sheet([template]);
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, tableName);
    
    // Generate filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${tableName}_template_${timestamp}.xlsx`;
    const filePath = path.join(importsDir, filename);
    
    // Write to file
    XLSX.writeFile(workbook, filePath);
    
    // Send file to client
    res.download(filePath, filename, (err) => {
      if (err) {
        console.error('Error sending file:', err);
      }
      
      // Delete file after sending
      fs.unlink(filePath, (unlinkErr) => {
        if (unlinkErr) {
          console.error('Error deleting temporary file:', unlinkErr);
        }
      });
    });
  } catch (error) {
    console.error('Error generating table template:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate table template',
      error: error.message
    });
  }
};

module.exports = {
  getTablesList,
  getTableStructure,
  importTableFromExcel,
  importMultipleTables,
  generateTableTemplate
};





