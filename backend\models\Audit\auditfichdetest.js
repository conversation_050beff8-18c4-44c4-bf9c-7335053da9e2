'use strict';

module.exports = (sequelize, DataTypes) => {
  const FicheDeTest = sequelize.define('FicheDeTest', {
    id: {
      type: DataTypes.STRING(50),
      primaryKey: true,
      allowNull: false
    },
    titre: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    questionnaire: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    elementTrouve: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    commentaire: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    preuve: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    dateSignature: {
      type: DataTypes.DATE,
      allowNull: true
    },
    signe: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    answers: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {}
    },
    ficheDeTravailID: {
      type: DataTypes.STRING(50),
      allowNull: false,
      references: {
        model: 'FicheDeTravail',
        key: 'id'
      }
    }
  }, {
    tableName: 'FicheDeTest',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['id']
      },
      {
        fields: ['ficheDeTravailID'] // For foreign key lookups
      },
      {
        fields: ['titre'] // For title searches
      },
      {
        fields: ['elementTrouve'] // For boolean filtering
      },
      {
        fields: ['signe'] // For boolean filtering
      },
      {
        fields: ['dateSignature'] // For date-based queries
      },
      {
        fields: ['createdAt'] // For date-based queries
      },
      {
        fields: ['updatedAt'] // For date-based queries
      }
    ]
  });

  FicheDeTest.associate = function(models) {
    // FicheDeTest belongs to FicheDeTravail
    FicheDeTest.belongsTo(models.FicheDeTravail, {
      foreignKey: 'ficheDeTravailID',
      as: 'ficheDeTravail'
    });
    
    // FicheDeTest has many Questions (many-to-many relationship)
    FicheDeTest.belongsToMany(models.Question, {
      through: 'FicheDeTestQuestions',
      foreignKey: 'ficheDeTestID',
      otherKey: 'questionID',
      as: 'questions'
    });
    
    // Add association with attachments
    FicheDeTest.hasMany(models.FicheDeTestAttachment, {
      foreignKey: 'ficheDeTestID',
      as: 'attachments'
    });
  };

  return FicheDeTest;
};
