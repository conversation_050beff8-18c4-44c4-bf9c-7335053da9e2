import { useOutletContext } from "react-router-dom";
import { Tag, Calendar, Building2, Hash, FileText, DollarSign, Building } from "lucide-react";
import { useEffect, useState } from "react";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";
import { useTranslation } from "react-i18next";

function EntitiesOverview() {
  const { entity } = useOutletContext();
  const [parentEntity, setParentEntity] = useState(null);
  const API_BASE_URL = getApiBaseUrl();
  const { t } = useTranslation();

  // Fetch parent entity if exists
  useEffect(() => {
    const fetchParentEntity = async () => {
      if (entity.parentEntityID) {
        try {
          const response = await axios.get(`${API_BASE_URL}/entities/${entity.parentEntityID}`, {
            withCredentials: true,
            headers: { "Content-Type": "application/json" },
          });

          if (response.data.success) {
            setParentEntity(response.data.data);
          }
        } catch (error) {
          console.error("Error fetching parent entity:", error);
        }
      }
    };

    fetchParentEntity();
  }, [entity.parentEntityID]);

  // Helper function to format dates
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-GB", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Helper function to render values with N/A styled in grey
  const renderValue = (value) => {
    if (value === "N/A" || value === null || value === undefined) {
      return <span className="text-gray-400">N/A</span>;
    }
    return value;
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6">
        <div>
          <h3 className="text-lg font-semibold text-[#242A33] mb-4">{t('admin.entities.overview.title', 'Entity Details')}</h3>
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Tag className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">{t('admin.entities.overview.fields.name', 'Name')}</p>
                <p className="font-medium">{renderValue(entity.name || t('admin.entities.overview.fields.none', 'N/A'))}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Hash className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">{t('admin.entities.overview.fields.code', 'Code')}</p>
                <p className="font-medium">{renderValue(entity.code || t('admin.entities.overview.fields.none', 'N/A'))}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Building2 className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">{t('admin.entities.overview.fields.type', 'Type')}</p>
                <p className="font-medium">{renderValue(entity.type || t('admin.entities.overview.fields.none', 'N/A'))}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Building className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">{t('admin.entities.overview.fields.internal_external', 'Internal/External')}</p>
                <p className="font-medium">{entity.internalExternal === 'External' ? t('admin.entities.overview.fields.external', 'External') : t('admin.entities.overview.fields.internal', 'Internal')}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <DollarSign className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">{t('admin.entities.overview.fields.local_currency', 'Local Currency')}</p>
                <p className="font-medium">{entity.localCurrency || t('admin.entities.overview.fields.none', 'N/A')}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Building2 className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">{t('admin.entities.overview.fields.parent_entity', 'Parent Entity')}</p>
                <p className="font-medium">{parentEntity ? parentEntity.name : t('admin.entities.overview.fields.none', 'None')}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <FileText className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">{t('admin.entities.overview.fields.comment', 'Comment')}</p>
                <p className="font-medium">{entity.comment || t('admin.entities.overview.fields.none', 'N/A')}</p>
              </div>
            </div>

            {entity.createdAt && (
              <div className="flex items-start gap-4">
                <div className="bg-gray-50 p-2 rounded-lg">
                  <Calendar className="h-5 w-5 text-[#555F6D]" />
                </div>
                <div>
                  <p className="text-sm text-[#555F6D]">{t('admin.entities.overview.fields.created_at', 'Created At')}</p>
                  <p className="font-medium">{formatDate(entity.createdAt)}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default EntitiesOverview;
