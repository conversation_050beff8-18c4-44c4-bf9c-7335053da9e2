{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "migrate:workflow": "node scripts/run-workflow-migration.js", "migrate:control-activity-enum": "node scripts/run-control-activity-enum-migration.js", "debug:control-activity": "node scripts/debug-control-activity-logs.js", "migrate:control-activity-table": "node scripts/run-control-activity-table-migration.js", "fix:control-activity": "node scripts/fix-control-activity-logs.js", "test:activity-logging": "node scripts/test-activity-logging.js", "fix:enum-type": "node scripts/fix-enum-type.js"}, "dependencies": {"@socket.io/admin-ui": "^0.5.1", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "docx": "^9.5.0", "dotenv": "^16.5.0", "express": "^4.21.2", "express-fileupload": "^1.5.1", "fs": "0.0.1-security", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "nodemailer": "^6.10.1", "path": "^0.12.7", "pdf-parse": "^1.1.1", "pdfkit": "^0.17.1", "pg": "^8.15.6", "pg-hstore": "^2.3.4", "playht": "^0.21.0", "puppeteer": "^22.15.0", "sequelize": "^6.37.7", "socket.io": "^4.8.1", "stream-buffers": "^3.0.3", "umzug": "^3.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "nodemon": "^3.1.9", "sequelize-cli": "^6.6.2"}}