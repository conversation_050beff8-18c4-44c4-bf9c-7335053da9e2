const { FinancialEntry, Incident } = require('../../models');

// Get all financial entries
const getAllFinancialEntries = async (req, res) => {
  try {
    const entries = await FinancialEntry.findAll();
    res.json({
      success: true,
      data: entries
    });
  } catch (error) {
    console.error('Error fetching financial entries:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch financial entries',
      error: error.message
    });
  }
};

// Get financial entries by incident ID
const getFinancialEntriesByIncident = async (req, res) => {
  try {
    const { incidentId } = req.params;
    console.log(`Fetching financial entries for incident: ${incidentId}`);
    
    const entries = await FinancialEntry.findAll({
      where: { incidentID: incidentId },
      order: [['createdAt', 'DESC']]
    });
    
    // Group entries by category
    const groupedEntries = {
      losses: entries.filter(entry => entry.category === 'losses'),
      gains: entries.filter(entry => entry.category === 'gains'),
      recoveries: entries.filter(entry => entry.category === 'recoveries'),
      provisions: entries.filter(entry => entry.category === 'provisions')
    };
    
    console.log(`Found ${entries.length} financial entries for incident ${incidentId}:`, 
      Object.keys(groupedEntries).map(key => `${key}: ${groupedEntries[key].length}`).join(', '));
    
    res.json({
      success: true,
      data: groupedEntries
    });
  } catch (error) {
    console.error('Error fetching financial entries for incident:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch financial entries',
      error: error.message
    });
  }
};

// Create new financial entry
const createFinancialEntry = async (req, res) => {
  try {
    const {
      name,
      amount,
      localAmount,
      currency,
      category,
      incidentID
    } = req.body;

    // Validate required fields
    if (!name || !category || !incidentID) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        missingFields: ['name', 'category', 'incidentID'].filter(field => !req.body[field])
      });
    }

    // Validate category
    const validCategories = ['losses', 'gains', 'recoveries', 'provisions'];
    if (!validCategories.includes(category)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid category',
        validOptions: validCategories
      });
    }

    // Validate incident exists
    const incident = await Incident.findByPk(incidentID);
    if (!incident) {
      return res.status(404).json({
        success: false,
        message: 'Incident not found'
      });
    }

    // Create entry
    const entry = await FinancialEntry.create({
      entryID: `FE_${Date.now()}`,
      name,
      amount: Number(amount) || 0,
      localAmount: localAmount ? Number(localAmount) : null,
      currency: currency || 'XOF',
      category,
      incidentID
    });

    // Update the incident's totals based on the category
    if (category === 'losses') {
      await incident.update({
        grossLoss: (Number(incident.grossLoss) || 0) + (Number(amount) || 0)
      });
    } else if (category === 'recoveries') {
      await incident.update({
        recoveries: (Number(incident.recoveries) || 0) + (Number(amount) || 0)
      });
    } else if (category === 'provisions') {
      await incident.update({
        provisions: (Number(incident.provisions) || 0) + (Number(amount) || 0)
      });
    }

    res.status(201).json({
      success: true,
      message: 'Financial entry created successfully',
      data: entry
    });
  } catch (error) {
    console.error('Error creating financial entry:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create financial entry',
      error: error.message
    });
  }
};

// Update financial entry
const updateFinancialEntry = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      amount,
      localAmount,
      currency,
      category
    } = req.body;

    // Find the entry
    const entry = await FinancialEntry.findByPk(id);
    if (!entry) {
      return res.status(404).json({
        success: false,
        message: 'Financial entry not found'
      });
    }

    // Get original amount for incident total calculations
    const originalAmount = Number(entry.amount) || 0;
    const originalCategory = entry.category;
    const newAmount = Number(amount) || 0;
    
    // Update entry
    await entry.update({
      name: name || entry.name,
      amount: amount !== undefined ? Number(amount) : entry.amount,
      localAmount: localAmount !== undefined ? (localAmount ? Number(localAmount) : null) : entry.localAmount,
      currency: currency || entry.currency,
      category: category || entry.category
    });

    // Update incident totals if amount or category changed
    if (originalAmount !== newAmount || originalCategory !== entry.category) {
      const incident = await Incident.findByPk(entry.incidentID);
      
      if (incident) {
        // Remove the amount from the original category
        if (originalCategory === 'losses') {
          await incident.update({
            grossLoss: Math.max(0, (Number(incident.grossLoss) || 0) - originalAmount)
          });
        } else if (originalCategory === 'recoveries') {
          await incident.update({
            recoveries: Math.max(0, (Number(incident.recoveries) || 0) - originalAmount)
          });
        } else if (originalCategory === 'provisions') {
          await incident.update({
            provisions: Math.max(0, (Number(incident.provisions) || 0) - originalAmount)
          });
        }
        
        // Add the amount to the new category
        if (entry.category === 'losses') {
          await incident.update({
            grossLoss: (Number(incident.grossLoss) || 0) + newAmount
          });
        } else if (entry.category === 'recoveries') {
          await incident.update({
            recoveries: (Number(incident.recoveries) || 0) + newAmount
          });
        } else if (entry.category === 'provisions') {
          await incident.update({
            provisions: (Number(incident.provisions) || 0) + newAmount
          });
        }
      }
    }

    res.json({
      success: true,
      message: 'Financial entry updated successfully',
      data: entry
    });
  } catch (error) {
    console.error('Error updating financial entry:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update financial entry',
      error: error.message
    });
  }
};

// Delete financial entry
const deleteFinancialEntry = async (req, res) => {
  try {
    const { id } = req.params;
    const entry = await FinancialEntry.findByPk(id);

    if (!entry) {
      return res.status(404).json({
        success: false,
        message: 'Financial entry not found'
      });
    }

    // Capture details for incident update
    const amount = Number(entry.amount) || 0;
    const category = entry.category;
    const incidentID = entry.incidentID;

    // Delete the entry
    await entry.destroy();

    // Update incident totals
    const incident = await Incident.findByPk(incidentID);
    if (incident) {
      if (category === 'losses') {
        await incident.update({
          grossLoss: Math.max(0, (Number(incident.grossLoss) || 0) - amount)
        });
      } else if (category === 'recoveries') {
        await incident.update({
          recoveries: Math.max(0, (Number(incident.recoveries) || 0) - amount)
        });
      } else if (category === 'provisions') {
        await incident.update({
          provisions: Math.max(0, (Number(incident.provisions) || 0) - amount)
        });
      }
    }

    res.json({
      success: true,
      message: 'Financial entry deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting financial entry:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete financial entry',
      error: error.message
    });
  }
};

// Batch create/update financial entries for an incident
const updateIncidentFinancialEntries = async (req, res) => {
  try {
    const { incidentId } = req.params;
    const { losses, gains, recoveries, provisions } = req.body;
    
    console.log(`Updating financial entries for incident ${incidentId}:`, 
      `losses: ${losses?.length || 0}, gains: ${gains?.length || 0}, ` +
      `recoveries: ${recoveries?.length || 0}, provisions: ${provisions?.length || 0}`);
    
    // Validate incident exists
    const incident = await Incident.findByPk(incidentId);
    if (!incident) {
      return res.status(404).json({
        success: false,
        message: 'Incident not found'
      });
    }
    
    const results = {
      created: 0,
      updated: 0,
      deleted: 0
    };
    
    // Function to process entries by category
    const processEntries = async (entries, category) => {
      if (!Array.isArray(entries)) return;
      
      // Get existing entries for this category
      const existingEntries = await FinancialEntry.findAll({
        where: { 
          incidentID: incidentId,
          category
        }
      });
      
      console.log(`Processing ${category}: Found ${existingEntries.length} existing entries, received ${entries.length} entries`);
      
      // Track IDs to determine which existing entries to delete
      const processedIds = [];
      
      // Create or update entries
      for (const entry of entries) {
        if (entry.id) {
          // Entry has ID so it might already exist
          const existingEntry = existingEntries.find(e => e.entryID === entry.id);
          if (existingEntry) {
            // Update existing entry
            await existingEntry.update({
              name: entry.name,
              amount: Number(entry.amount) || 0,
              localAmount: entry.localAmount ? Number(entry.localAmount) : null,
              currency: entry.currency || 'XOF'
            });
            results.updated++;
            processedIds.push(entry.id);
            console.log(`Updated ${category} entry: ${entry.id} - ${entry.name}`);
          } else {
            // Create new entry with provided ID
            await FinancialEntry.create({
              entryID: entry.id,
              name: entry.name,
              amount: Number(entry.amount) || 0,
              localAmount: entry.localAmount ? Number(entry.localAmount) : null,
              currency: entry.currency || 'XOF',
              category,
              incidentID: incidentId
            });
            results.created++;
            processedIds.push(entry.id);
            console.log(`Created ${category} entry with existing ID: ${entry.id} - ${entry.name}`);
          }
        } else {
          // Create new entry with generated ID
          const newEntryId = `FE_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
          const newEntry = await FinancialEntry.create({
            entryID: newEntryId,
            name: entry.name,
            amount: Number(entry.amount) || 0,
            localAmount: entry.localAmount ? Number(entry.localAmount) : null,
            currency: entry.currency || 'XOF',
            category,
            incidentID: incidentId
          });
          results.created++;
          processedIds.push(newEntry.entryID);
          console.log(`Created new ${category} entry with ID: ${newEntry.entryID} - ${entry.name}`);
        }
      }
      
      // Delete entries that weren't in the update
      for (const existingEntry of existingEntries) {
        if (!processedIds.includes(existingEntry.entryID)) {
          await existingEntry.destroy();
          results.deleted++;
          console.log(`Deleted ${category} entry: ${existingEntry.entryID} - ${existingEntry.name}`);
        }
      }
      
      // Calculate total for this category
      const total = entries.reduce((sum, entry) => sum + (Number(entry.amount) || 0), 0);
      
      // Update incident totals
      if (category === 'losses') {
        await incident.update({ grossLoss: total });
      } else if (category === 'recoveries') {
        await incident.update({ recoveries: total });
      } else if (category === 'provisions') {
        await incident.update({ provisions: total });
      }
    };
    
    // Process all categories
    await processEntries(losses, 'losses');
    await processEntries(gains, 'gains');
    await processEntries(recoveries, 'recoveries');
    await processEntries(provisions, 'provisions');
    
    console.log(`Financial entries updated for incident ${incidentId}: created: ${results.created}, updated: ${results.updated}, deleted: ${results.deleted}`);
    
    res.json({
      success: true,
      message: 'Financial entries updated successfully',
      results
    });
  } catch (error) {
    console.error('Error updating financial entries:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update financial entries',
      error: error.message
    });
  }
};

module.exports = {
  getAllFinancialEntries,
  getFinancialEntriesByIncident,
  createFinancialEntry,
  updateFinancialEntry,
  deleteFinancialEntry,
  updateIncidentFinancialEntries
}; 