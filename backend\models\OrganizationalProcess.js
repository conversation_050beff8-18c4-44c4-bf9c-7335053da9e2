module.exports = (sequelize, DataTypes) => {
  const OrganizationalProcess = sequelize.define('OrganizationalProcess', {
    organizationalProcessID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    parentOrganizationalProcess: {  // Keep original column name
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'OrganizationalProcess',
        key: 'organizationalProcessID',
      },
    },
    parentBusinessProcess: {  // Keep original column name
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'BusinessProcess',
        key: 'businessProcessID',
      },
    },
  }, {
    tableName: 'OrganizationalProcess',
    timestamps: false,
  });

  OrganizationalProcess.associate = function(models) {
    OrganizationalProcess.belongsTo(models.OrganizationalProcess, {
      foreignKey: 'parentOrganizationalProcess',
      as: 'parent',
      targetKey: 'organizationalProcessID'
    });

    OrganizationalProcess.hasMany(models.OrganizationalProcess, {
      foreignKey: 'parentOrganizationalProcess',
      as: 'children',
      sourceKey: 'organizationalProcessID'
    });

    if (models.BusinessProcess) {
      OrganizationalProcess.belongsTo(models.BusinessProcess, {
        foreignKey: 'parentBusinessProcess',
        as: 'businessProcess',
        targetKey: 'businessProcessID'
      });

      models.BusinessProcess.hasMany(OrganizationalProcess, {
        foreignKey: 'parentBusinessProcess',
        as: 'organizationalProcesses',
        sourceKey: 'businessProcessID'
      });
    }

    OrganizationalProcess.belongsToMany(models.AuditScope, {
      through: 'AuditScopeProcesses',
      foreignKey: 'organizationalProcessID',
      otherKey: 'auditScopeID',
      as: 'auditScopes'
    });
  };

  return OrganizationalProcess;
};
