const { Sequelize } = require('sequelize');
const notificationController = require('../controllers/notifications/notification-controller');
const socketUtils = require('../utils/socket-io');
require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });

// Create Sequelize instance
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: false
  }
);

async function testControlAssignmentNotification() {
  try {
    console.log('🧪 Testing Control Assignment Notification System...');
    
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Get a test user
    const [users] = await sequelize.query(`
      SELECT id, username, email FROM "Users" LIMIT 1;
    `);
    
    if (users.length === 0) {
      console.error('❌ No users found in database. Please create a user first.');
      return;
    }
    
    const user = users[0];
    console.log(`✅ Found test user: ID: ${user.id}, Username: ${user.username}, Email: ${user.email}`);

    // Get a test control
    const [controls] = await sequelize.query(`
      SELECT "controlID", name FROM "Control" LIMIT 1;
    `);
    
    if (controls.length === 0) {
      console.error('❌ No controls found in database. Please create a control first.');
      return;
    }
    
    const control = controls[0];
    console.log(`✅ Found test control: ID: ${control.controlID}, Name: ${control.name}`);

    // Create a test notification
    const notificationData = {
      userId: user.id,
      type: 'control_assignment',
      entityId: control.controlID,
      entityName: control.name || `Contrôle ${control.controlID}`,
      message: `Test: Vous avez été assigné(e) au contrôle : ${control.name || control.controlID}`,
      assignedBy: 'Test Script'
    };

    console.log('📝 Creating test notification:', notificationData);
    const notification = await notificationController.createNotification(notificationData);
    
    if (!notification) {
      console.error('❌ Failed to create notification');
      return;
    }
    
    console.log('✅ Notification created successfully:', {
      id: notification.id,
      type: notification.type,
      message: notification.message
    });

    // Test Socket.IO emission (if available)
    try {
      const io = socketUtils.getIo();
      if (io) {
        const targetRoom = `user-${user.id.toString()}`;
        const socketData = {
          ...notificationData,
          id: notification.id,
          notificationId: notification.id.toString(),
          createdAt: notification.createdAt,
          is_read: false
        };
        
        io.to(targetRoom).emit('notification', socketData);
        console.log(`✅ Socket notification sent to room ${targetRoom}`);
      } else {
        console.log('⚠️  Socket.IO instance not available (server not running)');
      }
    } catch (socketError) {
      console.log('⚠️  Socket.IO error (expected if server not running):', socketError.message);
    }

    // Verify notification was saved
    const [savedNotifications] = await sequelize.query(`
      SELECT id, type, entity_id, entity_name, message, user_id, is_read, created_at
      FROM "Notifications"
      WHERE id = ${notification.id};
    `);
    
    if (savedNotifications.length > 0) {
      console.log('✅ Notification verified in database:', savedNotifications[0]);
    } else {
      console.error('❌ Notification not found in database');
    }

    console.log('🎉 Control Assignment Notification test completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   • User: ${user.username} (ID: ${user.id})`);
    console.log(`   • Control: ${control.name} (ID: ${control.controlID})`);
    console.log(`   • Notification ID: ${notification.id}`);
    console.log(`   • Type: ${notification.type}`);
    console.log(`   • Message: ${notification.message}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Run the test
if (require.main === module) {
  testControlAssignmentNotification()
    .then(() => {
      console.log('✅ Test script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Test script failed:', error);
      process.exit(1);
    });
}

module.exports = testControlAssignmentNotification;
