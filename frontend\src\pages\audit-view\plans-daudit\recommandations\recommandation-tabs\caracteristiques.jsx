import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo, useRef } from "react";
import { debounce } from "lodash";
import {
  FileText,
  ClipboardList,
  AlertTriangle,
  Plus,
  Link,
  ChevronUp,
  ChevronDown,
  Edit,
  Trash2,
  Upload,
  Paperclip,
  Loader2,
  User,
  Search,
  CheckCircle
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Di<PERSON>, DialogContent, Di<PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTrigger } from "@/components/ui/dialog";
import { useCustomOutletContext } from "../edit-recommandation";
import { updateRecommendation, linkRecommendationToConstat } from "@/services/audit-recommendation-service";
import { getAllConstats } from "@/services/audit-constat-service";
import { toast } from "sonner";
import { useNavigate, useParams } from "react-router-dom";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";
import { DateInput } from "@/components/ui/date-input";

function RecommandationCaracteristiquesTab() {
  const navigate = useNavigate();
  const { planId, missionAuditId, activiteId } = useParams();
  const { recommandation, setRecommandation, refetchRecommandation } = useCustomOutletContext();
  const API_BASE_URL = getApiBaseUrl();

  // Section toggles
  const [isCaracteristiquesOpen, setIsCaracteristiquesOpen] = useState(true);
  const [isConstatsOpen, setIsConstatsOpen] = useState(true);

  // User selection state
  const [users, setUsers] = useState([]);
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState(null);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);
  const usersAbortControllerRef = useRef(null);

  // Form state
  const [formData, setFormData] = useState({
    name: recommandation?.name || "",
    code: recommandation?.code || "",
    priorite: recommandation?.priorite || "moyen",
    description: recommandation?.description || "",
    details: recommandation?.details || "",
    planification: recommandation?.planification || "",
    responsableId: recommandation?.responsableId || null,
    dateLimite: recommandation?.dateLimite || ""
  });

  // Dialog states
  const [openLinkDialog, setOpenLinkDialog] = useState(false);
  const [availableConstats, setAvailableConstats] = useState([]);
  const [selectedConstatId, setSelectedConstatId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const isMountedRef = useRef(true);

  // Priority options with color mapping (for recommendation priority)
  const prioriteOptions = [
    { value: "très fort", label: "Très fort" },
    { value: "fort", label: "Fort" },
    { value: "moyen", label: "Moyen" },
    { value: "faible", label: "Faible" },
    { value: "très faible", label: "Très faible" }
  ];

  // Colors for recommendation priority circles
  const prioriteColors = {
    'très fort': 'bg-red-500 text-white',
    'fort': 'bg-orange-500 text-white',
    'moyen': 'bg-yellow-500 text-white',
    'faible': 'bg-green-500 text-white',
    'très faible': 'bg-blue-500 text-white'
  };

  // Colors for impact values (in constats table)
  const impactColors = {
    'Tres faible': 'bg-blue-100 text-blue-800',
    'Faible': 'bg-green-100 text-green-800',
    'Moyen': 'bg-yellow-100 text-yellow-800',
    'Fort': 'bg-orange-100 text-orange-800',
    'Très fort': 'bg-red-100 text-red-800'
  };

  // Helper function to capitalize first letter
  const capitalizeFirstLetter = (string) => {
    return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
  };

  // Fetch available constats for linking
  useEffect(() => {
    const fetchAvailableConstats = async () => {
      try {
        const response = await getAllConstats();
        if (response.success) {
          // Filter out constats that are already linked to this recommendation
          const linkedConstatIds = recommandation.constats.map(c => c.id);
          const available = response.data.filter(c => !linkedConstatIds.includes(c.id));
          setAvailableConstats(available);
        }
      } catch (error) {
        console.error('Error fetching available constats:', error);
        toast.error('Erreur lors du chargement des constats disponibles');
      }
    };

    if (openLinkDialog) {
      fetchAvailableConstats();
    }
  }, [openLinkDialog, recommandation.constats]);

  // Fetch users for responsable selection
  useEffect(() => {
    const fetchUsers = async () => {
      // Don't fetch if component is unmounting
      if (!isMountedRef.current) return;

      // Cancel any existing request
      if (usersAbortControllerRef.current) {
        usersAbortControllerRef.current.abort();
      }

      // Create new abort controller
      usersAbortControllerRef.current = new AbortController();

      try {
        setIsLoadingUsers(true);
        const response = await axios.get(
          `${API_BASE_URL}/users`,
          {
            withCredentials: true,
            signal: usersAbortControllerRef.current.signal,
            timeout: 10000
          }
        );

        // Don't update state if component unmounted
        if (!isMountedRef.current) return;

        if (response.data.success) {
          setUsers(response.data.data);
        }
      } catch (error) {
        // Don't update state if component unmounted
        if (!isMountedRef.current) return;

        // Don't show error for aborted requests
        if (error.name === 'CanceledError' || error.code === 'ECONNABORTED') {
          console.log('Users request was aborted');
          return;
        }

        console.error("Error fetching users:", error);
        toast.error("Erreur lors du chargement des utilisateurs");
      } finally {
        if (isMountedRef.current) {
          setIsLoadingUsers(false);
        }
      }
    };

    fetchUsers();

    return () => {
      if (usersAbortControllerRef.current) {
        usersAbortControllerRef.current.abort();
      }
    };
  }, [API_BASE_URL]);

  // Initialize form data and selected user when recommendation and users are loaded
  useEffect(() => {
    if (recommandation && !isLoadingUsers) {
      console.log('=== DEBUG: Recommendation Data ===');
      console.log('Full recommandation:', recommandation);
      console.log('responsableId:', recommandation.responsableId, 'type:', typeof recommandation.responsableId);
      console.log('Available users:', users);

      // Handle responsable assignment
      let selectedUserData = null;

      // Find the user data for display from the users array
      if (recommandation.responsableId !== null && recommandation.responsableId !== undefined) {
        const userData = users.find(user =>
          user.id === recommandation.responsableId ||
          user.id === parseInt(recommandation.responsableId) ||
          user.id.toString() === recommandation.responsableId.toString()
        );
        if (userData) {
          selectedUserData = userData;
          console.log('Found user data:', selectedUserData);
        } else {
          console.log('No user found for ID:', recommandation.responsableId);
          console.log('Available user IDs:', users.map(u => ({ id: u.id, type: typeof u.id })));
        }
      }

      console.log('Selected user data:', selectedUserData);
      console.log('=== END DEBUG ===');

      setSelectedUser(selectedUserData);

      setFormData({
        name: recommandation?.name || "",
        code: recommandation?.code || "",
        priorite: recommandation?.priorite || "moyen",
        description: recommandation?.description || "",
        details: recommandation?.details || "",
        planification: recommandation?.planification || "",
        responsableId: recommandation?.responsableId || null,
        dateLimite: recommandation?.dateLimite || ""
      });
    }
  }, [recommandation, users, isLoadingUsers]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Auto-save function
  const autoSave = useCallback(async (currentFormData) => {
    if (!recommandation?.id || !isMountedRef.current) {
      return;
    }

    // Validate required fields before saving
    if (!currentFormData.name.trim()) {
      return; // Don't save if required fields are empty
    }

    setIsAutoSaving(true);
    try {
      const response = await updateRecommendation(recommandation.id, currentFormData);

      if (response.success && isMountedRef.current) {
        // Update the local state with the response data
        setFormData(prev => ({
          ...prev,
          name: response.data.name,
          code: response.data.code,
          priorite: response.data.priorite,
          description: response.data.description,
          details: response.data.details,
          planification: response.data.planification,
          responsableId: response.data.responsableId
        }));

        // Refresh the parent context to update the header
        if (refetchRecommandation) {
          await refetchRecommandation();
        }
      } else if (isMountedRef.current) {
        throw new Error(response.message || 'Erreur lors de la sauvegarde automatique');
      }
    } catch (error) {
      if (isMountedRef.current) {
        console.error('Error auto-saving recommendation:', error);
        toast.error('Erreur lors de la sauvegarde automatique');
      }
    } finally {
      if (isMountedRef.current) {
        setIsAutoSaving(false);
      }
    }
  }, [recommandation?.id, refetchRecommandation]);

  // Debounced auto-save function
  const debouncedAutoSave = useMemo(
    () => debounce(autoSave, 1500), // 1.5 second debounce delay
    [autoSave]
  );

  // Handlers
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      const newFormData = { ...prev, [name]: value };
      // Trigger auto-save
      debouncedAutoSave(newFormData);
      return newFormData;
    });
  };

  const handleSelectChange = (name, value) => {
    setFormData((prev) => {
      const newFormData = { ...prev, [name]: value };
      // Trigger auto-save
      debouncedAutoSave(newFormData);
      return newFormData;
    });
  };

  const handleLinkConstat = async () => {
    if (!selectedConstatId) {
      toast.error('Veuillez sélectionner un constat');
      return;
    }

    setIsLoading(true);
    try {
      const response = await linkRecommendationToConstat(recommandation.id, selectedConstatId);
      if (response.success) {
        setRecommandation(response.data);
        setOpenLinkDialog(false);
        setSelectedConstatId(null);
        toast.success('Constat lié avec succès');
      } else {
        throw new Error(response.message || 'Erreur lors de la liaison');
      }
    } catch (error) {
      console.error('Error linking constat:', error);
      toast.error(error.message || 'Erreur lors de la liaison du constat');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle user selection
  const handleUserSelect = (user) => {
    setSelectedUser(user);
    setFormData(prev => {
      const newFormData = { ...prev, responsableId: user.id };
      // Trigger auto-save with debouncing
      debouncedAutoSave(newFormData);
      return newFormData;
    });
    setIsUserModalOpen(false);
    setSearchTerm('');
  };

  // Handle clearing selected user
  const handleClearUser = () => {
    setSelectedUser(null);
    setFormData(prev => {
      const newFormData = { ...prev, responsableId: null };
      // Trigger auto-save with debouncing
      debouncedAutoSave(newFormData);
      return newFormData;
    });
  };

  // Add handler for date limite (dateLimite)
  const handleDateLimiteChange = async (e) => {
    const value = e.target.value;
    setFormData((prev) => ({ ...prev, dateLimite: value }));
    try {
      if (recommandation?.id) {
        await updateRecommendation(recommandation.id, { ...formData, dateLimite: value });
        toast.success('Date limite mise à jour');
        if (refetchRecommandation) await refetchRecommandation();
      }
    } catch {
      toast.error("Erreur lors de la mise à jour de la date limite");
    }
  };

  // Filter users based on search term
  const filteredUsers = users.filter(user =>
    user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Show loading state when data is still being fetched
  if (!recommandation || isLoadingUsers) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
          <p className="text-gray-500">Chargement des caractéristiques de la recommandation...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 py-4">
      {/* Section 1: Caractéristiques */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg"
          onClick={() => setIsCaracteristiquesOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isCaracteristiquesOpen ? (
              <ChevronUp className="h-5 w-5 text-blue-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-blue-600" />
            )}
            <FileText className="h-5 w-5 text-blue-600 mr-1" />
            <span className="text-lg font-medium text-blue-800">Caractéristiques</span>
          </div>
          <div className="flex items-center gap-2">
            {isAutoSaving && (
              <div className="flex items-center text-sm text-blue-600">
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Sauvegarde automatique...
              </div>
            )}
          </div>
        </button>
        {isCaracteristiquesOpen && (
          <div className="p-5 bg-white space-y-6">
            {/* Row 1: Nom (3/4) and Code (1/4) */}
            <div className="grid grid-cols-4 gap-4">
              <div className="col-span-3 space-y-2">
                <Label htmlFor="name">Nom *</Label>
                <Input 
                  id="name" 
                  name="name" 
                  value={formData.name} 
                  onChange={handleInputChange} 
                  placeholder="Nom de la recommandation" 
                  className="w-full" 
                  required 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="code">Code</Label>
                <Input 
                  id="code" 
                  name="code" 
                  value={formData.code} 
                  onChange={handleInputChange} 
                  placeholder="Code" 
                  className="w-full" 
                />
              </div>
            </div>

            {/* Row 2: Priorité + Date limite */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 items-end">
              <div className="space-y-2">
                <Label htmlFor="priorite">Priorité *</Label>
                <Select 
                  name="priorite" 
                  value={formData.priorite} 
                  onValueChange={(value) => handleSelectChange("priorite", value)}
                >
                  <SelectTrigger id="priorite" className="w-full">
                    <SelectValue placeholder="Sélectionner une priorité" />
                  </SelectTrigger>
                  <SelectContent>
                    {prioriteOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${prioriteColors[option.value]}`} />
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="dateLimite">Date limite</Label>
                <Input
                  id="dateLimite"
                  name="dateLimite"
                  type="date"
                  value={formData.dateLimite ? formData.dateLimite.slice(0, 10) : ""}
                  onChange={handleDateLimiteChange}
                  className="w-full"
                  disabled={isAutoSaving}
                />
              </div>
            </div>

            {/* Row 3: Responsable (full width) */}
            <div className="w-full space-y-2">
              <Label htmlFor="responsable">Responsable</Label>
              <div className="flex gap-2">
                <Dialog open={isUserModalOpen} onOpenChange={setIsUserModalOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="flex-1 justify-start"
                      type="button"
                      disabled={isLoadingUsers}
                    >
                      {isLoadingUsers ? (
                        <div className="flex items-center gap-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-600" />
                          <span>Chargement des utilisateurs...</span>
                        </div>
                      ) : selectedUser ? (
                        <div className="flex items-center gap-2 w-full justify-between">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-green-600" />
                            <span className="font-medium">{selectedUser.username || selectedUser.email}</span>
                          </div>
                          <span
                            className="text-red-600 cursor-pointer ml-2"
                            onClick={e => {
                              e.stopPropagation();
                              handleClearUser();
                            }}
                            title="Retirer le responsable"
                          >
                            ×
                          </span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-500">Aucun responsable assigné</span>
                        </div>
                      )}
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-md">
                    <DialogHeader>
                      <DialogTitle>Sélectionner un responsable</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="relative">
                        <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Rechercher un utilisateur..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                      <div className="max-h-60 overflow-y-auto space-y-2">
                        {isLoadingUsers ? (
                          <div className="flex items-center justify-center py-8">
                            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-600" />
                            <span className="ml-2 text-gray-500">Chargement des utilisateurs...</span>
                          </div>
                        ) : filteredUsers.length > 0 ? (
                          filteredUsers.map(user => (
                            <div
                              key={user.id}
                              className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                              onClick={() => handleUserSelect(user)}
                            >
                              <div className="flex items-center gap-3">
                                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                  <User className="h-4 w-4 text-blue-600" />
                                </div>
                                <div>
                                  <p className="font-medium">{user.username}</p>
                                  <p className="text-sm text-gray-500">{user.email}</p>
                                </div>
                              </div>
                              {selectedUser?.id === user.id && (
                                <CheckCircle className="h-5 w-5 text-green-500" />
                              )}
                            </div>
                          ))
                        ) : (
                          <p className="text-center text-gray-500 py-4">
                            {searchTerm ? 'Aucun utilisateur trouvé' : 'Aucun utilisateur disponible'}
                          </p>
                        )}
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            {/* Row 4: Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea 
                id="description" 
                name="description" 
                value={formData.description} 
                onChange={handleInputChange} 
                placeholder="Description de la recommandation" 
                rows={3} 
                className="w-full" 
              />
            </div>

            {/* Row 5: Planification */}
            <div className="space-y-2">
              <Label htmlFor="planification">Planification</Label>
              <Textarea 
                id="planification" 
                name="planification" 
                value={formData.planification} 
                onChange={handleInputChange} 
                placeholder="Planification de la recommandation" 
                rows={3} 
                className="w-full" 
              />
            </div>

            {/* Row 6: Détails */}
            <div className="space-y-2">
              <Label htmlFor="details">Détails</Label>
              <Textarea 
                id="details" 
                name="details" 
                value={formData.details} 
                onChange={handleInputChange} 
                placeholder="Détails de la recommandation" 
                rows={5} 
                className="w-full" 
              />
            </div>
          </div>
        )}
      </div>

      {/* Section 2: Constats */}
      <div className="border rounded-lg shadow-sm">
        <button
          type="button"
          className="w-full flex items-center p-4 bg-gradient-to-r from-amber-50 to-yellow-50 rounded-t-lg"
          onClick={() => setIsConstatsOpen((v) => !v)}
        >
          <div className="flex items-center gap-2">
            {isConstatsOpen ? (
              <ChevronUp className="h-5 w-5 text-yellow-600" />
            ) : (
              <ChevronDown className="h-5 w-5 text-yellow-600" />
            )}
            <AlertTriangle className="h-5 w-5 text-yellow-600 mr-1" />
            <span className="text-lg font-medium text-yellow-800">Constats</span>
          </div>
        </button>
        {isConstatsOpen && (
          <div className="p-5 bg-white space-y-4">
            <div className="flex gap-2 mb-4 justify-end">
              <Button 
                variant="outline" 
                className="flex items-center border-[#F62D51] text-[#F62D51]" 
                onClick={() => setOpenLinkDialog(true)}
              >
                <Link className="h-4 w-4 mr-2" />Relier
              </Button>
            </div>
            <Card className="overflow-hidden">
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <Table className="min-w-full">
                    <TableHeader>
                      <TableRow className="bg-gray-50 hover:bg-gray-100/50">
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Impact</TableHead>
                        <TableHead className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</TableHead>
                        <TableHead className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {recommandation.constats?.map((constat) => (
                        <TableRow key={constat.id} className="hover:bg-gray-50">
                          <TableCell className="px-4 py-3 text-sm font-medium text-gray-900">
                            {constat.name}
                          </TableCell>
                          <TableCell className="px-4 py-3 text-sm">
                            <Badge className={`${impactColors[capitalizeFirstLetter(constat.impact)]} border-0`}>
                              {capitalizeFirstLetter(constat.impact)}
                            </Badge>
                            </TableCell>
                          <TableCell className="px-4 py-3 text-sm">
                            {constat.type}
                            </TableCell>
                          <TableCell className="px-4 py-3 text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-500 hover:text-gray-700"
                              onClick={() => navigate(`/audit/plans-daudit/${planId}/missions-audits/${missionAuditId}/activites/${activiteId}/constats/${constat.id}`)}
                            >
                              Voir
                            </Button>
                            </TableCell>
                          </TableRow>
                      ))}
                      {(!recommandation.constats || recommandation.constats.length === 0) && (
                        <TableRow>
                          <TableCell colSpan={4} className="px-4 py-8 text-center text-gray-500">
                            Aucun constat lié
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Link Constat Dialog */}
      <Dialog open={openLinkDialog} onOpenChange={setOpenLinkDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Relier un constat</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="constat">Sélectionner un constat</Label>
              <Select 
                value={selectedConstatId} 
                onValueChange={setSelectedConstatId}
              >
                <SelectTrigger id="constat" className="w-full">
                  <SelectValue placeholder="Sélectionner un constat" />
                </SelectTrigger>
                <SelectContent>
                  {availableConstats.map((constat) => (
                    <SelectItem key={constat.id} value={constat.id}>
                      {constat.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenLinkDialog(false)}>
              Annuler
            </Button>
            <Button 
              onClick={handleLinkConstat} 
              disabled={isLoading || !selectedConstatId}
              className="bg-[#F62D51] hover:bg-[#F62D51]/90"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Liaison...
                </>
              ) : (
                'Relier'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default RecommandationCaracteristiquesTab; 