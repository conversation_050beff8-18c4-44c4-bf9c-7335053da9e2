'use strict';

module.exports = (sequelize, DataTypes) => {
  const Question = sequelize.define('Question', {
    id: {
      type: DataTypes.STRING(50),
      primaryKey: true,
      allowNull: false
    },
    question_text: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    input_type: {
      type: DataTypes.STRING(32),
      allowNull: false
    },
    options: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    order_index: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    ficheDeTravailID: {
      type: DataTypes.STRING(50),
      allowNull: false,
      references: {
        model: 'FicheDeTravail',
        key: 'id'
      }
    }
  }, {
    tableName: 'Questions',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['id']
      },
      {
        fields: ['ficheDeTravailID'] // For foreign key lookups
      },
      {
        fields: ['order_index'] // For ordering queries
      },
      {
        fields: ['ficheDeTravailID', 'order_index'] // Composite index for ordered queries
      },
      {
        fields: ['createdAt'] // For date-based queries
      },
      {
        fields: ['updatedAt'] // For date-based queries
      }
    ]
  });

  Question.associate = function(models) {
    Question.belongsTo(models.FicheDeTravail, {
      foreignKey: 'ficheDeTravailID',
      as: 'ficheDeTravail'
    });
  };

  return Question;
};