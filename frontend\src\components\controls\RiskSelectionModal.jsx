import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
import { Search } from "lucide-react";
import { useState } from "react";

// Helper functions for risk labels
const getImpactLabel = (value) => {
  if (!value) return { label: 'N/A', color: 'bg-gray-100 text-gray-500', dotColor: 'bg-gray-500' };
  const impactLabels = {
    '1': { label: 'Très Faible', color: 'bg-green-100 text-green-800', dotColor: 'bg-green-500' },
    '2': { label: 'Faible', color: 'bg-blue-100 text-blue-800', dotColor: 'bg-blue-500' },
    '3': { label: 'Moyen', color: 'bg-yellow-100 text-yellow-800', dotColor: 'bg-yellow-500' },
    '4': { label: 'Élevé', color: 'bg-orange-100 text-orange-800', dotColor: 'bg-orange-500' },
    '5': { label: 'Très <PERSON>lev<PERSON>', color: 'bg-red-100 text-red-800', dotColor: 'bg-red-500' }
  };
  return impactLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500', dotColor: 'bg-gray-500' };
};

const getProbabilityLabel = (value) => {
  if (!value) return { label: 'N/A', color: 'bg-gray-100 text-gray-500', dotColor: 'bg-gray-500' };
  const probabilityLabels = {
    '1': { label: 'Très Faible', color: 'bg-green-100 text-green-800', dotColor: 'bg-green-500' },
    '2': { label: 'Faible', color: 'bg-blue-100 text-blue-800', dotColor: 'bg-blue-500' },
    '3': { label: 'Moyen', color: 'bg-yellow-100 text-yellow-800', dotColor: 'bg-yellow-500' },
    '4': { label: 'Élevé', color: 'bg-orange-100 text-orange-800', dotColor: 'bg-orange-500' },
    '5': { label: 'Très Élevé', color: 'bg-red-100 text-red-800', dotColor: 'bg-red-500' }
  };
  return probabilityLabels[value.toString()] || { label: value, color: 'bg-gray-100 text-gray-500', dotColor: 'bg-gray-500' };
};

export function RiskSelectionModal({ open, onClose, onSelect, risks = [] }) {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredRisks = risks.filter((risk) =>
    Object.values(risk).some((value) =>
      value && value.toString().toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-[90vw] md:max-w-[900px] max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Sélectionner un Risque</DialogTitle>
          <p className="text-sm text-gray-500">
            Sélectionnez un risque à associer à ce contrôle.
          </p>
          <div className="relative mt-4">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Rechercher des risques..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:border-[#F62D51]"
            />
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto mt-4 pr-2">
          <div className="space-y-2">
            {filteredRisks.length > 0 ? (
              filteredRisks.map((risk) => (
                <div
                  key={risk.riskID || risk.id}
                  className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    onSelect(risk);
                    onClose();
                  }}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium">{risk.name}</h3>
                        {risk.code && (
                          <span className="text-xs text-gray-400">Code: {risk.code}</span>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">{risk.comment || 'Aucune description'}</p>
                    </div>
                    <div className="flex gap-3 text-sm ml-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium flex items-center ${getImpactLabel(risk.impact).color}`}>
                        <div className={`w-2 h-2 rounded-full mr-1 ${getImpactLabel(risk.impact).dotColor}`}></div>
                        Impact: {getImpactLabel(risk.impact).label}
                      </span>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium flex items-center ${getProbabilityLabel(risk.probability).color}`}>
                        <div className={`w-2 h-2 rounded-full mr-1 ${getProbabilityLabel(risk.probability).dotColor}`}></div>
                        Probabilité: {getProbabilityLabel(risk.probability).label}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-4 border rounded-lg text-center text-gray-500">
                Aucun risque trouvé correspondant à vos critères de recherche.
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="mt-4">
          <Button type="button" variant="outline" onClick={onClose}>
            Annuler
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
