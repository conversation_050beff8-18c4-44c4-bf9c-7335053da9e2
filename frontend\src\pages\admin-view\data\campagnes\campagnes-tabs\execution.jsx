import React, { useState, useEffect, useCallback } from "react";
import { useOutletContext } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { DateInput } from "@/components/ui/date-input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Activity,
  Loader2,
  AlertCircle,
  CheckCircle2,
  Save,
  Plus,
  Minus,
  Upload,
  File,
  Download,
  Trash,
  Link,
  ExternalLink
} from "lucide-react";
import { toast } from "sonner";
import { debounce } from "lodash";
import {
  getQuestionsByCampagneId,
  getResponsesByCampagneId,
  upsertCampagneResponse
} from "@/services/campagne-response-service";
import { getControlMethodeExecution } from "@/services/control-methode-execution-service";
import { getApiBaseUrl } from "@/utils/api-config";
import axios from "axios";

const API_BASE_URL = getApiBaseUrl();

// Separate component for multi-select to isolate hooks
const MultiSelect = ({ question, sampleNumber, value, onAnswerChange }) => {
  const selectedValues = Array.isArray(value) ? value : (value ? [value] : []);
  const [open, setOpen] = useState(false); // State to control dropdown open/close

  const handleSelectToggle = (option) => {
    let newValues;
    if (selectedValues.includes(option)) {
      // Remove if already selected
      newValues = selectedValues.filter(v => v !== option);
    } else {
      // Add if not selected
      newValues = [...selectedValues, option];
    }
    onAnswerChange(sampleNumber, question.id, newValues);
  };

  return (
    <Select open={open} onOpenChange={setOpen}>
      <SelectTrigger className="w-full text-xs h-8">
        {selectedValues.length > 0 ? (
          <span className="text-gray-700">
            {selectedValues.length} item(s) sélectionné(s)
          </span>
        ) : (
          <span className="text-gray-500">Sélectionnez</span>
        )}
      </SelectTrigger>
      <SelectContent>
        {question.options?.map((option, optIndex) => {
          const isSelected = selectedValues.includes(option);
          return (
            <div
              key={optIndex}
              className="relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
              onClick={() => handleSelectToggle(option)}
              onMouseDown={(e) => e.preventDefault()} // Keep dropdown open on click
            >
              <Checkbox
                checked={isSelected}
                onCheckedChange={() => handleSelectToggle(option)}
                className="mr-2"
              />
              <span className="flex-1">{option}</span>
            </div>
          );
        })}
      </SelectContent>
    </Select>
  );
};

function Execution() {
  const { campagne } = useOutletContext();
  const [questions, setQuestions] = useState([]);
  const [responses, setResponses] = useState({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [sampleNumbers, setSampleNumbers] = useState([1, 2, 3]); // Default 3 samples
  const [error, setError] = useState(null);
  const [executionData, setExecutionData] = useState(null);
  const [calculatedSampleSize, setCalculatedSampleSize] = useState(0);

  // File upload and reference states
  const [businessDocuments, setBusinessDocuments] = useState({});
  const [externalReferences, setExternalReferences] = useState({});
  const [isReferenceModalOpen, setIsReferenceModalOpen] = useState(false);
  const [newReference, setNewReference] = useState({
    url: '',
    description: '',
    questionId: null,
    sampleNumber: null
  });
  const [uploadProgress, setUploadProgress] = useState(0);

  // Fetch control execution data and calculate sample size
  const fetchControlExecutionData = async () => {
    if (!campagne?.controlId) return;

    try {
      const token = localStorage.getItem('token');
      const response = await getControlMethodeExecution(campagne.controlId);
      if (response.success && response.data) {
        setExecutionData(response.data);

        // Calculate sample size: taillePopulationTotale × (tailleEchantillon / 100)
        const populationSize = parseInt(response.data.taillePopulationTotale) || 0;
        const samplePercentage = parseInt(response.data.tailleEchantillon) || 0;
        const calculatedSize = Math.round(populationSize * (samplePercentage / 100));

        console.log('📊 Sample calculation:', {
          populationSize,
          samplePercentage,
          calculatedSize
        });

        setCalculatedSampleSize(calculatedSize);

        // Update sample numbers based on calculated size
        if (calculatedSize > 0) {
          const newSampleNumbers = Array.from({ length: calculatedSize }, (_, i) => i + 1);
          setSampleNumbers(newSampleNumbers);
        }
      }
    } catch (error) {
      console.error('Error fetching control execution data:', error);
      // Don't show error toast as this is not critical
    }
  };

  // Fetch existing attachments
  const fetchAttachments = async () => {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/all-attachments/campagne/${campagne.campagneID}`,
        {
          withCredentials: true
        }
      );

      if (response.data.success) {
        const attachments = response.data.data || [];
        const businessDocs = {};
        const externalRefs = {};

        attachments.forEach(attachment => {
          const { sampleNumber, questionID, type } = attachment;

          if (type === 'business-document') {
            if (!businessDocs[sampleNumber]) businessDocs[sampleNumber] = {};
            if (!businessDocs[sampleNumber][questionID]) businessDocs[sampleNumber][questionID] = [];
            businessDocs[sampleNumber][questionID].push(attachment);
          } else if (type === 'external-reference') {
            if (!externalRefs[sampleNumber]) externalRefs[sampleNumber] = {};
            if (!externalRefs[sampleNumber][questionID]) externalRefs[sampleNumber][questionID] = [];
            externalRefs[sampleNumber][questionID].push(attachment);
          }
        });

        setBusinessDocuments(businessDocs);
        setExternalReferences(externalRefs);
      }
    } catch (error) {
      console.error('Error fetching attachments:', error);
      // Don't show error toast for attachments as it's not critical
    }
  };

  // Fetch questions and responses
  useEffect(() => {
    if (!campagne?.campagneID) return;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch control execution data first to calculate sample size
        await fetchControlExecutionData();

        // Fetch questions from the associated control
        const questionsResponse = await getQuestionsByCampagneId(campagne.campagneID);
        if (questionsResponse.success) {
          setQuestions(questionsResponse.data || []);
        } else {
          throw new Error('Failed to fetch questions');
        }

        // Fetch existing responses
        const responsesResponse = await getResponsesByCampagneId(campagne.campagneID);
        if (responsesResponse.success) {
          // Convert responses array to object for easier lookup (matching audit format)
          const responsesMap = {};
          responsesResponse.data.forEach(resp => {
            if (!responsesMap[resp.sampleNumber]) {
              responsesMap[resp.sampleNumber] = {};
            }

            let answerValue = resp.response_value;
            if (resp.question?.input_type === 'multi-select' || resp.question?.input_type === 'checkbox') {
              if (typeof answerValue === 'string') {
                try {
                  answerValue = JSON.parse(answerValue);
                } catch (_e) {
                  answerValue = answerValue ? [answerValue] : [];
                }
              }
              if (!Array.isArray(answerValue)) {
                answerValue = answerValue ? [answerValue] : [];
              }
            }

            responsesMap[resp.sampleNumber][resp.questionID] = answerValue;
          });
          setResponses(responsesMap);
        }

        // Fetch existing attachments
        await fetchAttachments();
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Erreur lors du chargement des données');
        toast.error('Erreur lors du chargement des données');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [campagne?.campagneID, campagne?.controlId]);

  // Debounced save function
  const debouncedSave = useCallback(
    debounce(async (questionID, sampleNumber, value) => {
      try {
        setSaving(true);
        await upsertCampagneResponse(campagne.campagneID, {
          questionID,
          sampleNumber,
          response_value: value
        });
      } catch (error) {
        console.error('Error saving response:', error);
        toast.error('Erreur lors de la sauvegarde');
      } finally {
        setSaving(false);
      }
    }, 500),
    [campagne?.campagneID]
  );

  // Handle answer change (matching audit format)
  const handleAnswerChange = (sampleNumber, questionID, value) => {
    setResponses(prev => ({
      ...prev,
      [sampleNumber]: {
        ...prev[sampleNumber],
        [questionID]: value
      }
    }));

    // Auto-save
    debouncedSave(questionID, sampleNumber, value);
  };

  // Add sample
  const addSample = () => {
    const maxSample = Math.max(...sampleNumbers);
    setSampleNumbers([...sampleNumbers, maxSample + 1]);
  };

  // Remove sample
  const removeSample = () => {
    if (sampleNumbers.length > 1) {
      setSampleNumbers(sampleNumbers.slice(0, -1));
    }
  };

  // Helper functions for file uploads and references
  const handleBusinessDocumentUpload = async (e, sampleNumber, questionId) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    console.log('Uploading files for question:', questionId, 'sample:', sampleNumber);

    const allowedExtensions = [
      ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
      ".txt", ".csv", ".rtf", ".odt", ".ods", ".odp",
      ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg",
      ".zip", ".rar", ".7z", ".tar", ".gz",
    ];

    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    const oversizedFiles = files.filter((file) => file.size > MAX_FILE_SIZE);
    if (oversizedFiles.length > 0) {
      const fileNames = oversizedFiles.map((file) => file.name).join(", ");
      toast.error(`Fichiers trop volumineux : ${fileNames}`, {
        description: `Les fichiers suivants dépassent la limite de 50MB : ${fileNames}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }

    const invalidFiles = files.filter((file) => {
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
      return !allowedExtensions.includes(fileExtension);
    });

    if (invalidFiles.length > 0) {
      const fileNames = invalidFiles.map((file) => file.name).join(", ");
      toast.error(`Types de fichiers non autorisés : ${fileNames}`, {
        description: `Extensions autorisées : ${allowedExtensions.join(", ")}`,
        duration: 5000,
      });
      e.target.value = "";
      return;
    }

    try {
      setLoading(true);
      setUploadProgress(0);

      const uploadPromises = files.map(async (file, index) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', 'business-document');
        formData.append('campagneID', campagne.campagneID);
        formData.append('questionID', questionId);
        formData.append('sampleNumber', sampleNumber);

        const response = await axios.post(
          `${API_BASE_URL}/all-attachments/upload`,
          formData,
          {
            withCredentials: true,
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              setUploadProgress(Math.round(((index + progress / 100) / files.length) * 100));
            }
          }
        );

        return response.data;
      });

      const results = await Promise.all(uploadPromises);

      // Update business documents state
      setBusinessDocuments(prev => ({
        ...prev,
        [sampleNumber]: {
          ...prev[sampleNumber],
          [questionId]: [
            ...(prev[sampleNumber]?.[questionId] || []),
            ...results.map(result => result.data)
          ]
        }
      }));

      toast.success(`${files.length} fichier(s) téléchargé(s) avec succès`);
      e.target.value = "";
    } catch (error) {
      console.error('Error uploading files:', error);
      toast.error('Erreur lors du téléchargement des fichiers');
      e.target.value = "";
    } finally {
      setLoading(false);
      setUploadProgress(0);
    }
  };

  const handleReferenceInputChange = (e) => {
    const { name, value } = e.target;
    setNewReference(prev => ({ ...prev, [name]: value }));
  };

  const handleAddExternalReference = async () => {
    if (!newReference.url || !newReference.url.trim()) {
      toast.error("L'URL est requise");
      return;
    }

    if (!campagne?.campagneID) {
      toast.error('Aucune campagne sélectionnée');
      return;
    }

    setLoading(true);
    try {
      // Format URL to ensure it has a protocol
      let formattedUrl = newReference.url.trim();
      if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
        formattedUrl = 'https://' + formattedUrl;
      }

      const response = await axios.post(
        `${API_BASE_URL}/all-attachments/reference`,
        {
          url: formattedUrl,
          fileName: newReference.description || formattedUrl,
          description: newReference.description || '',
          type: 'external-reference',
          campagneID: campagne.campagneID,
          questionID: newReference.questionId,
          sampleNumber: newReference.sampleNumber
        },
        {
          withCredentials: true
        }
      );

      if (response.data.success) {
        // Update external references state
        setExternalReferences(prev => ({
          ...prev,
          [newReference.sampleNumber]: {
            ...prev[newReference.sampleNumber],
            [newReference.questionId]: [
              ...(prev[newReference.sampleNumber]?.[newReference.questionId] || []),
              response.data.data
            ]
          }
        }));

        toast.success('Référence externe ajoutée avec succès');
        setNewReference({
          url: '',
          description: '',
          questionId: null,
          sampleNumber: null
        });
        setIsReferenceModalOpen(false);
      }
    } catch (error) {
      console.error('Error adding external reference:', error);
      toast.error('Erreur lors de l\'ajout de la référence externe');
    } finally {
      setLoading(false);
    }
  };

  const downloadAttachment = async (attachmentId, fileName) => {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/all-attachments/download/${attachmentId}`,
        {
          withCredentials: true,
          responseType: 'blob'
        }
      );

      // Create blob link to download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast.success('Fichier téléchargé avec succès');
    } catch (error) {
      console.error('Error downloading file:', error);
      toast.error('Erreur lors du téléchargement du fichier');
    }
  };

  const deleteBusinessDocument = async (attachmentId) => {
    try {
      const response = await axios.delete(
        `${API_BASE_URL}/all-attachments/${attachmentId}`,
        {
          withCredentials: true
        }
      );

      if (response.data.success) {
        // Remove from business documents state
        setBusinessDocuments(prev => {
          const newState = { ...prev };
          Object.keys(newState).forEach(sampleNumber => {
            Object.keys(newState[sampleNumber]).forEach(questionId => {
              newState[sampleNumber][questionId] = newState[sampleNumber][questionId].filter(
                doc => doc.allAttachmentID !== attachmentId
              );
            });
          });
          return newState;
        });

        toast.success('Document supprimé avec succès');
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      toast.error('Erreur lors de la suppression du document');
    }
  };

  const deleteExternalReference = async (attachmentId) => {
    try {
      const response = await axios.delete(
        `${API_BASE_URL}/all-attachments/${attachmentId}`,
        {
          withCredentials: true
        }
      );

      if (response.data.success) {
        // Remove from external references state
        setExternalReferences(prev => {
          const newState = { ...prev };
          Object.keys(newState).forEach(sampleNumber => {
            Object.keys(newState[sampleNumber]).forEach(questionId => {
              newState[sampleNumber][questionId] = newState[sampleNumber][questionId].filter(
                ref => ref.allAttachmentID !== attachmentId
              );
            });
          });
          return newState;
        });

        toast.success('Référence supprimée avec succès');
      }
    } catch (error) {
      console.error('Error deleting reference:', error);
      toast.error('Erreur lors de la suppression de la référence');
    }
  };

  const openExternalReference = (url) => {
    window.open(url, '_blank');
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Render input component based on question type (matching audit implementation)
  const renderInputComponent = (question, sampleNumber) => {
    const value = responses[sampleNumber]?.[question.id] || "";
    const inputId = `sample-${sampleNumber}-question-${question.id}`;

    const docsForCell = businessDocuments[sampleNumber]?.[question.id] || [];
    const refsForCell = externalReferences[sampleNumber]?.[question.id] || [];



    switch (question.input_type) {
      case "text":
        return (
          <Input
            id={inputId}
            value={value}
            onChange={(e) => handleAnswerChange(sampleNumber, question.id, e.target.value)}
            placeholder="Entrez votre réponse"
            className="w-full text-xs"
          />
        );

      case "number":
        return (
          <Input
            id={inputId}
            type="number"
            value={value}
            onChange={(e) => handleAnswerChange(sampleNumber, question.id, e.target.value)}
            placeholder="Nombre"
            className="w-full text-xs"
          />
        );

      case "date":
        return (
          <DateInput
            id={inputId}
            value={value}
            onChange={(e) => handleAnswerChange(sampleNumber, question.id, e.target.value)}
            name={inputId}
            className="w-full text-xs"
          />
        );

      case "radio":
        return (
          <Select
            value={value}
            onValueChange={(val) => handleAnswerChange(sampleNumber, question.id, val)}
          >
            <SelectTrigger className="w-full text-xs h-8">
              <SelectValue placeholder="Sélectionnez" />
            </SelectTrigger>
            <SelectContent>
              {question.options?.map((option, optIndex) => (
                <SelectItem key={optIndex} value={option} className="text-xs">
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case "checkbox":
      case "multi-select":
        return (
          <MultiSelect
            question={question}
            sampleNumber={sampleNumber}
            value={value}
            onAnswerChange={handleAnswerChange}
          />
        );

      case "file":
      case "document":
        return (
          <div className="flex flex-col gap-2 p-1">
            <input
              type="file"
              id={`file-input-${sampleNumber}-${question.id}`}
              onChange={(e) => handleBusinessDocumentUpload(e, sampleNumber, question.id)}
              className="hidden"
              multiple
            />
            <Button
              type="button"
              onClick={() => document.getElementById(`file-input-${sampleNumber}-${question.id}`)?.click()}
              variant="outline"
              size="sm"
              className="justify-start text-xs"
              disabled={loading}
            >
              {loading && uploadProgress > 0 ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Upload className="h-4 w-4 mr-2" />
              )}
              {loading && uploadProgress > 0 ? `Téléchargement (${uploadProgress}%)` : "Ajouter document"}
            </Button>
            {docsForCell.length > 0 && (
              <div className="flex flex-col gap-1">
                {docsForCell.map((doc) => (
                  <div key={doc.allAttachmentID} className="flex items-center justify-between text-xs text-gray-600 bg-gray-50 p-1 rounded">
                    <div className="flex items-center truncate">
                      <File className="h-3 w-3 mr-1 flex-shrink-0" />
                      <span className="truncate" title={doc.fileName}>{doc.fileName}</span>
                      <span className="ml-1 text-gray-500 flex-shrink-0">({formatFileSize(doc.fileSize)})</span>
                    </div>
                    <div className="flex items-center space-x-1 flex-shrink-0">
                      <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => downloadAttachment(doc.allAttachmentID, doc.fileName)} title="Télécharger">
                        <Download className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-6 w-6 text-red-500" onClick={() => deleteBusinessDocument(doc.allAttachmentID)} title="Supprimer">
                        <Trash className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );

      case "url":
      case "reference":
        return (
          <div className="flex flex-col gap-2 p-1">
            <Button
              type="button"
              onClick={() => {
                setNewReference(prev => ({ ...prev, questionId: question.id, sampleNumber: sampleNumber }));
                setIsReferenceModalOpen(true);
              }}
              variant="outline"
              size="sm"
              className="justify-start text-xs"
            >
              <Link className="h-4 w-4 mr-2" />
              Ajouter référence
            </Button>
            {refsForCell.length > 0 && (
              <div className="flex flex-col gap-1">
                {refsForCell.map((ref) => (
                  <div key={ref.allAttachmentID} className="flex items-center justify-between text-xs text-blue-600 bg-blue-50 p-1 rounded">
                    <div className="flex items-center truncate">
                      <ExternalLink className="h-3 w-3 mr-1 flex-shrink-0" />
                      <span className="truncate" title={ref.description || ref.filePath}>{ref.description || ref.filePath}</span>
                    </div>
                    <div className="flex items-center space-x-1 flex-shrink-0">
                      <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => openExternalReference(ref.filePath)} title="Ouvrir">
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-6 w-6 text-red-500" onClick={() => deleteExternalReference(ref.allAttachmentID)} title="Supprimer">
                        <Trash className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );

      default:
        return (
          <Input
            id={inputId}
            value={value}
            onChange={(e) => handleAnswerChange(sampleNumber, question.id, e.target.value)}
            placeholder="Entrez votre réponse"
            className="w-full text-xs"
          />
        );
    }
  };

  if (!campagne) {
    return (
      <div className="flex items-center justify-center h-48">
        <p className="text-gray-500">Chargement des informations de la campagne...</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="flex flex-col items-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-2" />
          <p className="text-gray-500">Chargement des questions...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="flex flex-col items-center">
          <AlertCircle className="h-8 w-8 text-red-500 mb-2" />
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!questions.length) {
    return (
      <Card className="border-dashed border-2 border-gray-300">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Activity className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">
            Aucune question disponible
          </h3>
          <p className="text-gray-500 text-center max-w-md">
            Aucune question n'a été créée pour le contrôle associé à cette campagne.
            Veuillez d'abord créer des questions dans l'onglet Exécution du contrôle.
          </p>
        </CardContent>
      </Card>
    );
  }

  if (calculatedSampleSize === 0 && executionData) {
    return (
      <Card className="border-dashed border-2 border-yellow-300">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <AlertCircle className="h-12 w-12 text-yellow-500 mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">
            Taille d'échantillon non calculable
          </h3>
          <p className="text-gray-500 text-center max-w-md mb-4">
            Veuillez définir la taille de la population totale et le pourcentage d'échantillon dans l'exécution du contrôle.
          </p>
          <div className="text-sm text-gray-400">
            Population: {executionData.taillePopulationTotale || 'Non définie'} •
            Échantillon: {executionData.tailleEchantillon || 'Non défini'}%
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-blue-600" />
            Exécution de la Campagne
            {saving && <Loader2 className="h-4 w-4 animate-spin text-blue-600" />}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">
                {questions.length} question{questions.length !== 1 ? 's' : ''} • {sampleNumbers.length} échantillon{sampleNumbers.length !== 1 ? 's' : ''}
              </span>
              {executionData && (
                <div className="flex items-center gap-2 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                  <span>
                    Population: {executionData.taillePopulationTotale || 0} •
                    Échantillon: {executionData.tailleEchantillon || 0}% •
                    Calculé: {calculatedSampleSize}
                  </span>
                </div>
              )}
            </div>
            {!executionData && (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={removeSample}
                  disabled={sampleNumbers.length <= 1}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addSample}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Main Table */}
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-20 bg-gray-50 sticky left-0 z-10">Échantillon</TableHead>
              {questions.map((question, index) => (
                <TableHead key={question.id} className="min-w-48 bg-gray-50">
                  <div className="text-xs">
                    <div className="font-semibold">{index + 1}. {question.question_text}</div>
                    <div className="text-gray-500 mt-1">Type: {question.input_type}</div>
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {sampleNumbers.map(sampleNumber => (
              <TableRow key={sampleNumber}>
                <TableCell className="w-20 font-medium bg-white sticky left-0 z-10">
                  {sampleNumber}
                </TableCell>
                {questions.map(question => (
                  <TableCell key={question.id}>
                    {renderInputComponent(question, sampleNumber)}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Save Status */}
      {saving && (
        <div className="flex items-center justify-center py-2">
          <div className="flex items-center gap-2 text-blue-600">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm">Sauvegarde en cours...</span>
          </div>
        </div>
      )}

      {/* Reference Modal */}
      <Dialog open={isReferenceModalOpen} onOpenChange={setIsReferenceModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Ajouter une Référence Externe</DialogTitle>
            <DialogDescription>
              Ajoutez une URL et une description facultative pour cette référence.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="url">URL</Label>
              <Input
                id="url"
                value={newReference.url}
                onChange={handleReferenceInputChange}
                name="url"
                placeholder="https://example.com"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description (facultatif)</Label>
              <Textarea
                id="description"
                value={newReference.description}
                onChange={handleReferenceInputChange}
                name="description"
                placeholder="Description de la référence"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReferenceModalOpen(false)}>Annuler</Button>
            <Button onClick={handleAddExternalReference} disabled={loading}>
              {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Plus className="h-4 w-4 mr-2" />}
              Ajouter
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default Execution;