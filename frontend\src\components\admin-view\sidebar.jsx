import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  BadgeCheck,
  LayoutDashboard,
  Zap,
  AlertTriangle,
  ChevronDown,
  Plus,
  List,
  Users,
  Building2,
  Shield,
  LineChart,
  FileType,
  GitBranch,
  FolderTree,
  ChevronLeft,
  ChevronRight,
  ChevronsUpDown,
  Laptop,
  FileText,
  Sparkles,
  ClipboardCheck,
  Home,
  Activity,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
// Avatar components no longer needed as we're using a custom implementation
// import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import logo2 from "../../assets/whitelogovitalis.png";
import bpsIcon from '@/assets/BPS.png';
import orgIcon from '@/assets/org.png';
import operationIcon from '@/assets/operation.png';
import entityIcon from '@/assets/entity.png';

// Get sidebar menu items with translations
const getAdminSidebarMenuItems = (t) => [
  {
    id: "home",
    label: t("admin.sidebar.home", "Home"),
    path: "/admin/welcome",
    icon: <Home className="h-5 w-5" />,
  },
  {
    id: "dashboard",
    label: t("admin.sidebar.dashboard", "Dashboard"),
    path: "/admin/dashboard",
    icon: <LayoutDashboard className="h-5 w-5" />,
  },

  {
    id: "incidents",
    label: t("admin.sidebar.incidents", "Incidents"),
    icon: <Zap className="h-5 w-5" />,
    submenu: [
      {
        id: "incidents-list",
        label: t("admin.sidebar.incidents_list", "Incidents List"),
        path: "/admin/incident",
        icon: <List className="h-4 w-4" />,
      },
      {
        id: "create-incident",
        label: t("admin.sidebar.create_incident", "Create Incident"),
        path: "/admin/incident/add",
        icon: <Plus className="h-4 w-4" />,
      },
      {
        id: "ai-incident",
        label: t("admin.sidebar.ai_assistant", "AI Assistant"),
        path: "/admin/incident/ai",
        icon: <Sparkles className="h-4 w-4 text-purple-500 animate-pulse" />,
        className: "relative group",
        labelClassName: "bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-blue-500 font-medium",
      },
      {
        id: "incident-types",
        label: t("admin.sidebar.incident_types", "Incident Types"),
        path: "/admin/incident-types",
        icon: <FileType className="h-4 w-4" />,
      },
    ],
  },
  {
    id: "risks",
    label: t("admin.sidebar.risks", "Risks"),
    icon: <AlertTriangle className="h-5 w-5" />,
    submenu: [
      {
        id: "risks-list",
        label: t("admin.sidebar.risks_list", "Risks List"),
        path: "/admin/risks",
        icon: <List className="h-4 w-4" />,
      },
      {
        id: "risk-types",
        label: t("admin.sidebar.risk_types", "Risk Types"),
        path: "/admin/risk-types",
        icon: <FileType className="h-4 w-4" />,
      },
    ],
  },
  {
    id: "controls",
    label: t("admin.sidebar.controls", "Controls"),
    path: "/admin/controls",
    icon: <Shield className="h-5 w-5" />,
  },
  {
    id: "campagnes",
    label: "Campagnes",
    path: "/admin/campagnes",
    icon: <Activity className="h-5 w-5" />,
  },
  {
    id: "processes",
    label: t("admin.sidebar.processes", "Processes"),
    icon: <img src={bpsIcon} className="sidebar-icon" alt="processes" />,
    submenu: [
      {
        id: "tree-view",
        label: t("admin.sidebar.tree_view", "Tree View"),
        path: "/admin/processes/tree-view",
        icon: <FolderTree className="h-4 w-4" />,
      },
      {
        id: "business-processes",
        label: t("admin.sidebar.business_processes", "Business Processes"),
        path: "/admin/processes/business-processes",
        icon: <img src={bpsIcon} className="sidebar-icon-sm" alt="business processes" />,
      },
      {
        id: "organizational-processes",
        label: t("admin.sidebar.organizational_processes", "Org. Processes"),
        path: "/admin/processes/organizational-processes",
        icon: <img src={orgIcon} className="sidebar-icon-sm" alt="organizational processes" />,
      },
      {
        id: "operations",
        label: t("admin.sidebar.operations", "Operations"),
        path: "/admin/processes/operations",
        icon: <img src={operationIcon} className="sidebar-icon-sm" alt="operations" />,
      },
    ],
  },
  {
    id: "environment",
    label: t("admin.sidebar.environment", "Environment"),
    icon: <Users className="h-5 w-5" />,
    submenu: [
      {
        id: "entities",
        label: t("admin.sidebar.entities", "Entities"),
        path: "/admin/data/entities",
        icon: <img src={entityIcon} className="sidebar-icon-sm" alt="entities" />,
      },
      {
        id: "control-types",
        label: t("admin.sidebar.control_types", "Control Types"),
        path: "/admin/data/control-types",
        icon: <Shield className="h-4 w-4" />,
      },
      {
        id: "business-lines",
        label: t("admin.sidebar.business_lines", "Business Lines"),
        path: "/admin/data/business-lines",
        icon: <LineChart className="h-4 w-4" />,
      },
      {
        id: "applications",
        label: t("admin.sidebar.applications", "Applications"),
        path: "/admin/data/applications",
        icon: <Laptop className="h-4 w-4" />,
      },
      {
        id: "action-plans",
        label: t("admin.sidebar.action_plans", "Action Plans"),
        path: "/admin/data/action-plans",
        icon: <ClipboardCheck className="h-4 w-4" />,
      },
      {
        id: "actions",
        label: t("admin.sidebar.actions", "Actions"),
        path: "/admin/data/actions-management",
        icon: <GitBranch className="h-4 w-4" />,
      },
    ],
  },
  {
    id: "reports",
    label: t("admin.sidebar.reports", "Reports"),
    path: "/admin/reports",
    icon: <FileText className="h-5 w-5" />,
  },
];

function MenuItem({ item, isActive, onClick, isCollapsed }) {
  const [isOpen, setIsOpen] = React.useState(false);
  const hasSubmenu = item.submenu && item.submenu.length > 0;
  const location = useLocation();
  const { t, i18n } = useTranslation();

  const isSubmenuActive = hasSubmenu && item.submenu.some(subItem =>
    location.pathname.startsWith(subItem.path)
  );

  // This function is no longer needed with the popover implementation
  // but we'll keep it for reference
  // const handleParentClick = () => {
  //   if (isCollapsed && hasSubmenu) {
  //     // When collapsed and has submenu, navigate to the first submenu item
  //     onClick(item.submenu[0].path);
  //   } else if (!hasSubmenu) {
  //     // When no submenu, navigate to the item's path
  //     onClick(item.path);
  //   }
  //   // When expanded and has submenu, the default collapsible behavior will work
  // };

  if (hasSubmenu) {
    // When sidebar is collapsed, render a popover with submenu items
    if (isCollapsed) {
      return (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                "w-full text-white hover:bg-white hover:text-[#242A33]",
                isSubmenuActive && "bg-red-500 text-white hover:bg-red-500 hover:text-white",
                "justify-center px-2"
              )}
              title={item.label}
            >
              {item.icon}
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="bg-[#242A33] border-[#3A424E] p-2 w-48"
            side="right"
            align="start"
          >
            <div className="flex flex-col space-y-1">
              <div className="text-white font-medium px-2 py-1 border-b border-[#3A424E] mb-1">
                {item.label}
              </div>
              {item.submenu.map((subItem) => (
                <Button
                  key={subItem.id}
                  variant="ghost"
                  className={cn(
                    "w-full justify-start text-white hover:bg-white hover:text-[#242A33]",
                    isActive(subItem.path) && "bg-red-500 text-white hover:bg-red-500 hover:text-white"
                  )}
                  onClick={() => onClick(subItem.path)}
                >
                  {subItem.icon && <span className="mr-2">{subItem.icon}</span>}
                  {subItem.label}
                </Button>
              ))}
            </div>
          </PopoverContent>
        </Popover>
      );
    }

    // When sidebar is expanded, use the Collapsible component
    return (
      <Collapsible
        open={isOpen || isSubmenuActive}
        onOpenChange={setIsOpen}
      >
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-between text-white hover:bg-white hover:text-[#242A33]",
              isSubmenuActive && "text-white font-medium"
            )}
          >
            <div className="flex items-center gap-2">
              {item.icon}
              <span>{item.label}</span>
            </div>
            <ChevronDown
              className={cn("h-4 w-4 transition-transform", {
                "transform rotate-180": isOpen || isSubmenuActive,
              })}
            />
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="transition-all duration-200 ease-in-out">
          <div className="pt-1 pb-2">
            {item.submenu.map((subItem) => (
              <Button
                key={subItem.id}
                variant="ghost"
                className={cn(
                  "w-full pl-9 justify-start text-white hover:bg-white hover:text-[#242A33]",
                  isActive(subItem.path) && "bg-red-500 text-white hover:bg-red-500 hover:text-white"
                )}
                onClick={() => onClick(subItem.path)}
              >
                {subItem.icon && <span className="mr-2">{subItem.icon}</span>}
                {subItem.label}
              </Button>
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    );
  }

  return (
    <Button
      variant="ghost"
      className={cn(
        "w-full text-white hover:bg-white hover:text-[#242A33]",
        isActive(item.path) && "bg-red-500 text-white hover:bg-red-500 hover:text-white",
        isCollapsed ? "justify-center px-2" : "justify-start"
      )}
      onClick={() => onClick(item.path)}
      title={isCollapsed ? item.label : undefined}
    >
      {item.icon}
      {!isCollapsed && <span className="ml-2">{item.label}</span>}
    </Button>
  );
}

function AdminSideBar({ open, setOpen, onCollapseChange }) {
  const navigate = useNavigate();
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { t, i18n } = useTranslation();

  // Load collapsed state from localStorage on component mount
  useEffect(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState !== null) {
      setIsCollapsed(savedState === 'true');
    }
  }, []);

  // Save collapsed state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', isCollapsed);
    // Notify parent component about the collapse state change
    if (onCollapseChange) {
      onCollapseChange(isCollapsed);
    }
  }, [isCollapsed, onCollapseChange]);

  // Force re-render when language changes
  useEffect(() => {
    // This effect is just to trigger a re-render when language changes
  }, [i18n.language]);

  const toggleCollapse = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);

    // Update localStorage directly for immediate effect
    localStorage.setItem('sidebarCollapsed', newState);

    // Dispatch custom event to notify other components
    window.dispatchEvent(new Event('sidebarStateChanged'));

    // Notify parent component directly if callback exists
    if (onCollapseChange) {
      onCollapseChange(newState);
    }
  };

  const isActive = (path) => {
    // Exact match for dashboard
    if (path === "/admin/dashboard") {
      return location.pathname === path;
    }

    // Special case for incident list - only active when on list or editing an incident
    if (path === "/admin/incident") {
      return location.pathname === path ||
             location.pathname.startsWith("/admin/incident/edit/");
    }

    // Special case for create incident - only active when on create page
    if (path === "/admin/incident/add") {
      return location.pathname === path;
    }

    // Special case for risks list - only active when on list or editing a risk
    if (path === "/admin/risks") {
      return location.pathname === path ||
             location.pathname.startsWith("/admin/risks/edit/");
    }

    // Special case for create risk - only active when on create page
    if (path === "/admin/risks/add") {
      return location.pathname === path;
    }

    // For other pages, check if the current path starts with the menu path
    return location.pathname.startsWith(path);
  };

  return (
    <>
      {/* Mobile Sidebar */}
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetContent
          side="left"
          className="w-64 bg-[#242A33] text-white border-[#3A424E] p-0 overflow-hidden flex flex-col"
        >
          <SidebarContent
            navigate={navigate}
            isActive={isActive}
            setOpen={setOpen}
            isCollapsed={false} // Always expanded in mobile view
          />
        </SheetContent>
      </Sheet>

      {/* Desktop Sidebar */}
      <aside
        className={cn(
          "hidden lg:flex flex-col border-r border-[#3A424E] bg-[#242A33] h-screen fixed transition-all duration-300 overflow-visible",
          isCollapsed ? "w-16" : "w-64"
        )}
        style={{ zIndex: 30 }}
      >
        <SidebarContent
          navigate={navigate}
          isActive={isActive}
          isCollapsed={isCollapsed}
          toggleCollapse={toggleCollapse}
        />
      </aside>
    </>
  );
}

function SidebarContent({ navigate, isActive, setOpen, isCollapsed, toggleCollapse }) {
  const handleNavigate = (path) => {
    navigate(path);
    setOpen?.(false);
  };

  // Get user data from Redux store
  const user = useSelector((state) => state.auth.user);

  // Get translations
  const { t, i18n } = useTranslation();

  // Store menu items in state so they update when language changes
  const [menuItems, setMenuItems] = useState(getAdminSidebarMenuItems(t));

  // Update menu items when language changes
  useEffect(() => {
    setMenuItems(getAdminSidebarMenuItems(t));
  }, [t, i18n.language]);

  return (
    <div className="flex flex-col h-full relative">
      {/* Logo and Collapse Toggle */}
      <div className="p-3 border-b border-[#3A424E] flex justify-between items-center h-[60px]">
        <div
          onClick={() => handleNavigate("/admin/dashboard")}
          className="cursor-pointer flex items-center justify-center w-full"
        >
          <img src={logo2} alt="Logo" className="h-auto w-auto max-h-10" />
        </div>
        {toggleCollapse && (
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleCollapse}
            className={cn(
              "absolute text-white rounded-full shadow-md",
              "w-8 h-8 flex items-center justify-center",
              "bg-[#1E2329] hover:bg-[#2A3038] border border-[#3A424E]",
              "transition-all duration-200 hover:scale-110",
              "right-[-15px]"
            )}
            style={{
              transform: "translateX(75%)",
              zIndex: 999, // Ensure it's above everything
              top: "calc(30px - 1rem)" // Center vertically
            }}
          >
            {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        )}
      </div>

      {/* Menu Items */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className={cn("space-y-1", isCollapsed ? "px-1 py-4" : "px-3 py-4")}>
            {menuItems.map((item) => (
              <MenuItem
                key={item.id}
                item={item}
                isActive={isActive}
                onClick={handleNavigate}
                isCollapsed={isCollapsed}
              />
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Profile Section */}
      <div className="border-t border-[#3A424E] h-[72px] p-2 flex items-center justify-center">
        {/* When collapsed, use a simpler button structure */}
        {isCollapsed ? (
          <div
            className="w-10 h-10 flex items-center justify-center cursor-pointer"
            onClick={() => handleNavigate("/admin/profile")}
            title="Profile"
          >
            <div className="w-8 h-8 rounded-full bg-gray-800 border border-gray-600 flex items-center justify-center">
              <span className="text-white font-medium text-sm">
                {user?.username?.charAt(0)?.toUpperCase() || 'A'}
              </span>
            </div>
          </div>
        ) : (
          <Button
            variant="ghost"
            className="flex items-center text-white hover:bg-gray-700 hover:text-white p-3 rounded-lg justify-start gap-3 w-full h-[56px]"
            onClick={() => handleNavigate("/admin/profile")}
            title="Profile"
          >
            <div className="w-8 h-8 rounded-full bg-gray-800 border border-gray-600 flex items-center justify-center mr-2">
              <span className="text-white font-medium text-sm">
                {user?.username?.charAt(0)?.toUpperCase() || 'A'}
              </span>
            </div>
            <div className="flex flex-col items-start">
              <span className="text-sm font-medium text-white">{user?.username || 'Admin User'}</span>
              <span className="text-xs text-gray-400">{user?.email || '<EMAIL>'}</span>
            </div>
            <ChevronsUpDown className="h-5 w-5 ml-auto" />
          </Button>
        )}
      </div>
    </div>
  );
}

export default AdminSideBar;
