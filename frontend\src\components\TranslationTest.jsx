import React from 'react';
import { useTranslation } from 'react-i18next';

const TranslationTest = () => {
  const { t, i18n } = useTranslation();

  const testKeys = [
    'admin.controls.features.loading.reference_data',
    'admin.controls.features.sections.descriptions',
    'admin.controls.features.fields.name',
    'common.none',
    'common.yes',
    'common.no'
  ];

  return (
    <div className="p-4 border rounded-lg m-4">
      <h2 className="text-xl font-bold mb-4">Translation Test</h2>
      <p className="mb-2">Current language: {i18n.language}</p>
      
      <div className="mb-4">
        <button 
          onClick={() => i18n.changeLanguage('fr')}
          className="mr-2 px-3 py-1 bg-blue-500 text-white rounded"
        >
          French
        </button>
        <button 
          onClick={() => i18n.changeLanguage('en')}
          className="px-3 py-1 bg-green-500 text-white rounded"
        >
          English
        </button>
      </div>

      <div className="space-y-2">
        {testKeys.map(key => (
          <div key={key} className="flex justify-between">
            <span className="font-mono text-sm">{key}:</span>
            <span className="ml-4">{t(key, `[MISSING: ${key}]`)}</span>
          </div>
        ))}
      </div>

      <div className="mt-4 p-2 bg-gray-100 rounded">
        <h3 className="font-bold">Raw translation test:</h3>
        <p>Reference data: {t('admin.controls.features.loading.reference_data', 'Loading reference data...')}</p>
        <p>None: {t('common.none', 'None')}</p>
      </div>
    </div>
  );
};

export default TranslationTest;
