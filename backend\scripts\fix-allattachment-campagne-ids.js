const { sequelize } = require('../models');
const migration = require('../migrations/20250103000001-fix-allattachment-campagne-id.js');

async function runMigration() {
  try {
    console.log('🚀 Starting AllAttachment campagneID fix...');
    
    const queryInterface = sequelize.getQueryInterface();
    await migration.up(queryInterface, sequelize.Sequelize);
    
    console.log('✅ AllAttachment migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ AllAttachment migration failed:', error);
    process.exit(1);
  }
}

runMigration();
