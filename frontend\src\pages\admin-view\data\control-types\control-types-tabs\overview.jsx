import { useOutletContext } from "react-router-dom";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getAllControlTypes } from "@/store/slices/controlTypeSlice";
import { Tag, Calendar, User, Hash, FileText, Shield } from "lucide-react";
import { useTranslation } from "react-i18next";

function ControlTypesOverview() {
  const { controlType } = useOutletContext();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { controlTypes } = useSelector((state) => state.controlType);
  const [parentControlTypeName, setParentControlTypeName] = useState(t('admin.control_types.form.none', 'None'));

  // Fetch control types if they're not already loaded
  useEffect(() => {
    const shouldFetch = !controlTypes || controlTypes.length === 0;
    if (shouldFetch) {
      dispatch(getAllControlTypes());
    }
  }, [dispatch]);

  // Find parent control type name when control types are loaded
  useEffect(() => {
    // Check if parent ID exists and is not empty
    if (controlType?.parentControlTypeID && controlTypes && controlTypes.length > 0) {
      const parent = controlTypes.find(type => type.controlTypeID === controlType.parentControlTypeID);

      if (parent) {
        setParentControlTypeName(parent.name);
      } else {
        setParentControlTypeName(t('admin.control_types.overview.unknown', 'Unknown'));
      }
    } else {
      setParentControlTypeName(t('admin.control_types.form.none', 'None'));
    }
  }, [controlType, controlTypes]);

  // Helper function to format dates
  const formatDate = (dateString) => {
    if (!dateString) return t('admin.control_types.overview.not_available', 'N/A');
    return new Date(dateString).toLocaleDateString("en-GB", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6">
        <div>
          <h3 className="text-lg font-semibold text-[#242A33] mb-4">{t('admin.control_types.overview.details', 'Details')}</h3>
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Tag className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">{t('admin.control_types.form.name', 'Name')}</p>
                <p className="font-medium">{controlType.name || t('admin.control_types.overview.not_available', 'N/A')}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Hash className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">{t('admin.control_types.form.code', 'Code')}</p>
                <p className="font-medium">{controlType.code || t('admin.control_types.overview.not_available', 'N/A')}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <FileText className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">{t('admin.control_types.form.comment', 'Comment')}</p>
                <p className="font-medium">{controlType.comment || t('admin.control_types.overview.not_available', 'N/A')}</p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="bg-gray-50 p-2 rounded-lg">
                <Shield className="h-5 w-5 text-[#555F6D]" />
              </div>
              <div>
                <p className="text-sm text-[#555F6D]">{t('admin.control_types.form.parent', 'Parent Control Type')}</p>
                <p className="font-medium">{parentControlTypeName}</p>
              </div>
            </div>

            {controlType.createdAt && (
              <div className="flex items-start gap-4">
                <div className="bg-gray-50 p-2 rounded-lg">
                  <Calendar className="h-5 w-5 text-[#555F6D]" />
                </div>
                <div>
                  <p className="text-sm text-[#555F6D]">{t('admin.control_types.overview.created_at', 'Created At')}</p>
                  <p className="font-medium">{formatDate(controlType.createdAt)}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ControlTypesOverview;
