'use strict';

module.exports = (sequelize, DataTypes) => {
  const AllAttachment = sequelize.define('AllAttachment', {
    allAttachmentID: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false
    },
    fileName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    fileSize: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    fileType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    filePath: {
      type: DataTypes.STRING,
      allowNull: true
    },
    url: {
      type: DataTypes.STRING,
      allowNull: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    uploadDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    type: {
      type: DataTypes.ENUM('business-document', 'external-reference'),
      allowNull: false
    },
    auditActivityID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    auditConstatID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    auditFicheDeTravailID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    auditFicheDeTestID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    questionID: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    auditMissionID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    auditScopeID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    actionPlanID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    businessProcessID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    controlID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    entityID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    incidentID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    organizationalProcessID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    riskID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    campagneID: {
      type: DataTypes.STRING,
      allowNull: true
    },
    sampleNumber: {
      type: DataTypes.INTEGER,
      allowNull: true
    }
  }, {
    tableName: 'AllAttachment',
    timestamps: false
  });

  // Define associations
  AllAttachment.associate = function(models) {
    // Audit associations
    if (models.AuditActivity) {
      AllAttachment.belongsTo(models.AuditActivity, { foreignKey: 'auditActivityID', as: 'auditActivity' });
    }
    if (models.AuditConstat) {
      AllAttachment.belongsTo(models.AuditConstat, { foreignKey: 'auditConstatID', as: 'auditConstat' });
    }
    if (models.FicheDeTravail) {
      AllAttachment.belongsTo(models.FicheDeTravail, { foreignKey: 'auditFicheDeTravailID', as: 'auditFicheDeTravail' });
    }
    if (models.FicheDeTest) {
      AllAttachment.belongsTo(models.FicheDeTest, { foreignKey: 'auditFicheDeTestID', as: 'auditFicheDeTest' });
    }
    if (models.AuditMission) {
      AllAttachment.belongsTo(models.AuditMission, { foreignKey: 'auditMissionID', as: 'auditMission' });
    }
    if (models.AuditScope) {
      AllAttachment.belongsTo(models.AuditScope, { foreignKey: 'auditScopeID', as: 'auditScope' });
    }

    // Other model associations
    if (models.ActionPlan) {
      AllAttachment.belongsTo(models.ActionPlan, { foreignKey: 'actionPlanID', as: 'actionPlan' });
    }
    if (models.BusinessProcess) {
      AllAttachment.belongsTo(models.BusinessProcess, { foreignKey: 'businessProcessID', as: 'businessProcess' });
    }
    if (models.Control) {
      AllAttachment.belongsTo(models.Control, { foreignKey: 'controlID', as: 'control' });
    }
    if (models.Entity) {
      AllAttachment.belongsTo(models.Entity, { foreignKey: 'entityID', as: 'entity' });
    }
    if (models.Incident) {
      AllAttachment.belongsTo(models.Incident, { foreignKey: 'incidentID', as: 'incident' });
    }
    if (models.OrganizationalProcess) {
      AllAttachment.belongsTo(models.OrganizationalProcess, { foreignKey: 'organizationalProcessID', as: 'organizationalProcess' });
    }
    if (models.Risk) {
      AllAttachment.belongsTo(models.Risk, { foreignKey: 'riskID', as: 'risk' });
    }
    if (models.Campagne) {
      AllAttachment.belongsTo(models.Campagne, { foreignKey: 'campagneID', as: 'campagne' });
    }
    if (models.ControlQuestion) {
      AllAttachment.belongsTo(models.ControlQuestion, { foreignKey: 'questionID', as: 'question' });
    }
  };

  return AllAttachment;
};