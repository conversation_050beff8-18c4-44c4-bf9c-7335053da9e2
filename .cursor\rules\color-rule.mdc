---
description: 
globs: 
alwaysApply: true
---

# Your rule content

Impact/Severity/Probability Color Rule
Whenever you have a select, dropdown, badge, or table cell for impact, severity, probability, or similar levels, the color mapping will be:
| Value | Color Class | Color Name |
|---------------|--------------------|-------------|
| Très fort / 5 | bg-red-500 | Red |
| Fort / 4 | bg-orange-500 | Orange |
| Moyen / 3 | bg-yellow-400 | Yellow |
| Faible / 2 | bg-green-500 | Green |
| Très faible/1 | bg-blue-500 | Blue |
For selects: a colored circle will be shown next to each option.
For badges: the background/text color will match the value.
For table cells: a colored dot or badge will be shown.
This rule will be applied automatically to all relevant UI elements in my project, unless i explicitly request a different color scheme or logic.

like this exemple here in risk evaluation section:
@frontend\src\pages\admin-view\risks\risks-tabs\features.jsx


