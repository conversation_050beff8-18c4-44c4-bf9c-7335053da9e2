const db = require('../../models');
const { validateTransition, getAvailableTransitions, getAllStates } = require('../../utils/incidentStateMachine');
const activityController = require('./activity-controller');
const { sendEmailDirect } = require('../email-controller');

/**
 * Get the current workflow state for a specific incident
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getWorkflowState = async (req, res) => {
  try {
    const { incidentId } = req.params;

    const incident = await db.Incident.findByPk(incidentId);
    if (!incident) {
      return res.status(404).json({ success: false, message: 'Incident not found' });
    }

    // Get events related to this incident to build a timeline
    const events = await db.IncidentEvent.findAll({
      where: { incident_id: incidentId },
      order: [['timestamp', 'ASC']]
    });

    return res.status(200).json({
      success: true,
      data: {
        current_state: incident.current_state,
        timeline: events
      }
    });
  } catch (error) {
    console.error('Error in getWorkflowState:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve workflow state',
      error: error.message
    });
  }
};

/**
 * Transition an incident to a new state
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.transitionState = async (req, res) => {
  try {
    const { incidentId } = req.params;
    const { transition, userId, message } = req.body;

    if (!transition) {
      return res.status(400).json({
        success: false,
        message: 'Transition action is required'
      });
    }

    // Get user ID from request.user if not provided in the body
    const userIdToUse = userId || req.user.id || req.user.userId || req.user._id;

    if (!userIdToUse) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required but not found in request'
      });
    }

    // Get the incident
    const incident = await db.Incident.findByPk(incidentId);
    if (!incident) {
      return res.status(404).json({ success: false, message: 'Incident not found' });
    }

    // Get user making the change
    const user = await db.User.findByPk(userIdToUse, {
      include: [{ model: db.Role, as: 'roles' }]
    });

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    // Check user roles for permissions
    const userRoles = user.roles || [];
    // Support both role.name and role.code properties for backward compatibility
    const userRoleCodes = userRoles.map(role => {
      // Get the identifier - prefer code, fallback to name, convert to lowercase
      const roleIdentifier = ((role.code || role.name) || '').toLowerCase();
      return roleIdentifier;
    });

    // Add debug logging for roles
    console.log('User role codes for permission check:', {
      userId: userIdToUse,
      username: user.username || user.email,
      userRoles: userRoles.map(r => ({ id: r.id, name: r.name, code: r.code })),
      roleCodes: userRoleCodes,
      requestedTransition: transition,
      currentState: incident.current_state
    });

    // Check permissions based on transition type, current state, and user roles
    if (transition === 'Validate' || transition === 'Reject' || transition === 'Reset') {
      // Only allow grc_admin, grc_manager, risk_manager, incident_manager to validate, reject, or reset
      const hasRequiredRole = userRoleCodes.some(code =>
        ['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager', 'admin', 'super_admin'].includes(code)
      );

      // Also check role names for backward compatibility
      const hasRequiredRoleName = user.roles && user.roles.some(role => {
        if (!role.name) return false;
        const normalizedName = role.name.toLowerCase().replace(/\s+/g, '_');
        return ['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager', 'admin', 'super_admin'].some(
          adminRole => normalizedName.includes(adminRole) || adminRole.includes(normalizedName)
        );
      });

      if (!hasRequiredRole && !hasRequiredRoleName) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to validate, reject, or reset incidents'
        });
      }
    } else if (transition === 'Advance' || transition === 'Approve') {
      // Check if user has admin-level roles
      const hasAdminRole = userRoleCodes.some(code =>
        ['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager', 'admin', 'super_admin'].includes(code)
      );

      // Also check role names for backward compatibility
      const hasAdminRoleName = user.roles && user.roles.some(role => {
        if (!role.name) return false;
        const normalizedName = role.name.toLowerCase().replace(/\s+/g, '_');
        return ['grc_admin', 'grc_manager', 'risk_manager', 'incident_manager', 'admin', 'super_admin'].some(
          adminRole => normalizedName.includes(adminRole) || adminRole.includes(normalizedName)
        );
      });

      // Check if user is GRC Contributor
      const isGrcContributor = userRoleCodes.includes('grc_contributor') ||
        (user.roles && user.roles.some(role => {
          if (!role.name) return false;
          const normalizedName = role.name.toLowerCase().replace(/\s+/g, '_');
          return normalizedName.includes('contributor') || normalizedName.includes('grc_contributor');
        }));

      // If user has admin roles, they can always advance
      if (hasAdminRole || hasAdminRoleName) {
        // Admin roles can advance at any step
        console.log('User has admin role, allowing advance/approve action');
      }
      // If user is only a GRC Contributor and trying to advance beyond To Approve
      else if (isGrcContributor && incident.current_state === 'To Approve') {
        return res.status(403).json({
          success: false,
          message: 'GRC Contributors cannot advance incidents beyond the To Approve state'
        });
      }
      // If user has no valid role for advancing
      else if (!hasAdminRole && !hasAdminRoleName && !isGrcContributor) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to advance the workflow'
        });
      }
    }

    // Validate the transition
    const result = validateTransition(incident.current_state, transition);
    if (!result.valid) {
      return res.status(400).json({
        success: false,
        message: result.error
      });
    }

    // Start transaction
    const t = await db.sequelize.transaction();

    try {
      // Store old state for activity logging
      const oldState = incident.current_state;

      // Update the incident state
      await incident.update({ current_state: result.nextState }, { transaction: t });

      // Log the event with optional message
      await db.IncidentEvent.create({
        incident_id: incidentId,
        step: result.nextState,
        user: user.username || user.email,
        transition: transition,
        timestamp: new Date(),
        message: message || null
      }, { transaction: t });

      // Also log to the activity log
      const username = user.username || user.email || user.name || 'Unknown User';
      await db.IncidentActivityLog.create({
        incident_id: incidentId,
        user: username,
        type: 'transition',
        timestamp: new Date(),
        old_value: oldState,
        new_value: result.nextState,
        details: `Transition: ${transition} to ${result.nextState}${message ? ` - ${message}` : ''}`
      }, { transaction: t });

      // Commit transaction
      await t.commit();

      // After committing transaction, send email if state is now 'To Approve'
      if (result.nextState === 'To Approve') {
        // Find all users with the 'incident_manager' role
        const incidentManagerRole = await db.Role.findOne({ where: { code: 'incident_manager' } });
        if (incidentManagerRole) {
          const userRoles = await db.UserRole.findAll({ where: { roleId: incidentManagerRole.id } });
          const userIds = userRoles.map(ur => ur.userId);
          const users = await db.User.findAll({ where: { id: userIds } });
          for (const user of users) {
            if (user.email) {
              await sendEmailDirect({
                to: user.email,
                subject: "Incident à approuver",
                text: `Bonjour,\n\nL'incident "${incident.name}" nécessite votre approbation. Veuillez consulter les détails et le workflow de l'incident dans l'application.\n\nMerci.`,
                html: `<p>Bonjour,</p><p>L'incident "<b>${incident.name}</b>" nécessite votre approbation. Veuillez consulter les détails et le workflow de l'incident dans l'application.</p><p>Merci.</p>`
              });
            }
          }
        }
      }

      // Get updated events for response
      const events = await db.IncidentEvent.findAll({
        where: { incident_id: incidentId },
        order: [['timestamp', 'ASC']]
      });

      return res.status(200).json({
        success: true,
        message: `Incident successfully transitioned to ${result.nextState}`,
        data: {
          current_state: result.nextState,
          timeline: events
        }
      });
    } catch (error) {
      // Rollback transaction on error
      await t.rollback();
      throw error;
    }
  } catch (error) {
    console.error('Error in transitionState:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to transition incident state',
      error: error.message
    });
  }
};

/**
 * Get available transitions for an incident's current state
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAvailableTransitions = async (req, res) => {
  try {
    const { incidentId } = req.params;

    // Get the incident
    const incident = await db.Incident.findByPk(incidentId);
    if (!incident) {
      return res.status(404).json({
        success: false,
        message: 'Incident not found'
      });
    }

    // Get available transitions from the current state
    const availableTransitions = getAvailableTransitions(incident.current_state);

    return res.status(200).json({
      success: true,
      data: {
        current_state: incident.current_state,
        available_transitions: availableTransitions
      }
    });
  } catch (error) {
    console.error('Error in getAvailableTransitions:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve available transitions',
      error: error.message
    });
  }
};

/**
 * Get all possible workflow states
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAllPossibleStates = async (req, res) => {
  try {
    const states = getAllStates();

    return res.status(200).json({
      success: true,
      data: {
        states: states
      }
    });
  } catch (error) {
    console.error('Error in getAllPossibleStates:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve workflow states',
      error: error.message
    });
  }
};

/**
 * Initialize workflow for a new incident
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.initializeWorkflow = async (req, res) => {
  try {
    const { incidentId } = req.params;
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      });
    }

    // Get the incident
    const incident = await db.Incident.findByPk(incidentId);
    if (!incident) {
      return res.status(404).json({
        success: false,
        message: 'Incident not found'
      });
    }

    // Get user
    const user = await db.User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if workflow is already initialized (has events)
    const existingEvents = await db.IncidentEvent.findOne({
      where: { incident_id: incidentId }
    });

    if (existingEvents) {
      return res.status(400).json({
        success: false,
        message: 'Workflow is already initialized for this incident'
      });
    }

    // Start transaction
    const t = await db.sequelize.transaction();

    try {
      // Set initial state
      const initialState = 'Start';
      await incident.update({ current_state: initialState }, { transaction: t });

      // Log the initialization event
      await db.IncidentEvent.create({
        incident_id: incidentId,
        step: initialState,
        user: user.username || user.email,
        transition: 'Create',
        timestamp: new Date(),
        message: 'Workflow initialized'
      }, { transaction: t });

      // Commit transaction
      await t.commit();

      return res.status(200).json({
        success: true,
        message: 'Workflow successfully initialized',
        data: {
          current_state: initialState
        }
      });
    } catch (error) {
      // Rollback transaction on error
      await t.rollback();
      throw error;
    }
  } catch (error) {
    console.error('Error in initializeWorkflow:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to initialize workflow',
      error: error.message
    });
  }
};

/**
 * Get detailed workflow history for an incident
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getWorkflowHistory = async (req, res) => {
  try {
    const { incidentId } = req.params;

    // Get the incident
    const incident = await db.Incident.findByPk(incidentId);
    if (!incident) {
      return res.status(404).json({
        success: false,
        message: 'Incident not found'
      });
    }

    // Get events with additional information
    const events = await db.IncidentEvent.findAll({
      where: { incident_id: incidentId },
      order: [['timestamp', 'DESC']],
      attributes: ['id', 'timestamp', 'step', 'user', 'transition', 'message']
    });

    // Get users involved in the workflow
    const uniqueUsers = [...new Set(events.map(event => event.user))];

    // Get time in each state
    const stateTimeSpent = {};
    let previousState = null;
    let previousTime = null;

    // Sort events by timestamp ascending for calculations
    const chronologicalEvents = [...events].sort((a, b) =>
      new Date(a.timestamp) - new Date(b.timestamp)
    );

    chronologicalEvents.forEach(event => {
      const currentTime = new Date(event.timestamp);

      if (previousState && previousTime) {
        const timeSpent = currentTime - previousTime;
        stateTimeSpent[previousState] = (stateTimeSpent[previousState] || 0) + timeSpent;
      }

      previousState = event.step;
      previousTime = currentTime;
    });

    // Add time spent in current state (from last event to now)
    if (previousState && previousTime) {
      const currentTime = new Date();
      const timeSpent = currentTime - previousTime;
      stateTimeSpent[previousState] = (stateTimeSpent[previousState] || 0) + timeSpent;
    }

    // Convert milliseconds to readable format
    Object.keys(stateTimeSpent).forEach(state => {
      const totalMs = stateTimeSpent[state];
      const days = Math.floor(totalMs / (1000 * 60 * 60 * 24));
      const hours = Math.floor((totalMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((totalMs % (1000 * 60 * 60)) / (1000 * 60));

      stateTimeSpent[state] = {
        milliseconds: totalMs,
        readable: `${days}d ${hours}h ${minutes}m`
      };
    });

    return res.status(200).json({
      success: true,
      data: {
        current_state: incident.current_state,
        events: events,
        participants: uniqueUsers,
        state_time_spent: stateTimeSpent
      }
    });
  } catch (error) {
    console.error('Error in getWorkflowHistory:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve workflow history',
      error: error.message
    });
  }
};
