import { useEffect, useState, useRef } from "react";
import { useParams } from "react-router-dom";
import { Users, Search, Mail, Phone, MapPin, Briefcase, Loader2, AlertCircle, UserCheck } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "sonner";
import axios from "axios";
import { getApiBaseUrl } from "@/utils/api-config";
import { useApiRequest, withApiErrorHandling } from "@/hooks/useApiRequest";
import userService from "@/services/userService";

function AffectationRessourcesTab({ auditPlan }) {
  const { id } = useParams();
  const { makeRequest, cancelAllRequests } = useApiRequest();

  const [assignedUsers, setAssignedUsers] = useState([]);
  const [allUsers, setAllUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch all users from the system first using the user service
  useEffect(() => {
    const fetchAllUsers = async () => {
      try {
        const users = await userService.fetchUsers({ silent: true });
        setAllUsers(users);
      } catch (error) {
        console.warn("Could not fetch users:", error.message);
        setAllUsers([]);
      }
    };

    fetchAllUsers();

    // Cleanup on unmount
    return () => {
      cancelAllRequests();
    };
  }, [cancelAllRequests]);

  // Fetch all users assigned to any module of this specific plan
  useEffect(() => {
    const fetchAssignedUsers = withApiErrorHandling(async () => {
      if (!id) return;

      setLoading(true);

      // Fetch missions for this plan
      const missionsResponse = await makeRequest({
        method: 'get',
        url: `${getApiBaseUrl()}/audit-missions/plan/${id}`,
        withCredentials: true,
        headers: { 'Content-Type': 'application/json' }
      }, {
        retries: 2,
        onError: (error) => {
          if (!axios.isCancel(error)) {
            console.error("Error fetching missions:", error);
            toast.error("Erreur lors du chargement des missions");
          }
        }
      });

      let allAssignedUsers = [];

      if (missionsResponse && missionsResponse.data.success && missionsResponse.data.data) {
        const missions = missionsResponse.data.data;

        // Collect users from missions
        for (const mission of missions) {
          if (mission.chefmission) {
            allAssignedUsers.push({
              userId: mission.chefmission,
              role: 'Chef de mission',
              module: 'Mission',
              moduleName: mission.name,
              moduleId: mission.id
            });
          }
          if (mission.principalAudite) {
            allAssignedUsers.push({
              userId: mission.principalAudite,
              role: 'Principal audité',
              module: 'Mission',
              moduleName: mission.name,
              moduleId: mission.id
            });
          }

          // Fetch activities for each mission
          try {
            const activitiesResponse = await makeRequest({
              method: 'get',
              url: `${getApiBaseUrl()}/audit-activities/mission/${mission.id}`,
              withCredentials: true,
              headers: { 'Content-Type': 'application/json' }
            }, {
              retries: 1,
              silentError: true
            });

            if (activitiesResponse && activitiesResponse.data.success && activitiesResponse.data.data) {
              const activities = activitiesResponse.data.data;

              for (const activity of activities) {
                if (activity.responsable) {
                  allAssignedUsers.push({
                    userId: activity.responsable,
                    role: 'Responsable',
                    module: 'Activité',
                    moduleName: activity.name,
                    moduleId: activity.id
                  });
                }
              }
            }
          } catch (activityError) {
            if (!axios.isCancel(activityError)) {
              console.warn(`Could not fetch activities for mission ${mission.id}:`, activityError.message);
            }
          }
        }

        // Fetch recommendations for this plan
        try {
          const recommendationsResponse = await makeRequest({
            method: 'get',
            url: `${getApiBaseUrl()}/audit-recommendations/audit-plan/${id}`,
            withCredentials: true,
            headers: { 'Content-Type': 'application/json' }
          }, {
            retries: 1,
            silentError: true
          });

          if (recommendationsResponse && recommendationsResponse.data.success && recommendationsResponse.data.data) {
            const recommendations = recommendationsResponse.data.data;

            for (const recommendation of recommendations) {
              if (recommendation.responsableId) {
                allAssignedUsers.push({
                  userId: recommendation.responsableId,
                  role: 'Responsable',
                  module: 'Recommandation',
                  moduleName: recommendation.name,
                  moduleId: recommendation.id
                });
              }
            }
          }
        } catch (recommendationError) {
          if (!axios.isCancel(recommendationError)) {
            console.warn(`Could not fetch recommendations for plan ${id}:`, recommendationError.message);
          }
        }
      }

        // Map user details using user service
        const userIds = [...new Set(allAssignedUsers.map(u => u.userId))];
        const usersWithDetails = [];

        for (const userId of userIds) {
          // Use user service to get user details with fallback
          const user = userService.getUserById(userId);
          const userAssignments = allAssignedUsers.filter(a => a.userId === userId);

          usersWithDetails.push({
            ...user,
            assignments: userAssignments,
            // Add unique identifier to prevent duplicate keys
            uniqueId: `user-${userId}-${Date.now()}-${Math.random()}`
          });
        }

      setAssignedUsers(usersWithDetails);
      setLoading(false);
    }, {
      fallbackValue: null,
      autoRefresh: true, // Auto refresh on critical errors
      refreshDelay: 5000,
      onError: (error) => {
        setLoading(false);
        setAssignedUsers([]);
        if (!axios.isCancel(error)) {
          console.error("Critical error fetching assigned users:", error);
          toast.error("Erreur lors du chargement des ressources affectées");
        }
      }
    });

    if (allUsers.length > 0) {
      fetchAssignedUsers();
    }

    // Cleanup on unmount or dependency change
    return () => {
      cancelAllRequests();
    };
  }, [id, allUsers, makeRequest, cancelAllRequests]);

  // Filter users based on search query
  const filteredUsers = assignedUsers.filter(user =>
    user.username?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.assignments?.some(assignment =>
      assignment.role?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      assignment.moduleName?.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const getUserInitials = (user) => {
    if (user.username) {
      return user.username.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    return user.email ? user.email[0].toUpperCase() : 'U';
  };

  const getRoleBadgeColor = (role) => {
    const roleColors = {
      'Chef de mission': 'bg-blue-100 text-blue-800',
      'Principal audité': 'bg-green-100 text-green-800',
      'Responsable': 'bg-purple-100 text-purple-800'
    };
    return roleColors[role] || 'bg-gray-100 text-gray-800';
  };

  if (!auditPlan) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
          <p className="text-gray-500">Chargement des informations du plan d'audit...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Users className="h-6 w-6 text-[#F62D51]" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Affectation des Ressources</h2>
            <p className="text-sm text-gray-600">
              Plan d'audit: <span className="font-medium">{auditPlan.name}</span>
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <UserCheck className="h-4 w-4" />
          <span>{filteredUsers.length} utilisateur(s) affecté(s)</span>
        </div>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Rechercher des utilisateurs..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Users List */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F62D51] mx-auto mb-4"></div>
            <p className="text-gray-500">Chargement des ressources affectées...</p>
          </div>
        </div>
      ) : filteredUsers.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredUsers.map((user) => (
            <Card key={user.uniqueId || `user-${user.id}-${Math.random()}`} className="shadow-sm hover:shadow-md transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center gap-4 pb-4">
                <Avatar>
                  <AvatarImage src={user.avatarUrl} alt={user.username} />
                  <AvatarFallback>{getUserInitials(user)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900">{user.username}</h3>
                  <p className="text-sm text-gray-500 flex items-center gap-2">
                    <Mail className="h-3 w-3" />
                    {user.email}
                  </p>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-gray-800">Rôles et Affectations</h4>
                  <div className="space-y-2">
                    {user.assignments.map((assignment, index) => (
                      <div key={`${user.id}-${assignment.moduleId}-${assignment.module}-${assignment.role}-${index}`} className="flex items-start gap-3 p-2 rounded-md bg-gray-50">
                        <div className="flex-shrink-0">
                          <Badge className={getRoleBadgeColor(assignment.role)}>{assignment.role}</Badge>
                        </div>
                        <div className="flex-1 text-sm">
                          <p className="font-medium text-gray-700">{assignment.moduleName}</p>
                          <p className="text-xs text-gray-500">{assignment.module}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Aucune ressource affectée
              </h3>
              <p className="text-gray-600">
                {searchQuery
                  ? 'Aucun utilisateur trouvé pour cette recherche'
                  : 'Aucun utilisateur n\'est actuellement affecté à ce plan d\'audit'
                }
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default AffectationRessourcesTab;
